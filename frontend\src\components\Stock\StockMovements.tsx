import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ProductService, InvoiceService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import "../Invoices/Invoice.css";

// Fonctions utilitaires
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("fr-FR");
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency: "EUR",
  }).format(amount);
};

const getMovementTypeColor = (type: string) => {
  switch (type) {
    case "ENTREE":
      return "#27ae60";
    case "SORTIE":
      return "#e74c3c";
    case "AJUSTEMENT":
      return "#f39c12";
    case "TRANSFERT":
      return "#3498db";
    default:
      return "#95a5a6";
  }
};

const getMovementTypeLabel = (type: string) => {
  switch (type) {
    case "ENTREE":
      return "Entrée";
    case "SORTIE":
      return "Sortie";
    case "AJUSTEMENT":
      return "Ajustement";
    case "TRANSFERT":
      return "Transfert";
    default:
      return type;
  }
};

interface StockMovement {
  id: number;
  produit_id: number;
  produit_nom: string;
  type_mouvement: string;
  quantite: number;
  date_mouvement: string;
  reference: string;
  notes: string;
  produit: number;
  motif: string;
  quantite_avant: number;
  quantite_apres: number;
  reference_document?: string;
  valeur_totale?: number;
  produit_details?: {
    nom: string;
    code_produit: string;
  };
}

interface Product {
  id: number;
  nom: string;
  stock_actuel: number;
  code_produit: string;
}

const StockMovements: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewMovement, setShowNewMovement] = useState(false);
  const [filters, setFilters] = useState({
    produit: "",
    type_mouvement: "all",
    date_debut: "",
    date_fin: "",
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Formulaire nouveau mouvement
  const [newMovement, setNewMovement] = useState({
    produit: "",
    type_mouvement: "ENTREE",
    quantite: 0,
    motif: "",
    reference_document: "",
    cout_unitaire: 0,
  });

  useEffect(() => {
    loadData();
  }, [filters, currentPage]);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    const res = await ProductService.getAll();
    if (res.success && res.data) {
      const items = Array.isArray(res.data) ? res.data : res.data.results || [];
      setProducts(items as any);
    }
  };

  const loadData = async () => {
    setLoading(true);
    const filterParams: any = {
      page: currentPage,
      page_size: 10,
    };

    if (filters.produit) filterParams.produit = parseInt(filters.produit);
    if (filters.type_mouvement !== "all")
      filterParams.type_mouvement = filters.type_mouvement;
    if (filters.date_debut) filterParams.date_debut = filters.date_debut;
    if (filters.date_fin) filterParams.date_fin = filters.date_fin;

    // Simulation d'appel API - à remplacer par le vrai service
    const res = { success: true, data: { results: [] } };
    if (res.success && res.data) {
      const data = res.data as any;
      setMovements(data.results || []);
      setTotalPages(Math.ceil(data.count / 10));
    }
    setLoading(false);
  };

  const handleCreateMovement = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMovement.produit || !newMovement.quantite || !newMovement.motif) {
      notify.error("Erreur", "Veuillez remplir tous les champs obligatoires");
      return;
    }

    // Simulation d'appel API - à remplacer par le vrai service
    const res = { success: true, data: newMovement };

    if (res.success) {
      notify.success("Succès", "Mouvement de stock enregistré");
      setShowNewMovement(false);
      setNewMovement({
        produit: "",
        type_mouvement: "ENTREE",
        quantite: 0,
        motif: "",
        reference_document: "",
        cout_unitaire: 0,
      });
      loadData();
    } else {
      notify.error("Erreur", "Impossible d'enregistrer le mouvement");
    }
  };

  const resetFilters = () => {
    setFilters({
      produit: "",
      type_mouvement: "all",
      date_debut: "",
      date_fin: "",
    });
    setCurrentPage(1);
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Mouvements de Stock</h1>
            <p className="page-subtitle">Historique des entrées et sorties</p>
          </div>
          <div className="page-actions">
            <button
              className="page-action secondary"
              onClick={() => navigate("/dashboard/stock/alertes")}>
              ⚠️ Alertes
            </button>
            <button
              className="page-action secondary"
              onClick={() => navigate("/dashboard/stock/resume")}>
              📊 Résumé
            </button>
            <button
              className="page-action"
              onClick={() => setShowNewMovement(true)}>
              ➕ Nouveau mouvement
            </button>
          </div>
        </div>
      </div>

      <div className="page-content">
        {/* Filtres */}
        <div
          style={{
            marginBottom: "20px",
            background: "#f8f9fa",
            padding: "20px",
            borderRadius: "8px",
          }}>
          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: "wrap",
              alignItems: "center",
            }}>
            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Produit:
              </label>
              <select
                value={filters.produit}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, produit: e.target.value }))
                }
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                  minWidth: "200px",
                }}>
                <option value="">Tous les produits</option>
                {products.map((product) => (
                  <option
                    key={product.id}
                    value={product.id}>
                    {product.nom} ({product.code_produit})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Type:
              </label>
              <select
                value={filters.type_mouvement}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    type_mouvement: e.target.value,
                  }))
                }
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="all">Tous</option>
                <option value="ENTREE">Entrées</option>
                <option value="SORTIE">Sorties</option>
                <option value="AJUSTEMENT">Ajustements</option>
                <option value="INVENTAIRE">Inventaire</option>
              </select>
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Du:
              </label>
              <input
                type="date"
                value={filters.date_debut}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    date_debut: e.target.value,
                  }))
                }
                style={{
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Au:
              </label>
              <input
                type="date"
                value={filters.date_fin}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, date_fin: e.target.value }))
                }
                style={{
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </div>

            <button
              onClick={resetFilters}
              style={{
                padding: "8px 12px",
                background: "#e74c3c",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
              }}>
              🗑️ Effacer
            </button>
          </div>
        </div>

        {/* Table des mouvements */}
        {loading ? (
          <div style={{ textAlign: "center", padding: "40px" }}>
            <div className="loading-spinner"></div>
            <div className="loading-text">Chargement des mouvements...</div>
          </div>
        ) : (
          <>
            <div className="table-container">
              <table className="data-table">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Produit</th>
                    <th>Type</th>
                    <th>Quantité</th>
                    <th>Stock avant/après</th>
                    <th>Motif</th>
                    <th>Référence</th>
                    <th>Valeur</th>
                  </tr>
                </thead>
                <tbody>
                  {movements.map((movement) => (
                    <tr key={movement.id}>
                      <td>{formatDate(movement.date_mouvement)}</td>
                      <td>
                        <strong>
                          {movement.produit_details?.nom || movement.produit}
                        </strong>
                        <div style={{ fontSize: "12px", color: "#666" }}>
                          {movement.produit_details?.code_produit}
                        </div>
                      </td>
                      <td>
                        <span
                          style={{
                            padding: "4px 8px",
                            borderRadius: "4px",
                            fontSize: "12px",
                            background:
                              getMovementTypeColor(movement.type_mouvement) +
                              "20",
                            color: getMovementTypeColor(
                              movement.type_mouvement
                            ),
                          }}>
                          {getMovementTypeLabel(movement.type_mouvement)}
                        </span>
                      </td>
                      <td>
                        <span
                          style={{
                            color:
                              movement.type_mouvement === "ENTREE"
                                ? "#27ae60"
                                : "#e74c3c",
                            fontWeight: "bold",
                          }}>
                          {movement.type_mouvement === "ENTREE" ? "+" : "-"}
                          {movement.quantite}
                        </span>
                      </td>
                      <td>
                        <div style={{ fontSize: "12px" }}>
                          <div>
                            {movement.quantite_avant} →{" "}
                            {movement.quantite_apres}
                          </div>
                        </div>
                      </td>
                      <td>{movement.motif}</td>
                      <td>
                        {movement.reference_document && (
                          <code
                            style={{
                              background: "#f8f9fa",
                              padding: "2px 6px",
                              borderRadius: "4px",
                            }}>
                            {movement.reference_document}
                          </code>
                        )}
                      </td>
                      <td>
                        {movement.valeur_totale && (
                          <strong>
                            {formatCurrency(movement.valeur_totale)}
                          </strong>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: "10px",
                  alignItems: "center",
                  marginTop: "20px",
                }}>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  style={{
                    padding: "8px 12px",
                    background: currentPage === 1 ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: currentPage === 1 ? "not-allowed" : "pointer",
                  }}>
                  ← Précédent
                </button>

                <span style={{ margin: "0 15px" }}>
                  Page {currentPage} sur {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  style={{
                    padding: "8px 12px",
                    background:
                      currentPage === totalPages ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor:
                      currentPage === totalPages ? "not-allowed" : "pointer",
                  }}>
                  Suivant →
                </button>
              </div>
            )}
          </>
        )}

        {movements.length === 0 && !loading && (
          <div
            style={{ textAlign: "center", padding: "40px", color: "#7f8c8d" }}>
            <p>Aucun mouvement trouvé.</p>
          </div>
        )}
      </div>

      {/* Modal nouveau mouvement */}
      {showNewMovement && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(0,0,0,0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}>
          <div
            style={{
              background: "white",
              borderRadius: "12px",
              padding: "24px",
              width: "90%",
              maxWidth: "500px",
              maxHeight: "90vh",
              overflow: "auto",
            }}>
            <h3 style={{ margin: "0 0 20px 0" }}>Nouveau mouvement de stock</h3>

            <form onSubmit={handleCreateMovement}>
              <div style={{ marginBottom: "15px" }}>
                <label
                  style={{
                    display: "block",
                    marginBottom: "5px",
                    fontWeight: "bold",
                  }}>
                  Produit *
                </label>
                <select
                  value={newMovement.produit}
                  onChange={(e) =>
                    setNewMovement((prev) => ({
                      ...prev,
                      produit: e.target.value,
                    }))
                  }
                  style={{
                    width: "100%",
                    padding: "8px 12px",
                    border: "1px solid #dee2e6",
                    borderRadius: "6px",
                  }}
                  required>
                  <option value="">Sélectionner un produit</option>
                  {products.map((product) => (
                    <option
                      key={product.id}
                      value={product.id}>
                      {product.nom} ({product.code_produit}) - Stock:{" "}
                      {product.stock_actuel}
                    </option>
                  ))}
                </select>
              </div>

              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "15px",
                  marginBottom: "15px",
                }}>
                <div>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "5px",
                      fontWeight: "bold",
                    }}>
                    Type *
                  </label>
                  <select
                    value={newMovement.type_mouvement}
                    onChange={(e) =>
                      setNewMovement((prev) => ({
                        ...prev,
                        type_mouvement: e.target.value,
                      }))
                    }
                    style={{
                      width: "100%",
                      padding: "8px 12px",
                      border: "1px solid #dee2e6",
                      borderRadius: "6px",
                    }}>
                    <option value="ENTREE">Entrée</option>
                    <option value="SORTIE">Sortie</option>
                    <option value="AJUSTEMENT">Ajustement</option>
                  </select>
                </div>

                <div>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "5px",
                      fontWeight: "bold",
                    }}>
                    Quantité *
                  </label>
                  <input
                    type="number"
                    value={newMovement.quantite}
                    onChange={(e) =>
                      setNewMovement((prev) => ({
                        ...prev,
                        quantite: parseFloat(e.target.value) || 0,
                      }))
                    }
                    style={{
                      width: "100%",
                      padding: "8px 12px",
                      border: "1px solid #dee2e6",
                      borderRadius: "6px",
                    }}
                    min="0"
                    step="1"
                    required
                  />
                </div>
              </div>

              <div style={{ marginBottom: "15px" }}>
                <label
                  style={{
                    display: "block",
                    marginBottom: "5px",
                    fontWeight: "bold",
                  }}>
                  Motif *
                </label>
                <input
                  type="text"
                  value={newMovement.motif}
                  onChange={(e) =>
                    setNewMovement((prev) => ({
                      ...prev,
                      motif: e.target.value,
                    }))
                  }
                  style={{
                    width: "100%",
                    padding: "8px 12px",
                    border: "1px solid #dee2e6",
                    borderRadius: "6px",
                  }}
                  placeholder="Raison du mouvement"
                  required
                />
              </div>

              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "15px",
                  marginBottom: "20px",
                }}>
                <div>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "5px",
                      fontWeight: "bold",
                    }}>
                    Référence document
                  </label>
                  <input
                    type="text"
                    value={newMovement.reference_document}
                    onChange={(e) =>
                      setNewMovement((prev) => ({
                        ...prev,
                        reference_document: e.target.value,
                      }))
                    }
                    style={{
                      width: "100%",
                      padding: "8px 12px",
                      border: "1px solid #dee2e6",
                      borderRadius: "6px",
                    }}
                    placeholder="N° facture, bon..."
                  />
                </div>

                <div>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "5px",
                      fontWeight: "bold",
                    }}>
                    Coût unitaire
                  </label>
                  <input
                    type="number"
                    value={newMovement.cout_unitaire}
                    onChange={(e) =>
                      setNewMovement((prev) => ({
                        ...prev,
                        cout_unitaire: parseFloat(e.target.value) || 0,
                      }))
                    }
                    style={{
                      width: "100%",
                      padding: "8px 12px",
                      border: "1px solid #dee2e6",
                      borderRadius: "6px",
                    }}
                    min="0"
                    step="0.001"
                    placeholder="0.000"
                  />
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  gap: "10px",
                  justifyContent: "flex-end",
                }}>
                <button
                  type="button"
                  onClick={() => setShowNewMovement(false)}
                  style={{
                    padding: "10px 20px",
                    background: "#95a5a6",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                  }}>
                  Annuler
                </button>
                <button
                  type="submit"
                  style={{
                    padding: "10px 20px",
                    background: "#27ae60",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                  }}>
                  Enregistrer
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockMovements;
