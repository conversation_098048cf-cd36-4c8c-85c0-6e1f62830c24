/* Styles pour le module Stock */

.stock-container {
  padding: 20px;
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.stock-title {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.stock-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.stock-tab {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.stock-tab.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.stock-tab:hover {
  color: #007bff;
}

.stock-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stock-item {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 15px;
  transition: all 0.2s;
}

.stock-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.stock-product {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.stock-quantity {
  font-size: 1.1rem;
  font-weight: 600;
}

.stock-quantity.low {
  color: #dc3545;
}

.stock-quantity.medium {
  color: #ffc107;
}

.stock-quantity.high {
  color: #28a745;
}

.stock-details {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.stock-alert {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.stock-alert.critical {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.alert-icon {
  font-size: 1.2rem;
  margin-right: 10px;
}

/* Responsive */
@media (max-width: 768px) {
  .stock-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .stock-tabs {
    flex-wrap: wrap;
  }
  
  .stock-tab {
    flex: 1;
    min-width: 120px;
  }
}
