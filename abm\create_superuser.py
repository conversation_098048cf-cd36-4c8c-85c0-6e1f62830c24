#!/usr/bin/env python
"""
Script pour créer un superuser Django
"""
import os
import sys
import django

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'abm.settings')
django.setup()

from Authentication.models import CustomUser

def create_superuser():
    """Créer un superuser si il n'existe pas"""
    
    # Vérifier si un superuser existe déjà
    if CustomUser.objects.filter(is_superuser=True).exists():
        print("✅ Un superuser existe déjà.")
        superuser = CustomUser.objects.filter(is_superuser=True).first()
        print(f"   Username: {superuser.username}")
        print(f"   Email: {superuser.email}")
        return
    
    # Créer un nouveau superuser
    print("🔧 Création d'un nouveau superuser...")
    
    try:
        superuser = CustomUser.objects.create_superuser(
            username='ayou',
            email='<EMAIL>',
            password='ayoub123',
            first_name='Ayoub',
            last_name='Guenichi'
        )
        
        print("✅ Superuser créé avec succès !")
        print(f"   Username: {superuser.username}")
        print(f"   Email: {superuser.email}")
        print(f"   Password: admin123")
        print("\n🌐 Vous pouvez maintenant vous connecter à l'admin Django :")
        print("   URL: http://127.0.0.1:8000/admin/")
        print("   Username: ayoub")
        print("   Password: ayoub123")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du superuser: {e}")

if __name__ == "__main__":
    create_superuser()
