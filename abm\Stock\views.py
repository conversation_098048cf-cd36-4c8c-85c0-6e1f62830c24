from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count, Q, F
from django.utils import timezone

from .models import MouvementStock, Inventaire, LigneInventaire
from .serializers import MouvementStockSerializer, InventaireSerializer, LigneInventaireSerializer
from Authentication.permissions import HasModulePermission

class MouvementStockViewSet(viewsets.ModelViewSet):
    """ViewSet pour les mouvements de stock"""

    queryset = MouvementStock.objects.all()
    serializer_class = MouvementStockSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'stock'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type_mouvement', 'produit', 'commande', 'facture']
    search_fields = ['reference', 'motif', 'produit__nom', 'produit__reference']
    ordering_fields = ['date_mouvement', 'quantite', 'valeur_totale']
    ordering = ['-date_mouvement']

    def perform_create(self, serializer):
        # Générer une référence automatique
        reference = self.generer_reference_mouvement()

        # Récupérer le produit et calculer les quantités
        produit = serializer.validated_data['produit']
        quantite = serializer.validated_data['quantite']
        type_mouvement = serializer.validated_data['type_mouvement']

        quantite_avant = produit.stock_actuel

        # Calculer la nouvelle quantité selon le type de mouvement
        if type_mouvement in ['ENTREE', 'RETOUR']:
            quantite_apres = quantite_avant + quantite
        elif type_mouvement in ['SORTIE']:
            quantite_apres = quantite_avant - quantite
        else:  # AJUSTEMENT, INVENTAIRE
            quantite_apres = quantite
            quantite = quantite_apres - quantite_avant

        # Sauvegarder le mouvement
        mouvement = serializer.save(
            reference=reference,
            quantite_avant=quantite_avant,
            quantite_apres=quantite_apres,
            quantite=abs(quantite),
            created_by=self.request.user
        )

        # Mettre à jour le stock du produit
        produit.stock_actuel = quantite_apres
        produit.save()

        return mouvement

    def generer_reference_mouvement(self):
        """Génère une référence de mouvement unique"""
        today = timezone.now()
        prefix = f"MVT-{today.year}-{today.month:02d}-"

        last_mouvement = MouvementStock.objects.filter(
            reference__startswith=prefix
        ).order_by('-reference').first()

        if last_mouvement:
            last_number = int(last_mouvement.reference.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:04d}"

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Statistiques des mouvements de stock"""

        # Mouvements du jour
        today = timezone.now().date()
        mouvements_jour = MouvementStock.objects.filter(
            date_mouvement__date=today
        ).count()

        # Mouvements par type (7 derniers jours)
        week_ago = timezone.now() - timedelta(days=7)
        mouvements_semaine = MouvementStock.objects.filter(
            date_mouvement__gte=week_ago
        ).values('type_mouvement').annotate(
            count=Count('id'),
            quantite_totale=Sum('quantite')
        ).order_by('-count')

        # Valeur des mouvements
        valeur_entrees = MouvementStock.objects.filter(
            type_mouvement='ENTREE',
            date_mouvement__gte=week_ago,
            valeur_totale__isnull=False
        ).aggregate(total=Sum('valeur_totale'))['total'] or 0

        valeur_sorties = MouvementStock.objects.filter(
            type_mouvement='SORTIE',
            date_mouvement__gte=week_ago,
            valeur_totale__isnull=False
        ).aggregate(total=Sum('valeur_totale'))['total'] or 0

        data = {
            'mouvements_jour': mouvements_jour,
            'mouvements_semaine': list(mouvements_semaine),
            'valeur_entrees_semaine': float(valeur_entrees),
            'valeur_sorties_semaine': float(valeur_sorties)
        }

        return Response(data)


class InventaireViewSet(viewsets.ModelViewSet):
    """ViewSet pour les inventaires"""

    queryset = Inventaire.objects.all()
    serializer_class = InventaireSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'stock'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut']
    search_fields = ['numero', 'nom', 'description']
    ordering = ['-date_debut']

    def perform_create(self, serializer):
        # Générer un numéro d'inventaire automatique
        numero = self.generer_numero_inventaire()
        serializer.save(
            numero=numero,
            created_by=self.request.user
        )

    def generer_numero_inventaire(self):
        """Génère un numéro d'inventaire unique"""
        today = timezone.now()
        prefix = f"INV-{today.year}-"

        last_inventaire = Inventaire.objects.filter(
            numero__startswith=prefix
        ).order_by('-numero').first()

        if last_inventaire:
            last_number = int(last_inventaire.numero.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:04d}"

    @action(detail=True, methods=['post'])
    def valider(self, request, pk=None):
        """Valide un inventaire et applique les écarts"""
        inventaire = self.get_object()

        if inventaire.statut != 'TERMINE':
            return Response(
                {'error': 'Seuls les inventaires terminés peuvent être validés'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Appliquer les écarts
        for ligne in inventaire.lignes.all():
            if ligne.ecart != 0:
                # Créer un mouvement de stock pour l'écart
                MouvementStock.objects.create(
                    produit=ligne.produit,
                    type_mouvement='INVENTAIRE',
                    quantite=abs(ligne.ecart),
                    quantite_avant=ligne.stock_theorique,
                    quantite_apres=ligne.stock_physique,
                    prix_unitaire=ligne.prix_unitaire,
                    motif=f"Inventaire {inventaire.numero}",
                    reference_externe=inventaire.numero,
                    created_by=request.user
                )

                # Mettre à jour le stock du produit
                ligne.produit.stock_actuel = ligne.stock_physique
                ligne.produit.save()

        # Marquer l'inventaire comme validé
        inventaire.statut = 'VALIDE'
        inventaire.date_validation = timezone.now()
        inventaire.validated_by = request.user
        inventaire.save()

        return Response({'message': 'Inventaire validé et écarts appliqués'})


class LigneInventaireViewSet(viewsets.ModelViewSet):
    """ViewSet pour les lignes d'inventaire"""

    queryset = LigneInventaire.objects.all()
    serializer_class = LigneInventaireSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'stock'

    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['inventaire', 'produit']
