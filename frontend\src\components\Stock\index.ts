/**
 * Index des composants Stock
 */

export { default as Stock } from './Stock';
export { default as StockOverview } from './StockOverview';
export { default as StockMovements } from './StockMovements';
export { default as StockAlerts } from './StockAlerts';
export { default as StockInventory } from './StockInventory';
export { default as StockStats } from './StockStats';

// Types
export interface StockMovement {
  id: string;
  produit_id: string;
  produit_nom: string;
  type: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'INVENTAIRE';
  quantite: number;
  quantite_avant: number;
  quantite_apres: number;
  prix_unitaire?: number;
  valeur_totale?: number;
  motif: string;
  reference?: string;
  date: string;
  utilisateur: string;
  created_at: string;
}

export interface StockAlert {
  id: string;
  produit_id: string;
  produit_nom: string;
  stock_actuel: number;
  stock_minimum: number;
  stock_maximum?: number;
  type: 'RUPTURE' | 'MINIMUM' | 'MAXIMUM' | 'PEREMPTION';
  niveau: 'CRITIQUE' | 'ALERTE' | 'INFO';
  date_creation: string;
  resolu: boolean;
}

// Routes
export const STOCK_ROUTES = {
  MAIN: '/stock',
  OVERVIEW: '/stock/overview',
  MOVEMENTS: '/stock/movements',
  ALERTS: '/stock/alerts',
  INVENTORY: '/stock/inventory',
  STATS: '/stock/stats'
} as const;
