/**
 * Module E-commerce - Gestion boutique en ligne
 */

import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import ProductCatalog from './ProductCatalog';
import ShoppingCart from './ShoppingCart';
import OrdersList from './OrdersList';
import EcommerceStats from './EcommerceStats';
import EcommerceSettings from './EcommerceSettings';
import { useAuth } from '../../contexts/AuthContext';
import './Ecommerce.css';

const Ecommerce: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [activeView, setActiveView] = useState(() => {
    const path = location.pathname;
    if (path.includes('/catalog')) return 'catalog';
    if (path.includes('/cart')) return 'cart';
    if (path.includes('/orders')) return 'orders';
    if (path.includes('/stats')) return 'stats';
    if (path.includes('/settings')) return 'settings';
    return 'catalog';
  });

  // Vérifier les permissions
  const hasAccess = () => {
    const allowedRoles = ['NORMAL', 'COMPTABLE', 'ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || '');
  };

  const hasAdminAccess = () => {
    const allowedRoles = ['ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || '');
  };

  if (!hasAccess()) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder au module e-commerce.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="ecommerce-module">
      {/* Header */}
      <div className="ecommerce-header">
        <div className="header-title">
          <h1>🛒 E-commerce</h1>
          <p>Boutique en ligne et gestion des ventes</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-outline"
            onClick={() => navigate('/dashboard')}
          >
            ← Dashboard
          </button>
          {hasAdminAccess() && (
            <button 
              className="btn btn-info"
              onClick={() => navigate('/ecommerce/settings')}
            >
              ⚙️ Configuration
            </button>
          )}
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/ecommerce/stats')}
          >
            📊 Statistiques
          </button>
        </div>
      </div>

      {/* Navigation interne */}
      <div className="ecommerce-nav">
        <button
          className={`nav-btn ${activeView === 'catalog' ? 'active' : ''}`}
          onClick={() => navigate('/ecommerce/catalog')}
        >
          📦 Catalogue
        </button>
        <button
          className={`nav-btn ${activeView === 'cart' ? 'active' : ''}`}
          onClick={() => navigate('/ecommerce/cart')}
        >
          🛒 Panier
        </button>
        <button
          className={`nav-btn ${activeView === 'orders' ? 'active' : ''}`}
          onClick={() => navigate('/ecommerce/orders')}
        >
          📋 Commandes
        </button>
        <button
          className={`nav-btn ${activeView === 'stats' ? 'active' : ''}`}
          onClick={() => navigate('/ecommerce/stats')}
        >
          📊 Statistiques
        </button>
        {hasAdminAccess() && (
          <button
            className={`nav-btn ${activeView === 'settings' ? 'active' : ''}`}
            onClick={() => navigate('/ecommerce/settings')}
          >
            ⚙️ Configuration
          </button>
        )}
      </div>

      {/* Contenu */}
      <div className="ecommerce-content">
        <Routes>
          <Route path="/" element={<ProductCatalog />} />
          <Route path="/catalog" element={<ProductCatalog />} />
          <Route path="/cart" element={<ShoppingCart />} />
          <Route path="/orders" element={<OrdersList />} />
          <Route path="/stats" element={<EcommerceStats />} />
          {hasAdminAccess() && (
            <Route path="/settings" element={<EcommerceSettings />} />
          )}
        </Routes>
      </div>
    </div>
  );
};

export default Ecommerce;
