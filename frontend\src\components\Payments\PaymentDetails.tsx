import React from "react";
import "./PaymentDetails.css";

interface PaymentDetailsProps {
  payment: any;
  onClose: () => void;
  onGenerateReceipt?: () => void;
}

const PaymentDetails: React.FC<PaymentDetailsProps> = ({
  payment,
  onClose,
  onGenerateReceipt,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "VALIDE":
        return "#10b981";
      case "EN_ATTENTE":
        return "#f59e0b";
      case "REJETE":
        return "#ef4444";
      case "REMBOURSE":
        return "#8b5cf6";
      default:
        return "#6b7280";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "VALIDE":
        return "Validé";
      case "EN_ATTENTE":
        return "En attente";
      case "REJETE":
        return "Rejeté";
      case "REMBOURSE":
        return "Remboursé";
      case "ANNULE":
        return "Annulé";
      default:
        return status;
    }
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case "ESPECES":
        return "Espèces";
      case "CHEQUE":
        return "Chèque";
      case "VIREMENT":
        return "Virement";
      case "CARTE":
        return "Carte bancaire";
      case "PRELEVEMENT":
        return "Prélèvement";
      case "PAYPAL":
        return "PayPal";
      case "STRIPE":
        return "Stripe";
      default:
        return method;
    }
  };

  const getPaymentTypeLabel = (type: string) => {
    switch (type) {
      case "ENCAISSEMENT":
        return "Encaissement";
      case "DECAISSEMENT":
        return "Décaissement";
      case "VIREMENT":
        return "Virement";
      case "REMBOURSEMENT":
        return "Remboursement";
      default:
        return type;
    }
  };

  return (
    <div className="payment-details-overlay">
      <div className="payment-details-modal">
        <div className="payment-details-header">
          <div className="header-left">
            <h2>💳 Détails du Paiement</h2>
            <span className="payment-reference">{payment.reference}</span>
          </div>
          <button className="close-btn" onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="payment-details-content">
          <div className="details-grid">
            {/* Informations principales */}
            <div className="detail-section">
              <h3>📋 Informations Générales</h3>
              <div className="detail-row">
                <span className="label">Référence:</span>
                <span className="value">{payment.reference}</span>
              </div>
              <div className="detail-row">
                <span className="label">Type:</span>
                <span className="value">{getPaymentTypeLabel(payment.type_paiement)}</span>
              </div>
              <div className="detail-row">
                <span className="label">Mode de paiement:</span>
                <span className="value">{getPaymentMethodLabel(payment.mode_paiement)}</span>
              </div>
              <div className="detail-row">
                <span className="label">Statut:</span>
                <span 
                  className="value status-badge"
                  style={{ 
                    backgroundColor: getStatusColor(payment.statut),
                    color: 'white',
                    padding: '4px 12px',
                    borderRadius: '20px',
                    fontSize: '0.85rem',
                    fontWeight: '600'
                  }}
                >
                  {getStatusLabel(payment.statut)}
                </span>
              </div>
            </div>

            {/* Montants */}
            <div className="detail-section">
              <h3>💰 Montants</h3>
              <div className="detail-row">
                <span className="label">Montant:</span>
                <span className="value amount">{payment.montant.toFixed(3)} {payment.devise}</span>
              </div>
              <div className="detail-row">
                <span className="label">Devise:</span>
                <span className="value">{payment.devise}</span>
              </div>
            </div>

            {/* Dates */}
            <div className="detail-section">
              <h3>📅 Dates</h3>
              <div className="detail-row">
                <span className="label">Date de paiement:</span>
                <span className="value">
                  {new Date(payment.date_paiement).toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
              {payment.date_valeur && (
                <div className="detail-row">
                  <span className="label">Date de valeur:</span>
                  <span className="value">
                    {new Date(payment.date_valeur).toLocaleDateString('fr-FR', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
              )}
              <div className="detail-row">
                <span className="label">Créé le:</span>
                <span className="value">
                  {new Date(payment.created_at).toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            </div>

            {/* Client/Facture */}
            <div className="detail-section">
              <h3>👤 Client & Facture</h3>
              {payment.client_nom && (
                <div className="detail-row">
                  <span className="label">Client:</span>
                  <span className="value">{payment.client_nom}</span>
                </div>
              )}
              {payment.facture_numero && (
                <div className="detail-row">
                  <span className="label">Facture:</span>
                  <span className="value">{payment.facture_numero}</span>
                </div>
              )}
              {payment.fournisseur && (
                <div className="detail-row">
                  <span className="label">Fournisseur:</span>
                  <span className="value">{payment.fournisseur}</span>
                </div>
              )}
            </div>

            {/* Rapprochement */}
            <div className="detail-section">
              <h3>🔄 Rapprochement</h3>
              <div className="detail-row">
                <span className="label">Rapproché:</span>
                <span className={`value ${payment.rapproche ? 'success' : 'warning'}`}>
                  {payment.rapproche ? '✅ Oui' : '⏳ Non'}
                </span>
              </div>
            </div>

            {/* Description */}
            {payment.description && (
              <div className="detail-section full-width">
                <h3>📝 Description</h3>
                <div className="description-content">
                  {payment.description}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="payment-details-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Fermer
          </button>
          {onGenerateReceipt && (
            <button className="btn btn-primary" onClick={onGenerateReceipt}>
              📄 Générer un reçu
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentDetails;
