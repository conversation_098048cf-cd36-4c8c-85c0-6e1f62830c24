# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Clients', '0001_initial'),
        ('Produits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CategorieEcommerce',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('nom', models.CharField(max_length=200)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='categories_ecommerce/')),
                ('ordre', models.IntegerField(default=0)),
                ('meta_title', models.CharField(blank=True, max_length=200)),
                ('meta_description', models.TextField(blank=True)),
                ('meta_keywords', models.CharField(blank=True, max_length=500)),
                ('vues_total', models.PositiveIntegerField(default=0)),
                ('produits_vendus', models.PositiveIntegerField(default=0)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='enfants', to='Ecommerce.categorieecommerce')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Catégorie E-commerce',
                'verbose_name_plural': 'Catégories E-commerce',
                'ordering': ['ordre', 'nom'],
            },
        ),
        migrations.CreateModel(
            name='CommandeEcommerce',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numero', models.CharField(max_length=50, unique=True)),
                ('statut', models.CharField(choices=[('EN_ATTENTE', 'En attente'), ('CONFIRMEE', 'Confirmée'), ('EN_PREPARATION', 'En préparation'), ('EXPEDIEE', 'Expédiée'), ('LIVREE', 'Livrée'), ('ANNULEE', 'Annulée'), ('REMBOURSEE', 'Remboursée')], default='EN_ATTENTE', max_length=20)),
                ('total_ht', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_tva', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_ttc', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('frais_livraison', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('adresse_facturation', models.JSONField()),
                ('adresse_livraison', models.JSONField()),
                ('transporteur', models.CharField(blank=True, max_length=100)),
                ('numero_suivi', models.CharField(blank=True, max_length=100)),
                ('date_expedition', models.DateTimeField(blank=True, null=True)),
                ('date_livraison_prevue', models.DateTimeField(blank=True, null=True)),
                ('date_livraison_reelle', models.DateTimeField(blank=True, null=True)),
                ('methode_paiement', models.CharField(blank=True, max_length=50)),
                ('statut_paiement', models.CharField(default='EN_ATTENTE', max_length=20)),
                ('reference_paiement', models.CharField(blank=True, max_length=100)),
                ('notes_client', models.TextField(blank=True)),
                ('notes_interne', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='commandes_ecommerce', to='Clients.client')),
            ],
            options={
                'verbose_name': 'Commande E-commerce',
                'verbose_name_plural': 'Commandes E-commerce',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Panier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Clients.client')),
            ],
            options={
                'verbose_name': 'Panier',
                'verbose_name_plural': 'Paniers',
            },
        ),
        migrations.CreateModel(
            name='ProduitEcommerce',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('slug', models.SlugField(unique=True)),
                ('visible_en_ligne', models.BooleanField(default=True)),
                ('en_vedette', models.BooleanField(default=False)),
                ('nouveau', models.BooleanField(default=False)),
                ('en_promotion', models.BooleanField(default=False)),
                ('prix_public', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('prix_promo', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('date_debut_promo', models.DateTimeField(blank=True, null=True)),
                ('date_fin_promo', models.DateTimeField(blank=True, null=True)),
                ('stock_disponible', models.IntegerField(default=0)),
                ('stock_reserve', models.IntegerField(default=0)),
                ('gestion_stock', models.BooleanField(default=True)),
                ('autoriser_commande_sans_stock', models.BooleanField(default=False)),
                ('meta_title', models.CharField(blank=True, max_length=200)),
                ('meta_description', models.TextField(blank=True)),
                ('meta_keywords', models.CharField(blank=True, max_length=500)),
                ('description_courte', models.TextField(blank=True)),
                ('description_longue', models.TextField(blank=True)),
                ('caracteristiques', models.JSONField(blank=True, default=dict)),
                ('poids', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('dimensions_longueur', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('dimensions_largeur', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('dimensions_hauteur', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('nombre_vues', models.IntegerField(default=0)),
                ('nombre_ventes', models.IntegerField(default=0)),
                ('note_moyenne', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('nombre_avis', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('categories_ecommerce', models.ManyToManyField(blank=True, to='Ecommerce.categorieecommerce')),
                ('produit', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='ecommerce', to='Produits.produit')),
            ],
            options={
                'verbose_name': 'Produit E-commerce',
                'verbose_name_plural': 'Produits E-commerce',
                'ordering': ['-en_vedette', '-nouveau', 'produit__nom'],
            },
        ),
        migrations.CreateModel(
            name='LigneCommandeEcommerce',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom_produit', models.CharField(max_length=200)),
                ('code_produit', models.CharField(max_length=50)),
                ('quantite', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10)),
                ('taux_tva', models.DecimalField(decimal_places=2, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('commande', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lignes', to='Ecommerce.commandeecommerce')),
                ('produit_ecommerce', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='Ecommerce.produitecommerce')),
            ],
            options={
                'verbose_name': 'Ligne Commande E-commerce',
                'verbose_name_plural': 'Lignes Commandes E-commerce',
            },
        ),
        migrations.CreateModel(
            name='ImageProduitEcommerce',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='produits_ecommerce/')),
                ('alt_text', models.CharField(blank=True, max_length=200)),
                ('ordre', models.IntegerField(default=0)),
                ('principale', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('produit_ecommerce', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='Ecommerce.produitecommerce')),
            ],
            options={
                'verbose_name': 'Image Produit E-commerce',
                'verbose_name_plural': 'Images Produits E-commerce',
                'ordering': ['ordre', '-principale'],
            },
        ),
        migrations.CreateModel(
            name='ItemPanier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantite', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('panier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='Ecommerce.panier')),
                ('produit_ecommerce', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Ecommerce.produitecommerce')),
            ],
            options={
                'verbose_name': 'Article Panier',
                'verbose_name_plural': 'Articles Panier',
                'unique_together': {('panier', 'produit_ecommerce')},
            },
        ),
    ]
