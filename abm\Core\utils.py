"""
Utilitaires intelligents et fonctionnalités avancées
"""
import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.core.cache import cache
from django.db.models import Model, Q
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
import re


logger = logging.getLogger(__name__)


class SmartCache:
    """Système de cache intelligent avec invalidation automatique"""
    
    @staticmethod
    def get_key(prefix: str, *args, **kwargs) -> str:
        """Génère une clé de cache unique"""
        key_data = f"{prefix}:{':'.join(map(str, args))}"
        if kwargs:
            key_data += f":{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    @staticmethod
    def set_smart(key: str, value: Any, timeout: int = 3600, tags: List[str] = None):
        """Cache intelligent avec tags pour invalidation groupée"""
        cache.set(key, value, timeout)
        if tags:
            for tag in tags:
                tag_key = f"tag:{tag}"
                tagged_keys = cache.get(tag_key, set())
                tagged_keys.add(key)
                cache.set(tag_key, tagged_keys, timeout * 2)
    
    @staticmethod
    def invalidate_by_tag(tag: str):
        """Invalide tous les caches avec un tag donné"""
        tag_key = f"tag:{tag}"
        tagged_keys = cache.get(tag_key, set())
        for key in tagged_keys:
            cache.delete(key)
        cache.delete(tag_key)


class AIAnalyzer:
    """Analyseur IA pour insights business"""
    
    @staticmethod
    def analyze_sales_trend(data: List[Dict]) -> Dict:
        """Analyse les tendances de vente"""
        if not data:
            return {"trend": "insufficient_data", "confidence": 0.0}
        
        # Calcul de la tendance simple
        values = [item.get('amount', 0) for item in data]
        if len(values) < 2:
            return {"trend": "insufficient_data", "confidence": 0.0}
        
        # Tendance linéaire simple
        n = len(values)
        sum_x = sum(range(n))
        sum_y = sum(values)
        sum_xy = sum(i * values[i] for i in range(n))
        sum_x2 = sum(i * i for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        if slope > 0.1:
            trend = "increasing"
            confidence = min(abs(slope) * 10, 1.0)
        elif slope < -0.1:
            trend = "decreasing"
            confidence = min(abs(slope) * 10, 1.0)
        else:
            trend = "stable"
            confidence = 1.0 - abs(slope) * 10
        
        return {
            "trend": trend,
            "slope": slope,
            "confidence": confidence,
            "recommendation": AIAnalyzer._get_sales_recommendation(trend, slope)
        }
    
    @staticmethod
    def _get_sales_recommendation(trend: str, slope: float) -> str:
        """Génère des recommandations basées sur la tendance"""
        if trend == "increasing":
            return "Excellente performance ! Considérez augmenter le stock des produits populaires."
        elif trend == "decreasing":
            return "Attention : baisse des ventes. Analysez les causes et lancez des promotions."
        else:
            return "Ventes stables. Opportunité d'innovation pour stimuler la croissance."
    
    @staticmethod
    def predict_stock_needs(product_data: Dict, sales_history: List[Dict]) -> Dict:
        """Prédiction intelligente des besoins en stock"""
        if not sales_history:
            return {"prediction": 0, "confidence": 0.0, "days_until_stockout": None}
        
        # Calcul de la consommation moyenne
        total_sold = sum(item.get('quantity', 0) for item in sales_history)
        days = len(sales_history)
        avg_daily_consumption = total_sold / days if days > 0 else 0
        
        current_stock = product_data.get('stock', 0)
        
        if avg_daily_consumption > 0:
            days_until_stockout = current_stock / avg_daily_consumption
            # Prédiction pour 30 jours
            predicted_need = avg_daily_consumption * 30
            confidence = min(days / 30, 1.0)  # Plus de données = plus de confiance
        else:
            days_until_stockout = None
            predicted_need = 0
            confidence = 0.0
        
        return {
            "prediction": predicted_need,
            "confidence": confidence,
            "days_until_stockout": days_until_stockout,
            "avg_daily_consumption": avg_daily_consumption,
            "recommendation": AIAnalyzer._get_stock_recommendation(days_until_stockout)
        }
    
    @staticmethod
    def _get_stock_recommendation(days_until_stockout: Optional[float]) -> str:
        """Recommandations de stock"""
        if days_until_stockout is None:
            return "Pas de données de vente récentes. Surveillez les mouvements de stock."
        elif days_until_stockout < 7:
            return "🚨 URGENT: Stock critique ! Réapprovisionnement immédiat nécessaire."
        elif days_until_stockout < 14:
            return "⚠️ Stock faible. Planifiez un réapprovisionnement sous 7 jours."
        elif days_until_stockout < 30:
            return "📊 Stock correct. Surveillez l'évolution des ventes."
        else:
            return "✅ Stock suffisant pour le mois à venir."


class SmartValidator:
    """Validateur intelligent avec règles métier"""
    
    @staticmethod
    def validate_tunisian_phone(phone: str) -> Dict[str, Any]:
        """Validation intelligente des numéros tunisiens"""
        if not phone:
            return {"valid": False, "error": "Numéro requis"}
        
        # Nettoyage
        clean_phone = re.sub(r'[^\d+]', '', phone)
        
        # Formats tunisiens
        patterns = [
            r'^\+216[2-9]\d{7}$',  # International
            r'^216[2-9]\d{7}$',    # Sans +
            r'^[2-9]\d{7}$',       # Local
        ]
        
        for pattern in patterns:
            if re.match(pattern, clean_phone):
                # Normalisation au format international
                if clean_phone.startswith('+216'):
                    normalized = clean_phone
                elif clean_phone.startswith('216'):
                    normalized = '+' + clean_phone
                else:
                    normalized = '+216' + clean_phone
                
                return {
                    "valid": True,
                    "normalized": normalized,
                    "type": "mobile" if normalized[4] in ['2', '4', '5', '9'] else "landline"
                }
        
        return {"valid": False, "error": "Format de numéro tunisien invalide"}
    
    @staticmethod
    def validate_tunisian_tax_id(tax_id: str) -> Dict[str, Any]:
        """Validation du matricule fiscal tunisien"""
        if not tax_id:
            return {"valid": False, "error": "Matricule fiscal requis"}
        
        # Format: 1234567 X/Y/Z/000
        pattern = r'^\d{7}\s*[A-Z]/[A-Z]/[A-Z]/\d{3}$'
        
        if re.match(pattern, tax_id.upper()):
            return {"valid": True, "normalized": tax_id.upper()}
        
        return {"valid": False, "error": "Format de matricule fiscal invalide"}


class SmartFormatter:
    """Formateur intelligent pour l'affichage"""
    
    @staticmethod
    def format_currency(amount: Decimal, currency: str = "TND") -> str:
        """Formatage intelligent des montants"""
        if currency == "TND":
            return f"{amount:,.3f} DT"
        elif currency == "EUR":
            return f"{amount:,.2f} €"
        elif currency == "USD":
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.2f} {currency}"
    
    @staticmethod
    def format_percentage(value: float, decimals: int = 1) -> str:
        """Formatage des pourcentages"""
        return f"{value:.{decimals}f}%"
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """Formatage intelligent des tailles de fichier"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


class SmartSearch:
    """Moteur de recherche intelligent"""
    
    @staticmethod
    def build_search_query(model_class: Model, search_term: str, fields: List[str]) -> Q:
        """Construction intelligente de requêtes de recherche"""
        if not search_term:
            return Q()
        
        terms = search_term.split()
        query = Q()
        
        for term in terms:
            term_query = Q()
            for field in fields:
                if '__' in field:  # Relation
                    term_query |= Q(**{f"{field}__icontains": term})
                else:
                    term_query |= Q(**{f"{field}__icontains": term})
            query &= term_query
        
        return query
    
    @staticmethod
    def get_search_suggestions(model_class: Model, field: str, partial: str, limit: int = 10) -> List[str]:
        """Suggestions de recherche intelligentes"""
        if len(partial) < 2:
            return []
        
        suggestions = model_class.objects.filter(
            **{f"{field}__icontains": partial}
        ).values_list(field, flat=True).distinct()[:limit]
        
        return list(suggestions)


class PerformanceMonitor:
    """Moniteur de performance intelligent"""
    
    @staticmethod
    def measure_execution_time(func):
        """Décorateur pour mesurer le temps d'exécution"""
        def wrapper(*args, **kwargs):
            start_time = timezone.now()
            result = func(*args, **kwargs)
            end_time = timezone.now()
            execution_time = (end_time - start_time).total_seconds()
            
            logger.info(f"Function {func.__name__} executed in {execution_time:.3f}s")
            return result
        return wrapper
    
    @staticmethod
    def log_slow_queries(threshold_ms: int = 100):
        """Log des requêtes lentes"""
        # À implémenter avec django-debug-toolbar ou similar
        pass
