import React, { useState, useEffect } from "react";

interface PaymentStatsData {
  total_paiements: number;
  montant_total_encaisse: number;
  paiements_en_attente: number;
  montant_en_attente: number;
  paiements_valides: number;
  montant_valide: number;
  paiements_rejetes: number;
  montant_rejete: number;
  delai_moyen_paiement: number;
  taux_recouvrement: number;
  methodes_paiement: Array<{
    methode: string;
    count: number;
    montant: number;
    pourcentage: number;
  }>;
  evolution_mensuelle: Array<{
    mois: string;
    montant: number;
    nb_paiements: number;
  }>;
}

const PaymentStats: React.FC = () => {
  const [stats, setStats] = useState<PaymentStatsData>({
    total_paiements: 0,
    montant_total_encaisse: 0,
    paiements_en_attente: 0,
    montant_en_attente: 0,
    paiements_valides: 0,
    montant_valide: 0,
    paiements_rejetes: 0,
    montant_rejete: 0,
    delai_moyen_paiement: 0,
    taux_recouvrement: 0,
    methodes_paiement: [],
    evolution_mensuelle: [],
  });
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState("month");

  useEffect(() => {
    loadStats();
  }, [period]);

  const loadStats = async () => {
    try {
      setLoading(true);

      // Simulation de données statistiques de paiements
      const mockStats: PaymentStatsData = {
        total_paiements: 156,
        montant_total_encaisse: 425000,
        paiements_en_attente: 12,
        montant_en_attente: 25000,
        paiements_valides: 138,
        montant_valide: 395000,
        paiements_rejetes: 6,
        montant_rejete: 5000,
        delai_moyen_paiement: 18,
        taux_recouvrement: 92.5,
        methodes_paiement: [
          { methode: "VIREMENT", count: 85, montant: 285000, pourcentage: 67 },
          { methode: "CHEQUE", count: 45, montant: 95000, pourcentage: 22 },
          { methode: "ESPECES", count: 18, montant: 32000, pourcentage: 8 },
          { methode: "CARTE", count: 8, montant: 13000, pourcentage: 3 },
        ],
        evolution_mensuelle: [
          { mois: "Jan", montant: 38000, nb_paiements: 18 },
          { mois: "Fév", montant: 42000, nb_paiements: 22 },
          { mois: "Mar", montant: 45000, nb_paiements: 25 },
          { mois: "Avr", montant: 48000, nb_paiements: 28 },
          { mois: "Mai", montant: 52000, nb_paiements: 30 },
          { mois: "Jun", montant: 55000, nb_paiements: 32 },
          { mois: "Jul", montant: 58000, nb_paiements: 35 },
          { mois: "Aoû", montant: 45000, nb_paiements: 25 },
        ],
      };

      setStats(mockStats);
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  const getMethodeIcon = (methode: string) => {
    switch (methode) {
      case "VIREMENT":
        return "🏦";
      case "CHEQUE":
        return "📝";
      case "ESPECES":
        return "💵";
      case "CARTE":
        return "💳";
      default:
        return "💰";
    }
  };

  const getMethodeLabel = (methode: string) => {
    switch (methode) {
      case "VIREMENT":
        return "Virement bancaire";
      case "CHEQUE":
        return "Chèque";
      case "ESPECES":
        return "Espèces";
      case "CARTE":
        return "Carte bancaire";
      default:
        return methode;
    }
  };

  if (loading) {
    return (
      <div className="payment-stats-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des statistiques...</p>
      </div>
    );
  }

  return (
    <div className="payment-stats">
      <div className="stats-header">
        <div className="header-content">
          <h1>💳 Statistiques Paiements</h1>
          <p>Analyse complète de vos encaissements et paiements</p>
        </div>

        <div className="period-selector">
          <label>Période:</label>
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}>
            <option value="month">Ce mois</option>
            <option value="quarter">Ce trimestre</option>
            <option value="year">Cette année</option>
          </select>
        </div>
      </div>

      {/* KPIs principaux */}
      <div className="kpi-grid">
        <div className="kpi-card">
          <div className="kpi-icon">💳</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.total_paiements}</div>
            <div className="kpi-label">Total paiements</div>
            <div className="kpi-sublabel">
              {stats.paiements_valides} validés
            </div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">💰</div>
          <div className="kpi-content">
            <div className="kpi-value">
              {formatCurrency(stats.montant_total_encaisse)}
            </div>
            <div className="kpi-label">Total encaissé</div>
            <div className="kpi-sublabel">
              {formatCurrency(stats.montant_valide)} validé
            </div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">⏳</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.paiements_en_attente}</div>
            <div className="kpi-label">En attente</div>
            <div className="kpi-sublabel">
              {formatCurrency(stats.montant_en_attente)}
            </div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">📅</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.delai_moyen_paiement} jours</div>
            <div className="kpi-label">Délai moyen</div>
            <div className="kpi-sublabel">Paiement client</div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">🎯</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.taux_recouvrement}%</div>
            <div className="kpi-label">Taux recouvrement</div>
            <div className="kpi-sublabel">Factures payées</div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">❌</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.paiements_rejetes}</div>
            <div className="kpi-label">Paiements rejetés</div>
            <div className="kpi-sublabel">
              {formatCurrency(stats.montant_rejete)}
            </div>
          </div>
        </div>
      </div>

      {/* Répartition par méthode de paiement */}
      <div className="stats-section">
        <h3>💳 Répartition par méthode de paiement</h3>
        <div className="payment-methods">
          {stats.methodes_paiement.map((methode, index) => (
            <div
              key={index}
              className="payment-method-item">
              <div className="method-info">
                <div className="method-icon">
                  {getMethodeIcon(methode.methode)}
                </div>
                <div className="method-details">
                  <h4>{getMethodeLabel(methode.methode)}</h4>
                  <p>
                    {methode.count} paiements ({methode.pourcentage}%)
                  </p>
                </div>
              </div>
              <div className="method-amount">
                <span className="amount">
                  {formatCurrency(methode.montant)}
                </span>
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${methode.pourcentage}%` }}></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Évolution mensuelle */}
      <div className="stats-section">
        <h3>📈 Évolution mensuelle des encaissements</h3>
        <div className="evolution-chart">
          <div className="chart-container">
            {stats.evolution_mensuelle.map((data, index) => (
              <div
                key={index}
                className="chart-bar">
                <div
                  className="bar"
                  style={{
                    height: `${
                      (data.montant /
                        Math.max(
                          ...stats.evolution_mensuelle.map((d) => d.montant)
                        )) *
                      100
                    }%`,
                  }}></div>
                <div className="bar-label">{data.mois}</div>
                <div className="bar-value">{formatCurrency(data.montant)}</div>
                <div className="bar-count">{data.nb_paiements} paiements</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentStats;
