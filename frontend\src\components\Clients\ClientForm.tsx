import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ClientService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import "../Invoices/Invoice.css";

interface Client {
  id?: number;
  nom: string;
  email: string;
  telephone: string;
  adresse: string;
  type_client: string;
}

const ClientForm: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const { id } = useParams<{ id: string }>();
  const isEdit = !!id;

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    nom: "",
    prenom: "",
    email: "",
    telephone: "",
    type_client: "PARTICULIER",
    matricule_fiscal: "",
    adresse: "",
    ville: "",
    code_postal: "",
    pays: "Tunisie",
    notes: "",
  });

  useEffect(() => {
    if (isEdit && id) {
      const load = async () => {
        setLoading(true);
        const res = await ClientService.getClient(id);
        if (res && res.data) {
          const client = res.data as any;
          setFormData({
            nom: client.nom || "",
            prenom: client.prenom || "",
            email: client.email || "",
            telephone: client.telephone || "",
            type_client: client.type_client || "PARTICULIER",
            matricule_fiscal: client.matricule_fiscal || "",
            adresse: client.adresse || "",
            ville: client.ville || "",
            code_postal: client.code_postal || "",
            pays: client.pays || "Tunisie",
            notes: client.notes || "",
          });
        }
        setLoading(false);
      };
      load();
    }
  }, [isEdit, id]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!formData.nom || !formData.email) {
        notify.error("Erreur de validation", "Nom et email sont obligatoires");
        return;
      }

      const res = isEdit
        ? await ClientService.updateClient(id!, formData as any)
        : await ClientService.createClient(formData as any);

      if (res) {
        notify.success(
          "Succès",
          `Client ${isEdit ? "modifié" : "créé"} avec succès`
        );
        navigate("/dashboard/clients");
      } else {
        notify.error("Erreur", res.message || "Une erreur est survenue");
      }
    } catch (error) {
      notify.error("Erreur", "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">
              {isEdit ? "Modifier" : "Nouveau"} Client
            </h1>
            <p className="page-subtitle">
              {isEdit
                ? "Modifier les informations du client"
                : "Créer un nouveau client"}
            </p>
          </div>
          <div className="page-actions">
            <button
              type="button"
              className="page-action secondary"
              onClick={() => navigate("/dashboard/clients")}>
              ← Retour
            </button>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="content-grid two-columns">
          {/* Informations générales */}
          <div className="content-card">
            <div className="card-header">
              <h3 className="card-title">Informations générales</h3>
            </div>
            <div className="card-content">
              <div className="form-group">
                <label>Type de client *</label>
                <select
                  value={formData.type_client}
                  onChange={(e) =>
                    handleInputChange("type_client", e.target.value)
                  }
                  className="form-control"
                  required>
                  <option value="PARTICULIER">Particulier</option>
                  <option value="ENTREPRISE">Entreprise</option>
                </select>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Nom *</label>
                  <input
                    type="text"
                    value={formData.nom}
                    onChange={(e) => handleInputChange("nom", e.target.value)}
                    className="form-control"
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Prénom</label>
                  <input
                    type="text"
                    value={formData.prenom}
                    onChange={(e) =>
                      handleInputChange("prenom", e.target.value)
                    }
                    className="form-control"
                  />
                </div>
              </div>

              <div className="form-group">
                <label>Email *</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="form-control"
                  required
                />
              </div>

              <div className="form-group">
                <label>Téléphone</label>
                <input
                  type="tel"
                  value={formData.telephone}
                  onChange={(e) =>
                    handleInputChange("telephone", e.target.value)
                  }
                  className="form-control"
                  placeholder="+216 XX XXX XXX"
                />
              </div>

              {formData.type_client === "ENTREPRISE" && (
                <div className="form-group">
                  <label>Matricule fiscal</label>
                  <input
                    type="text"
                    value={formData.matricule_fiscal}
                    onChange={(e) =>
                      handleInputChange("matricule_fiscal", e.target.value)
                    }
                    className="form-control"
                    placeholder="XXXXXXX/X/X/X/XXX"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Adresse */}
          <div className="content-card">
            <div className="card-header">
              <h3 className="card-title">Adresse</h3>
            </div>
            <div className="card-content">
              <div className="form-group">
                <label>Adresse</label>
                <textarea
                  value={formData.adresse}
                  onChange={(e) => handleInputChange("adresse", e.target.value)}
                  className="form-control"
                  rows={3}
                  placeholder="Adresse complète"
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Ville</label>
                  <input
                    type="text"
                    value={formData.ville}
                    onChange={(e) => handleInputChange("ville", e.target.value)}
                    className="form-control"
                  />
                </div>
                <div className="form-group">
                  <label>Code postal</label>
                  <input
                    type="text"
                    value={formData.code_postal}
                    onChange={(e) =>
                      handleInputChange("code_postal", e.target.value)
                    }
                    className="form-control"
                  />
                </div>
              </div>

              <div className="form-group">
                <label>Pays</label>
                <input
                  type="text"
                  value={formData.pays}
                  onChange={(e) => handleInputChange("pays", e.target.value)}
                  className="form-control"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="content-card">
          <div className="card-header">
            <h3 className="card-title">Notes</h3>
          </div>
          <div className="card-content">
            <div className="form-group">
              <label>Notes internes</label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                className="form-control"
                rows={4}
                placeholder="Notes internes sur le client..."
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="form-actions">
          <button
            type="button"
            className="page-action secondary"
            onClick={() => navigate("/dashboard/clients")}>
            Annuler
          </button>
          <button
            type="submit"
            className="page-action"
            disabled={loading}>
            {loading ? "Enregistrement..." : isEdit ? "Modifier" : "Créer"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ClientForm;
