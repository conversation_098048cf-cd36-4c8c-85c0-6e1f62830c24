/**
 * Index des composants Comptabilité
 */

// Composants principaux
export { default as Comptable } from './Comptable';
export { default as ComptabiliteGeneral } from './ComptabiliteGeneral';
export { default as JournalVentes } from './JournalVentes';
export { default as JournalAchats } from './JournalAchats';
export { default as GrandLivre } from './GrandLivre';
export { default as Balance } from './Balance';
export { default as BilanComptable } from './BilanComptable';
export { default as TVADeclaration } from './TVADeclaration';
export { default as ExportComptable } from './ExportComptable';

// Types TypeScript
export interface ComptabiliteStats {
  chiffre_affaires_mensuel: number;
  chiffre_affaires_annuel: number;
  tva_collectee: number;
  tva_deductible: number;
  tva_a_payer: number;
  factures_impayees: number;
  factures_en_retard: number;
  creances_clients: number;
  tresorerie: number;
  resultat_mensuel: number;
  resultat_annuel: number;
  marge_brute: number;
}

export interface VenteEntry {
  id: string;
  date: string;
  numero_facture: string;
  client_nom: string;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
  taux_tva: number;
  statut: string;
  mode_paiement?: string;
}

export interface AchatEntry {
  id: string;
  date: string;
  numero_facture: string;
  fournisseur_nom: string;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
  taux_tva: number;
  statut: string;
  mode_paiement?: string;
}

export interface CompteComptable {
  numero: string;
  libelle: string;
  type: 'ACTIF' | 'PASSIF' | 'CHARGE' | 'PRODUIT';
  solde_debiteur: number;
  solde_crediteur: number;
  mouvements: EcritureComptable[];
}

export interface EcritureComptable {
  id: string;
  date: string;
  libelle: string;
  compte_debit: string;
  compte_credit: string;
  montant: number;
  piece_justificative?: string;
}

export interface TVADeclarationData {
  periode_debut: string;
  periode_fin: string;
  tva_collectee_20: number;
  tva_collectee_10: number;
  tva_collectee_5_5: number;
  tva_deductible: number;
  tva_a_payer: number;
  credit_precedent: number;
  acompte_verse: number;
  solde_a_payer: number;
}

// Constantes
export const COMPTES_COMPTABLES = {
  // Classe 1 - Comptes de capitaux
  CAPITAL: '101000',
  RESERVES: '106000',
  RESULTAT: '120000',
  
  // Classe 2 - Comptes d'immobilisations
  IMMOBILISATIONS: '200000',
  MATERIEL: '215000',
  
  // Classe 3 - Comptes de stocks
  STOCKS: '300000',
  
  // Classe 4 - Comptes de tiers
  CLIENTS: '411000',
  FOURNISSEURS: '401000',
  TVA_COLLECTEE: '445710',
  TVA_DEDUCTIBLE: '445620',
  
  // Classe 5 - Comptes financiers
  BANQUE: '512000',
  CAISSE: '530000',
  
  // Classe 6 - Comptes de charges
  ACHATS: '607000',
  CHARGES_EXTERNES: '620000',
  SALAIRES: '641000',
  
  // Classe 7 - Comptes de produits
  VENTES: '707000',
  PRESTATIONS: '706000'
} as const;

export const TAUX_TVA = {
  NORMAL: 20,
  INTERMEDIAIRE: 10,
  REDUIT: 5.5,
  SUPER_REDUIT: 2.1,
  ZERO: 0
} as const;

export const MODES_PAIEMENT = [
  'Espèces',
  'Chèque',
  'Virement',
  'Carte bancaire',
  'Prélèvement',
  'Traite',
  'Autre'
] as const;

export const PERIODES_TVA = [
  { value: 'mensuelle', label: 'Mensuelle' },
  { value: 'trimestrielle', label: 'Trimestrielle' },
  { value: 'annuelle', label: 'Annuelle' }
] as const;

// Fonctions utilitaires
export const calculerTVA = (montantHT: number, tauxTVA: number): number => {
  return montantHT * (tauxTVA / 100);
};

export const calculerTTC = (montantHT: number, tauxTVA: number): number => {
  return montantHT + calculerTVA(montantHT, tauxTVA);
};

export const calculerHT = (montantTTC: number, tauxTVA: number): number => {
  return montantTTC / (1 + tauxTVA / 100);
};

export const formatCompteComptable = (numero: string): string => {
  return numero.replace(/(\d{3})(\d{3})/, '$1.$2');
};

export const getTypeCompte = (numero: string): string => {
  const classe = numero.charAt(0);
  switch (classe) {
    case '1': return 'Capitaux';
    case '2': return 'Immobilisations';
    case '3': return 'Stocks';
    case '4': return 'Tiers';
    case '5': return 'Financier';
    case '6': return 'Charges';
    case '7': return 'Produits';
    default: return 'Autre';
  }
};

export const validateNumeroCompte = (numero: string): boolean => {
  return /^\d{6}$/.test(numero);
};

export const generateNumeroEcriture = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `ECR-${year}${month}${day}-${random}`;
};

// Routes comptables
export const COMPTABLE_ROUTES = {
  MAIN: '/comptable',
  GENERAL: '/comptable/general',
  VENTES: '/comptable/ventes',
  ACHATS: '/comptable/achats',
  GRAND_LIVRE: '/comptable/grand-livre',
  BALANCE: '/comptable/balance',
  BILAN: '/comptable/bilan',
  TVA: '/comptable/tva',
  EXPORT: '/comptable/export'
} as const;
