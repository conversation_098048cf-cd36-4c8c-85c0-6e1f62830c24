/* Styles pour les composants Paramètres */

/* === LAYOUT GÉNÉRAL === */
.settings-module {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* === HEADER === */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-title h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.header-title p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* === NAVIGATION === */
.settings-nav {
  display: flex;
  gap: 4px;
  background: white;
  padding: 8px;
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow-x: auto;
}

.nav-tab {
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.nav-tab:hover {
  background: #f8fafc;
  color: #475569;
}

.nav-tab.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* === CONTENU === */
.settings-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.company-settings,
.user-settings,
.invoice-settings,
.system-settings {
  padding: 30px;
}

/* === SECTIONS === */
.settings-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 2px solid #f1f5f9;
}

.settings-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.settings-section h3 {
  margin: 0 0 24px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  margin: 0;
}

/* === FORMULAIRES === */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #e74c3c;
}

.error-text {
  color: #e74c3c;
  font-size: 0.8rem;
  font-weight: 500;
}

.form-help {
  color: #64748b;
  font-size: 0.8rem;
  font-style: italic;
}

/* === CHECKBOXES === */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.checkbox-label:hover {
  background: #f8fafc;
}

.form-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #3498db;
}

.checkbox-text {
  font-weight: 500;
  color: #374151;
}

/* === BOUTONS === */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn.btn-primary {
  background: #3498db;
  color: white;
}

.btn.btn-primary:hover {
  background: #2980b9;
}

.btn.btn-warning {
  background: #f39c12;
  color: white;
}

.btn.btn-warning:hover {
  background: #e67e22;
}

.btn.btn-outline {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn.btn-outline:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

.btn.btn-sm {
  padding: 8px 16px;
  font-size: 0.8rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* === ACTIONS === */
.settings-actions,
.password-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

/* === INFORMATIONS COMPTE === */
.account-info {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: #64748b;
}

.info-item span {
  color: #1e293b;
  font-weight: 500;
}

/* === BADGES RÔLES === */
.role-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-badge.role-superadmin {
  background: #fef3c7;
  color: #d97706;
}

.role-badge.role-admin {
  background: #e0e7ff;
  color: #4338ca;
}

.role-badge.role-comptable {
  background: #dcfce7;
  color: #16a34a;
}

.role-badge.role-normal {
  background: #f1f5f9;
  color: #64748b;
}

/* === CHARGEMENT === */
.settings-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* === ACCÈS REFUSÉ === */
.access-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px;
}

.access-denied-content {
  text-align: center;
  max-width: 400px;
}

.access-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.6;
}

.access-denied h2 {
  margin: 0 0 16px 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
}

.access-denied p {
  margin: 0 0 24px 0;
  color: #64748b;
  line-height: 1.6;
}

/* === APERÇU FACTURE === */
.invoice-preview {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.preview-template {
  font-size: 0.8rem;
  color: #64748b;
  background: white;
  padding: 4px 8px;
  border-radius: 6px;
}

.preview-content {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.preview-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3498db;
  margin-bottom: 12px;
}

.preview-conditions {
  font-size: 0.9rem;
  color: #374151;
  margin-bottom: 8px;
}

.preview-mentions {
  font-size: 0.8rem;
  color: #64748b;
  font-style: italic;
}

/* === INFORMATIONS SYSTÈME === */
.system-info {
  margin-bottom: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-card {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.info-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.info-content {
  flex: 1;
}

.info-content h4 {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-content span {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
}

/* === TYPES DE FICHIERS === */
.file-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
}

.file-type {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  margin: 0;
  transition: all 0.3s ease;
}

.file-type:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.file-type .checkbox-text {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* === ACTIONS SYSTÈME === */
.system-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.action-info {
  flex: 1;
}

.action-info h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.action-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #64748b;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .settings-module {
    padding: 15px;
  }

  .settings-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .settings-nav {
    justify-content: flex-start;
    padding: 4px;
  }

  .nav-tab {
    padding: 10px 16px;
    font-size: 0.8rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .file-types-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  }

  .settings-actions,
  .password-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .action-card {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .preview-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
