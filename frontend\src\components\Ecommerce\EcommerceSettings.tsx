import React, { useState, useEffect } from 'react';

const EcommerceSettings: React.FC = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setTimeout(() => setLoading(false), 500);
  }, []);

  if (loading) {
    return (
      <div className="loading-spinner">
        <div>Chargement des paramètres e-commerce...</div>
      </div>
    );
  }

  return (
    <div className="ecommerce-settings">
      <div className="coming-soon">
        <div className="coming-soon-icon">⚙️</div>
        <h3>Paramètres E-commerce</h3>
        <p>Module en cours de développement</p>
        <p>Fonctionnalités prévues :</p>
        <ul>
          <li>🛒 Configuration boutique en ligne</li>
          <li>💳 Paramètres de paiement</li>
          <li>🚚 Options de livraison</li>
          <li>📧 Templates d'emails</li>
          <li>🎨 Personnalisation thème</li>
        </ul>
      </div>
    </div>
  );
};

export default EcommerceSettings;
