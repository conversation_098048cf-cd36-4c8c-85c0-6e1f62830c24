import { ApiService } from "./apiService";

// Types pour la gestion des médias
export interface MediaFile {
  id: number;
  nom: string;
  nom_original: string;
  type_fichier: string;
  taille: number;
  url: string;
  miniature?: string;
  description?: string;
  tags?: string[];
  date_upload: string;
  utilisateur?: string;
  public: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResponse {
  success: boolean;
  data?: MediaFile;
  message?: string;
}

class MediaService {
  private static readonly BASE_PATH = "/core/media";

  // Upload de fichiers
  static async uploadFile(
    file: File,
    options?: {
      description?: string;
      tags?: string[];
      public?: boolean;
      onProgress?: (progress: UploadProgress) => void;
    }
  ): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append("file", file);

    if (options?.description) {
      formData.append("description", options.description);
    }
    if (options?.tags) {
      formData.append("tags", JSON.stringify(options.tags));
    }
    if (options?.public !== undefined) {
      formData.append("public", options.public.toString());
    }

    try {
      // Solution temporaire pour l'upload
      const formData = new FormData();
      formData.append("file", file);

      const response = await (ApiService as any).post(
        `${this.BASE_PATH}/upload/`,
        formData
      );

      return {
        success: (response as any).success,
        data: (response as any).data as MediaFile,
        message: (response as any).message,
      };
    } catch (error) {
      return {
        success: false,
        message: "Erreur lors de l'upload du fichier",
      };
    }
  }

  // Upload multiple
  static async uploadMultipleFiles(
    files: File[],
    options?: {
      description?: string;
      tags?: string[];
      public?: boolean;
      onProgress?: (fileIndex: number, progress: UploadProgress) => void;
      onComplete?: (fileIndex: number, result: UploadResponse) => void;
    }
  ): Promise<UploadResponse[]> {
    const results: UploadResponse[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const result = await this.uploadFile(file, {
        ...options,
        onProgress: (progress) => options?.onProgress?.(i, progress),
      });

      results.push(result);
      options?.onComplete?.(i, result);
    }

    return results;
  }

  // Gestion des fichiers
  static async getFiles(filters?: {
    type_fichier?: string;
    search?: string;
    tags?: string[];
    public?: boolean;
    page?: number;
    page_size?: number;
  }) {
    const params = new URLSearchParams();
    if (filters?.type_fichier)
      params.append("type_fichier", filters.type_fichier);
    if (filters?.search) params.append("search", filters.search);
    if (filters?.tags) params.append("tags", filters.tags.join(","));
    if (filters?.public !== undefined)
      params.append("public", filters.public.toString());
    if (filters?.page) params.append("page", filters.page.toString());
    if (filters?.page_size)
      params.append("page_size", filters.page_size.toString());

    const url = `${this.BASE_PATH}/files/?${params.toString()}`;
    return (ApiService as any).get(url);
  }

  static async getFile(id: number) {
    return (ApiService as any).get(`${this.BASE_PATH}/files/${id}/`);
  }

  static async updateFile(
    id: number,
    data: {
      nom?: string;
      description?: string;
      tags?: string[];
      public?: boolean;
    }
  ) {
    return (ApiService as any).patch(`${this.BASE_PATH}/files/${id}/`, data);
  }

  static async deleteFile(id: number) {
    return (ApiService as any).delete(`${this.BASE_PATH}/files/${id}/`);
  }

  // Gestion des dossiers/catégories
  static async getFolders() {
    return (ApiService as any).get(`${this.BASE_PATH}/folders/`);
  }

  static async createFolder(data: {
    nom: string;
    description?: string;
    parent?: number;
  }) {
    return (ApiService as any).post(`${this.BASE_PATH}/folders/`, data);
  }

  // Utilitaires pour les images
  static async resizeImage(
    file: File,
    maxWidth: number,
    maxHeight: number,
    quality: number = 0.8
  ): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d")!;
      const img = new Image();

      img.onload = () => {
        // Calculer les nouvelles dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Dessiner l'image redimensionnée
        ctx.drawImage(img, 0, 0, width, height);

        // Convertir en blob puis en File
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(resizedFile);
            } else {
              resolve(file);
            }
          },
          file.type,
          quality
        );
      };

      img.src = URL.createObjectURL(file);
    });
  }

  // Validation des fichiers
  static validateFile(
    file: File,
    options?: {
      maxSize?: number; // en bytes
      allowedTypes?: string[];
      maxWidth?: number;
      maxHeight?: number;
    }
  ): { valid: boolean; error?: string } {
    const {
      maxSize = 10 * 1024 * 1024,
      allowedTypes,
      maxWidth,
      maxHeight,
    } = options || {};

    // Vérifier la taille
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `Le fichier est trop volumineux (max: ${this.formatFileSize(
          maxSize
        )})`,
      };
    }

    // Vérifier le type
    if (allowedTypes && !allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Type de fichier non autorisé. Types acceptés: ${allowedTypes.join(
          ", "
        )}`,
      };
    }

    // Pour les images, vérifier les dimensions si spécifiées
    if (file.type.startsWith("image/") && (maxWidth || maxHeight)) {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          if (maxWidth && img.width > maxWidth) {
            resolve({
              valid: false,
              error: `Image trop large (max: ${maxWidth}px)`,
            });
            return;
          }
          if (maxHeight && img.height > maxHeight) {
            resolve({
              valid: false,
              error: `Image trop haute (max: ${maxHeight}px)`,
            });
            return;
          }
          resolve({ valid: true });
        };
        img.src = URL.createObjectURL(file);
      }) as any;
    }

    return { valid: true };
  }

  // Utilitaires
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  static getFileIcon(type: string): string {
    if (type.startsWith("image/")) return "🖼️";
    if (type.startsWith("video/")) return "🎥";
    if (type.startsWith("audio/")) return "🎵";
    if (type.includes("pdf")) return "📄";
    if (type.includes("word") || type.includes("document")) return "📝";
    if (type.includes("excel") || type.includes("spreadsheet")) return "📊";
    if (type.includes("powerpoint") || type.includes("presentation"))
      return "📽️";
    if (type.includes("zip") || type.includes("rar")) return "📦";
    return "📁";
  }

  static isImage(type: string): boolean {
    return type.startsWith("image/");
  }

  static isVideo(type: string): boolean {
    return type.startsWith("video/");
  }

  static isDocument(type: string): boolean {
    return [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ].includes(type);
  }

  static formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString("fr-FR");
  }
}

export default MediaService;
