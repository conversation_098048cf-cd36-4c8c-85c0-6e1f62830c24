/**
 * Composant de protection des routes basé sur l'authentification et les rôles
 */

import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { UserRole } from "../../contexts/AuthContext";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRole?: UserRole;
  requiredMinimumRole?: UserRole;
  requiredModule?: string;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredRole,
  requiredMinimumRole,
  requiredModule,
  redirectTo = "/login",
}) => {
  const {
    isAuthenticated,
    isLoading,
    user,
    hasRole,
    hasMinimumRole,
    hasPermission,
  } = useAuth();
  const location = useLocation();

  // Afficher un loader pendant la vérification de l'authentification
  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Vérification de l'authentification...</p>
        </div>
      </div>
    );
  }

  // Vérifier l'authentification
  if (requireAuth && !isAuthenticated) {
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location }}
        replace
      />
    );
  }

  // Si l'utilisateur est connecté mais qu'on ne veut pas qu'il accède à cette route
  // (par exemple, page de connexion quand déjà connecté)
  if (!requireAuth && isAuthenticated) {
    return (
      <Navigate
        to="/dashboard"
        replace
      />
    );
  }

  // Vérifier le rôle spécifique
  if (requiredRole && !hasRole(requiredRole)) {
    return (
      <Navigate
        to="/unauthorized"
        replace
      />
    );
  }

  // Vérifier le rôle minimum
  if (requiredMinimumRole && !hasMinimumRole(requiredMinimumRole)) {
    return (
      <Navigate
        to="/unauthorized"
        replace
      />
    );
  }

  // Vérifier l'accès au module
  if (requiredModule && !hasPermission(requiredModule)) {
    return (
      <Navigate
        to="/unauthorized"
        replace
      />
    );
  }

  return <>{children}</>;
};

// Composant pour les routes publiques (accessible seulement si non connecté)
export const PublicRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return <ProtectedRoute requireAuth={false}>{children}</ProtectedRoute>;
};

// Composant pour les routes d'administration
export const AdminRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <ProtectedRoute requiredMinimumRole="ADMIN">{children}</ProtectedRoute>
  );
};

// Composant pour les routes de comptabilité
export const ComptableRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <ProtectedRoute requiredMinimumRole="COMPTABLE">{children}</ProtectedRoute>
  );
};

// Composant pour les routes de super admin
export const SuperAdminRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return <ProtectedRoute requiredRole="ADMIN">{children}</ProtectedRoute>;
};

// Composant pour les routes basées sur les modules
export const ModuleRoute: React.FC<{
  children: React.ReactNode;
  module: string;
}> = ({ children, module }) => {
  return <ProtectedRoute requiredModule={module}>{children}</ProtectedRoute>;
};

export default ProtectedRoute;
