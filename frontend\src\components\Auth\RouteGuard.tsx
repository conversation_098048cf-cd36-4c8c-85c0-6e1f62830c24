import React, { ReactNode } from "react";
import { useAuth, UserRole } from "../../contexts/AuthContext";
import "./RouteGuard.css";

interface RouteGuardProps {
  children: ReactNode;
  requiredRole?: UserRole;
  requiredModule?: string;
  fallback?: ReactNode;
}

const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requiredRole,
  requiredModule,
  fallback,
}) => {
  const { 
    isAuthenticated, 
    isLoading, 
    user, 
    hasMinimumRole, 
    hasModuleAccess 
  } = useAuth();

  // Affichage pendant le chargement
  if (isLoading) {
    return (
      <div className="route-guard-loading">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <h3>🔐 Vérification des permissions</h3>
          <p>Authentification en cours...</p>
        </div>
      </div>
    );
  }

  // Utilisateur non connecté
  if (!isAuthenticated) {
    return (
      <div className="route-guard-unauthorized">
        <div className="unauthorized-container">
          <div className="unauthorized-icon">🚫</div>
          <h2>Accès non autorisé</h2>
          <p>Vous devez être connecté pour accéder à cette page.</p>
          <button 
            className="btn-login"
            onClick={() => window.location.href = "/login"}>
            🔑 Se connecter
          </button>
        </div>
      </div>
    );
  }

  // Vérification du rôle minimum requis
  if (requiredRole && !hasMinimumRole(requiredRole)) {
    return (
      <div className="route-guard-forbidden">
        <div className="forbidden-container">
          <div className="forbidden-icon">⛔</div>
          <h2>Permissions insuffisantes</h2>
          <p>
            Votre rôle actuel ({user?.role}) ne vous permet pas d'accéder à cette page.
          </p>
          <p>
            Rôle minimum requis: <strong>{requiredRole}</strong>
          </p>
          <div className="forbidden-actions">
            <button 
              className="btn-back"
              onClick={() => window.history.back()}>
              ← Retour
            </button>
            <button 
              className="btn-dashboard"
              onClick={() => window.location.href = "/dashboard"}>
              🏠 Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Vérification de l'accès au module
  if (requiredModule && !hasModuleAccess(requiredModule)) {
    return (
      <div className="route-guard-forbidden">
        <div className="forbidden-container">
          <div className="forbidden-icon">🔒</div>
          <h2>Module non autorisé</h2>
          <p>
            Vous n'avez pas accès au module <strong>{requiredModule}</strong>.
          </p>
          <p>
            Contactez votre administrateur pour obtenir les permissions nécessaires.
          </p>
          <div className="forbidden-actions">
            <button 
              className="btn-back"
              onClick={() => window.history.back()}>
              ← Retour
            </button>
            <button 
              className="btn-dashboard"
              onClick={() => window.location.href = "/dashboard"}>
              🏠 Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Utilisateur autorisé, afficher le contenu
  return <>{children}</>;
};

// Composant pour afficher les informations de l'utilisateur connecté
export const UserInfo: React.FC = () => {
  const { user, logout } = useAuth();

  if (!user) return null;

  const handleLogout = async () => {
    if (window.confirm("Êtes-vous sûr de vouloir vous déconnecter ?")) {
      await logout();
      window.location.href = "/login";
    }
  };

  return (
    <div className="user-info">
      <div className="user-avatar">
        {user.avatar ? (
          <img src={user.avatar} alt={`${user.prenom} ${user.nom}`} />
        ) : (
          <div className="avatar-placeholder">
            {user.prenom.charAt(0)}{user.nom.charAt(0)}
          </div>
        )}
      </div>
      <div className="user-details">
        <div className="user-name">{user.prenom} {user.nom}</div>
        <div className="user-role">{user.role}</div>
      </div>
      <button className="logout-btn" onClick={handleLogout} title="Se déconnecter">
        🚪
      </button>
    </div>
  );
};

// Composant pour afficher le statut de la session
export const SessionStatus: React.FC = () => {
  const { sessionExpiry, extendSession } = useAuth();

  if (!sessionExpiry) return null;

  const now = new Date();
  const expiry = new Date(sessionExpiry);
  const timeLeft = expiry.getTime() - now.getTime();
  const minutesLeft = Math.floor(timeLeft / (1000 * 60));

  // Afficher un avertissement si moins de 15 minutes restantes
  if (minutesLeft <= 15 && minutesLeft > 0) {
    return (
      <div className="session-warning">
        <span className="warning-icon">⏰</span>
        <span>Session expire dans {minutesLeft} min</span>
        <button onClick={extendSession} className="extend-btn">
          Prolonger
        </button>
      </div>
    );
  }

  return null;
};

export default RouteGuard;
