/**
 * Liste des commandes avec filtres et actions
 */

import React, { useState, useEffect, useCallback } from "react";
import { OrderService } from "../../services/apiService";
import { toast } from "react-toastify";
import OrderStats from "./OrderStats";
import OrderDetails from "./OrderDetails";

interface Order {
  id: string;
  numero: string;
  client_id: string;
  client_nom: string;
  client_email: string;
  date_commande: string;
  date_livraison_prevue?: string;
  date_livraison_reelle?: string;
  montant_ht: number;
  montant_tva: number;
  montant_total: number;
  statut:
    | "BROUILLON"
    | "CONFIRMEE"
    | "EN_PREPARATION"
    | "EXPEDIEE"
    | "LIVREE"
    | "ANNULEE";
  priorite: "NORMALE" | "HAUTE" | "URGENTE";
  notes?: string;
  lignes: any[];
  created_at: string;
  updated_at: string;
}

interface OrdersListProps {
  embedded?: boolean;
  limit?: number;
  status?: string;
}

const OrdersList: React.FC<OrdersListProps> = ({
  embedded = false,
  limit,
  status,
}) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Filtres
  const [filters, setFilters] = useState({
    search: "",
    statut: status || "",
    priorite: "",
    client: "",
    date_debut: "",
    date_fin: "",
    montant_min: "",
    montant_max: "",
  });

  const [sortBy, setSortBy] = useState("date_commande");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  const loadOrders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Préparer les paramètres de requête
      const params: any = {
        page: currentPage,
        page_size: limit || 20,
        ordering: sortOrder === "desc" ? `-${sortBy}` : sortBy,
      };

      // Ajouter les filtres actifs
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value.toString().trim() !== "") {
          params[key] = value;
        }
      });

      // Données simulées pour les commandes
      const mockOrders = [
        {
          id: "1",
          numero: "CMD-2024-001",
          client_id: "1",
          client_nom: "CLIENT FOND FUTURISTE",
          client_email: "<EMAIL>",
          date_commande: "2024-03-15T10:30:00Z",
          date_livraison_prevue: "2024-03-20T10:30:00Z",
          statut: "LIVREE" as const,
          montant_ht: 831.65,
          montant_tva: 166.35,
          montant_total: 998.0,
          priorite: "NORMALE" as const,
          lignes: [],
          created_at: "2024-03-15T10:30:00Z",
          updated_at: "2024-03-20T10:30:00Z",
        },
        {
          id: "2",
          numero: "CMD-2024-002",
          client_id: "2",
          client_nom: "Société Innovation Tech",
          client_email: "<EMAIL>",
          date_commande: "2024-03-14T14:15:00Z",
          date_livraison_prevue: "2024-03-18T14:15:00Z",
          statut: "EN_PREPARATION" as const,
          montant_ht: 850.0,
          montant_tva: 170.0,
          montant_total: 1020.0,
          priorite: "URGENTE" as const,
          lignes: [],
          created_at: "2024-03-14T14:15:00Z",
          updated_at: "2024-03-14T14:15:00Z",
        },
        {
          id: "3",
          numero: "CMD-2024-003",
          client_id: "3",
          client_nom: "Entreprise Moderne SARL",
          client_email: "<EMAIL>",
          date_commande: "2024-03-13T09:45:00Z",
          date_livraison_prevue: "2024-03-17T09:45:00Z",
          statut: "CONFIRMEE" as const,
          montant_ht: 1250.0,
          montant_tva: 250.0,
          montant_total: 1500.0,
          priorite: "NORMALE" as const,
          lignes: [],
          created_at: "2024-03-13T09:45:00Z",
          updated_at: "2024-03-13T09:45:00Z",
        },
        {
          id: "4",
          numero: "CMD-2024-004",
          client_id: "4",
          client_nom: "Cabinet Conseil Expert",
          client_email: "<EMAIL>",
          date_commande: "2024-03-12T16:20:00Z",
          date_livraison_prevue: "2024-03-16T16:20:00Z",
          statut: "EXPEDIEE" as const,
          montant_ht: 125.75,
          montant_tva: 25.25,
          montant_total: 151.0,
          priorite: "NORMALE" as const,
          lignes: [],
          created_at: "2024-03-12T16:20:00Z",
          updated_at: "2024-03-12T16:20:00Z",
        },
      ];

      // Simuler un délai de chargement
      await new Promise((resolve) => setTimeout(resolve, 800));

      setOrders(mockOrders);
      setTotalCount(mockOrders.length);
      setTotalPages(1);
    } catch (err: any) {
      setError(err.message || "Erreur lors du chargement des commandes");
      toast.error("Erreur lors du chargement des commandes");
    } finally {
      setLoading(false);
    }
  }, [currentPage, filters, sortBy, sortOrder, limit]);

  const createInvoiceFromOrder = (order: any) => {
    // Créer une facture basée sur la commande
    const invoice = {
      id: `invoice_${order.id}`,
      numero: `FAC-2024-${String(Math.floor(Math.random() * 9000) + 1000)}`,
      client_nom: order.client_nom,
      client_adresse: order.adresse_livraison,
      date_facture: new Date().toISOString(),
      date_echeance: new Date(
        Date.now() + 30 * 24 * 60 * 60 * 1000
      ).toISOString(),
      statut: "BROUILLON",
      montant_ht: order.montant_ht,
      montant_ttc: order.montant_total,
      tva: order.montant_total - order.montant_ht,
      type_document: "Facture",
      commande_origine: order.numero,
      items: [
        {
          code: "CMD-ITEM",
          designation: `Articles de la commande ${order.numero}`,
          quantite: order.items_count,
          prix_unitaire: order.montant_ht / order.items_count,
          remise: 0.0,
          montant_ht: order.montant_ht,
          tva_taux: 20,
        },
      ],
    };

    // Ouvrir la prévisualisation de la facture générée
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(generateInvoiceHTML(invoice));
      printWindow.document.close();
    }

    toast.success(
      `Facture ${invoice.numero} générée pour la commande ${order.numero}`
    );
  };

  const generateInvoiceHTML = (invoice: any) => {
    const companyInfo = {
      name: "Société Chaabane de Commerce",
      address: "10, Rue de la Commission",
      postalCode: "6000 - 06 287 297 - 06 287 863",
      phone: "Tél: 06 287 297",
      mf: "MF: 1283508 W/AM/000",
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Facture ${invoice.numero}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
          .header { display: flex; justify-content: space-between; margin-bottom: 30px; }
          .company-info { text-align: left; }
          .company-info h2 { color: #2c5aa0; margin: 0; }
          .invoice-info { text-align: right; border: 2px solid #333; padding: 10px; }
          .invoice-title { background: #e8f4f8; padding: 10px; text-align: center; font-size: 16px; font-weight: bold; margin: 20px 0; }
          .client-section { display: flex; justify-content: space-between; margin: 20px 0; }
          .client-info, .fiscal-info { border: 1px solid #333; padding: 10px; width: 45%; }
          .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .items-table th { background: #4a5568; color: white; padding: 8px; text-align: center; font-size: 10px; }
          .items-table td { border: 1px solid #333; padding: 6px; text-align: center; font-size: 10px; }
          .totals-section { margin-top: 30px; }
          .totals-table { width: 100%; border-collapse: collapse; }
          .totals-table td { border: 1px solid #333; padding: 8px; }
          .footer { margin-top: 50px; display: flex; justify-content: space-between; font-size: 10px; }
          .signature-section { text-align: center; margin-top: 30px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-info">
            <h2>✓ ${companyInfo.name}</h2>
            <p>${companyInfo.address}<br>
            ${companyInfo.postalCode}<br>
            ${companyInfo.phone}<br>
            ${companyInfo.mf}</p>
          </div>
          <div class="invoice-info">
            <strong>Adresse: 10 Rue de la Commission</strong><br>
            6000 - 06 287 297 - 06 287 863<br><br>
            <strong>MF: 1283508 W/AM/000</strong>
          </div>
        </div>

        <div class="invoice-title">
          <strong>FACTURE N°: ${invoice.numero}</strong>
          <span style="float: right;">Date: ${new Date(
            invoice.date_facture
          ).toLocaleDateString("fr-FR")}</span>
        </div>

        <div class="client-section">
          <div class="client-info">
            <strong>N° Client: 2017</strong><br>
            <strong>Dén M°: ${invoice.client_nom}</strong><br>
            ${invoice.client_adresse || "Adresse client"}
          </div>
          <div class="fiscal-info">
            <strong>Matricule Fiscal: MF 1234567890</strong><br>
            <strong>Téléphone: +216 71 123 456</strong>
          </div>
        </div>

        <table class="items-table">
          <thead>
            <tr>
              <th>Code Article</th>
              <th>Désignation</th>
              <th>Qté</th>
              <th>P.U H.T</th>
              <th>Rem %</th>
              <th>P.U T.T.C</th>
              <th>Mt Net H.T</th>
              <th>TVA %</th>
            </tr>
          </thead>
          <tbody>
            ${
              invoice.items
                ? invoice.items
                    .map(
                      (item: any) => `
              <tr>
                <td>${item.code}</td>
                <td style="text-align: left;">${item.designation}</td>
                <td>${item.quantite}</td>
                <td>${item.prix_unitaire.toFixed(3)}</td>
                <td>${item.remise.toFixed(2)}</td>
                <td>${(item.prix_unitaire * 1.2).toFixed(3)}</td>
                <td>${item.montant_ht.toFixed(3)}</td>
                <td>${item.tva_taux}</td>
              </tr>
            `
                    )
                    .join("")
                : ""
            }
          </tbody>
        </table>

        <div class="totals-section">
          <table class="totals-table" style="width: 40%; margin-left: auto;">
            <tr>
              <td>Montant HT</td>
              <td style="text-align: right;">${(
                invoice.montant_ht || 0
              ).toFixed(3)}</td>
            </tr>
            <tr>
              <td>TVA (20%)</td>
              <td style="text-align: right;">${(invoice.tva || 0).toFixed(
                3
              )}</td>
            </tr>
            <tr>
              <td>Timbre Fiscal</td>
              <td style="text-align: right;">0.600</td>
            </tr>
            <tr style="background: #f0f0f0;">
              <td><strong>TOTAL T.T.C</strong></td>
              <td style="text-align: right;"><strong>${(
                invoice.montant_ttc || 0
              ).toFixed(3)}</strong></td>
            </tr>
          </table>
        </div>

        <div class="footer">
          <div>
            <strong>Siège commercial:</strong> 10, Rue de la Commission<br>
            <strong>Tél/Fax:</strong> 06 287 297<br>
            06 287 863
          </div>
          <div style="text-align: right;">
            <strong>Email:</strong> <EMAIL><br>
            <strong>MF:</strong> 1283508 W/AM/000
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const showOrderDetailsModal = (order: any) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const updateOrderStatus = (orderId: string, newStatus: Order["statut"]) => {
    setOrders((prev) =>
      prev.map((order) =>
        order.id === orderId ? { ...order, statut: newStatus } : order
      )
    );
    toast.success(`Statut de la commande mis à jour: ${newStatus}`);
  };

  useEffect(() => {
    loadOrders();
  }, [loadOrders]);

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders((prev) =>
      prev.includes(orderId)
        ? prev.filter((id) => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = () => {
    if (selectedOrders.length === orders.length) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(orders.map((order) => order.id));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "BROUILLON":
        return "status-draft";
      case "CONFIRMEE":
        return "status-confirmed";
      case "EN_PREPARATION":
        return "status-preparing";
      case "EXPEDIEE":
        return "status-shipped";
      case "LIVREE":
        return "status-delivered";
      case "ANNULEE":
        return "status-cancelled";
      default:
        return "status-default";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "BROUILLON":
        return "Brouillon";
      case "CONFIRMEE":
        return "Confirmée";
      case "EN_PREPARATION":
        return "En préparation";
      case "EXPEDIEE":
        return "Expédiée";
      case "LIVREE":
        return "Livrée";
      case "ANNULEE":
        return "Annulée";
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "URGENTE":
        return "priority-urgent";
      case "HAUTE":
        return "priority-high";
      case "NORMALE":
        return "priority-normal";
      default:
        return "priority-default";
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "URGENTE":
        return "🔴 Urgente";
      case "HAUTE":
        return "🟡 Haute";
      case "NORMALE":
        return "🟢 Normale";
      default:
        return priority;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR");
  };

  if (loading && orders.length === 0) {
    return (
      <div className="orders-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des commandes...</p>
      </div>
    );
  }

  return (
    <div className={`orders-list ${embedded ? "embedded" : ""}`}>
      {!embedded && (
        <>
          {/* En-tête */}
          <div className="orders-header">
            <div className="header-title">
              <h1>🛒 Gestion des Commandes</h1>
              <p>Suivez et gérez toutes vos commandes clients</p>
            </div>
            <div className="header-actions">
              <button
                className="btn btn-primary"
                onClick={() => toast.info("Fonctionnalité en développement")}>
                ➕ Nouvelle Commande
              </button>
              <button
                className="btn btn-info"
                onClick={() => toast.info("Fonctionnalité en développement")}>
                📊 Statistiques
              </button>
            </div>
          </div>

          {/* Statistiques */}
          <OrderStats embedded={true} />
        </>
      )}

      {error && (
        <div className="error-message">
          <span className="error-icon">❌</span>
          <span>{error}</span>
          <button
            onClick={loadOrders}
            className="retry-btn">
            🔄 Réessayer
          </button>
        </div>
      )}

      {/* Actions groupées */}
      {selectedOrders.length > 0 && (
        <div className="bulk-actions">
          <span className="selected-count">
            {selectedOrders.length} commande(s) sélectionnée(s)
          </span>
          <div className="bulk-buttons">
            <button className="btn btn-success btn-sm">✅ Confirmer</button>
            <button className="btn btn-info btn-sm">📦 Préparer</button>
            <button className="btn btn-warning btn-sm">🚚 Expédier</button>
            <button className="btn btn-danger btn-sm">❌ Annuler</button>
          </div>
        </div>
      )}

      {/* Tableau des commandes */}
      <div className="orders-table-container">
        <table className="orders-table">
          <thead>
            <tr>
              <th className="checkbox-col">
                <input
                  type="checkbox"
                  checked={
                    selectedOrders.length === orders.length && orders.length > 0
                  }
                  onChange={handleSelectAll}
                />
              </th>
              <th
                className={`sortable ${
                  sortBy === "numero" ? `sorted-${sortOrder}` : ""
                }`}
                onClick={() => handleSort("numero")}>
                Numéro
              </th>
              <th>Client</th>
              <th
                className={`sortable ${
                  sortBy === "date_commande" ? `sorted-${sortOrder}` : ""
                }`}
                onClick={() => handleSort("date_commande")}>
                Date
              </th>
              <th>Livraison</th>
              <th
                className={`sortable ${
                  sortBy === "montant_total" ? `sorted-${sortOrder}` : ""
                }`}
                onClick={() => handleSort("montant_total")}>
                Montant
              </th>
              <th>Priorité</th>
              <th>Statut</th>
              <th className="actions-col">Actions</th>
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => (
              <tr
                key={order.id}
                className={`order-row ${
                  selectedOrders.includes(order.id) ? "selected" : ""
                }`}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedOrders.includes(order.id)}
                    onChange={() => handleSelectOrder(order.id)}
                  />
                </td>
                <td className="order-number">
                  <span
                    className="clickable"
                    onClick={() =>
                      toast.info(`Détail de la commande: ${order.numero}`)
                    }>
                    {order.numero}
                  </span>
                </td>
                <td className="client-info">
                  <div className="client-name">{order.client_nom}</div>
                  <div className="client-email">{order.client_email}</div>
                </td>
                <td>{formatDate(order.date_commande)}</td>
                <td className="delivery-info">
                  {order.date_livraison_prevue && (
                    <div className="delivery-planned">
                      Prévue: {formatDate(order.date_livraison_prevue)}
                    </div>
                  )}
                  {order.date_livraison_reelle && (
                    <div className="delivery-actual">
                      Livrée: {formatDate(order.date_livraison_reelle)}
                    </div>
                  )}
                  {!order.date_livraison_prevue &&
                    !order.date_livraison_reelle && (
                      <span className="no-delivery">Non planifiée</span>
                    )}
                </td>
                <td className="amount">
                  {formatCurrency(order.montant_total)}
                </td>
                <td>
                  <span
                    className={`priority-badge ${getPriorityColor(
                      order.priorite
                    )}`}>
                    {getPriorityLabel(order.priorite)}
                  </span>
                </td>
                <td>
                  <span
                    className={`status-badge ${getStatusColor(order.statut)}`}>
                    {getStatusLabel(order.statut)}
                  </span>
                </td>
                <td className="actions">
                  <button
                    className="action-btn view-btn"
                    onClick={() => showOrderDetailsModal(order)}
                    title="Voir détails">
                    👁️
                  </button>
                  <button
                    className="action-btn edit-btn"
                    onClick={() => toast.info(`Modifier: ${order.numero}`)}
                    title="Modifier"
                    disabled={
                      order.statut === "LIVREE" || order.statut === "ANNULEE"
                    }>
                    ✏️
                  </button>
                  <button
                    className="action-btn invoice-btn"
                    onClick={() => createInvoiceFromOrder(order)}
                    title="Créer facture"
                    disabled={
                      order.statut === "BROUILLON" || order.statut === "ANNULEE"
                    }>
                    📄
                  </button>
                  <button
                    className="action-btn delete-btn"
                    onClick={() => {
                      if (window.confirm("Supprimer cette commande ?")) {
                        // handleDeleteOrder(order.id);
                      }
                    }}
                    title="Supprimer"
                    disabled={
                      order.statut === "EXPEDIEE" || order.statut === "LIVREE"
                    }>
                    🗑️
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {orders.length === 0 && !loading && (
          <div className="empty-state">
            <div className="empty-icon">🛒</div>
            <h3>Aucune commande trouvée</h3>
            <p>Commencez par créer votre première commande</p>
            <button
              className="btn btn-primary"
              onClick={() => toast.info("Fonctionnalité en développement")}>
              ➕ Créer une commande
            </button>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!embedded && totalPages > 1 && (
        <div className="pagination">
          <button
            className="pagination-btn"
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(currentPage - 1)}>
            ← Précédent
          </button>

          <div className="pagination-info">
            Page {currentPage} sur {totalPages} ({totalCount} commandes)
          </div>

          <button
            className="pagination-btn"
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}>
            Suivant →
          </button>
        </div>
      )}

      {/* Modal des détails de commande */}
      {showOrderDetails && selectedOrder && (
        <div
          className="modal-overlay"
          onClick={() => setShowOrderDetails(false)}>
          <div
            className="modal-content order-details-modal"
            onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>📦 Détails de la Commande - {selectedOrder.numero}</h3>
              <div className="modal-actions">
                <button
                  className="btn btn-primary"
                  onClick={() => createInvoiceFromOrder(selectedOrder)}>
                  📄 Créer Facture
                </button>
                <button
                  className="btn btn-danger"
                  onClick={() => setShowOrderDetails(false)}>
                  ✕ Fermer
                </button>
              </div>
            </div>
            <div className="modal-body">
              <div className="order-details-grid">
                <div className="detail-section">
                  <h4>📋 Informations Générales</h4>
                  <div className="detail-item">
                    <span className="detail-label">Numéro:</span>
                    <span className="detail-value">{selectedOrder.numero}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Date de commande:</span>
                    <span className="detail-value">
                      {new Date(selectedOrder.date_commande).toLocaleDateString(
                        "fr-FR"
                      )}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Statut:</span>
                    <span
                      className={`status ${selectedOrder.statut
                        .toLowerCase()
                        .replace("_", "-")}`}>
                      {selectedOrder.statut}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Urgente:</span>
                    <span
                      className={`urgency ${
                        selectedOrder.urgente ? "urgent" : "normal"
                      }`}>
                      {selectedOrder.urgente ? "🚨 OUI" : "✅ NON"}
                    </span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>👤 Client</h4>
                  <div className="detail-item">
                    <span className="detail-label">Nom:</span>
                    <span className="detail-value">
                      {selectedOrder.client_nom}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Email:</span>
                    <span className="detail-value">
                      {selectedOrder.client_email}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Adresse de livraison:</span>
                    <span className="detail-value">
                      {selectedOrder.adresse_livraison}
                    </span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>💰 Montants</h4>
                  <div className="detail-item">
                    <span className="detail-label">Montant HT:</span>
                    <span className="detail-value">
                      {selectedOrder.montant_ht.toFixed(3)} TND
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">TVA:</span>
                    <span className="detail-value">
                      {(
                        selectedOrder.montant_total - selectedOrder.montant_ht
                      ).toFixed(3)}{" "}
                      TND
                    </span>
                  </div>
                  <div className="amount-display">
                    <span className="amount-label">Total TTC:</span>
                    <span className="amount-value">
                      {selectedOrder.montant_total.toFixed(3)} TND
                    </span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>🚚 Livraison</h4>
                  <div className="detail-item">
                    <span className="detail-label">Date prévue:</span>
                    <span className="detail-value">
                      {new Date(
                        selectedOrder.date_livraison_prevue
                      ).toLocaleDateString("fr-FR")}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Mode:</span>
                    <span className="detail-value">
                      {selectedOrder.mode_livraison}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Transporteur:</span>
                    <span className="detail-value">
                      {selectedOrder.transporteur}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Nombre d'articles:</span>
                    <span className="detail-value">
                      {selectedOrder.items_count} articles
                    </span>
                  </div>
                </div>

                <div className="detail-section full-width">
                  <h4>⚙️ Actions Rapides</h4>
                  <div className="quick-actions">
                    <button
                      className="quick-action-btn"
                      onClick={() =>
                        updateOrderStatus(selectedOrder.id, "CONFIRMEE")
                      }
                      disabled={selectedOrder.statut === "CONFIRMEE"}>
                      ✅ Confirmer
                    </button>
                    <button
                      className="quick-action-btn"
                      onClick={() =>
                        updateOrderStatus(selectedOrder.id, "EN_PREPARATION")
                      }
                      disabled={selectedOrder.statut === "EN_PREPARATION"}>
                      🔄 En Préparation
                    </button>
                    <button
                      className="quick-action-btn"
                      onClick={() =>
                        updateOrderStatus(selectedOrder.id, "EXPEDIEE")
                      }
                      disabled={selectedOrder.statut === "EXPEDIEE"}>
                      📦 Expédier
                    </button>
                    <button
                      className="quick-action-btn"
                      onClick={() =>
                        updateOrderStatus(selectedOrder.id, "LIVREE")
                      }
                      disabled={selectedOrder.statut === "LIVREE"}>
                      🎯 Livrer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de détails de commande */}
      {showOrderDetails && selectedOrder && (
        <OrderDetails
          order={selectedOrder}
          onClose={() => {
            setShowOrderDetails(false);
            setSelectedOrder(null);
          }}
          onUpdateStatus={updateOrderStatus}
        />
      )}
    </div>
  );
};

export default OrdersList;
