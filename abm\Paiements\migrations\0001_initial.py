# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Clients', '0001_initial'),
        ('Facturation', '0001_initial'),
        ('Fournisseurs', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CompteBancaire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200, verbose_name='Nom du compte')),
                ('banque', models.CharField(max_length=200, verbose_name='Nom de la banque')),
                ('iban', models.Char<PERSON><PERSON>(max_length=34, unique=True)),
                ('bic', models.Char<PERSON>ield(blank=True, max_length=11)),
                ('type_compte', models.CharField(choices=[('COURANT', 'Compte courant'), ('EPARGNE', 'Compte épargne'), ('PROFESSIONNEL', 'Compte professionnel')], default='COURANT', max_length=20)),
                ('solde_initial', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('solde_actuel', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('actif', models.BooleanField(default=True)),
                ('principal', models.BooleanField(default=False, verbose_name='Compte principal')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Compte bancaire',
                'verbose_name_plural': 'Comptes bancaires',
                'ordering': ['-principal', 'nom'],
            },
        ),
        migrations.CreateModel(
            name='Paiement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reference', models.CharField(max_length=50, unique=True, verbose_name='Référence')),
                ('type_paiement', models.CharField(choices=[('ENCAISSEMENT', 'Encaissement'), ('DECAISSEMENT', 'Décaissement'), ('VIREMENT', 'Virement'), ('REMBOURSEMENT', 'Remboursement')], max_length=20)),
                ('mode_paiement', models.CharField(choices=[('ESPECES', 'Espèces'), ('CHEQUE', 'Chèque'), ('VIREMENT', 'Virement bancaire'), ('CARTE', 'Carte bancaire'), ('PRELEVEMENT', 'Prélèvement'), ('PAYPAL', 'PayPal'), ('STRIPE', 'Stripe')], max_length=20)),
                ('statut', models.CharField(choices=[('EN_ATTENTE', 'En attente'), ('VALIDE', 'Validé'), ('REJETE', 'Rejeté'), ('REMBOURSE', 'Remboursé'), ('ANNULE', 'Annulé')], default='EN_ATTENTE', max_length=20)),
                ('montant', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('devise', models.CharField(default='EUR', max_length=3)),
                ('date_paiement', models.DateTimeField(verbose_name='Date du paiement')),
                ('date_valeur', models.DateField(blank=True, null=True, verbose_name='Date de valeur')),
                ('date_echeance', models.DateField(blank=True, null=True, verbose_name="Date d'échéance")),
                ('numero_cheque', models.CharField(blank=True, max_length=50)),
                ('banque_emettrice', models.CharField(blank=True, max_length=200)),
                ('numero_transaction', models.CharField(blank=True, max_length=100)),
                ('description', models.TextField(blank=True)),
                ('notes_internes', models.TextField(blank=True)),
                ('rapproche', models.BooleanField(default=False, verbose_name='Rapproché')),
                ('date_rapprochement', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paiements', to='Clients.client')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paiements_created', to=settings.AUTH_USER_MODEL)),
                ('facture', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paiements', to='Facturation.facture')),
                ('fournisseur', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paiements', to='Fournisseurs.fournisseur')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paiements_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Paiement',
                'verbose_name_plural': 'Paiements',
                'ordering': ['-date_paiement'],
                'indexes': [models.Index(fields=['reference'], name='Paiements_p_referen_9f2f30_idx'), models.Index(fields=['statut'], name='Paiements_p_statut_df96bd_idx'), models.Index(fields=['type_paiement'], name='Paiements_p_type_pa_4f2743_idx'), models.Index(fields=['date_paiement'], name='Paiements_p_date_pa_13ec08_idx'), models.Index(fields=['rapproche'], name='Paiements_p_rapproc_a25244_idx')],
            },
        ),
    ]
