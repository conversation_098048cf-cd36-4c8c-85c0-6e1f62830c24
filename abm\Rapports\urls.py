from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    RapportTemplateViewSet, 
    RapportGenereViewSet, 
    TableauBordViewSet,
    RapportVentesView,
    RapportClientsView,
    RapportProduitsView,
    RapportFinancierView
)

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'templates', RapportTemplateViewSet, basename='rapport-template')
router.register(r'generes', RapportGenereViewSet, basename='rapport-genere')
router.register(r'tableaux-bord', TableauBordViewSet, basename='tableau-bord')

urlpatterns = [
    path('', include(router.urls)),
    
    # Rapports spécifiques
    path('ventes/', RapportVentesView.as_view(), name='rapport-ventes'),
    path('clients/', RapportClientsView.as_view(), name='rapport-clients'),
    path('produits/', RapportProduitsView.as_view(), name='rapport-produits'),
    path('financier/', RapportFinancierView.as_view(), name='rapport-financier'),
]
