/**
 * Hook pour le rafraîchissement automatique des données
 */

import { useEffect, useCallback, useRef } from 'react';

interface UseAutoRefreshOptions {
  interval?: number; // Intervalle en millisecondes (défaut: 30000 = 30s)
  onFocus?: boolean; // Rafraîchir au focus de la fenêtre (défaut: true)
  onVisibilityChange?: boolean; // Rafraîchir quand la page devient visible (défaut: true)
  enabled?: boolean; // Activer/désactiver le rafraîchissement (défaut: true)
}

export const useAutoRefresh = (
  refreshFunction: () => void | Promise<void>,
  options: UseAutoRefreshOptions = {}
) => {
  const {
    interval = 30000,
    onFocus = true,
    onVisibilityChange = true,
    enabled = true
  } = options;

  const refreshFunctionRef = useRef(refreshFunction);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Mettre à jour la référence de la fonction
  useEffect(() => {
    refreshFunctionRef.current = refreshFunction;
  }, [refreshFunction]);

  // Fonction de rafraîchissement sécurisée
  const safeRefresh = useCallback(async () => {
    try {
      await refreshFunctionRef.current();
    } catch (error) {
      console.error('Erreur lors du rafraîchissement automatique:', error);
    }
  }, []);

  // Rafraîchissement par intervalle
  useEffect(() => {
    if (!enabled || !interval) return;

    console.log(`🔄 Démarrage du rafraîchissement automatique (${interval}ms)`);
    
    intervalRef.current = setInterval(() => {
      console.log('⏰ Rafraîchissement automatique par intervalle');
      safeRefresh();
    }, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        console.log('⏹️ Arrêt du rafraîchissement automatique');
      }
    };
  }, [enabled, interval, safeRefresh]);

  // Rafraîchissement au focus de la fenêtre
  useEffect(() => {
    if (!enabled || !onFocus) return;

    const handleFocus = () => {
      console.log('👀 Fenêtre en focus - Rafraîchissement');
      safeRefresh();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [enabled, onFocus, safeRefresh]);

  // Rafraîchissement quand la page devient visible
  useEffect(() => {
    if (!enabled || !onVisibilityChange) return;

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('👁️ Page visible - Rafraîchissement');
        safeRefresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [enabled, onVisibilityChange, safeRefresh]);

  // Fonction pour forcer un rafraîchissement manuel
  const forceRefresh = useCallback(() => {
    console.log('🔄 Rafraîchissement manuel forcé');
    safeRefresh();
  }, [safeRefresh]);

  // Fonction pour redémarrer l'intervalle
  const restartInterval = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    if (enabled && interval) {
      console.log(`🔄 Redémarrage du rafraîchissement automatique (${interval}ms)`);
      intervalRef.current = setInterval(() => {
        console.log('⏰ Rafraîchissement automatique par intervalle');
        safeRefresh();
      }, interval);
    }
  }, [enabled, interval, safeRefresh]);

  return {
    forceRefresh,
    restartInterval,
    isEnabled: enabled
  };
};

// Hook spécialisé pour les clients
export const useClientAutoRefresh = (
  refreshFunction: () => void | Promise<void>,
  options: Omit<UseAutoRefreshOptions, 'interval'> & { interval?: number } = {}
) => {
  return useAutoRefresh(refreshFunction, {
    interval: 30000, // 30 secondes pour les clients
    ...options
  });
};

// Hook spécialisé pour les statistiques
export const useStatsAutoRefresh = (
  refreshFunction: () => void | Promise<void>,
  options: Omit<UseAutoRefreshOptions, 'interval'> & { interval?: number } = {}
) => {
  return useAutoRefresh(refreshFunction, {
    interval: 60000, // 60 secondes pour les statistiques
    ...options
  });
};

export default useAutoRefresh;
