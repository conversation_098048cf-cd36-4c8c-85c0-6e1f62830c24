/* Simple Dashboard Styles */
.simple-dashboard {
  padding: 2rem;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.welcome-section h1 {
  margin: 0;
  font-size: 2rem;
  color: #2c3e50;
  font-weight: 600;
}

.welcome-section p {
  margin: 0.5rem 0 0 0;
  color: #6c757d;
  font-size: 1.1rem;
}

.date-section {
  color: #6c757d;
  font-size: 1rem;
  font-weight: 500;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  padding: 1.5rem;
  border-radius: 12px;
  color: white;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
}

.stat-card.clickable {
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-card.clickable:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.stat-arrow {
  font-size: 1.5rem;
  font-weight: bold;
  opacity: 0;
  transition: all 0.3s ease;
  margin-left: auto;
}

.stat-card.clickable:hover .stat-arrow {
  opacity: 1;
  transform: translateX(5px);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.9;
}

.stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.stat-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0.25rem 0;
}

.stat-description {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.dashboard-section {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-section h2 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.action-icon {
  font-size: 1.5rem;
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  transition: all 0.2s ease;
  position: relative;
}

.activity-item.clickable {
  cursor: pointer;
}

.activity-item.clickable:hover {
  background: #ebf8ff;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-icon {
  font-size: 1.5rem;
  margin-right: 12px;
  flex-shrink: 0;
}

.activity-arrow {
  font-size: 1.2rem;
  color: #a0aec0;
  opacity: 0;
  transition: all 0.2s ease;
  margin-left: auto;
}

.activity-item.clickable:hover .activity-arrow {
  opacity: 1;
  transform: translateX(4px);
  color: #3182ce;
}

.activity-content p {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-weight: 500;
}

.activity-time {
  color: #6c757d;
  font-size: 0.85rem;
}

/* Performance Overview */
.performance-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.performance-item h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.performance-item span {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .simple-dashboard {
    padding: 1rem;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .welcome-section h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .action-button {
    padding: 1rem;
  }

  .welcome-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dashboard-logo {
    height: 60px;
  }
}

/* Logo Dashboard */
.welcome-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.dashboard-logo {
  height: 80px;
  width: auto;
  object-fit: contain;
}

.welcome-text h1 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
}

.welcome-text p {
  margin: 0;
  color: #64748b;
  font-size: 1.1rem;
}
