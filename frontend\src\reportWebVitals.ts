// Type pour les métriques de performance
type ReportHandler = (metric: any) => void;

const reportWebVitals = (onPerfEntry?: ReportHandler): void => {
  if (onPerfEntry && onPerfEntry instanceof Function) {
    // Import dynamique pour éviter les erreurs si web-vitals n'est pas installé
    import("web-vitals")
      .then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(onPerfEntry);
        getFID(onPerfEntry);
        getFCP(onPerfEntry);
        getLCP(onPerfEntry);
        getTTFB(onPerfEntry);
      })
      .catch(() => {
        // Silently fail if web-vitals is not available
        console.log("Web Vitals not available");
      });
  }
};

export default reportWebVitals;
