from rest_framework import serializers
from .models import Paiement, CompteBancaire

class CompteBancaireSerializer(serializers.ModelSerializer):
    """Serializer pour les comptes bancaires"""
    
    class Meta:
        model = CompteBancaire
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'solde_actuel')

class PaiementSerializer(serializers.ModelSerializer):
    """Serializer pour les paiements"""
    
    client_nom = serializers.CharField(source='client.nom_complet', read_only=True)
    fournisseur_nom = serializers.CharField(source='fournisseur.nom', read_only=True)
    facture_numero = serializers.CharField(source='facture.numero', read_only=True)
    est_encaissement = serializers.ReadOnlyField()
    est_decaissement = serializers.ReadOnlyField()
    peut_modifier = serializers.ReadOnlyField()
    peut_valider = serializers.ReadOnlyField()
    
    class Meta:
        model = Paiement
        fields = '__all__'
        read_only_fields = (
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'date_rapprochement'
        )
    
    def validate(self, data):
        """Validation des données de paiement"""
        
        # Vérifier qu'on a soit un client soit un fournisseur
        if data['type_paiement'] == 'ENCAISSEMENT' and not data.get('client'):
            raise serializers.ValidationError(
                "Un client est requis pour un encaissement"
            )
        
        if data['type_paiement'] == 'DECAISSEMENT' and not data.get('fournisseur'):
            raise serializers.ValidationError(
                "Un fournisseur est requis pour un décaissement"
            )
        
        return data

class PaiementStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques de paiements"""
    
    periode = serializers.DictField()
    encaissements = serializers.FloatField()
    decaissements = serializers.FloatField()
    solde_net = serializers.FloatField()
    modes_paiement = serializers.ListField()
    en_attente = serializers.DictField()
