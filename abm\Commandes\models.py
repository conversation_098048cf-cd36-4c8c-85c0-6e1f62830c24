from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid

User = get_user_model()

class Commande(models.Model):
    """Modèle Commande pour la gestion des ventes"""

    STATUT_CHOICES = [
        ('BROUILLON', 'Brouillon'),
        ('CONFIRMEE', 'Confirmée'),
        ('PREPARATION', 'En préparation'),
        ('EXPEDITION', 'Expédiée'),
        ('LIVREE', 'Livrée'),
        ('ANNULEE', 'Annulée'),
    ]

    PRIORITE_CHOICES = [
        ('BASSE', 'Basse'),
        ('NORMALE', 'Normale'),
        ('HAUTE', 'Haute'),
        ('URGENTE', 'Urgente'),
    ]

    TYPE_CHOICES = [
        ('VENTE', 'Vente'),
        ('DEVIS', 'Devis'),
        ('RETOUR', 'Retour'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    numero = models.CharField(max_length=50, unique=True, verbose_name="Numéro de commande")

    # Relations
    client = models.ForeignKey('Clients.Client', on_delete=models.PROTECT, related_name='commandes')

    # Informations de base
    type_commande = models.CharField(max_length=20, choices=TYPE_CHOICES, default='VENTE')
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='BROUILLON')
    priorite = models.CharField(max_length=20, choices=PRIORITE_CHOICES, default='NORMALE')

    # Dates
    date_commande = models.DateTimeField(auto_now_add=True)
    date_livraison_prevue = models.DateField(null=True, blank=True)
    date_livraison_reelle = models.DateField(null=True, blank=True)

    # Adresse de livraison
    adresse_livraison = models.TextField(blank=True)
    ville_livraison = models.CharField(max_length=100, blank=True)
    code_postal_livraison = models.CharField(max_length=10, blank=True)
    pays_livraison = models.CharField(max_length=100, default='France')

    # Montants
    sous_total = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    remise_globale = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    montant_tva = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    montant_total = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Informations complémentaires
    notes = models.TextField(blank=True, verbose_name="Notes")
    notes_internes = models.TextField(blank=True, verbose_name="Notes internes")

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='commandes_created')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='commandes_updated')

    class Meta:
        verbose_name = "Commande"
        verbose_name_plural = "Commandes"
        ordering = ['-date_commande']
        indexes = [
            models.Index(fields=['numero']),
            models.Index(fields=['statut']),
            models.Index(fields=['client']),
            models.Index(fields=['date_commande']),
        ]

    def __str__(self):
        return f"Commande {self.numero} - {self.client.nom}"

    def calculer_totaux(self):
        """Calcule les totaux de la commande"""
        lignes = self.lignes.all()

        self.sous_total = sum(ligne.total_ht for ligne in lignes)
        self.montant_tva = sum(ligne.montant_tva for ligne in lignes)

        # Appliquer la remise globale
        sous_total_avec_remise = self.sous_total * (1 - self.remise_globale / 100)
        self.montant_total = sous_total_avec_remise + self.montant_tva

        self.save()

    @property
    def peut_modifier(self):
        """Vérifie si la commande peut être modifiée"""
        return self.statut in ['BROUILLON', 'CONFIRMEE']

    @property
    def peut_annuler(self):
        """Vérifie si la commande peut être annulée"""
        return self.statut not in ['LIVREE', 'ANNULEE']


class LigneCommande(models.Model):
    """Lignes de commande"""

    commande = models.ForeignKey(Commande, on_delete=models.CASCADE, related_name='lignes')
    produit = models.ForeignKey('Produits.Produit', on_delete=models.PROTECT)

    # Quantités et prix
    quantite = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2)
    remise = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    taux_tva = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('20.00'))

    # Totaux calculés
    total_ht = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    montant_tva = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_ttc = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Métadonnées
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Ligne de commande"
        verbose_name_plural = "Lignes de commande"
        ordering = ['id']

    def __str__(self):
        return f"{self.produit.nom} x{self.quantite}"

    def save(self, *args, **kwargs):
        """Calcule automatiquement les totaux"""
        # Prix avec remise
        prix_avec_remise = self.prix_unitaire * (1 - self.remise / 100)

        # Total HT
        self.total_ht = self.quantite * prix_avec_remise

        # TVA
        self.montant_tva = self.total_ht * (self.taux_tva / 100)

        # Total TTC
        self.total_ttc = self.total_ht + self.montant_tva

        super().save(*args, **kwargs)

        # Recalculer les totaux de la commande
        if self.commande_id:
            self.commande.calculer_totaux()


class StatutCommande(models.Model):
    """Historique des changements de statut"""

    commande = models.ForeignKey(Commande, on_delete=models.CASCADE, related_name='historique_statuts')
    ancien_statut = models.CharField(max_length=20, blank=True)
    nouveau_statut = models.CharField(max_length=20)
    commentaire = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "Historique statut"
        verbose_name_plural = "Historique des statuts"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.commande.numero}: {self.ancien_statut} → {self.nouveau_statut}"
