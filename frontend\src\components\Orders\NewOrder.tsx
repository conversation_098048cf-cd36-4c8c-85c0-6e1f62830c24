import React, { useState } from "react";
import { toast } from "react-toastify";
import "./NewOrder.css";

interface OrderItem {
  id: string;
  code: string;
  designation: string;
  quantite: number;
  prix_unitaire: number;
  montant_ht: number;
}

interface NewOrderData {
  numero: string;
  client_nom: string;
  client_email: string;
  date_commande: string;
  date_livraison_prevue: string;
  priorite: "NORMALE" | "HAUTE" | "URGENTE";
  items: OrderItem[];
  notes: string;
}

const NewOrder: React.FC = () => {
  const navigateBack = () => {
    window.history.pushState({}, "", "/commandes");
    window.location.href = "/commandes";
  };

  const [orderData, setOrderData] = useState<NewOrderData>({
    numero: `CMD-2024-${String(Math.floor(Math.random() * 9000) + 1000)}`,
    client_nom: "",
    client_email: "",
    date_commande: new Date().toISOString().split('T')[0],
    date_livraison_prevue: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    priorite: "NORMALE",
    items: [
      {
        id: "1",
        code: "",
        designation: "",
        quantite: 1,
        prix_unitaire: 0,
        montant_ht: 0,
      },
    ],
    notes: "",
  });

  const [clients] = useState([
    { id: "1", nom: "CLIENT FOND FUTURISTE", email: "<EMAIL>" },
    { id: "2", nom: "Société Innovation Tech", email: "<EMAIL>" },
    { id: "3", nom: "Entreprise Moderne SARL", email: "<EMAIL>" },
    { id: "4", nom: "Cabinet Conseil Expert", email: "<EMAIL>" },
  ]);

  const [products] = useState([
    { code: "B1994471111", designation: "PRODUIT DESIGN FUTURISTE", prix: 57.015 },
    { code: "B1991720001", designation: "ARTICLE PREMIER PLAN", prix: 25.0 },
    { code: "B1993500002", designation: "SERVICE FUTURISME ELEGANT", prix: 45.5 },
    { code: "PACK001", designation: "Pack Solution Complète", prix: 125.75 },
  ]);

  const handleClientSelect = (clientId: string) => {
    const client = clients.find(c => c.id === clientId);
    if (client) {
      setOrderData(prev => ({
        ...prev,
        client_nom: client.nom,
        client_email: client.email,
      }));
    }
  };

  const handleItemChange = (index: number, field: keyof OrderItem, value: any) => {
    const newItems = [...orderData.items];
    newItems[index] = { ...newItems[index], [field]: value };
    
    // Recalculer le montant HT
    if (field === 'quantite' || field === 'prix_unitaire') {
      const item = newItems[index];
      item.montant_ht = item.quantite * item.prix_unitaire;
    }

    setOrderData(prev => ({ ...prev, items: newItems }));
  };

  const addItem = () => {
    const newItem: OrderItem = {
      id: String(orderData.items.length + 1),
      code: "",
      designation: "",
      quantite: 1,
      prix_unitaire: 0,
      montant_ht: 0,
    };
    setOrderData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  const removeItem = (index: number) => {
    if (orderData.items.length > 1) {
      const newItems = orderData.items.filter((_, i) => i !== index);
      setOrderData(prev => ({ ...prev, items: newItems }));
    }
  };

  const calculateTotals = () => {
    const montant_ht = orderData.items.reduce((sum, item) => sum + item.montant_ht, 0);
    const montant_tva = montant_ht * 0.20; // 20% TVA
    const montant_total = montant_ht + montant_tva;
    
    return { montant_ht, montant_tva, montant_total };
  };

  const handleSave = () => {
    if (!orderData.client_nom) {
      toast.error("Veuillez sélectionner un client");
      return;
    }

    const totals = calculateTotals();
    const newOrder = {
      ...orderData,
      ...totals,
      statut: "BROUILLON",
      created_at: new Date().toISOString(),
    };

    console.log("Nouvelle commande créée:", newOrder);
    toast.success(`Commande ${orderData.numero} créée avec succès !`);
    navigateBack();
  };

  const { montant_ht, montant_tva, montant_total } = calculateTotals();

  return (
    <div className="new-order-container">
      <div className="new-order-header">
        <div className="header-left">
          <button className="btn-back" onClick={navigateBack}>
            ← Retour
          </button>
          <h1>🛒 Nouvelle Commande</h1>
        </div>
        <div className="header-actions">
          <button className="btn btn-secondary" onClick={navigateBack}>
            Annuler
          </button>
          <button className="btn btn-primary" onClick={handleSave}>
            💾 Enregistrer
          </button>
        </div>
      </div>

      <div className="new-order-content">
        <div className="order-form">
          {/* Informations générales */}
          <div className="form-section">
            <h3>📋 Informations Générales</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>Numéro de commande</label>
                <input
                  type="text"
                  value={orderData.numero}
                  onChange={(e) => setOrderData(prev => ({ ...prev, numero: e.target.value }))}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label>Date de commande</label>
                <input
                  type="date"
                  value={orderData.date_commande}
                  onChange={(e) => setOrderData(prev => ({ ...prev, date_commande: e.target.value }))}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label>Date de livraison prévue</label>
                <input
                  type="date"
                  value={orderData.date_livraison_prevue}
                  onChange={(e) => setOrderData(prev => ({ ...prev, date_livraison_prevue: e.target.value }))}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label>Priorité</label>
                <select
                  value={orderData.priorite}
                  onChange={(e) => setOrderData(prev => ({ ...prev, priorite: e.target.value as "NORMALE" | "HAUTE" | "URGENTE" }))}
                  className="form-input">
                  <option value="NORMALE">🟢 Normale</option>
                  <option value="HAUTE">🟡 Haute</option>
                  <option value="URGENTE">🔴 Urgente</option>
                </select>
              </div>
            </div>
          </div>

          {/* Client */}
          <div className="form-section">
            <h3>👤 Client</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>Sélectionner un client</label>
                <select
                  onChange={(e) => handleClientSelect(e.target.value)}
                  className="form-input">
                  <option value="">-- Choisir un client --</option>
                  {clients.map(client => (
                    <option key={client.id} value={client.id}>
                      {client.nom}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group">
                <label>Nom du client</label>
                <input
                  type="text"
                  value={orderData.client_nom}
                  onChange={(e) => setOrderData(prev => ({ ...prev, client_nom: e.target.value }))}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label>Email</label>
                <input
                  type="email"
                  value={orderData.client_email}
                  onChange={(e) => setOrderData(prev => ({ ...prev, client_email: e.target.value }))}
                  className="form-input"
                />
              </div>
            </div>
          </div>

          {/* Articles */}
          <div className="form-section">
            <div className="section-header">
              <h3>📦 Articles</h3>
              <button className="btn btn-secondary" onClick={addItem}>
                ➕ Ajouter un article
              </button>
            </div>
            
            <div className="items-table">
              <div className="table-header">
                <div>Code</div>
                <div>Désignation</div>
                <div>Quantité</div>
                <div>Prix Unit.</div>
                <div>Montant HT</div>
                <div>Actions</div>
              </div>
              
              {orderData.items.map((item, index) => (
                <div key={item.id} className="table-row">
                  <div>
                    <select
                      value={item.code}
                      onChange={(e) => {
                        const product = products.find(p => p.code === e.target.value);
                        if (product) {
                          handleItemChange(index, 'code', product.code);
                          handleItemChange(index, 'designation', product.designation);
                          handleItemChange(index, 'prix_unitaire', product.prix);
                        }
                      }}
                      className="form-input-small">
                      <option value="">-- Produit --</option>
                      {products.map(product => (
                        <option key={product.code} value={product.code}>
                          {product.code}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <input
                      type="text"
                      value={item.designation}
                      onChange={(e) => handleItemChange(index, 'designation', e.target.value)}
                      className="form-input-small"
                    />
                  </div>
                  <div>
                    <input
                      type="number"
                      value={item.quantite}
                      onChange={(e) => handleItemChange(index, 'quantite', parseFloat(e.target.value) || 0)}
                      className="form-input-small"
                      min="0"
                      step="1"
                    />
                  </div>
                  <div>
                    <input
                      type="number"
                      value={item.prix_unitaire}
                      onChange={(e) => handleItemChange(index, 'prix_unitaire', parseFloat(e.target.value) || 0)}
                      className="form-input-small"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div className="amount-cell">
                    {item.montant_ht.toFixed(3)} TND
                  </div>
                  <div>
                    <button
                      className="btn-remove"
                      onClick={() => removeItem(index)}
                      disabled={orderData.items.length === 1}>
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Totaux */}
          <div className="form-section">
            <div className="totals-section">
              <div className="totals-grid">
                <div className="total-row">
                  <span>Total HT:</span>
                  <span className="total-value">{montant_ht.toFixed(3)} TND</span>
                </div>
                <div className="total-row">
                  <span>TVA (20%):</span>
                  <span className="total-value">{montant_tva.toFixed(3)} TND</span>
                </div>
                <div className="total-row total-ttc">
                  <span>Total TTC:</span>
                  <span className="total-value">{montant_total.toFixed(3)} TND</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="form-section">
            <h3>📝 Notes</h3>
            <textarea
              value={orderData.notes}
              onChange={(e) => setOrderData(prev => ({ ...prev, notes: e.target.value }))}
              className="form-textarea"
              rows={4}
              placeholder="Notes additionnelles sur la commande..."
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewOrder;
