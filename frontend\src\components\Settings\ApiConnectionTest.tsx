/**
 * Composant pour tester la connexion API avec le backend Django
 */

import React, { useState } from "react";
import { API_CONFIG } from "../../config/api";
import {
  ClientService,
  ProductService,
  InvoiceService,
  OrderService,
  PaymentService,
  DashboardService,
} from "../../services/apiService";
import { toast } from "react-toastify";

interface TestResult {
  service: string;
  endpoint: string;
  status: "pending" | "success" | "error";
  message: string;
  responseTime?: number;
}

const ApiConnectionTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const testEndpoints = [
    {
      service: "Dashboard",
      endpoint: "/core/kpis/",
      test: () => DashboardService.getStats(),
    },
    {
      service: "Clients",
      endpoint: "/clients/",
      test: () => ClientService.getClients({ page: 1, page_size: 1 }),
    },
    {
      service: "Produits",
      endpoint: "/products/",
      test: () => ProductService.getProducts({ page: 1, page_size: 1 }),
    },
    {
      service: "Factures",
      endpoint: "/factures/",
      test: () => InvoiceService.getInvoices({ page: 1, page_size: 1 }),
    },
    {
      service: "Commandes",
      endpoint: "/orders/",
      test: () => OrderService.getOrders({ page: 1, page_size: 1 }),
    },
    {
      service: "Paiements",
      endpoint: "/payments/",
      test: () => PaymentService.getPayments({ page: 1, page_size: 1 }),
    },
  ];

  const runTests = async () => {
    setTesting(true);
    setResults([]);

    const testResults: TestResult[] = [];

    for (const test of testEndpoints) {
      const startTime = Date.now();

      try {
        const result: TestResult = {
          service: test.service,
          endpoint: test.endpoint,
          status: "pending",
          message: "Test en cours...",
        };

        setResults((prev) => [...prev, result]);

        const response = await test.test();
        const responseTime = Date.now() - startTime;

        const updatedResult: TestResult = {
          ...result,
          status: response.error ? "error" : "success",
          message: response.error || "Connexion réussie",
          responseTime,
        };

        testResults.push(updatedResult);
        setResults((prev) =>
          prev.map((r) => (r.service === test.service ? updatedResult : r))
        );
      } catch (error: any) {
        const responseTime = Date.now() - startTime;
        const errorResult: TestResult = {
          service: test.service,
          endpoint: test.endpoint,
          status: "error",
          message: error.message || "Erreur de connexion",
          responseTime,
        };

        testResults.push(errorResult);
        setResults((prev) =>
          prev.map((r) => (r.service === test.service ? errorResult : r))
        );
      }
    }

    setTesting(false);

    // Afficher un résumé
    const successCount = testResults.filter(
      (r) => r.status === "success"
    ).length;
    const totalCount = testResults.length;

    if (successCount === totalCount) {
      toast.success(`Tous les tests réussis (${successCount}/${totalCount})`);
    } else {
      toast.warning(`${successCount}/${totalCount} tests réussis`);
    }
  };

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "pending":
        return "⏳";
      case "success":
        return "✅";
      case "error":
        return "❌";
      default:
        return "❓";
    }
  };

  const getStatusColor = (status: TestResult["status"]) => {
    switch (status) {
      case "pending":
        return "#ffa500";
      case "success":
        return "#28a745";
      case "error":
        return "#dc3545";
      default:
        return "#6c757d";
    }
  };

  return (
    <div className="api-connection-test">
      <div className="test-header">
        <h2>Test de Connexion API</h2>
        <p>Vérifiez la connectivité avec le backend Django</p>

        <div className="api-info">
          <strong>URL de base:</strong> {API_CONFIG.BASE_URL}
        </div>
      </div>

      <div className="test-actions">
        <button
          onClick={runTests}
          disabled={testing}
          className="btn-primary">
          {testing ? "Test en cours..." : "Lancer les tests"}
        </button>
      </div>

      {results.length > 0 && (
        <div className="test-results">
          <h3>Résultats des tests</h3>

          <div className="results-grid">
            {results.map((result, index) => (
              <div
                key={index}
                className="result-card"
                style={{
                  borderLeft: `4px solid ${getStatusColor(result.status)}`,
                }}>
                <div className="result-header">
                  <span className="result-icon">
                    {getStatusIcon(result.status)}
                  </span>
                  <h4>{result.service}</h4>
                  {result.responseTime && (
                    <span className="response-time">
                      {result.responseTime}ms
                    </span>
                  )}
                </div>

                <div className="result-details">
                  <code className="endpoint">{result.endpoint}</code>
                  <p className="result-message">{result.message}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="test-summary">
            <h4>Résumé</h4>
            <div className="summary-stats">
              <span className="stat">
                ✅ Réussis:{" "}
                {results.filter((r) => r.status === "success").length}
              </span>
              <span className="stat">
                ❌ Échecs: {results.filter((r) => r.status === "error").length}
              </span>
              <span className="stat">
                ⏳ En cours:{" "}
                {results.filter((r) => r.status === "pending").length}
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="test-help">
        <h4>Aide au dépannage</h4>
        <ul>
          <li>Vérifiez que le serveur Django est démarré sur le port 8000</li>
          <li>
            Assurez-vous que CORS est configuré pour autoriser les requêtes
            depuis le frontend
          </li>
          <li>
            Vérifiez que les URLs dans le backend correspondent aux endpoints
            testés
          </li>
          <li>
            Consultez la console du navigateur pour plus de détails sur les
            erreurs
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ApiConnectionTest;
