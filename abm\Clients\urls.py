from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import ClientViewSet, ContactClientViewSet

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'clients', ClientViewSet, basename='client')
router.register(r'contacts', ContactClientViewSet, basename='contact-client')

urlpatterns = [
    path('', include(router.urls)),
]
