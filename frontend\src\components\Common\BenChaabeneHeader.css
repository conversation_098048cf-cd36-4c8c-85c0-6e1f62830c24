/**
 * Styles pour le composant BenChaabeneHeader
 * Branding cohérent pour toute l'application
 */

.ben-chaabene-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #0d9488;
}

.header-logo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-logo {
  object-fit: contain;
  transition: transform 0.2s ease;
}

.company-logo:hover {
  transform: scale(1.05);
}

.logo-fallback {
  font-size: 2rem;
  color: #0d9488;
  display: none;
}

.header-text {
  flex: 1;
}

.header-title {
  margin: 0 0 0.25rem 0;
  color: #1e293b;
  font-weight: 700;
  line-height: 1.2;
}

.header-subtitle {
  margin: 0;
  color: #64748b;
  font-weight: 400;
  line-height: 1.4;
}

/* Tailles */
.ben-chaabene-header.small {
  padding: 0.75rem;
  gap: 0.75rem;
}

.ben-chaabene-header.small .company-logo {
  height: 40px;
  width: auto;
}

.ben-chaabene-header.small .header-title {
  font-size: 1.25rem;
}

.ben-chaabene-header.small .header-subtitle {
  font-size: 0.875rem;
}

.ben-chaabene-header.small .logo-fallback {
  font-size: 1.5rem;
}

.ben-chaabene-header.medium {
  padding: 1rem;
  gap: 1rem;
}

.ben-chaabene-header.medium .company-logo {
  height: 60px;
  width: auto;
}

.ben-chaabene-header.medium .header-title {
  font-size: 1.5rem;
}

.ben-chaabene-header.medium .header-subtitle {
  font-size: 1rem;
}

.ben-chaabene-header.medium .logo-fallback {
  font-size: 2rem;
}

.ben-chaabene-header.large {
  padding: 1.5rem;
  gap: 1.5rem;
}

.ben-chaabene-header.large .company-logo {
  height: 80px;
  width: auto;
}

.ben-chaabene-header.large .header-title {
  font-size: 2rem;
}

.ben-chaabene-header.large .header-subtitle {
  font-size: 1.125rem;
}

.ben-chaabene-header.large .logo-fallback {
  font-size: 2.5rem;
}

/* Variantes de couleur */
.ben-chaabene-header.primary {
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
  color: white;
  border-left-color: #ffffff;
}

.ben-chaabene-header.primary .header-title {
  color: white;
}

.ben-chaabene-header.primary .header-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.ben-chaabene-header.primary .logo-fallback {
  color: white;
}

.ben-chaabene-header.dark {
  background: linear-gradient(135deg, #1e293b 0%, #**********%);
  color: white;
  border-left-color: #0d9488;
}

.ben-chaabene-header.dark .header-title {
  color: white;
}

.ben-chaabene-header.dark .header-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

.ben-chaabene-header.dark .logo-fallback {
  color: #0d9488;
}

/* Responsive */
@media (max-width: 768px) {
  .ben-chaabene-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .ben-chaabene-header.small {
    flex-direction: row;
    text-align: left;
  }
  
  .ben-chaabene-header.large .company-logo {
    height: 60px;
  }
  
  .ben-chaabene-header.large .header-title {
    font-size: 1.75rem;
  }
  
  .ben-chaabene-header.large .header-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .ben-chaabene-header {
    padding: 0.75rem;
  }
  
  .ben-chaabene-header.medium .company-logo,
  .ben-chaabene-header.large .company-logo {
    height: 50px;
  }
  
  .ben-chaabene-header.medium .header-title,
  .ben-chaabene-header.large .header-title {
    font-size: 1.25rem;
  }
  
  .ben-chaabene-header.medium .header-subtitle,
  .ben-chaabene-header.large .header-subtitle {
    font-size: 0.875rem;
  }
}

/* Animation d'entrée */
.ben-chaabene-header {
  animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* États de focus et hover */
.ben-chaabene-header:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.ben-chaabene-header:focus-within {
  outline: 2px solid #0d9488;
  outline-offset: 2px;
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
  .ben-chaabene-header {
    animation: none;
  }
  
  .ben-chaabene-header:hover {
    transform: none;
  }
  
  .company-logo:hover {
    transform: none;
  }
}

/* Mode sombre système */
@media (prefers-color-scheme: dark) {
  .ben-chaabene-header:not(.primary):not(.dark) {
    background: linear-gradient(135deg, #1e293b 0%, #**********%);
    color: white;
  }
  
  .ben-chaabene-header:not(.primary):not(.dark) .header-title {
    color: white;
  }
  
  .ben-chaabene-header:not(.primary):not(.dark) .header-subtitle {
    color: rgba(255, 255, 255, 0.7);
  }
  
  .ben-chaabene-header:not(.primary):not(.dark) .logo-fallback {
    color: #0d9488;
  }
}
