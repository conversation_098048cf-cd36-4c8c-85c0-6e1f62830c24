/**
 * Index des composants Paramètres
 * Exporte tous les composants de configuration
 */

// Composants principaux
export { default as Settings } from "./Settings";
export { default as CompanySettings } from "./CompanySettings";
export { default as UserSettings } from "./UserSettings";
export { default as InvoiceSettings } from "./InvoiceSettings";
export { default as SystemSettings } from "./SystemSettings";

// Types TypeScript
export interface CompanySettings {
  nom: string;
  siret: string;
  tva_intracommunautaire: string;
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
  telephone: string;
  email: string;
  site_web: string;
  logo_url: string;
  capital_social: number;
  forme_juridique: string;
  code_ape: string;
  rcs: string;
  banque_nom: string;
  banque_iban: string;
  banque_bic: string;
}

export interface UserSettings {
  first_name: string;
  last_name: string;
  email: string;
  telephone: string;
  poste: string;
  avatar_url: string;
  langue: string;
  fuseau_horaire: string;
  format_date: string;
  format_heure: string;
  notifications_email: boolean;
  notifications_desktop: boolean;
  theme: "light" | "dark" | "auto";
}

export interface InvoiceSettings {
  numerotation_auto: boolean;
  prefixe_facture: string;
  compteur_facture: number;
  template_defaut: string;
  mentions_legales: string;
  conditions_paiement: string;
  delai_paiement_defaut: number;
  taux_penalite: number;
  escompte_defaut: number;
  devise_defaut: string;
  langue_defaut: string;
  envoi_auto_email: boolean;
  relance_auto: boolean;
  delai_relance: number;
}

export interface SystemSettings {
  maintenance_mode: boolean;
  debug_mode: boolean;
  log_level: string;
  backup_auto: boolean;
  backup_frequence: string;
  retention_logs: number;
  max_file_size: number;
  allowed_file_types: string[];
  session_timeout: number;
  max_login_attempts: number;
  password_min_length: number;
  password_complexity: boolean;
  two_factor_auth: boolean;
}

// Constantes
export const LEGAL_FORMS = [
  "SARL",
  "SAS",
  "SA",
  "EURL",
  "SNC",
  "Auto-entrepreneur",
  "Association",
] as const;

export const COUNTRIES = [
  "France",
  "Belgique",
  "Suisse",
  "Luxembourg",
  "Canada",
] as const;

export const LANGUAGES = [
  { code: "fr", label: "🇫🇷 Français" },
  { code: "en", label: "🇬🇧 English" },
  { code: "es", label: "🇪🇸 Español" },
] as const;

export const TIMEZONES = [
  { value: "Europe/Paris", label: "Europe/Paris (UTC+1)" },
  { value: "Europe/London", label: "Europe/London (UTC+0)" },
  { value: "America/New_York", label: "America/New_York (UTC-5)" },
  { value: "Asia/Tokyo", label: "Asia/Tokyo (UTC+9)" },
] as const;

export const DATE_FORMATS = ["DD/MM/YYYY", "MM/DD/YYYY", "YYYY-MM-DD"] as const;

export const TIME_FORMATS = ["24h", "12h"] as const;

export const THEMES = [
  { value: "light", label: "☀️ Clair" },
  { value: "dark", label: "🌙 Sombre" },
  { value: "auto", label: "🔄 Automatique" },
] as const;

export const CURRENCIES = [
  { code: "EUR", label: "€ Euro", symbol: "€" },
  { code: "USD", label: "$ Dollar US", symbol: "$" },
  { code: "GBP", label: "£ Livre Sterling", symbol: "£" },
  { code: "CHF", label: "CHF Franc Suisse", symbol: "CHF" },
] as const;

export const INVOICE_TEMPLATES = [
  { id: "modern", label: "Moderne", preview: "/templates/modern.png" },
  { id: "classic", label: "Classique", preview: "/templates/classic.png" },
  { id: "minimal", label: "Minimal", preview: "/templates/minimal.png" },
] as const;

export const LOG_LEVELS = [
  "DEBUG",
  "INFO",
  "WARNING",
  "ERROR",
  "CRITICAL",
] as const;

export const BACKUP_FREQUENCIES = [
  { value: "daily", label: "Quotidienne" },
  { value: "weekly", label: "Hebdomadaire" },
  { value: "monthly", label: "Mensuelle" },
] as const;

// Fonctions utilitaires
export const validateIBAN = (iban: string): boolean => {
  const cleaned = iban.replace(/\s/g, "");
  return /^[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}[A-Z0-9]{1,23}$/.test(cleaned);
};

export const formatIBAN = (iban: string): string => {
  const cleaned = iban.replace(/\s/g, "");
  return cleaned.replace(/(.{4})/g, "$1 ").trim();
};

export const getDefaultCompanySettings = (): CompanySettings => ({
  nom: "",
  siret: "",
  tva_intracommunautaire: "",
  adresse: "",
  ville: "",
  code_postal: "",
  pays: "France",
  telephone: "",
  email: "",
  site_web: "",
  logo_url: "",
  capital_social: 0,
  forme_juridique: "",
  code_ape: "",
  rcs: "",
  banque_nom: "",
  banque_iban: "",
  banque_bic: "",
});

export const getDefaultUserSettings = (): UserSettings => ({
  first_name: "",
  last_name: "",
  email: "",
  telephone: "",
  poste: "",
  avatar_url: "",
  langue: "fr",
  fuseau_horaire: "Europe/Paris",
  format_date: "DD/MM/YYYY",
  format_heure: "24h",
  notifications_email: true,
  notifications_desktop: true,
  theme: "light",
});

export const getDefaultInvoiceSettings = (): InvoiceSettings => ({
  numerotation_auto: true,
  prefixe_facture: "FAC",
  compteur_facture: 1,
  template_defaut: "modern",
  mentions_legales: "",
  conditions_paiement: "Paiement à 30 jours",
  delai_paiement_defaut: 30,
  taux_penalite: 3,
  escompte_defaut: 0,
  devise_defaut: "EUR",
  langue_defaut: "fr",
  envoi_auto_email: false,
  relance_auto: false,
  delai_relance: 7,
});

export const getDefaultSystemSettings = (): SystemSettings => ({
  maintenance_mode: false,
  debug_mode: false,
  log_level: "INFO",
  backup_auto: true,
  backup_frequence: "daily",
  retention_logs: 30,
  max_file_size: 10 * 1024 * 1024, // 10MB
  allowed_file_types: [
    "pdf",
    "jpg",
    "jpeg",
    "png",
    "doc",
    "docx",
    "xls",
    "xlsx",
  ],
  session_timeout: 3600, // 1 heure
  max_login_attempts: 5,
  password_min_length: 8,
  password_complexity: true,
  two_factor_auth: false,
});

// Routes des paramètres
export const SETTINGS_ROUTES = {
  MAIN: "/settings",
  COMPANY: "/settings/company",
  USER: "/settings/user",
  INVOICES: "/settings/invoices",
  SYSTEM: "/settings/system",
} as const;
