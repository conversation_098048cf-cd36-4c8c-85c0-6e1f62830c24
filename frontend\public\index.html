<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/logo_ben_chaabene.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#0d9488" />
  <meta name="description" content="ABM - Automated Business Manager pour Société Ben Chaabène de Commerce" />
  
  <!-- Préchargement du logo -->
  <link rel="preload" href="%PUBLIC_URL%/logo_ben_chaabene.png" as="image" />
  
  <!-- Polices Google Fonts pour un design moderne -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Styles de base -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      color: #1e293b;
    }
    
    #root {
      min-height: 100vh;
    }
    
    /* Loading spinner */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #0d9488;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }
    
    .loading-text {
      color: #64748b;
      font-size: 1.1rem;
      font-weight: 500;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    
    /* Styles pour les navigateurs non supportés */
    .no-js {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      text-align: center;
      padding: 2rem;
    }
    
    .no-js h1 {
      color: #dc2626;
      margin-bottom: 1rem;
    }
    
    .no-js p {
      color: #64748b;
      max-width: 500px;
      line-height: 1.6;
    }
  </style>
  
  <title>ABM - Société Ben Chaabène de Commerce</title>
</head>
<body>
  <noscript>
    <div class="no-js">
      <img src="%PUBLIC_URL%/logo_ben_chaabene.png" alt="Ben Chaabène" style="width: 100px; margin-bottom: 2rem;" />
      <h1>🚫 JavaScript Requis</h1>
      <p>
        L'application ABM (Automated Business Manager) nécessite JavaScript pour fonctionner correctement.
        Veuillez activer JavaScript dans votre navigateur et recharger la page.
      </p>
    </div>
  </noscript>
  
  <!-- Point d'ancrage principal pour React -->
  <div id="root">
    <!-- Écran de chargement affiché pendant que React se charge -->
    <div class="loading-container">
      <img src="%PUBLIC_URL%/logo_ben_chaabene.png" alt="Ben Chaabène" class="loading-logo" />
      <div class="loading-spinner"></div>
      <div class="loading-text">Chargement de l'application ABM...</div>
    </div>
  </div>
  
  <!-- Scripts additionnels si nécessaires -->
  <script>
    // Configuration globale pour l'application
    window.ABM_CONFIG = {
      version: '2.0.0',
      company: 'Société Ben Chaabène de Commerce',
      theme: 'professional'
    };
    
    // Gestion des erreurs globales
    window.addEventListener('error', function(e) {
      console.error('Erreur globale:', e.error);
    });
    
    // Performance monitoring
    window.addEventListener('load', function() {
      console.log('Application ABM chargée en', performance.now(), 'ms');
    });
  </script>
</body>
</html>
