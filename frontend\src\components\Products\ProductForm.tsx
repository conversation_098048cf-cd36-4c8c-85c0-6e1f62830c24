import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ProductService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import FileUpload from "../Media/FileUpload";
import { MediaFile } from "../../services/mediaService";
import "../Invoices/Invoice.css";

interface Product {
  id?: number;
  nom: string;
  description: string;
  prix_unitaire: number;
  taux_tva: number;
  stock_actuel: number;
  code_produit: string;
  is_active: boolean;
}

const ProductForm: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const { id } = useParams<{ id: string }>();
  const isEdit = !!id;

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    reference: "",
    nom: "",
    description: "",
    description_courte: "",
    prix_achat: 0,
    prix_vente: 0,
    prix_unitaire: 0,
    taux_tva: 20,
    unite_mesure: "PIECE",
    stock_actuel: 0,
    stock_minimum: 5,
    stock_maximum: 100,
    type_produit: "PHYSIQUE",
    statut: "ACTIF",
    gestion_stock: true,
    visible_ecommerce: false,
    categorie: "",
    tags: "",
    code_produit: "",
    is_active: true,
    image: "",
  });
  const [showImageUpload, setShowImageUpload] = useState(false);

  useEffect(() => {
    if (isEdit && id) {
      const load = async () => {
        setLoading(true);
        const res = await ProductService.getProduct(id);
        if (res && res.data) {
          const product = res.data as any;
          setFormData({
            reference: product.reference || "",
            nom: product.nom || "",
            description: product.description || "",
            description_courte: product.description_courte || "",
            prix_achat: product.prix_achat || 0,
            prix_vente: product.prix_vente || 0,
            prix_unitaire: product.prix_unitaire || product.prix_vente || 0,
            taux_tva: product.taux_tva || 20,
            unite_mesure: product.unite_mesure || "PIECE",
            stock_actuel: product.stock_actuel || 0,
            stock_minimum: product.stock_minimum || 5,
            stock_maximum: product.stock_maximum || 100,
            type_produit: product.type_produit || "PHYSIQUE",
            statut: product.statut || "ACTIF",
            gestion_stock: product.gestion_stock !== false,
            visible_ecommerce: product.visible_ecommerce || false,
            categorie: product.categorie || "",
            tags: product.tags || "",
            code_produit: product.code_produit || product.reference || "",
            is_active: product.is_active !== false,
            image: product.image || "",
          });
        }
        setLoading(false);
      };
      load();
    }
  }, [isEdit, id]);

  const handleInputChange = (
    field: string,
    value: string | number | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = (files: MediaFile[]) => {
    if (files.length > 0) {
      setFormData((prev) => ({ ...prev, image: files[0].url }));
      setShowImageUpload(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!formData.nom || !formData.code_produit) {
        notify.error(
          "Erreur de validation",
          "Nom et code produit sont obligatoires"
        );
        return;
      }

      const res = isEdit
        ? await ProductService.updateProduct(id!, formData as any)
        : await ProductService.createProduct(formData as any);

      if (res) {
        notify.success(
          "Succès",
          `Produit ${isEdit ? "modifié" : "créé"} avec succès`
        );
        navigate("/dashboard/produits");
      } else {
        notify.error("Erreur", res.message || "Une erreur est survenue");
      }
    } catch (error) {
      notify.error("Erreur", "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">
              {isEdit ? "Modifier" : "Nouveau"} Produit
            </h1>
            <p className="page-subtitle">
              {isEdit
                ? "Modifier les informations du produit"
                : "Créer un nouveau produit"}
            </p>
          </div>
          <div className="page-actions">
            <button
              type="button"
              className="page-action secondary"
              onClick={() => navigate("/dashboard/produits")}>
              ← Retour
            </button>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="content-grid two-columns">
          {/* Informations générales */}
          <div className="content-card">
            <div className="card-header">
              <h3 className="card-title">Informations générales</h3>
            </div>
            <div className="card-content">
              <div className="form-group">
                <label>Nom *</label>
                <input
                  type="text"
                  value={formData.nom}
                  onChange={(e) => handleInputChange("nom", e.target.value)}
                  className="form-control"
                  required
                />
              </div>

              <div className="form-group">
                <label>Code produit *</label>
                <input
                  type="text"
                  value={formData.code_produit}
                  onChange={(e) =>
                    handleInputChange("code_produit", e.target.value)
                  }
                  className="form-control"
                  placeholder="PROD-001"
                  required
                />
              </div>

              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  className="form-control"
                  rows={3}
                  placeholder="Description du produit..."
                />
              </div>

              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) =>
                      handleInputChange("is_active", e.target.checked)
                    }
                    style={{ marginRight: "8px" }}
                  />
                  Produit actif
                </label>
              </div>
            </div>
          </div>

          {/* Prix et TVA */}
          <div className="content-card">
            <div className="card-header">
              <h3 className="card-title">Prix et TVA</h3>
            </div>
            <div className="card-content">
              <div className="form-group">
                <label>Prix unitaire (TND) *</label>
                <input
                  type="number"
                  value={formData.prix_unitaire}
                  onChange={(e) =>
                    handleInputChange(
                      "prix_unitaire",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className="form-control"
                  min="0"
                  step="0.001"
                  required
                />
              </div>

              <div className="form-group">
                <label>Taux TVA (%)</label>
                <select
                  value={formData.taux_tva}
                  onChange={(e) =>
                    handleInputChange("taux_tva", parseInt(e.target.value))
                  }
                  className="form-control">
                  <option value={0}>0%</option>
                  <option value={7}>7%</option>
                  <option value={13}>13%</option>
                  <option value={19}>19%</option>
                </select>
              </div>

              <div className="form-group">
                <label>Unité de mesure</label>
                <input
                  type="text"
                  value={formData.unite_mesure}
                  onChange={(e) =>
                    handleInputChange("unite_mesure", e.target.value)
                  }
                  className="form-control"
                  placeholder="unité, kg, m², heure..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Stock */}
        <div className="content-card">
          <div className="card-header">
            <h3 className="card-title">Gestion du stock</h3>
          </div>
          <div className="card-content">
            <div className="form-row">
              <div className="form-group">
                <label>Stock actuel</label>
                <input
                  type="number"
                  value={formData.stock_actuel}
                  onChange={(e) =>
                    handleInputChange(
                      "stock_actuel",
                      parseInt(e.target.value) || 0
                    )
                  }
                  className="form-control"
                  min="0"
                />
              </div>
              <div className="form-group">
                <label>Stock minimum</label>
                <input
                  type="number"
                  value={formData.stock_minimum}
                  onChange={(e) =>
                    handleInputChange(
                      "stock_minimum",
                      parseInt(e.target.value) || 0
                    )
                  }
                  className="form-control"
                  min="0"
                />
              </div>
              <div className="form-group">
                <label>Stock maximum</label>
                <input
                  type="number"
                  value={formData.stock_maximum}
                  onChange={(e) =>
                    handleInputChange(
                      "stock_maximum",
                      parseInt(e.target.value) || 0
                    )
                  }
                  className="form-control"
                  min="0"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Section Image */}
        <div className="content-card">
          <div className="card-header">
            <h3 className="card-title">Image du produit</h3>
          </div>
          <div className="card-content">
            <div
              style={{
                display: "flex",
                gap: "20px",
                alignItems: "flex-start",
              }}>
              {formData.image && (
                <div style={{ flex: "0 0 150px" }}>
                  <img
                    src={formData.image}
                    alt="Aperçu produit"
                    style={{
                      width: "150px",
                      height: "150px",
                      objectFit: "cover",
                      borderRadius: "8px",
                      border: "1px solid #dee2e6",
                    }}
                  />
                </div>
              )}
              <div style={{ flex: 1 }}>
                <button
                  type="button"
                  onClick={() => setShowImageUpload(true)}
                  style={{
                    padding: "12px 20px",
                    background: "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    marginBottom: "10px",
                  }}>
                  📷 {formData.image ? "Changer l'image" : "Ajouter une image"}
                </button>
                {formData.image && (
                  <button
                    type="button"
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, image: "" }))
                    }
                    style={{
                      padding: "8px 16px",
                      background: "#e74c3c",
                      color: "white",
                      border: "none",
                      borderRadius: "6px",
                      cursor: "pointer",
                      marginLeft: "10px",
                    }}>
                    🗑️ Supprimer
                  </button>
                )}
                <p
                  style={{
                    fontSize: "12px",
                    color: "#666",
                    margin: "10px 0 0 0",
                  }}>
                  Formats acceptés: JPG, PNG, WebP • Taille max: 5MB
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="form-actions">
          <button
            type="button"
            className="page-action secondary"
            onClick={() => navigate("/dashboard/produits")}>
            Annuler
          </button>
          <button
            type="submit"
            className="page-action"
            disabled={loading}>
            {loading ? "Enregistrement..." : isEdit ? "Modifier" : "Créer"}
          </button>
        </div>
      </form>

      {/* Modal d'upload d'image */}
      {showImageUpload && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(0,0,0,0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}>
          <div
            style={{
              background: "white",
              borderRadius: "12px",
              padding: "24px",
              width: "90%",
              maxWidth: "600px",
              maxHeight: "90vh",
              overflow: "auto",
            }}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "20px",
              }}>
              <h3 style={{ margin: 0 }}>Sélectionner une image</h3>
              <button
                onClick={() => setShowImageUpload(false)}
                style={{
                  background: "none",
                  border: "none",
                  fontSize: "24px",
                  cursor: "pointer",
                  color: "#666",
                }}>
                ×
              </button>
            </div>

            <FileUpload
              accept="image/*"
              maxSize={5 * 1024 * 1024} // 5MB
              onUploadComplete={handleImageUpload}
              onUploadError={(error) => notify.error("Erreur", error)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductForm;
