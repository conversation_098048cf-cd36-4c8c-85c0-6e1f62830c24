"""
Système de Facturation Professionnel - Version Moderne
Basé sur l'image fournie avec templates personnalisables et génération PDF avancée
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.core.exceptions import ValidationError
from decimal import Decimal
import uuid
import os
from django.conf import settings
from Core.models import BaseModel

User = get_user_model()


class ConfigurationFacturation(BaseModel):
    """Configuration globale pour la facturation"""

    # Informations société
    nom_societe = models.CharField(max_length=200, verbose_name="Nom de la société")
    adresse_ligne1 = models.CharField(max_length=200, verbose_name="Adresse ligne 1")
    adresse_ligne2 = models.CharField(max_length=200, blank=True, verbose_name="Adresse ligne 2")
    code_postal = models.CharField(max_length=10, verbose_name="Code postal")
    ville = models.CharField(max_length=100, verbose_name="Ville")
    pays = models.CharField(max_length=100, default="Tunisie")

    # Informations légales
    siret = models.CharField(max_length=20, verbose_name="Matricule Fiscal", help_text="Numéro d'identification fiscale")
    numero_tva = models.CharField(max_length=20, verbose_name="Numéro TVA", blank=True)
    code_ape = models.CharField(max_length=10, verbose_name="Code Activité", blank=True)
    capital_social = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)

    # Contact
    telephone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    site_web = models.URLField(blank=True)

    # Configuration facturation
    numerotation_auto = models.BooleanField(default=True)
    prefixe_facture = models.CharField(max_length=10, default="F")
    prefixe_devis = models.CharField(max_length=10, default="D")
    prefixe_bon_livraison = models.CharField(max_length=10, default="BL")
    compteur_facture = models.IntegerField(default=1)
    compteur_devis = models.IntegerField(default=1)
    compteur_bon_livraison = models.IntegerField(default=1)

    # Conditions par défaut
    conditions_paiement = models.TextField(
        default="Paiement à 30 jours fin de mois",
        verbose_name="Conditions de paiement"
    )
    mentions_legales = models.TextField(
        default="",
        blank=True,
        verbose_name="Mentions légales"
    )

    # Logo et branding
    logo = models.ImageField(
        upload_to='logos/',
        null=True,
        blank=True,
        verbose_name="Logo de l'entreprise"
    )
    couleur_principale = models.CharField(
        max_length=7,
        default="#2563eb",
        verbose_name="Couleur principale (hex)"
    )

    class Meta:
        verbose_name = "Configuration de facturation"
        verbose_name_plural = "Configurations de facturation"

    def __str__(self):
        return f"Configuration - {self.nom_societe}"

    def save(self, *args, **kwargs):
        # S'assurer qu'il n'y a qu'une seule configuration
        if not self.pk and ConfigurationFacturation.objects.exists():
            raise ValidationError("Une seule configuration de facturation est autorisée")
        super().save(*args, **kwargs)

    @classmethod
    def get_config(cls):
        """Récupère la configuration active ou en crée une par défaut"""
        config, created = cls.objects.get_or_create(
            defaults={
                'nom_societe': 'Société Ben Chaabene de Commerce',
                'adresse_ligne1': '10, Rue de la Commission',
                'code_postal': '6037',
                'ville': 'Gabès',
                'pays': 'Tunisie',
                'siret': '1234567890123',
                'telephone': '+216 75 287 853',
                'email': '<EMAIL>'
            }
        )
        return config


class StatusChoices(models.TextChoices):
    """Statuts des documents de facturation"""
    BROUILLON = 'BROUILLON', 'Brouillon'
    ENVOYE = 'ENVOYE', 'Envoyé'
    ACCEPTE = 'ACCEPTE', 'Accepté'
    REFUSE = 'REFUSE', 'Refusé'
    PAYE = 'PAYE', 'Payé'
    ANNULE = 'ANNULE', 'Annulé'


class TypeDocument(models.TextChoices):
    """Types de documents"""
    DEVIS = 'DEVIS', 'Devis'
    FACTURE = 'FACTURE', 'Facture'
    AVOIR = 'AVOIR', 'Avoir'
    BON_LIVRAISON = 'BON_LIVRAISON', 'Bon de livraison'


class Facture(BaseModel):
    """Modèle principal pour les factures et devis"""

    # Identification
    numero = models.CharField(max_length=50, unique=True, verbose_name="Numéro")
    type_document = models.CharField(
        max_length=20,
        choices=TypeDocument.choices,
        default=TypeDocument.FACTURE
    )

    # Client
    client = models.ForeignKey(
        'Clients.Client',
        on_delete=models.PROTECT,
        related_name='factures'
    )

    # Dates
    date_emission = models.DateField(default=timezone.now)
    date_echeance = models.DateField(null=True, blank=True)
    date_paiement = models.DateField(null=True, blank=True)

    # Statut
    statut = models.CharField(
        max_length=20,
        choices=StatusChoices.choices,
        default=StatusChoices.BROUILLON
    )

    # Montants
    montant_ht = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name="Montant HT"
    )
    montant_tva = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name="Montant TVA"
    )
    montant_ttc = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name="Montant TTC"
    )

    # Remises
    remise_pourcentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    remise_montant = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000')
    )

    # Informations complémentaires
    notes = models.TextField(blank=True, verbose_name="Notes")
    conditions_paiement = models.TextField(blank=True)

    # Fichiers
    fichier_pdf = models.FileField(
        upload_to='factures/pdf/',
        null=True,
        blank=True
    )

    # Métadonnées
    creee_par = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='factures_creees'
    )

    # Relation avec la facture d'origine (pour les bons de livraison)
    facture_origine = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='bons_livraison',
        verbose_name="Facture d'origine"
    )

    class Meta:
        verbose_name = "Facture"
        verbose_name_plural = "Factures"
        ordering = ['-date_emission', '-numero']
        indexes = [
            models.Index(fields=['numero']),
            models.Index(fields=['client', 'date_emission']),
            models.Index(fields=['statut']),
            models.Index(fields=['type_document']),
        ]

    def __str__(self):
        return f"{self.get_type_document_display()} {self.numero}"

    def save(self, *args, **kwargs):
        is_new = self.pk is None

        if not self.numero:
            self.numero = self.generer_numero()

        # Calculer les montants
        self.calculer_montants()

        super().save(*args, **kwargs)

        # Créer automatiquement un bon de livraison pour chaque nouvelle facture
        if is_new and self.type_document == TypeDocument.FACTURE:
            self.creer_bon_livraison()

    def generer_numero(self):
        """Génère un numéro automatique"""
        config = ConfigurationFacturation.get_config()

        if self.type_document == TypeDocument.FACTURE:
            prefixe = config.prefixe_facture
            compteur = config.compteur_facture
            config.compteur_facture += 1
        elif self.type_document == TypeDocument.BON_LIVRAISON:
            prefixe = config.prefixe_bon_livraison
            compteur = config.compteur_bon_livraison
            config.compteur_bon_livraison += 1
        else:  # DEVIS
            prefixe = config.prefixe_devis
            compteur = config.compteur_devis
            config.compteur_devis += 1

        config.save()

        # Format: F2024-0001, BL2024-0001, D2024-0001
        annee = timezone.now().year
        return f"{prefixe}{annee}-{compteur:04d}"

    def creer_bon_livraison(self):
        """Crée automatiquement un bon de livraison associé à cette facture"""
        if self.type_document != TypeDocument.FACTURE:
            return None

        # Vérifier qu'un bon de livraison n'existe pas déjà
        bon_existant = Facture.objects.filter(
            type_document=TypeDocument.BON_LIVRAISON,
            facture_origine=self
        ).first()

        if bon_existant:
            return bon_existant

        # Créer le bon de livraison avec le même numéro que la facture
        bon_livraison = Facture.objects.create(
            type_document=TypeDocument.BON_LIVRAISON,
            numero=self.numero.replace('F', 'BL'),  # F2024-0001 -> BL2024-0001
            client=self.client,
            date_emission=self.date_emission,
            date_echeance=self.date_echeance,
            statut=StatusChoices.BROUILLON,
            notes=f"Bon de livraison généré automatiquement pour la facture {self.numero}",
            conditions_paiement=self.conditions_paiement,
            creee_par=self.creee_par,
            facture_origine=self
        )

        # Copier toutes les lignes de la facture
        for ligne_facture in self.lignes.all():
            LigneFacture.objects.create(
                facture=bon_livraison,
                produit=ligne_facture.produit,
                designation=ligne_facture.designation,
                description=ligne_facture.description,
                quantite=ligne_facture.quantite,
                prix_unitaire_ht=ligne_facture.prix_unitaire_ht,
                taux_tva=ligne_facture.taux_tva,
                ordre=ligne_facture.ordre
            )

        return bon_livraison

    def calculer_montants(self):
        """Calcule les montants de la facture"""
        lignes = self.lignes.all()

        # Calcul du sous-total HT
        sous_total_ht = sum(ligne.montant_ht for ligne in lignes)

        # Application de la remise
        if self.remise_pourcentage > 0:
            self.remise_montant = sous_total_ht * (self.remise_pourcentage / 100)

        self.montant_ht = sous_total_ht - self.remise_montant

        # Calcul de la TVA
        self.montant_tva = sum(ligne.montant_tva for ligne in lignes)
        if self.remise_montant > 0:
            # Réduire la TVA proportionnellement
            ratio_remise = self.remise_montant / sous_total_ht if sous_total_ht > 0 else 0
            self.montant_tva = self.montant_tva * (1 - ratio_remise)

        # Calcul du TTC
        self.montant_ttc = self.montant_ht + self.montant_tva

    @property
    def est_payee(self):
        return self.statut == StatusChoices.PAYE

    @property
    def est_en_retard(self):
        if self.date_echeance and not self.est_payee:
            return timezone.now().date() > self.date_echeance
        return False


class LigneFacture(BaseModel):
    """Ligne de facture"""

    facture = models.ForeignKey(
        Facture,
        on_delete=models.CASCADE,
        related_name='lignes'
    )

    # Produit ou service
    produit = models.ForeignKey(
        'Produits.Produit',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    # Description manuelle (si pas de produit)
    designation = models.CharField(max_length=500)
    description = models.TextField(blank=True)

    # Quantité et prix
    quantite = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        default=Decimal('1.000')
    )
    prix_unitaire_ht = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name="Prix unitaire HT"
    )

    # TVA
    taux_tva = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('19.00'),
        verbose_name="Taux TVA (%)"
    )

    # Montants calculés
    montant_ht = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name="Montant HT"
    )
    montant_tva = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name="Montant TVA"
    )
    montant_ttc = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name="Montant TTC"
    )

    # Ordre d'affichage
    ordre = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = "Ligne de facture"
        verbose_name_plural = "Lignes de facture"
        ordering = ['ordre', 'id']

    def __str__(self):
        return f"{self.designation} - {self.quantite} x {self.prix_unitaire_ht}"

    def save(self, *args, **kwargs):
        # Récupérer les infos du produit si sélectionné
        if self.produit and not self.designation:
            self.designation = self.produit.nom
            self.prix_unitaire_ht = self.produit.prix_vente_ht
            self.taux_tva = self.produit.taux_tva

        # Calculer les montants
        self.calculer_montants()

        super().save(*args, **kwargs)

        # Recalculer les montants de la facture
        if self.facture_id:
            self.facture.calculer_montants()
            self.facture.save()

    def calculer_montants(self):
        """Calcule les montants de la ligne"""
        self.montant_ht = self.quantite * self.prix_unitaire_ht
        self.montant_tva = self.montant_ht * (self.taux_tva / Decimal('100'))
        self.montant_ttc = self.montant_ht + self.montant_tva


class PaiementFacture(BaseModel):
    """Paiements associés à une facture"""

    facture = models.ForeignKey(
        Facture,
        on_delete=models.CASCADE,
        related_name='paiements'
    )

    date_paiement = models.DateField(default=timezone.now)
    montant = models.DecimalField(max_digits=12, decimal_places=3)

    MODE_PAIEMENT_CHOICES = [
        ('ESPECES', 'Espèces'),
        ('CHEQUE', 'Chèque'),
        ('VIREMENT', 'Virement'),
        ('CARTE', 'Carte bancaire'),
        ('AUTRE', 'Autre'),
    ]

    mode_paiement = models.CharField(
        max_length=20,
        choices=MODE_PAIEMENT_CHOICES,
        default='VIREMENT'
    )

    reference = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)

    class Meta:
        verbose_name = "Paiement"
        verbose_name_plural = "Paiements"
        ordering = ['-date_paiement']

    def __str__(self):
        return f"Paiement {self.montant}€ - {self.facture.numero}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Vérifier si la facture est entièrement payée
        total_paiements = self.facture.paiements.aggregate(
            total=models.Sum('montant')
        )['total'] or Decimal('0')

        if total_paiements >= self.facture.montant_ttc:
            self.facture.statut = StatusChoices.PAYE
            self.facture.date_paiement = self.date_paiement
            self.facture.save()


# Signaux pour maintenir la cohérence
from django.db.models.signals import post_delete
from django.dispatch import receiver

@receiver(post_delete, sender=LigneFacture)
def recalculer_facture_apres_suppression(sender, instance, **kwargs):
    """Recalcule la facture après suppression d'une ligne"""
    if instance.facture_id:
        try:
            facture = Facture.objects.get(id=instance.facture_id)
            facture.calculer_montants()
            facture.save()
        except Facture.DoesNotExist:
            pass
