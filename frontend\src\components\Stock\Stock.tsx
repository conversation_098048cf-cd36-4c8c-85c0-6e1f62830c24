/**
 * Module Stock - Gestion complète des stocks
 */

import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import StockOverview from './StockOverview';
import StockMovements from './StockMovements';
import StockAlerts from './StockAlerts';
import StockInventory from './StockInventory';
import StockStats from './StockStats';
import { useAuth } from '../../contexts/AuthContext';
import './Stock.css';

const Stock: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [activeTab, setActiveTab] = useState(() => {
    const path = location.pathname.split('/').pop();
    return path || 'overview';
  });

  // Vérifier les permissions
  const hasAccess = () => {
    const allowedRoles = ['COMPTABLE', 'ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || '');
  };

  const tabs = [
    {
      id: 'overview',
      label: '📊 Vue d\'ensemble',
      component: StockOverview,
      permission: hasAccess()
    },
    {
      id: 'movements',
      label: '📦 Mouvements',
      component: StockMovements,
      permission: hasAccess()
    },
    {
      id: 'alerts',
      label: '🚨 Alertes',
      component: StockAlerts,
      permission: hasAccess()
    },
    {
      id: 'inventory',
      label: '📋 Inventaire',
      component: StockInventory,
      permission: hasAccess()
    },
    {
      id: 'stats',
      label: '📈 Statistiques',
      component: StockStats,
      permission: hasAccess()
    }
  ].filter(tab => tab.permission);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    navigate(`/stock/${tabId}`);
  };

  if (!hasAccess()) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder au module stock.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="stock-module">
      {/* Header */}
      <div className="stock-header">
        <div className="header-title">
          <h1>📦 Gestion des Stocks</h1>
          <p>Suivi des stocks, mouvements et inventaires</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-outline"
            onClick={() => navigate('/dashboard')}
          >
            ← Dashboard
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => navigate('/stock/alerts')}
          >
            🚨 Alertes
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/stock/inventory')}
          >
            📋 Inventaire
          </button>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="stock-nav">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => handleTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Contenu */}
      <div className="stock-content">
        <Routes>
          <Route path="/" element={<StockOverview />} />
          <Route path="/overview" element={<StockOverview />} />
          <Route path="/movements" element={<StockMovements />} />
          <Route path="/alerts" element={<StockAlerts />} />
          <Route path="/inventory" element={<StockInventory />} />
          <Route path="/stats" element={<StockStats />} />
        </Routes>
      </div>
    </div>
  );
};

export default Stock;
