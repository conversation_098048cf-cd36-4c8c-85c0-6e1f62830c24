/* Styles globaux pour l'application <PERSON> */

:root {
  --ben-chaabene-primary: #2c5aa0;
  --ben-chaabene-secondary: #1e3f73;
  --ben-chaabene-success: #28a745;
  --ben-chaabene-warning: #ffc107;
  --ben-chaabene-danger: #dc3545;
  --ben-chaabene-info: #17a2b8;
  --ben-chaabene-light: #f8f9fa;
  --ben-chaabene-dark: #343a40;
  --ben-chaabene-border: #e0e0e0;
  --ben-chaabene-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Layout général */
.ben-chaabene-app {
  font-family: 'Arial', sans-serif;
  color: #333;
  line-height: 1.6;
}

/* Headers avec logo */
.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo {
  width: 60px;
  height: auto;
  border-radius: 4px;
}

.company-info h1 {
  color: var(--ben-chaabene-primary);
  margin: 0;
  font-size: 24px;
}

.company-info h2 {
  color: var(--ben-chaabene-secondary);
  margin: 5px 0 0 0;
  font-size: 16px;
  font-weight: normal;
}

.company-info p {
  margin: 2px 0;
  color: #666;
  font-size: 14px;
}

/* Cartes statistiques */
.stats-overview,
.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card,
.kpi-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: var(--ben-chaabene-shadow);
  border-left: 4px solid var(--ben-chaabene-primary);
  transition: transform 0.2s;
}

.stat-card:hover,
.kpi-card:hover {
  transform: translateY(-2px);
}

.stat-icon,
.kpi-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.stat-value,
.kpi-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--ben-chaabene-primary);
  margin-bottom: 5px;
}

.stat-label,
.kpi-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 3px;
}

.stat-sublabel,
.kpi-sublabel {
  font-size: 12px;
  color: #999;
}

/* Onglets */
.detail-tabs,
.bilan-tabs {
  margin-bottom: 30px;
}

.tabs-header {
  display: flex;
  border-bottom: 2px solid var(--ben-chaabene-border);
  margin-bottom: 20px;
}

.tab-button {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
}

.tab-button.active {
  color: var(--ben-chaabene-primary);
  border-bottom-color: var(--ben-chaabene-primary);
  font-weight: bold;
}

.tab-button:hover {
  color: var(--ben-chaabene-primary);
  background: var(--ben-chaabene-light);
}

/* Tableaux */
.balance-table,
.bilan-table,
.resultat-table,
.journal-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--ben-chaabene-shadow);
}

.balance-table th,
.bilan-table th,
.resultat-table th,
.journal-table th {
  background: var(--ben-chaabene-primary);
  color: white;
  padding: 12px 8px;
  text-align: left;
  font-weight: bold;
  font-size: 13px;
}

.balance-table td,
.bilan-table td,
.resultat-table td,
.journal-table td {
  padding: 10px 8px;
  border-bottom: 1px solid var(--ben-chaabene-border);
  font-size: 13px;
}

.balance-table tr:hover,
.bilan-table tr:hover,
.resultat-table tr:hover,
.journal-table tr:hover {
  background: var(--ben-chaabene-light);
}

.montant {
  text-align: right;
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

.montant.positive {
  color: var(--ben-chaabene-success);
}

.montant.negative {
  color: var(--ben-chaabene-danger);
}

.totals-row {
  background: var(--ben-chaabene-light);
  font-weight: bold;
}

.totals-row td {
  border-top: 2px solid var(--ben-chaabene-primary);
}

/* Types de comptes */
.type-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.type-badge.actif {
  background: #e3f2fd;
  color: #1976d2;
}

.type-badge.passif {
  background: #f3e5f5;
  color: #7b1fa2;
}

.type-badge.charge {
  background: #ffebee;
  color: #d32f2f;
}

.type-badge.produit {
  background: #e8f5e8;
  color: #388e3c;
}

/* Variations */
.variation.positive {
  color: var(--ben-chaabene-success);
}

.variation.negative {
  color: var(--ben-chaabene-danger);
}

.variation.neutral {
  color: #666;
}

/* Équilibre */
.equilibre-check {
  text-align: center;
  margin: 20px 0;
}

.equilibre-status {
  padding: 15px;
  border-radius: 8px;
  font-weight: bold;
  font-size: 16px;
}

.equilibre-status.equilibre {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.equilibre-status.desequilibre {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Résultat */
.resultat-final {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
  border-radius: 8px;
}

.resultat-final.benefice {
  background: #d4edda;
  color: #155724;
  border: 2px solid #28a745;
}

.resultat-final.perte {
  background: #f8d7da;
  color: #721c24;
  border: 2px solid #dc3545;
}

/* Actions */
.balance-actions,
.bilan-actions,
.journal-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--ben-chaabene-border);
}

/* Filtres */
.balance-controls,
.bilan-controls,
.journal-filters {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: var(--ben-chaabene-light);
  border-radius: 8px;
}

.period-selector,
.filter-selector,
.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.period-selector label,
.filter-selector label,
.filter-group label {
  font-size: 12px;
  font-weight: bold;
  color: #666;
}

.period-selector select,
.filter-selector select,
.filter-group select,
.filter-group input {
  padding: 8px;
  border: 1px solid var(--ben-chaabene-border);
  border-radius: 4px;
  font-size: 13px;
}

/* Résumé journal */
.journal-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.summary-card {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: var(--ben-chaabene-shadow);
  display: flex;
  align-items: center;
  gap: 15px;
}

.summary-icon {
  font-size: 20px;
}

.summary-content {
  flex: 1;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--ben-chaabene-primary);
}

.summary-label {
  font-size: 12px;
  color: #666;
}

/* Responsive pour tablettes */
@media (max-width: 1024px) {
  .stats-overview,
  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .balance-controls,
  .bilan-controls,
  .journal-filters {
    flex-wrap: wrap;
  }
}

/* Responsive pour mobiles */
@media (max-width: 768px) {
  .stats-overview,
  .kpi-grid,
  .journal-summary {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .balance-actions,
  .bilan-actions,
  .journal-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .balance-table,
  .bilan-table,
  .resultat-table,
  .journal-table {
    font-size: 11px;
  }
  
  .balance-table th,
  .bilan-table th,
  .resultat-table th,
  .journal-table th {
    padding: 8px 4px;
  }
  
  .balance-table td,
  .bilan-table td,
  .resultat-table td,
  .journal-table td {
    padding: 6px 4px;
  }
}
