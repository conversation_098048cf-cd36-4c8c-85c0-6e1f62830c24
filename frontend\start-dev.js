#!/usr/bin/env node

/**
 * Script de démarrage pour le serveur de développement React
 * Contourne les problèmes potentiels avec react-scripts
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Démarrage du serveur de développement React...');
console.log('📁 Répertoire:', __dirname);

// Configuration de l'environnement
process.env.GENERATE_SOURCEMAP = 'false';
process.env.BROWSER = 'none';
process.env.PORT = '3000';

// Lancement de react-scripts
const reactScripts = spawn('npx', ['react-scripts', 'start'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    FORCE_COLOR: '1'
  }
});

reactScripts.on('error', (error) => {
  console.error('❌ Erreur lors du démarrage:', error);
  process.exit(1);
});

reactScripts.on('close', (code) => {
  console.log(`🔚 Serveur fermé avec le code: ${code}`);
  process.exit(code);
});

// Gestion des signaux
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  reactScripts.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur...');
  reactScripts.kill('SIGTERM');
});

console.log('✅ Script de démarrage initialisé');
console.log('🌐 L\'application sera disponible sur http://localhost:3000');
console.log('⏳ Veuillez patienter pendant la compilation...');
