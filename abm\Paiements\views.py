from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta

from .models import <PERSON><PERSON><PERSON>, CompteBancaire
from .serializers import PaiementSerializer, CompteBancaireSerializer
from Authentication.permissions import HasModulePermission

class PaiementViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des paiements"""

    queryset = Paiement.objects.all()
    serializer_class = PaiementSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'paiements'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'type_paiement', 'mode_paiement', 'rapproche']
    search_fields = ['reference', 'description', 'client__nom', 'fournisseur__nom']
    ordering_fields = ['date_paiement', 'montant', 'created_at']
    ordering = ['-date_paiement']

    def perform_create(self, serializer):
        # Générer une référence automatique
        reference = self.generer_reference_paiement()
        serializer.save(
            reference=reference,
            created_by=self.request.user
        )

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    def generer_reference_paiement(self):
        """Génère une référence de paiement unique"""
        today = timezone.now()
        prefix = f"PAY-{today.year}-{today.month:02d}-"

        last_paiement = Paiement.objects.filter(
            reference__startswith=prefix
        ).order_by('-reference').first()

        if last_paiement:
            last_number = int(last_paiement.reference.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:04d}"

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Statistiques des paiements"""

        # Paramètres de période
        date_debut = request.query_params.get('date_debut')
        date_fin = request.query_params.get('date_fin')

        if not date_debut or not date_fin:
            # Par défaut : mois en cours
            now = timezone.now()
            date_debut = now.replace(day=1).date()
            date_fin = now.date()
        else:
            date_debut = datetime.strptime(date_debut, '%Y-%m-%d').date()
            date_fin = datetime.strptime(date_fin, '%Y-%m-%d').date()

        paiements_periode = Paiement.objects.filter(
            date_paiement__gte=date_debut,
            date_paiement__lte=date_fin
        )

        # Encaissements
        encaissements = paiements_periode.filter(
            type_paiement='ENCAISSEMENT',
            statut='VALIDE'
        ).aggregate(total=Sum('montant'))['total'] or 0

        # Décaissements
        decaissements = paiements_periode.filter(
            type_paiement='DECAISSEMENT',
            statut='VALIDE'
        ).aggregate(total=Sum('montant'))['total'] or 0

        # Répartition par mode
        modes = paiements_periode.filter(statut='VALIDE').values('mode_paiement').annotate(
            count=Count('id'),
            montant_total=Sum('montant')
        ).order_by('-montant_total')

        # Paiements en attente
        en_attente = Paiement.objects.filter(statut='EN_ATTENTE').aggregate(
            count=Count('id'),
            montant=Sum('montant')
        )

        data = {
            'periode': {
                'debut': date_debut.isoformat(),
                'fin': date_fin.isoformat()
            },
            'encaissements': float(encaissements),
            'decaissements': float(decaissements),
            'solde_net': float(encaissements - decaissements),
            'modes_paiement': list(modes),
            'en_attente': {
                'count': en_attente['count'] or 0,
                'montant': float(en_attente['montant'] or 0)
            }
        }

        return Response(data)

    @action(detail=True, methods=['post'])
    def valider(self, request, pk=None):
        """Valide un paiement"""
        paiement = self.get_object()

        if not paiement.peut_valider:
            return Response(
                {'error': 'Ce paiement ne peut pas être validé'},
                status=status.HTTP_400_BAD_REQUEST
            )

        paiement.statut = 'VALIDE'
        paiement.updated_by = request.user
        paiement.save()

        return Response({'message': 'Paiement validé avec succès'})

    @action(detail=False, methods=['post'])
    def rapprochement(self, request):
        """Effectue un rapprochement bancaire"""
        paiement_ids = request.data.get('paiement_ids', [])

        if not paiement_ids:
            return Response(
                {'error': 'Aucun paiement sélectionné'},
                status=status.HTTP_400_BAD_REQUEST
            )

        paiements = Paiement.objects.filter(id__in=paiement_ids, statut='VALIDE')
        nb_rapproches = paiements.update(
            rapproche=True,
            date_rapprochement=timezone.now()
        )

        return Response({
            'message': f'{nb_rapproches} paiements rapprochés',
            'nb_rapproches': nb_rapproches
        })


class CompteBancaireViewSet(viewsets.ModelViewSet):
    """ViewSet pour les comptes bancaires"""

    queryset = CompteBancaire.objects.all()
    serializer_class = CompteBancaireSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'paiements'

    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['nom', 'banque', 'iban']
    ordering = ['-principal', 'nom']

    @action(detail=True, methods=['post'])
    def calculer_solde(self, request, pk=None):
        """Recalcule le solde d'un compte"""
        compte = self.get_object()
        nouveau_solde = compte.calculer_solde()

        return Response({
            'message': 'Solde recalculé',
            'nouveau_solde': float(nouveau_solde)
        })
