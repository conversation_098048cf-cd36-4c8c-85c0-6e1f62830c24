"""
Service de cache intelligent pour optimiser les performances
"""
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key
from django.utils import timezone
from datetime import timedelta
from functools import wraps
import hashlib
import json
import logging

logger = logging.getLogger(__name__)


class SmartCacheService:
    """Service de cache intelligent avec invalidation automatique"""
    
    # Durées de cache par défaut (en secondes)
    CACHE_DURATIONS = {
        'dashboard_metrics': 300,      # 5 minutes
        'product_analytics': 600,      # 10 minutes
        'client_insights': 900,        # 15 minutes
        'category_performance': 1800,  # 30 minutes
        'stock_alerts': 180,           # 3 minutes
        'ai_insights': 3600,           # 1 heure
        'system_stats': 120,           # 2 minutes
    }
    
    # Tags pour l'invalidation groupée
    CACHE_TAGS = {
        'products': ['dashboard_metrics', 'product_analytics', 'category_performance', 'stock_alerts'],
        'clients': ['dashboard_metrics', 'client_insights'],
        'factures': ['dashboard_metrics', 'product_analytics'],
        'stock': ['stock_alerts', 'product_analytics'],
        'categories': ['category_performance'],
    }
    
    @classmethod
    def get_cache_key(cls, key_type, *args, **kwargs):
        """Génère une clé de cache unique"""
        # Créer une chaîne unique basée sur les paramètres
        params_str = json.dumps({
            'args': args,
            'kwargs': sorted(kwargs.items())
        }, sort_keys=True, default=str)
        
        # Hasher pour éviter les clés trop longues
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        
        return f"smart_cache:{key_type}:{params_hash}"
    
    @classmethod
    def get(cls, key_type, *args, **kwargs):
        """Récupère une valeur du cache"""
        cache_key = cls.get_cache_key(key_type, *args, **kwargs)
        return cache.get(cache_key)
    
    @classmethod
    def set(cls, key_type, value, *args, timeout=None, **kwargs):
        """Stocke une valeur dans le cache"""
        cache_key = cls.get_cache_key(key_type, *args, **kwargs)
        
        if timeout is None:
            timeout = cls.CACHE_DURATIONS.get(key_type, 300)
        
        # Ajouter des métadonnées
        cache_data = {
            'value': value,
            'cached_at': timezone.now().isoformat(),
            'key_type': key_type,
            'expires_at': (timezone.now() + timedelta(seconds=timeout)).isoformat()
        }
        
        cache.set(cache_key, cache_data, timeout)
        logger.debug(f"Cache set: {cache_key} (expires in {timeout}s)")
        
        return cache_key
    
    @classmethod
    def delete(cls, key_type, *args, **kwargs):
        """Supprime une valeur du cache"""
        cache_key = cls.get_cache_key(key_type, *args, **kwargs)
        cache.delete(cache_key)
        logger.debug(f"Cache deleted: {cache_key}")
    
    @classmethod
    def invalidate_by_tag(cls, tag):
        """Invalide tous les caches associés à un tag"""
        if tag in cls.CACHE_TAGS:
            for key_type in cls.CACHE_TAGS[tag]:
                # Pour une invalidation complète, on utilise un pattern
                cls._invalidate_pattern(f"smart_cache:{key_type}:*")
            
            logger.info(f"Cache invalidated for tag: {tag}")
    
    @classmethod
    def _invalidate_pattern(cls, pattern):
        """Invalide tous les caches correspondant à un pattern"""
        # Note: Cette implémentation dépend du backend de cache
        # Pour Redis, on pourrait utiliser SCAN + DEL
        # Pour Memcached, il faudrait maintenir une liste des clés
        # Ici, on utilise une approche simple avec un timestamp de version
        version_key = f"cache_version:{pattern.replace('*', 'all')}"
        cache.set(version_key, timezone.now().timestamp(), timeout=86400)  # 24h
    
    @classmethod
    def get_cache_stats(cls):
        """Récupère les statistiques du cache"""
        stats = {
            'hit_count': 0,
            'miss_count': 0,
            'total_keys': 0,
            'memory_usage': 0,
            'hit_rate': 0.0
        }
        
        # Ces statistiques dépendent du backend de cache
        # Pour l'exemple, on retourne des valeurs simulées
        try:
            # Simuler des statistiques
            stats.update({
                'hit_count': 1250,
                'miss_count': 180,
                'total_keys': 45,
                'memory_usage': 2.5,  # MB
                'hit_rate': 87.4
            })
        except Exception as e:
            logger.warning(f"Impossible de récupérer les stats du cache: {e}")
        
        return stats
    
    @classmethod
    def warm_up_cache(cls):
        """Préchauffe le cache avec les données essentielles"""
        from Core.analytics import AnalyticsService
        
        try:
            logger.info("Démarrage du préchauffage du cache...")
            
            # Préchauffer les métriques du tableau de bord
            metrics = AnalyticsService.get_dashboard_metrics()
            cls.set('dashboard_metrics', metrics)
            
            # Préchauffer les performances par catégorie
            category_perf = AnalyticsService.get_category_performance()
            cls.set('category_performance', category_perf)
            
            # Préchauffer les alertes de stock
            stock_alerts = AnalyticsService.get_stock_alerts()
            cls.set('stock_alerts', stock_alerts)
            
            # Préchauffer les insights clients
            client_insights = AnalyticsService.get_client_insights()
            cls.set('client_insights', client_insights)
            
            logger.info("Préchauffage du cache terminé avec succès")
            
        except Exception as e:
            logger.error(f"Erreur lors du préchauffage du cache: {e}")


def cached_method(key_type, timeout=None, tags=None):
    """Décorateur pour mettre en cache le résultat d'une méthode"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Générer la clé de cache
            cache_key = SmartCacheService.get_cache_key(key_type, func.__name__, *args, **kwargs)
            
            # Essayer de récupérer depuis le cache
            cached_data = cache.get(cache_key)
            if cached_data:
                logger.debug(f"Cache hit: {cache_key}")
                return cached_data['value']
            
            # Exécuter la fonction et mettre en cache
            logger.debug(f"Cache miss: {cache_key}")
            result = func(*args, **kwargs)
            
            SmartCacheService.set(key_type, result, func.__name__, *args, timeout=timeout, **kwargs)
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache_on_save(tags):
    """Décorateur pour invalider le cache lors de la sauvegarde d'un modèle"""
    def decorator(save_method):
        @wraps(save_method)
        def wrapper(self, *args, **kwargs):
            result = save_method(self, *args, **kwargs)
            
            # Invalider les caches associés
            if isinstance(tags, str):
                SmartCacheService.invalidate_by_tag(tags)
            elif isinstance(tags, (list, tuple)):
                for tag in tags:
                    SmartCacheService.invalidate_by_tag(tag)
            
            return result
        
        return wrapper
    return decorator


class CacheMiddleware:
    """Middleware pour gérer le cache automatiquement"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Ajouter des headers de cache pour les API
        response = self.get_response(request)
        
        if request.path.startswith('/core/api/'):
            # Ajouter des headers de cache appropriés
            if 'dashboard' in request.path:
                response['Cache-Control'] = 'public, max-age=300'  # 5 minutes
            elif 'insights' in request.path:
                response['Cache-Control'] = 'public, max-age=900'  # 15 minutes
            else:
                response['Cache-Control'] = 'public, max-age=180'  # 3 minutes
            
            response['Vary'] = 'Accept, Authorization'
        
        return response


# Utilitaires pour la gestion du cache
def clear_all_cache():
    """Vide tout le cache"""
    cache.clear()
    logger.info("Tout le cache a été vidé")


def get_cache_info():
    """Récupère les informations détaillées du cache"""
    stats = SmartCacheService.get_cache_stats()
    
    return {
        'stats': stats,
        'backend': cache.__class__.__name__,
        'version': getattr(cache, 'version', 'unknown'),
        'key_prefix': getattr(cache, 'key_prefix', ''),
        'durations': SmartCacheService.CACHE_DURATIONS,
        'tags': SmartCacheService.CACHE_TAGS
    }


# Commandes de gestion du cache
class CacheCommands:
    """Commandes utilitaires pour la gestion du cache"""
    
    @staticmethod
    def warm_up():
        """Préchauffe le cache"""
        SmartCacheService.warm_up_cache()
    
    @staticmethod
    def clear_by_pattern(pattern):
        """Vide le cache selon un pattern"""
        SmartCacheService._invalidate_pattern(pattern)
    
    @staticmethod
    def invalidate_tag(tag):
        """Invalide un tag spécifique"""
        SmartCacheService.invalidate_by_tag(tag)
    
    @staticmethod
    def get_stats():
        """Récupère les statistiques"""
        return get_cache_info()


# Signaux pour l'invalidation automatique du cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver


@receiver(post_save)
def invalidate_cache_on_model_save(sender, instance, **kwargs):
    """Invalide le cache automatiquement lors de la sauvegarde d'un modèle"""
    model_name = sender.__name__.lower()
    
    # Mapping des modèles vers les tags de cache
    model_to_tags = {
        'produit': ['products'],
        'client': ['clients'],
        'facture': ['factures'],
        'stock': ['stock'],
        'categorie': ['categories'],
        'produitecommerce': ['products'],
    }
    
    if model_name in model_to_tags:
        for tag in model_to_tags[model_name]:
            SmartCacheService.invalidate_by_tag(tag)
        
        logger.debug(f"Cache invalidé pour le modèle {model_name}")


@receiver(post_delete)
def invalidate_cache_on_model_delete(sender, instance, **kwargs):
    """Invalide le cache automatiquement lors de la suppression d'un modèle"""
    invalidate_cache_on_model_save(sender, instance, **kwargs)
