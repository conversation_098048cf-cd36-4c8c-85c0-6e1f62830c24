import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { InvoiceService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import "../Invoices/Invoice.css";

interface Invoice {
  id: number;
  numero: string;
  client_nom: string;
  montant_total: number;
  montant_paye: number;
  solde_restant: number;
}

const PaymentForm: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const { invoiceId } = useParams<{ invoiceId: string }>();

  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(false);

  const [payment, setPayment] = useState({
    montant: "",
    methode_paiement: "VIREMENT",
    date_paiement: new Date().toISOString().split("T")[0],
    reference: "",
    notes: "",
  });

  useEffect(() => {
    if (invoiceId) {
      loadInvoice();
    }
  }, [invoiceId]);

  const loadInvoice = async () => {
    if (!invoiceId) return;

    try {
      // Simulation de données de facture pour paiement
      const mockInvoice: Invoice = {
        id: parseInt(invoiceId),
        numero: "FAC-2025-5004",
        client_nom: "CLIENT FOND FUTURISTE",
        montant_total: 989.664,
        montant_paye: 500.0,
        solde_restant: 489.664,
      };

      setInvoice(mockInvoice);
      setPayment((prev) => ({
        ...prev,
        montant: mockInvoice.solde_restant.toString(),
      }));
    } catch (error) {
      notify.error("Erreur", "Erreur lors du chargement de la facture");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!invoice || !payment.montant) {
      notify.error("Erreur", "Veuillez remplir tous les champs obligatoires");
      return;
    }

    const montant = parseFloat(payment.montant);
    if (montant <= 0 || montant > invoice.solde_restant) {
      notify.error(
        "Erreur",
        "Le montant doit être positif et ne pas dépasser le solde restant"
      );
      return;
    }

    setLoading(true);
    try {
      const result = await InvoiceService.addPayment(invoice.id, {
        ...payment,
        montant,
      });

      if (result) {
        notify.success("Succès", "Paiement enregistré avec succès");
        navigate("/dashboard/paiements");
      }
    } catch (error) {
      notify.error("Erreur", "Erreur lors de l'enregistrement du paiement");
    } finally {
      setLoading(false);
    }
  };

  if (!invoice) {
    return (
      <div className="page-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">Chargement...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Nouveau Paiement</h1>
        <button
          type="button"
          onClick={() => navigate("/dashboard/paiements")}
          className="btn-secondary">
          ← Retour
        </button>
      </div>

      <div className="invoice-info-card">
        <h3>Facture {invoice.numero}</h3>
        <div className="invoice-details">
          <div>
            <strong>Client:</strong> {invoice.client_nom}
          </div>
          <div>
            <strong>Montant total:</strong> {invoice.montant_total.toFixed(2)} €
          </div>
          <div>
            <strong>Déjà payé:</strong> {invoice.montant_paye.toFixed(2)} €
          </div>
          <div>
            <strong>Solde restant:</strong>{" "}
            <span className="amount-due">
              {invoice.solde_restant.toFixed(2)} €
            </span>
          </div>
        </div>
      </div>

      <form
        onSubmit={handleSubmit}
        className="payment-form">
        <div className="form-section">
          <h3>Informations du paiement</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Montant *</label>
              <input
                type="number"
                step="0.01"
                min="0.01"
                max={invoice.solde_restant}
                value={payment.montant}
                onChange={(e) =>
                  setPayment({ ...payment, montant: e.target.value })
                }
                required
              />
              <small>Maximum: {invoice.solde_restant.toFixed(2)} €</small>
            </div>

            <div className="form-group">
              <label>Méthode de paiement *</label>
              <select
                value={payment.methode_paiement}
                onChange={(e) =>
                  setPayment({ ...payment, methode_paiement: e.target.value })
                }
                required>
                <option value="VIREMENT">Virement bancaire</option>
                <option value="CHEQUE">Chèque</option>
                <option value="ESPECES">Espèces</option>
                <option value="CARTE">Carte bancaire</option>
                <option value="MOBILE_MONEY">Mobile Money</option>
              </select>
            </div>

            <div className="form-group">
              <label>Date de paiement *</label>
              <input
                type="date"
                value={payment.date_paiement}
                onChange={(e) =>
                  setPayment({ ...payment, date_paiement: e.target.value })
                }
                required
              />
            </div>

            <div className="form-group">
              <label>Référence</label>
              <input
                type="text"
                value={payment.reference}
                onChange={(e) =>
                  setPayment({ ...payment, reference: e.target.value })
                }
                placeholder="Numéro de chèque, référence virement..."
              />
            </div>
          </div>

          <div className="form-group">
            <label>Notes</label>
            <textarea
              value={payment.notes}
              onChange={(e) =>
                setPayment({ ...payment, notes: e.target.value })
              }
              rows={3}
              placeholder="Notes additionnelles..."
            />
          </div>
        </div>

        <div className="form-actions">
          <button
            type="button"
            onClick={() => navigate("/dashboard/paiements")}
            className="btn-secondary">
            Annuler
          </button>
          <button
            type="submit"
            disabled={loading || !payment.montant}
            className="btn-primary">
            {loading ? "Enregistrement..." : "Enregistrer le paiement"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PaymentForm;
