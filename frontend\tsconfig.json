{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "noImplicitAny": false, "noUnusedLocals": false, "noUnusedParameters": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src"], "exclude": ["src/components/Facturation/**/*", "src/components/Factures/**/*", "src/components/Orders/**/*", "src/components/Payments/**/*", "src/components/Stock/**/*", "src/components/Comptable/**/*", "src/components/Ecommerce/**/*", "src/services/authService.ts", "src/services/coreService.ts", "src/services/dashboardService.ts", "src/services/ecommerceService.ts", "src/services/mediaService.ts"]}