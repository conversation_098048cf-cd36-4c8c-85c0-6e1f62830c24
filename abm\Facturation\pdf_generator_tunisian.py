from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import cm, mm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.lib.utils import ImageReader
from io import BytesIO
from datetime import datetime
import os
import math
from django.conf import settings

class TunisianFacturePDFGenerator:
    """Générateur PDF authentique style tunisien - reproduction exacte de la facture Ben Chaabene"""
    
    def __init__(self):
        self.width, self.height = A4
        self.styles = getSampleStyleSheet()

    def number_to_words_french(self, number):
        """Convertit un nombre en lettres en français"""
        if number == 0:
            return "zéro dinars"

        # Conversion simple pour les nombres jusqu'à 999
        ones = ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf']
        tens = ['', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix']
        teens = ['dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf']

        def convert_hundreds(n):
            result = ''
            if n >= 100:
                if n // 100 == 1:
                    result += 'cent '
                else:
                    result += ones[n // 100] + ' cent '
                n %= 100

            if n >= 20:
                result += tens[n // 10]
                if n % 10:
                    result += '-' + ones[n % 10]
            elif n >= 10:
                result += teens[n - 10]
            elif n > 0:
                result += ones[n]

            return result.strip()

        # Séparer dinars et millimes
        dinars = int(number)
        millimes = int((number - dinars) * 1000)

        result = convert_hundreds(dinars) + ' dinars'
        if millimes > 0:
            result += ' et ' + convert_hundreds(millimes) + ' millimes'

        return result.capitalize()
        
    def generate_facture_pdf(self, facture_data):
        """Génère le PDF de la facture style tunisien authentique avec fond futuriste"""
        buffer = BytesIO()

        # Créer le canvas directement pour un contrôle total
        c = canvas.Canvas(buffer, pagesize=A4)

        # Dessiner le fond futuriste en arrière-plan
        self.draw_futuristic_background(c)

        # Dessiner la facture basique
        self.draw_header(c, facture_data)
        self.draw_client_info(c, facture_data)
        self.draw_products_table(c, facture_data)
        self.draw_totals(c, facture_data)
        # Footer maintenant géré dans draw_totals pour éviter la duplication
        
        c.save()
        buffer.seek(0)
        return buffer

    def draw_futuristic_background(self, c):
        """Dessine un fond futuriste avec la phrase 'Ste Ben Chaabène de Commerce' en arrière-plan"""
        # Sauvegarder l'état graphique
        c.saveState()

        # Fond très subtil avec dégradé
        # Créer un effet de dégradé subtil avec des rectangles transparents
        for i in range(20):
            alpha = 0.02 - (i * 0.001)  # Transparence très faible
            gray_value = 0.98 - (i * 0.002)  # Gris très clair
            c.setFillColor(colors.Color(gray_value, gray_value, gray_value + 0.01, alpha=alpha))
            c.rect(0, i * (self.height / 20), self.width, self.height / 20, fill=1, stroke=0)

        # Texte en filigrane "Ste Ben Chaabène de Commerce"
        watermark_text = "STE BEN CHAABENE DE COMMERCE"

        # Configuration du texte en filigrane
        c.setFillColor(colors.Color(0.9, 0.9, 0.92, alpha=0.08))  # Gris très clair, très transparent
        c.setFont('Helvetica-Bold', 48)  # Police grande pour effet filigrane

        # Calculer la largeur du texte pour centrage
        text_width = c.stringWidth(watermark_text, 'Helvetica-Bold', 48)

        # Position centrale avec rotation diagonale
        center_x = self.width / 2
        center_y = self.height / 2

        # Rotation de 45 degrés pour effet diagonal élégant
        c.saveState()
        c.translate(center_x, center_y)
        c.rotate(45)
        c.drawCentredString(0, 0, watermark_text)
        c.restoreState()

        # Ajouter des copies supplémentaires pour effet de répétition subtile
        positions = [
            (center_x - 8*cm, center_y + 6*cm),
            (center_x + 8*cm, center_y - 6*cm),
            (center_x - 6*cm, center_y - 8*cm),
            (center_x + 6*cm, center_y + 8*cm)
        ]

        # Texte encore plus transparent pour les répétitions
        c.setFillColor(colors.Color(0.92, 0.92, 0.94, alpha=0.04))
        c.setFont('Helvetica', 36)  # Police plus petite pour les répétitions

        for pos_x, pos_y in positions:
            c.saveState()
            c.translate(pos_x, pos_y)
            c.rotate(45)
            c.drawCentredString(0, 0, watermark_text)
            c.restoreState()

        # Lignes décoratives futuristes très subtiles
        c.setStrokeColor(colors.Color(0.85, 0.87, 0.9, alpha=0.06))
        c.setLineWidth(0.5)

        # Lignes diagonales décoratives
        for i in range(0, int(self.width), int(4*cm)):
            c.line(i, 0, i + 10*cm, self.height)
            c.line(i + 2*cm, 0, i + 12*cm, self.height)

        # Lignes horizontales très fines
        for i in range(0, int(self.height), int(3*cm)):
            c.setStrokeColor(colors.Color(0.88, 0.9, 0.92, alpha=0.03))
            c.line(0, i, self.width, i)

        # Motifs géométriques futuristes aux coins
        self.draw_corner_patterns(c)

        # Restaurer l'état graphique
        c.restoreState()

    def draw_corner_patterns(self, c):
        """Dessine des motifs géométriques futuristes aux coins"""
        # Couleur très transparente pour les motifs
        pattern_color = colors.Color(0.8, 0.85, 0.9, alpha=0.05)
        c.setStrokeColor(pattern_color)
        c.setFillColor(pattern_color)
        c.setLineWidth(0.3)

        # Motif coin supérieur gauche
        corner_size = 3*cm

        # Coin supérieur gauche - Lignes convergentes
        for i in range(5):
            offset = i * 0.5*cm
            c.line(0, self.height - offset, corner_size - offset, self.height)
            c.line(offset, self.height, corner_size, self.height - corner_size + offset)

        # Coin supérieur droit - Arcs futuristes
        corner_x = self.width - corner_size
        corner_y = self.height - corner_size
        for i in range(3):
            radius = (i + 1) * 0.8*cm
            c.circle(self.width, self.height, radius, fill=0, stroke=1)

        # Coin inférieur gauche - Grille
        grid_size = 0.4*cm
        grid_count = int(corner_size / grid_size)
        for i in range(grid_count):
            for j in range(grid_count):
                x = i * grid_size
                y = j * grid_size
                if (i + j) % 2 == 0:  # Motif en damier
                    c.rect(x, y, grid_size/2, grid_size/2, fill=1, stroke=0)

        # Coin inférieur droit - Spirale
        spiral_center_x = self.width - corner_size/2
        spiral_center_y = corner_size/2
        import math
        for angle in range(0, 720, 15):  # Spirale sur 2 tours
            rad = math.radians(angle)
            radius = angle * 0.01
            x = spiral_center_x + radius * math.cos(rad)
            y = spiral_center_y + radius * math.sin(rad)
            c.circle(x, y, 1, fill=1, stroke=0)

    def draw_border(self, c):
        """Dessine l'encadrement de la facture comme dans l'image de référence"""
        c.saveState()

        # Couleur de l'encadrement - gris foncé professionnel
        c.setStrokeColor(colors.HexColor('#2c3e50'))
        c.setLineWidth(2)

        # Marges pour l'encadrement
        margin = 0.5*cm

        # Dessiner le rectangle d'encadrement
        c.rect(margin, margin,
               self.width - 2*margin,
               self.height - 2*margin,
               fill=0, stroke=1)

        c.restoreState()

    def draw_watermark(self, c):
        """Dessine un filigrane très discret comme dans l'image de référence"""
        # Sauvegarder l'état actuel
        c.saveState()

        # Position centrale
        center_x = self.width / 2
        center_y = self.height / 2

        # Couleur ultra-claire pour le filigrane (comme dans l'image de référence)
        c.setFillColorRGB(0.98, 0.98, 0.98)  # Gris ultra-clair, presque invisible

        # Texte principal du filigrane - très discret
        c.saveState()
        c.translate(center_x, center_y)
        c.rotate(45)  # Rotation de 45 degrés

        # "Ste Ben Chaabène de Commerce" - texte très fin comme dans l'image
        c.setFont('Helvetica', 18)  # Police encore plus petite
        c.drawCentredString(0, 0, "STE BEN CHAABENE DE COMMERCE")

        c.restoreState()

        # Restaurer l'état
        c.restoreState()



    def draw_header_gradient(self, c):
        """Dessine un en-tête simple et professionnel comme la référence"""
        # Fond blanc pour l'en-tête (comme dans l'image de référence)
        c.setFillColor(colors.white)
        c.rect(0, self.height - 4*cm, self.width, 4*cm, fill=1, stroke=0)

        # Bordure fine en bas de l'en-tête
        c.setStrokeColor(colors.HexColor('#0d9488'))  # Teal moderne
        c.setLineWidth(2)
        c.line(0, self.height - 4*cm, self.width, self.height - 4*cm)

    def draw_logo(self, c):
        """Dessine le logo en haut à gauche - style discret comme la référence"""
        try:
            # Chemin vers le logo
            logo_path = os.path.join(settings.STATIC_ROOT or 'static', 'images', 'logo_ben_chaabene_moderne.png')

            # Si le fichier n'existe pas, créer un logo vectoriel moderne
            if not os.path.exists(logo_path):
                self.draw_vector_logo(c)
            else:
                # Dessiner le logo depuis le fichier - plus petit et discret
                c.drawImage(logo_path, 0.5*cm, self.height - 3.2*cm,
                           width=2.5*cm, height=2*cm, mask='auto')
        except Exception:
            # En cas d'erreur, dessiner un logo vectoriel
            self.draw_vector_logo(c)

    def draw_vector_logo(self, c):
        """Dessine le logo vectoriel exact avec les ailes stylisées de l'image"""
        c.saveState()

        # Position du logo - en haut à gauche
        logo_x = 0.8*cm
        logo_y = self.height - 2.8*cm

        # Couleur teal pour les ailes (comme dans l'image)
        teal = colors.HexColor('#1B7A7A')  # Couleur teal foncée de l'image
        text_color = colors.HexColor('#2C3E50')  # Gris foncé pour le texte

        # Dessiner les ailes stylisées (reproduction exacte de l'image)
        c.setStrokeColor(teal)
        c.setFillColor(teal)
        c.setLineWidth(0.5)

        # Aile gauche - forme organique avec courbes
        left_wing = c.beginPath()
        left_wing.moveTo(logo_x + 0.6*cm, logo_y)  # Point central
        # Courbe supérieure de l'aile gauche
        left_wing.curveTo(logo_x + 0.2*cm, logo_y + 0.4*cm, logo_x - 0.1*cm, logo_y + 0.6*cm, logo_x + 0.1*cm, logo_y + 0.8*cm)
        left_wing.curveTo(logo_x + 0.3*cm, logo_y + 0.7*cm, logo_x + 0.5*cm, logo_y + 0.4*cm, logo_x + 0.6*cm, logo_y)
        c.drawPath(left_wing, fill=1, stroke=0)

        # Deuxième plume de l'aile gauche
        left_wing2 = c.beginPath()
        left_wing2.moveTo(logo_x + 0.6*cm, logo_y)
        left_wing2.curveTo(logo_x + 0.3*cm, logo_y + 0.2*cm, logo_x + 0.1*cm, logo_y + 0.4*cm, logo_x + 0.2*cm, logo_y + 0.6*cm)
        left_wing2.curveTo(logo_x + 0.4*cm, logo_y + 0.5*cm, logo_x + 0.5*cm, logo_y + 0.2*cm, logo_x + 0.6*cm, logo_y)
        c.drawPath(left_wing2, fill=1, stroke=0)

        # Aile droite - symétrique
        right_wing = c.beginPath()
        right_wing.moveTo(logo_x + 0.6*cm, logo_y)  # Point central
        # Courbe supérieure de l'aile droite
        right_wing.curveTo(logo_x + 1.0*cm, logo_y + 0.4*cm, logo_x + 1.3*cm, logo_y + 0.6*cm, logo_x + 1.1*cm, logo_y + 0.8*cm)
        right_wing.curveTo(logo_x + 0.9*cm, logo_y + 0.7*cm, logo_x + 0.7*cm, logo_y + 0.4*cm, logo_x + 0.6*cm, logo_y)
        c.drawPath(right_wing, fill=1, stroke=0)

        # Deuxième plume de l'aile droite
        right_wing2 = c.beginPath()
        right_wing2.moveTo(logo_x + 0.6*cm, logo_y)
        right_wing2.curveTo(logo_x + 0.9*cm, logo_y + 0.2*cm, logo_x + 1.1*cm, logo_y + 0.4*cm, logo_x + 1.0*cm, logo_y + 0.6*cm)
        right_wing2.curveTo(logo_x + 0.8*cm, logo_y + 0.5*cm, logo_x + 0.7*cm, logo_y + 0.2*cm, logo_x + 0.6*cm, logo_y)
        c.drawPath(right_wing2, fill=1, stroke=0)

        # Texte du logo - reproduction exacte
        c.setFillColor(text_color)

        # "Société" - première ligne
        c.setFont('Helvetica-Bold', 11)
        c.drawString(logo_x + 1.8*cm, logo_y + 0.5*cm, "Société")

        # "Ben Chaabène" - deuxième ligne
        c.setFont('Helvetica-Bold', 11)
        c.drawString(logo_x + 1.8*cm, logo_y + 0.2*cm, "Ben Chaabène")

        # "de Commerce" - troisième ligne
        c.setFont('Helvetica-Bold', 11)
        c.drawString(logo_x + 1.8*cm, logo_y - 0.1*cm, "de Commerce")

        c.restoreState()

    def draw_header(self, c, facture_data):
        """Dessine l'en-tête exactement comme dans l'image"""
        # Pas de bordure extérieure - facture sans cadre principal

        # Logo et nom de l'entreprise (côté gauche)
        self.draw_company_logo(c, 1*cm, self.height - 3*cm)

        # Informations entreprise (côté droit dans un cadre)
        info_x = self.width - 6*cm
        info_y = self.height - 1*cm
        info_width = 5.5*cm
        info_height = 2.5*cm

        # Cadre pour les informations
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.rect(info_x, info_y - info_height, info_width, info_height, fill=0, stroke=1)

        # Texte des informations
        c.setFont('Helvetica', 8)
        c.setFillColor(colors.black)
        c.drawString(info_x + 0.1*cm, info_y - 0.3*cm, 'Adresse : 10 Rue de la Commission')
        c.drawString(info_x + 0.1*cm, info_y - 0.6*cm, 'GSM : 98 505 297 - 96 287 853')
        c.drawString(info_x + 0.1*cm, info_y - 0.9*cm, 'Télephone : 71 327 035 ')
        c.drawString(info_x + 0.1*cm, info_y - 1.5*cm, 'Matricule.Fiscal : 1283049 W/A/M/000')

        # Section FACTURE avec bordure teal
        self.draw_invoice_section(c, facture_data)


    
    def draw_company_logo(self, c, x, y):
        """Dessine le logo moderne de l'entreprise depuis le fichier PNG"""
        try:
            # Chemin vers le logo moderne
            logo_path = os.path.join(settings.BASE_DIR, 'abm', 'static', 'images', 'logo_ben_chaabene_moderne2.png')

            if os.path.exists(logo_path):
                # Charger et dessiner le logo PNG
                logo_width = 3*cm  # Largeur du logo
                logo_height = 2*cm  # Hauteur du logo

                # Dessiner le logo
                c.drawImage(logo_path, x, y - logo_height, width=logo_width, height=logo_height, mask='auto')

            else:
                # Fallback : Logo teal en forme de V stylisé si le fichier n'existe pas
                c.setFillColor(colors.HexColor('#4a9b8e'))  # Couleur teal de l'image
                c.setStrokeColor(colors.HexColor('#4a9b8e'))
                c.setLineWidth(3)

                # Forme en V du logo
                c.line(x, y + 0.8*cm, x + 0.4*cm, y + 0.2*cm)
                c.line(x + 0.4*cm, y + 0.2*cm, x + 0.8*cm, y + 0.8*cm)

        except Exception as e:
            # En cas d'erreur, utiliser le logo de fallback
            c.setFillColor(colors.HexColor('#4a9b8e'))
            c.setStrokeColor(colors.HexColor('#4a9b8e'))
            c.setLineWidth(3)
            c.line(x, y + 0.8*cm, x + 0.4*cm, y + 0.2*cm)
            c.line(x + 0.4*cm, y + 0.2*cm, x + 0.8*cm, y + 0.8*cm)

        # Texte de l'entreprise (ajusté pour le logo PNG)
        c.setFillColor(colors.black)
        c.setFont('Helvetica-Bold', 12)
        c.drawString(x + 3.5*cm, y - 0.4*cm, 'Société')
        c.drawString(x + 3.5*cm, y - 0.8*cm, 'Ben Chaabène')
        c.drawString(x + 3.5*cm, y - 1.2*cm, 'de Commerce')

    def draw_invoice_section(self, c, facture_data):
        """Dessine la section FACTURE avec bordure teal comme dans l'image"""
        # Position de la section
        section_y = self.height - 4.5*cm
        section_width = self.width - 1*cm
        section_height = 1.2*cm

        # Bordure teal épaisse
        c.setStrokeColor(colors.HexColor('#4a9b8e'))
        c.setLineWidth(3)
        c.rect(0.5*cm, section_y - section_height, section_width, section_height, fill=0, stroke=1)

        # Onglet "Page 1/1" (côté gauche)
        tab_width = 2*cm
        tab_height = 0.6*cm
        c.setFillColor(colors.HexColor('#d0d0d0'))
        c.rect(0.7*cm, section_y - 0.3*cm, tab_width, tab_height, fill=1, stroke=1)
        c.setFillColor(colors.black)
        c.setFont('Helvetica', 8)
        c.drawCentredString(0.7*cm + tab_width/2, section_y - 0.1*cm, 'Page 1/1')

        # Titre FACTURE au centre
        c.setFont('Helvetica-Bold', 16)
        facture_num = facture_data.get('numero', '5000')
        title_text = f'FACTURE N°: {facture_num}'
        text_width = c.stringWidth(title_text, 'Helvetica-Bold', 16)
        center_x = (self.width - text_width) / 2
        c.drawString(center_x, section_y - 0.7*cm, title_text)

        # Date (côté droit)
        c.setFont('Helvetica', 10)
        date_text = f"Date : {facture_data.get('date', '02/08/2025')}"
        c.drawRightString(self.width - 1*cm, section_y - 0.7*cm, date_text)

    def draw_client_info(self, c, facture_data):
        """Dessine les informations client exactement comme dans l'image"""
        client = facture_data.get('client', {})

        # Position des cadres client
        y_pos = self.height - 6.5*cm

        # Cadre gauche - Informations client
        left_width = 8*cm
        left_height = 1.2*cm
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.rect(0.7*cm, y_pos - left_height, left_width, left_height, fill=0, stroke=1)

        # Contenu cadre gauche
        c.setFont('Helvetica', 9)
        c.setFillColor(colors.black)
        # Données dynamiques du client
        client_num = client.get('numero', str(facture_data.get('numero', '5001')))
        c.drawString(0.8*cm, y_pos - 0.3*cm, f'N° Client: {client_num}')

        # Nom du client - données dynamiques
        client_name = client.get('nom', 'Client Non Spécifié')
        if not client_name or client_name.strip() == '':
            client_name = 'Client Non Spécifié'
        c.drawString(0.8*cm, y_pos - 0.6*cm, f'Doit M. : {client_name}')

        # Adresse du client - données dynamiques
        client_address = client.get('adresse', 'Adresse Non Spécifiée')
        if not client_address or client_address.strip() == '':
            client_address = 'Adresse Non Spécifiée'
        c.drawString(0.8*cm, y_pos - 0.9*cm, f'Adresse : {client_address}')

        # Cadre droit - Matricule et téléphone
        right_x = 9*cm
        right_width = 6*cm
        c.rect(right_x, y_pos - left_height, right_width, left_height, fill=0, stroke=1)

        # Contenu cadre droit - données dynamiques
        matricule = client.get('matricule_fiscal', 'Non spécifié')
        if not matricule or matricule.strip() == '':
            matricule = 'Non spécifié'
        c.drawString(right_x + 0.1*cm, y_pos - 0.6*cm, f'Matricule Fiscal : {matricule}')

        telephone = client.get('telephone', 'Non spécifié')
        if not telephone or telephone.strip() == '':
            telephone = 'Non spécifié'
        c.drawString(right_x + 0.1*cm, y_pos - 0.9*cm, f'Téléphone : {telephone}')
    
    def draw_products_table(self, c, facture_data):
        """Dessine le tableau des produits exactement comme dans l'image"""
        # Position de départ du tableau
        start_y = self.height - 8.5*cm
        table_width = self.width - 1.4*cm

        # Définition des colonnes exactement comme dans l'image
        col_positions = {
            'code': 0.7*cm,
            'designation': 2.5*cm,
            'qte': 8.5*cm,
            'pu_ht': 9.5*cm,
            'rem': 11*cm,
            'pu_ttc': 12.5*cm,
            'mt_net_ht': 14.5*cm,
            'tva': 16.5*cm
        }

        # En-tête du tableau avec fond bleu foncé
        header_height = 0.8*cm
        c.setFillColor(colors.HexColor('#2c3e50'))  # Bleu foncé comme dans l'image
        c.rect(0.7*cm, start_y - header_height, table_width, header_height, fill=1, stroke=1)

        # Texte des en-têtes en blanc
        c.setFillColor(colors.white)
        c.setFont('Helvetica-Bold', 7)

        # En-têtes des colonnes exactement comme dans l'image
        c.drawCentredString(col_positions['code'] + 0.9*cm, start_y - 0.5*cm, 'Code Article')
        c.drawCentredString(col_positions['designation'] + 3*cm, start_y - 0.5*cm, 'Désignation')
        c.drawCentredString(col_positions['qte'] + 0.5*cm, start_y - 0.5*cm, 'Qté')
        c.drawCentredString(col_positions['pu_ht'] + 0.7*cm, start_y - 0.5*cm, 'P.U H.T')
        c.drawCentredString(col_positions['rem'] + 0.7*cm, start_y - 0.5*cm, 'Rem %')
        c.drawCentredString(col_positions['pu_ttc'] + 1*cm, start_y - 0.5*cm, 'P.U T.T.C')
        c.drawCentredString(col_positions['mt_net_ht'] + 1*cm, start_y - 0.5*cm, 'Mt Net H.T')
        c.drawCentredString(col_positions['tva'] + 0.7*cm, start_y - 0.5*cm, 'T.V.A %')

        # Lignes verticales pour séparer les colonnes (en-tête seulement pour l'instant)
        c.setStrokeColor(colors.black)
        c.setLineWidth(0.5)
        for pos in col_positions.values():
            if pos > 0.7*cm:
                c.line(pos, start_y, pos, start_y - header_height)

        # Lignes de produits avec alternance de couleurs
        c.setFont('Helvetica', 8)
        y_pos = start_y - header_height - 0.1*cm
        row_height = 0.5*cm

        lignes = facture_data.get('produits', [])
        for i, ligne in enumerate(lignes):
            if y_pos < 6*cm:
                break

            # Fond alterné pour les lignes
            if i % 2 == 0:
                c.setFillColor(colors.HexColor('#f8f9fa'))
                c.rect(0.7*cm, y_pos - row_height + 0.1*cm, table_width, row_height, fill=1, stroke=0)

            # Texte en noir
            c.setFillColor(colors.black)

            # Données du produit
            code = ligne.get('code', f'8199{1000+i}')
            designation = ligne.get('designation', '')
            if len(designation) > 25:
                designation = designation[:22] + '...'
            qte = ligne.get('quantite', 1)
            pu_ht = ligne.get('prix_unitaire', 0)
            remise = ligne.get('remise_pct', 0)
            pu_ttc = pu_ht * 1.19  # TVA 19%
            mt_net_ht = ligne.get('total', qte * pu_ht)
            tva_pct = 19

            # Affichage des données
            c.drawString(col_positions['code'] + 0.1*cm, y_pos - 0.3*cm, code)
            c.drawString(col_positions['designation'] + 0.1*cm, y_pos - 0.3*cm, designation)
            c.drawCentredString(col_positions['qte'] + 0.5*cm, y_pos - 0.3*cm, f'{qte:.0f}')
            c.drawRightString(col_positions['pu_ht'] + 1.3*cm, y_pos - 0.3*cm, f'{pu_ht:.3f}')
            c.drawCentredString(col_positions['rem'] + 0.7*cm, y_pos - 0.3*cm, f'{remise:.2f}')
            c.drawRightString(col_positions['pu_ttc'] + 1.8*cm, y_pos - 0.3*cm, f'{pu_ttc:.3f}')
            c.drawRightString(col_positions['mt_net_ht'] + 1.8*cm, y_pos - 0.3*cm, f'{mt_net_ht:.3f}')
            c.drawCentredString(col_positions['tva'] + 0.7*cm, y_pos - 0.3*cm, f'{tva_pct}')

            y_pos -= row_height

        # Bordure finale du tableau
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        # Ajuster final_y pour que la dernière ligne soit à l'intérieur du tableau
        final_y = y_pos  # La dernière ligne doit être au-dessus de la bordure
        c.rect(0.7*cm, final_y, table_width, start_y - final_y, fill=0, stroke=1)

        # Lignes verticales pour séparer toutes les colonnes du tableau (corps complet)
        c.setStrokeColor(colors.black)
        c.setLineWidth(0.5)
        for pos in col_positions.values():
            if pos > 0.7*cm:
                c.line(pos, start_y, pos, final_y)


    
    def draw_totals(self, c, facture_data):
        """Dessine la section des totaux exactement comme dans l'image de référence"""
        # Position de la section signature et totaux (ajustée pour laisser place à la section montant)
        signature_y = 8*cm  # Augmenté pour que la section montant soit visible

        # Dessiner la section signature et totaux selon l'image de référence
        final_y = self.draw_signature_section(c, signature_y, facture_data)

        # Dessiner le footer avec les informations de l'entreprise (avec espace supplémentaire)
        self.draw_company_footer(c, final_y - 1*cm)

    def draw_signature_section(self, c, y_pos, facture_data=None):
        """Dessine la section signature et totaux uniforme selon l'image 2 avec montant en lettres intégré"""
        # Dimensions de la section haute seulement (sans la section montant)
        table_width = self.width - 1.4*cm
        upper_section_height = 3*cm  # Hauteur pour la section haute seulement
        table_x = 0.7*cm

        # Bordure de la section haute seulement
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.rect(table_x, y_pos - upper_section_height, table_width, upper_section_height, fill=0, stroke=1)

        # Définition des colonnes selon l'image 2
        col1_width = 4*cm   # Approbation Client
        col2_width = 4*cm   # Cachet et Signature
        col3_width = 3*cm   # Labels totaux
        col4_width = 6.6*cm # Montants

        # Lignes verticales de séparation dans la section haute seulement
        c.line(table_x + col1_width, y_pos, table_x + col1_width, y_pos - upper_section_height)
        c.line(table_x + col1_width + col2_width, y_pos, table_x + col1_width + col2_width, y_pos - upper_section_height)

        # En-têtes avec fond gris
        header_height = 0.5*cm
        c.setFillColor(colors.HexColor('#d0d0d0'))

        # En-tête "Approbation Client"
        c.rect(table_x, y_pos - header_height, col1_width, header_height, fill=1, stroke=1)
        c.setFillColor(colors.black)
        c.setFont('Helvetica-Bold', 9)
        c.drawCentredString(table_x + col1_width/2, y_pos - 0.3*cm, 'Approbation Client')

        # En-tête "Cachet et Signature"
        c.setFillColor(colors.HexColor('#d0d0d0'))
        c.rect(table_x + col1_width, y_pos - header_height, col2_width, header_height, fill=1, stroke=1)
        c.setFillColor(colors.black)
        c.drawCentredString(table_x + col1_width + col2_width/2, y_pos - 0.3*cm, 'Cachet et Signature')

        # Section totaux compacte à droite (selon l'image de référence)
        upper_section_height = 3*cm  # Hauteur de la section haute seulement
        totals_height = upper_section_height - header_height
        totals_x = table_x + col1_width + col2_width
        totals_width = col3_width + col4_width
        self.draw_totals_grid_compact(c, totals_x, y_pos - header_height, totals_width, totals_height, facture_data)

        # Section montant en lettres sur toute la largeur en bas (selon l'image)
        amount_section_height = 1.5*cm
        amount_section_y = y_pos - upper_section_height  # Juste en dessous de la section haute
        self.draw_amount_section_full_width(c, table_x, amount_section_y - amount_section_height, table_width, amount_section_height, facture_data)

        return y_pos - upper_section_height - amount_section_height

        # Zone signature - espace vide pour cachet et signature
        # (Pas de tampon - espace réservé pour signature manuelle)

    def draw_totals_grid(self, c, x, y, width, height, facture_data=None):
        """Dessine la grille des totaux exactement comme dans l'image réorganisée avec données dynamiques"""
        # Calculs des totaux dynamiques
        if facture_data:
            # Calculer les totaux à partir des produits
            produits = facture_data.get('produits', [])
            montant_brut = sum(p.get('quantite', 0) * p.get('prix_unitaire', 0) for p in produits)
            remise_total = sum(p.get('quantite', 0) * p.get('prix_unitaire', 0) * (p.get('remise_pct', 0) / 100) for p in produits)
            montant_ht = montant_brut - remise_total
            tva_19 = montant_ht * 0.19
            rf_1 = montant_ht * 0.01
            timbre_fiscal = 1.000
            total_ttc = montant_ht + tva_19 + rf_1 + timbre_fiscal
        else:
            # Valeurs par défaut si pas de données
            montant_brut = 0.000
            remise_total = 0.000
            montant_ht = 0.000
            tva_19 = 0.000
            rf_1 = 0.000
            timbre_fiscal = 1.000
            total_ttc = timbre_fiscal

        # Données des totaux avec fond gris pour les labels - dynamiques
        totals_data = [
            ('Montant Brut :', f'{montant_brut:.3f} TND'),
            ('Remise ( %)', f'{remise_total:.3f} TND'),
            ('Montant HT', f'{montant_ht:.3f} TND'),
            ('TVA (19%)', f'{tva_19:.3f} TND'),
            ('R.F 1%', f'{rf_1:.3f} TND'),
            ('Timbre Fiscal', f'{timbre_fiscal:.3f}'),
            ('TOTAL TTC', f'{total_ttc:.3f} TND')
        ]

        # Dimensions des cellules selon la nouvelle image
        label_width = 3*cm
        value_width = 6.6*cm
        row_height = height / len(totals_data)

        # Dessiner chaque ligne de total
        for i, (label, value) in enumerate(totals_data):
            current_y = y - (i + 1) * row_height

            # Fond gris pour la colonne des labels
            c.setFillColor(colors.HexColor('#d0d0d0'))
            c.rect(x, current_y, label_width, row_height, fill=1, stroke=1)

            # Fond blanc pour la colonne des valeurs
            c.setFillColor(colors.white)
            c.rect(x + label_width, current_y, value_width, row_height, fill=1, stroke=1)

            # Texte du label
            c.setFillColor(colors.black)
            if label == 'TOTAL TTC':
                c.setFont('Helvetica-Bold', 9)
            else:
                c.setFont('Helvetica', 8)

            c.drawString(x + 0.1*cm, current_y + row_height/2 - 0.1*cm, label)

            # Texte de la valeur alignée à droite
            c.drawRightString(x + label_width + value_width - 0.1*cm, current_y + row_height/2 - 0.1*cm, value)

        # Ligne verticale de séparation entre labels et valeurs
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.line(x + label_width, y, x + label_width, y - height)

    def draw_amount_section_integrated(self, c, x, y, width, height):
        """Dessine la section montant en lettres intégrée dans le tableau selon l'image 2"""
        # Ligne horizontale de séparation avec la section totaux
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.line(x, y + height, x + width, y + height)

        # Fond gris pour la section "Arrêtez la présente facture"
        section_title_height = 0.4*cm
        c.setFillColor(colors.HexColor('#d0d0d0'))
        c.rect(x, y + height - section_title_height, width, section_title_height, fill=1, stroke=1)

        # Texte "Arrêtez la présente facture à la somme de"
        c.setFillColor(colors.black)
        c.setFont('Helvetica-Bold', 8)
        c.drawString(x + 0.2*cm, y + height - 0.25*cm, 'Arrêtez la présente facture à la somme de :')

        # Zone blanche pour le montant en lettres
        montant_text_height = height - section_title_height
        c.setFillColor(colors.white)
        c.rect(x, y, width, montant_text_height, fill=1, stroke=1)

        # Calcul du total exact
        montant_brut = 570.150
        tva_19 = montant_brut * 0.19
        rf_1 = montant_brut * 0.01
        timbre_fiscal = 1.000
        total_ttc = montant_brut + tva_19 + rf_1 + timbre_fiscal

        # Montant en lettres (exemple)
        montant_lettres = "Six cent quatre-vingt-cinq dinars et cent quatre-vingts millimes"
        c.setFillColor(colors.black)
        c.setFont('Helvetica', 9)
        c.drawString(x + 0.2*cm, y + montant_text_height - 0.4*cm, montant_lettres)

        # Mode de paiement avec options (Espèce ou Chèque)
        c.setFont('Helvetica', 8)
        c.drawString(x + 0.2*cm, y + 0.2*cm, 'Payé en :')

        # Cases à cocher pour Espèce, Chèque et Traite
        checkbox_size = 0.25*cm
        espece_x = x + 2*cm
        cheque_x = x + 4*cm
        traite_x = x + 6*cm
        checkbox_y = y + 0.15*cm

        # Case Espèce
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.rect(espece_x, checkbox_y, checkbox_size, checkbox_size, fill=0, stroke=1)
        c.drawString(espece_x + checkbox_size + 0.1*cm, checkbox_y + 0.05*cm, 'Espèce')

        # Case Chèque
        c.rect(cheque_x, checkbox_y, checkbox_size, checkbox_size, fill=0, stroke=1)
        c.drawString(cheque_x + checkbox_size + 0.1*cm, checkbox_y + 0.05*cm, 'Chèque')

        # Case Traite
        c.rect(traite_x, checkbox_y, checkbox_size, checkbox_size, fill=0, stroke=1)
        c.drawString(traite_x + checkbox_size + 0.1*cm, checkbox_y + 0.05*cm, 'Traite')

    def draw_totals_grid_compact(self, c, x, y, width, height, facture_data=None):
        """Dessine la grille des totaux compacte selon l'image de référence avec données dynamiques"""
        # Calcul des totaux dynamiques
        if facture_data:
            # Calculer les totaux à partir des produits
            produits = facture_data.get('produits', [])
            montant_brut = sum(p.get('quantite', 0) * p.get('prix_unitaire', 0) for p in produits)
            remise_total = sum(p.get('quantite', 0) * p.get('prix_unitaire', 0) * (p.get('remise_pct', 0) / 100) for p in produits)
            montant_ht = montant_brut - remise_total
            tva_19 = montant_ht * 0.19
            rf_1 = montant_ht * 0.01
            timbre_fiscal = 1.000
            total_ttc = montant_ht + tva_19 + rf_1 + timbre_fiscal
        else:
            # Valeurs par défaut si pas de données
            montant_brut = 0.000
            remise_total = 0.000
            montant_ht = 0.000
            tva_19 = 0.000
            rf_1 = 0.000
            timbre_fiscal = 1.000
            total_ttc = timbre_fiscal

        # Données des totaux selon l'image - dynamiques
        remise_pct = (remise_total / montant_brut * 100) if montant_brut > 0 else 0
        totals_data = [
            ('Montant Brut', f'{montant_brut:.3f} TND'),
            (f'Remise ({remise_pct:.1f}%)', f'{remise_total:.3f} TND'),
            ('Montant HT', f'{montant_ht:.3f} TND'),
            ('TVA (19%)', f'{tva_19:.3f} TND'),
            ('R.F 1%', f'{rf_1:.3f} TND'),
            ('Timbre Fiscal', f'{timbre_fiscal:.3f}'),
            ('TOTAL TTC', f'{total_ttc:.3f} TND')
        ]

        # Dimensions de chaque ligne
        row_height = height / len(totals_data)
        label_width = width * 0.4  # 40% pour les labels
        value_width = width * 0.6  # 60% pour les valeurs

        # Dessiner chaque ligne de total
        for i, (label, value) in enumerate(totals_data):
            row_y = y - (i + 1) * row_height

            # Fond gris pour le label
            c.setFillColor(colors.HexColor('#d0d0d0'))
            c.rect(x, row_y, label_width, row_height, fill=1, stroke=1)

            # Fond blanc pour la valeur
            c.setFillColor(colors.white)
            c.rect(x + label_width, row_y, value_width, row_height, fill=1, stroke=1)

            # Texte du label en noir
            c.setFillColor(colors.black)
            if label == 'TOTAL TTC':
                c.setFont('Helvetica-Bold', 8)
            else:
                c.setFont('Helvetica', 7)
            c.drawString(x + 0.1*cm, row_y + row_height/2 - 0.1*cm, label)

            # Texte de la valeur en noir, aligné à droite
            c.drawRightString(x + width - 0.1*cm, row_y + row_height/2 - 0.1*cm, value)

        # Ligne verticale de séparation entre labels et valeurs
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.line(x + label_width, y, x + label_width, y - height)

    def draw_amount_section_full_width(self, c, x, y, width, height, facture_data=None):
        """Dessine la section montant en lettres sur toute la largeur selon l'image avec données dynamiques"""
        # Bordure de la section
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.rect(x, y, width, height, fill=0, stroke=1)

        # Ligne horizontale pour séparer le titre du contenu
        title_height = 0.4*cm
        c.line(x, y + height - title_height, x + width, y + height - title_height)

        # Fond gris pour la section titre
        c.setFillColor(colors.HexColor('#d0d0d0'))
        c.rect(x, y + height - title_height, width, title_height, fill=1, stroke=1)

        # Texte "Arrêtez la présente facture à la somme de"
        c.setFillColor(colors.black)
        c.setFont('Helvetica-Bold', 8)
        c.drawString(x + 0.2*cm, y + height - 0.25*cm, 'Arrêtez la présente facture à la somme de :')

        # Zone blanche pour le montant en lettres
        content_height = height - title_height
        c.setFillColor(colors.white)
        c.rect(x, y, width, content_height, fill=1, stroke=0)

        # Calcul du total dynamique
        if facture_data:
            produits = facture_data.get('produits', [])
            montant_brut = sum(p.get('quantite', 0) * p.get('prix_unitaire', 0) for p in produits)
            remise_total = sum(p.get('quantite', 0) * p.get('prix_unitaire', 0) * (p.get('remise_pct', 0) / 100) for p in produits)
            montant_ht = montant_brut - remise_total
            tva_19 = montant_ht * 0.19
            rf_1 = montant_ht * 0.01
            timbre_fiscal = 1.000
            total_ttc = montant_ht + tva_19 + rf_1 + timbre_fiscal
        else:
            total_ttc = 0.000

        # Montant en lettres dynamique
        montant_lettres = self.number_to_words_french(total_ttc)
        c.setFillColor(colors.black)
        c.setFont('Helvetica', 9)
        c.drawString(x + 0.2*cm, y + content_height - 0.4*cm, montant_lettres)

        # Mode de paiement avec options (Espèce, Chèque, Traite)
        c.setFont('Helvetica', 8)
        c.drawString(x + 0.2*cm, y + 0.2*cm, 'Payé en :')

        # Cases à cocher pour Espèce, Chèque et Traite
        checkbox_size = 0.25*cm
        espece_x = x + 2*cm
        cheque_x = x + 4*cm
        traite_x = x + 6*cm
        checkbox_y = y + 0.15*cm

        # Case Espèce
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.rect(espece_x, checkbox_y, checkbox_size, checkbox_size, fill=0, stroke=1)
        c.drawString(espece_x + checkbox_size + 0.1*cm, checkbox_y + 0.05*cm, 'Espèce')

        # Case Chèque
        c.rect(cheque_x, checkbox_y, checkbox_size, checkbox_size, fill=0, stroke=1)
        c.drawString(cheque_x + checkbox_size + 0.1*cm, checkbox_y + 0.05*cm, 'Chèque')

        # Case Traite
        c.rect(traite_x, checkbox_y, checkbox_size, checkbox_size, fill=0, stroke=1)
        c.drawString(traite_x + checkbox_size + 0.1*cm, checkbox_y + 0.05*cm, 'Traite')

    def draw_amount_section_separated(self, c, y_pos):
        """Dessine la section montant en lettres séparée sous le tableau selon la nouvelle image"""
        # Dimensions de la section séparée
        section_width = self.width - 1.4*cm
        section_height = 1.2*cm
        section_x = 0.7*cm

        # Bordure de la section
        c.setStrokeColor(colors.black)
        c.setLineWidth(1)
        c.rect(section_x, y_pos - section_height, section_width, section_height, fill=0, stroke=1)

        # Calcul du total exact
        montant_brut = 570.150
        tva_19 = montant_brut * 0.19
        rf_1 = montant_brut * 0.01
        timbre_fiscal = 1.000
        total_ttc = montant_brut + tva_19 + rf_1 + timbre_fiscal

        # Texte "Arrêtez la présente facture à la somme de"
        c.setFillColor(colors.black)
        c.setFont('Helvetica', 9)
        c.drawString(section_x + 0.2*cm, y_pos - 0.4*cm, 'Arrêtez la présente facture à la somme de :')

        # Montant en lettres (exemple)
        montant_lettres = "Six cent soixante-dix-neuf dinars et quatre cent soixante-dix-neuf millimes"
        c.drawString(section_x + 0.2*cm, y_pos - 0.7*cm, montant_lettres)

        # Mode de paiement séparé
        c.drawString(section_x + 0.2*cm, y_pos - 1.5*cm, 'Payé en : Espèce')

    def draw_totals_section(self, c, facture_data, y_pos):
        """Méthode maintenue pour compatibilité - redirige vers draw_totals_grid"""
        # Cette méthode est maintenant gérée par draw_totals_grid dans draw_signature_section
        pass

    def draw_amount_section_in_table(self, c, x, y, width):
        """Dessine la section montant en lettres dans la partie gauche du tableau"""
        # Calcul du total exact
        montant_brut = 570.150
        tva_19 = montant_brut * 0.19
        rf_1 = montant_brut * 0.01
        timbre_fiscal = 1.000
        total_ttc = montant_brut + tva_19 + rf_1 + timbre_fiscal

        # Section avec fond gris pour "Arrêtez la présente facture"
        section_height = 1*cm
        c.setFillColor(colors.HexColor('#d0d0d0'))
        c.rect(x, y, width, section_height, fill=1, stroke=1)

        # Texte "Arrêtez la présente facture à la somme de"
        c.setFillColor(colors.black)
        c.setFont('Helvetica', 8)
        c.drawString(x + 0.1*cm, y + section_height - 0.3*cm, 'Arrêtez la présente facture à la somme de :')

        # Montant en lettres (exemple)
        montant_lettres = "Six cent soixante-dix-neuf dinars et quatre cent soixante-dix-neuf millimes"
        c.drawString(x + 0.1*cm, y + section_height - 0.6*cm, montant_lettres)

        # Mode de paiement
        c.drawString(x + 0.1*cm, y + section_height - 0.9*cm, 'Payé en : Espèce')

    def draw_company_stamp(self, c, x, y):
        """Espace réservé pour le cachet et signature - pas de tampon automatique"""
        # Cette méthode est maintenant vide - l'espace est réservé pour signature manuelle
        # Le cachet et la signature seront ajoutés manuellement sur le document imprimé
        pass

    def draw_company_footer(self, c, y_pos):
        """Dessine le footer innovant avec design futuriste gris en bas de la feuille"""
        # Footer futuriste en bas de la feuille
        footer_height = 1.8*cm
        footer_y = 0.5*cm  # Position fixe en bas de la feuille

        # Fond gris futuriste avec dégradé
        c.setFillColor(colors.HexColor('#2c3e50'))  # Gris bleu foncé futuriste
        c.rect(0, footer_y, self.width, footer_height, fill=1, stroke=0)

        # Ligne supérieure accent futuriste
        c.setStrokeColor(colors.HexColor('#34495e'))  # Gris plus clair
        c.setLineWidth(2)
        c.line(0, footer_y + footer_height, self.width, footer_y + footer_height)

        # Ligne accent fine en haut
        c.setStrokeColor(colors.HexColor('#5d6d7e'))  # Gris métallique
        c.setLineWidth(1)
        c.line(0, footer_y + footer_height - 0.1*cm, self.width, footer_y + footer_height - 0.1*cm)

        # Informations côté gauche en blanc
        left_info = [
            "Siège commercial : 10, Rue de la Commission",
            "Tél : 71 327 035 - 98 505 297",
            "96 287 853"
        ]

        # Informations côté droit en blanc
        right_info = [
            "Email : <EMAIL>",
            "MF : 1283049 W/A/M/000"
        ]

        # Texte en blanc pour contraste sur fond gris foncé
        c.setFillColor(colors.white)
        c.setFont('Helvetica', 8)

        # Dessiner les informations côté gauche
        for i, info in enumerate(left_info):
            c.drawString(1*cm, footer_y + footer_height - 0.4*cm - (i * 0.35*cm), info)

        # Dessiner les informations côté droit
        for i, info in enumerate(right_info):
            c.drawRightString(self.width - 1*cm, footer_y + footer_height - 0.4*cm - (i * 0.35*cm), info)

        # Petit accent décoratif futuriste (ligne verticale séparatrice)
        separator_x = self.width / 2
        c.setStrokeColor(colors.HexColor('#5d6d7e'))
        c.setLineWidth(1)
        c.line(separator_x, footer_y + 0.3*cm, separator_x, footer_y + footer_height - 0.3*cm)

    def draw_amount_in_words(self, c, facture_data, y_pos):
        """Cette méthode est maintenant intégrée dans draw_amount_section_in_table"""
        # Le montant en lettres est maintenant dans la section signature
        pass






    def draw_watermark(self, c):
        """Dessine un filigrane simple"""
        c.saveState()
        c.setFillColor(colors.HexColor('#f0f0f0'))
        c.setFont('Helvetica', 60)
        c.rotate(45)
        c.drawString(8*cm, 2*cm, 'FACTURE')
        c.restoreState()


    def number_to_words_fr(self, n):
        """Convertit un nombre en mots français (version simplifiée)"""
        if n == 0: return "zéro"
        if n == 1: return "un"
        if n == 2: return "deux"
        if n == 3: return "trois"
        if n == 4: return "quatre"
        if n == 5: return "cinq"
        if n == 6: return "six"
        if n == 7: return "sept"
        if n == 8: return "huit"
        if n == 9: return "neuf"
        if n == 10: return "dix"
        # Simplification pour les autres nombres
        return str(n)
    
    def draw_footer(self, c):
        """Dessine le pied de page sobre et professionnel sans couleur"""
        # Footer sobre avec fond blanc/gris très clair
        footer_height = 1.5*cm
        c.setFillColor(colors.HexColor('#f8f9fa'))  # Gris très clair, presque blanc
        c.rect(0, 0, self.width, footer_height, fill=1, stroke=0)

        # Bordure fine en haut du footer
        c.setStrokeColor(colors.HexColor('#dee2e6'))  # Gris clair pour la bordure
        c.setLineWidth(1)
        c.line(0, footer_height, self.width, footer_height)

        # Informations de contact en noir sur fond clair
        c.setFillColor(colors.HexColor('#343a40'))  # Gris foncé pour le texte
        c.setFont('Helvetica', 9)  # Police normale, pas bold

        # Colonne gauche
        c.drawString(1*cm, footer_height - 0.4*cm, 'Siège commercial : 10, Rue de la Commission')
        c.drawString(1*cm, footer_height - 0.7*cm, 'Télephone : 71 327 035 - 98 505 297 - 96 287 853')

        # Colonne droite
        c.drawRightString(self.width - 1*cm, footer_height - 0.4*cm, 'Email : <EMAIL>')
        c.drawRightString(self.width - 1*cm, footer_height - 0.7*cm, 'Matricule Fiscale : 1283049 W/A/M/000')

# Alias pour compatibilité
ModernFacturePDFGenerator = TunisianFacturePDFGenerator
