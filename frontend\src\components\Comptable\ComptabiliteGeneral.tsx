/**
 * Vue d'ensemble comptable avec indicateurs clés
 */

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { DashboardService, InvoiceService } from "../../services/apiService";
import { useAuth } from "../../contexts/AuthContext";
import { useNotify } from "../Common/NotificationSystem";

interface ComptabiliteStats {
  chiffre_affaires_mensuel: number;
  chiffre_affaires_annuel: number;
  tva_collectee: number;
  tva_deductible: number;
  tva_a_payer: number;
  factures_impayees: number;
  factures_en_retard: number;
  creances_clients: number;
  tresorerie: number;
  resultat_mensuel: number;
  resultat_annuel: number;
  marge_brute: number;
}

const ComptabiliteGeneral: React.FC = () => {
  const navigate = useNavigate();
  const { hasRole } = useAuth();
  const notify = useNotify();

  // Vérifier si l'utilisateur peut voir les chiffres d'affaires
  const canViewRevenue = hasRole("ADMIN");

  const [stats, setStats] = useState<ComptabiliteStats>({
    chiffre_affaires_mensuel: 0,
    chiffre_affaires_annuel: 0,
    tva_collectee: 0,
    tva_deductible: 0,
    tva_a_payer: 0,
    factures_impayees: 0,
    factures_en_retard: 0,
    creances_clients: 0,
    tresorerie: 0,
    resultat_mensuel: 0,
    resultat_annuel: 0,
    marge_brute: 0,
  });

  const [loading, setLoading] = useState(true);
  const [periode, setPeriode] = useState("mois");

  useEffect(() => {
    loadComptabiliteStats();
  }, [periode]);

  const loadComptabiliteStats = async () => {
    try {
      setLoading(true);

      // Simuler le chargement des données comptables
      const mockStats: ComptabiliteStats = {
        chiffre_affaires_mensuel: 45680.5,
        chiffre_affaires_annuel: 487250.75,
        tva_collectee: 9136.1,
        tva_deductible: 2847.3,
        tva_a_payer: 6288.8,
        factures_impayees: 12,
        factures_en_retard: 3,
        creances_clients: 23450.0,
        tresorerie: 67890.25,
        resultat_mensuel: 8920.4,
        resultat_annuel: 89750.6,
        marge_brute: 18.4,
      };

      setStats(mockStats);
    } catch (error: any) {
      notify.error("Erreur lors du chargement des données comptables");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="comptable-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des données comptables...</p>
      </div>
    );
  }

  return (
    <div className="comptabilite-general">
      {/* Filtres de période */}
      <div className="periode-filters">
        <h3>📅 Période d'analyse</h3>
        <div className="filter-buttons">
          <button
            className={`filter-btn ${periode === "mois" ? "active" : ""}`}
            onClick={() => setPeriode("mois")}>
            📅 Ce mois
          </button>
          <button
            className={`filter-btn ${periode === "trimestre" ? "active" : ""}`}
            onClick={() => setPeriode("trimestre")}>
            📊 Ce trimestre
          </button>
          <button
            className={`filter-btn ${periode === "annee" ? "active" : ""}`}
            onClick={() => setPeriode("annee")}>
            📈 Cette année
          </button>
        </div>
      </div>

      {/* Indicateurs clés */}
      <div className="kpi-grid">
        {/* Chiffre d'affaires - Visible uniquement pour Admin/SuperAdmin */}
        {canViewRevenue && (
          <div className="kpi-card revenue">
            <div className="kpi-header">
              <h4>💰 Chiffre d'Affaires</h4>
              <span className="kpi-period">
                {periode === "mois"
                  ? "Ce mois"
                  : periode === "trimestre"
                  ? "Ce trimestre"
                  : "Cette année"}
              </span>
            </div>
            <div className="kpi-value">
              {formatCurrency(
                periode === "mois"
                  ? stats.chiffre_affaires_mensuel
                  : stats.chiffre_affaires_annuel
              )}
            </div>
            <div className="kpi-trend positive">
              <span className="trend-icon">📈</span>
              <span>+12.5% vs période précédente</span>
            </div>
          </div>
        )}

        {/* TVA */}
        <div className="kpi-card tva">
          <div className="kpi-header">
            <h4>🧾 TVA à payer</h4>
            <span className="kpi-period">Ce mois</span>
          </div>
          <div className="kpi-value">{formatCurrency(stats.tva_a_payer)}</div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>Collectée: {formatCurrency(stats.tva_collectee)}</span>
            </div>
            <div className="detail-item">
              <span>Déductible: {formatCurrency(stats.tva_deductible)}</span>
            </div>
          </div>
        </div>

        {/* Créances */}
        <div className="kpi-card receivables">
          <div className="kpi-header">
            <h4>💳 Créances clients</h4>
            <span className="kpi-period">
              {stats.factures_impayees} factures
            </span>
          </div>
          <div className="kpi-value">
            {formatCurrency(stats.creances_clients)}
          </div>
          <div className="kpi-alert">
            <span className="alert-icon">⚠️</span>
            <span>{stats.factures_en_retard} factures en retard</span>
          </div>
        </div>

        {/* Trésorerie */}
        <div className="kpi-card treasury">
          <div className="kpi-header">
            <h4>🏦 Trésorerie</h4>
            <span className="kpi-period">Position actuelle</span>
          </div>
          <div className="kpi-value">{formatCurrency(stats.tresorerie)}</div>
          <div className="kpi-trend positive">
            <span className="trend-icon">💹</span>
            <span>Position saine</span>
          </div>
        </div>

        {/* Résultat */}
        <div className="kpi-card profit">
          <div className="kpi-header">
            <h4>📈 Résultat</h4>
            <span className="kpi-period">
              {periode === "mois" ? "Ce mois" : "Cette année"}
            </span>
          </div>
          <div className="kpi-value">
            {formatCurrency(
              periode === "mois"
                ? stats.resultat_mensuel
                : stats.resultat_annuel
            )}
          </div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>Marge brute: {formatPercentage(stats.marge_brute)}</span>
            </div>
          </div>
        </div>

        {/* Actions rapides */}
        <div className="kpi-card actions">
          <div className="kpi-header">
            <h4>⚡ Actions rapides</h4>
          </div>
          <div className="quick-actions">
            <button
              className="action-btn"
              onClick={() => navigate("/comptable/tva")}>
              🧾 Déclaration TVA
            </button>
            <button
              className="action-btn"
              onClick={() => navigate("/comptable/export")}>
              📤 Export comptable
            </button>
            <button
              className="action-btn"
              onClick={() => navigate("/factures?statut=IMPAYEE")}>
              💳 Factures impayées
            </button>
            <button
              className="action-btn"
              onClick={() => navigate("/comptable/bilan")}>
              📋 Générer bilan
            </button>
          </div>
        </div>
      </div>

      {/* Graphiques et analyses */}
      <div className="comptable-charts">
        <div className="chart-section">
          <h3>📊 Évolution du CA</h3>
          <div className="chart-placeholder">
            <div className="chart-info">
              <span>📈 Graphique d'évolution du chiffre d'affaires</span>
              <p>Intégration avec Chart.js ou Recharts à prévoir</p>
            </div>
          </div>
        </div>

        <div className="chart-section">
          <h3>🧾 Répartition TVA</h3>
          <div className="chart-placeholder">
            <div className="chart-info">
              <span>🥧 Graphique en secteurs de la TVA</span>
              <p>TVA collectée vs TVA déductible</p>
            </div>
          </div>
        </div>
      </div>

      {/* Alertes comptables */}
      <div className="comptable-alerts">
        <h3>🚨 Alertes Comptables</h3>
        <div className="alerts-list">
          <div className="alert-item warning">
            <div className="alert-icon">⚠️</div>
            <div className="alert-content">
              <h4>Factures en retard</h4>
              <p>{stats.factures_en_retard} factures dépassent leur échéance</p>
            </div>
            <button
              className="alert-action"
              onClick={() => navigate("/factures?statut=EN_RETARD")}>
              Voir →
            </button>
          </div>

          <div className="alert-item info">
            <div className="alert-icon">ℹ️</div>
            <div className="alert-content">
              <h4>Déclaration TVA</h4>
              <p>Échéance de déclaration dans 15 jours</p>
            </div>
            <button
              className="alert-action"
              onClick={() => navigate("/comptable/tva")}>
              Préparer →
            </button>
          </div>

          <div className="alert-item success">
            <div className="alert-icon">✅</div>
            <div className="alert-content">
              <h4>Sauvegarde comptable</h4>
              <p>Dernière sauvegarde effectuée avec succès</p>
            </div>
            <button
              className="alert-action"
              onClick={() => navigate("/comptable/export")}>
              Télécharger →
            </button>
          </div>
        </div>
      </div>

      {/* Raccourcis vers autres modules */}
      <div className="comptable-shortcuts">
        <h3>🔗 Raccourcis</h3>
        <div className="shortcuts-grid">
          <button
            className="shortcut-card"
            onClick={() => navigate("/factures/new")}>
            <div className="shortcut-icon">📄</div>
            <div className="shortcut-label">Nouvelle facture</div>
          </button>

          <button
            className="shortcut-card"
            onClick={() => navigate("/clients/new")}>
            <div className="shortcut-icon">👥</div>
            <div className="shortcut-label">Nouveau client</div>
          </button>

          <button
            className="shortcut-card"
            onClick={() => navigate("/products")}>
            <div className="shortcut-icon">📦</div>
            <div className="shortcut-label">Catalogue produits</div>
          </button>

          <button
            className="shortcut-card"
            onClick={() => navigate("/orders")}>
            <div className="shortcut-icon">🛒</div>
            <div className="shortcut-label">Commandes</div>
          </button>

          <button
            className="shortcut-card"
            onClick={() => navigate("/settings/invoices")}>
            <div className="shortcut-icon">⚙️</div>
            <div className="shortcut-label">Paramètres factures</div>
          </button>

          <button
            className="shortcut-card"
            onClick={() => navigate("/settings/company")}>
            <div className="shortcut-icon">🏢</div>
            <div className="shortcut-label">Infos entreprise</div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ComptabiliteGeneral;
