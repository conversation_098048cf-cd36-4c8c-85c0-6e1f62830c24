/**
 * <PERSON><PERSON><PERSON> complet d'une commande avec actions et suivi
 */

import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { OrderService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";

interface Order {
  id: string;
  numero: string;
  client_id: string;
  client_nom: string;
  client_email: string;
  client_telephone?: string;
  client_adresse?: string;
  date_commande: string;
  date_livraison_prevue?: string;
  date_livraison_reelle?: string;
  montant_ht: number;
  montant_tva: number;
  montant_total: number;
  statut:
    | "BROUILLON"
    | "CONFIRMEE"
    | "EN_PREPARATION"
    | "EXPEDIEE"
    | "LIVREE"
    | "ANNULEE";
  priorite: "NORMALE" | "HAUTE" | "URGENTE";
  notes?: string;
  lignes: any[];
  created_at: string;
  updated_at: string;
}

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const notify = useNotify();

  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [statusHistory, setStatusHistory] = useState<any[]>([]);

  useEffect(() => {
    if (id) {
      loadOrder(id);
      loadStatusHistory(id);
    }
  }, [id]);

  const loadOrder = async (orderId: string) => {
    try {
      setLoading(true);

      // Simulation de données de commande
      const mockOrder: Order = {
        id: orderId,
        numero: "CMD-2025-001",
        client_id: "2017",
        client_nom: "CLIENT FOND FUTURISTE",
        client_email: "<EMAIL>",
        client_telephone: "+216 71 123 456",
        client_adresse: "7123 Avenue Information Futuriste",
        date_commande: "2025-08-10",
        date_livraison_prevue: "2025-08-15",
        date_livraison_reelle: undefined,
        montant_ht: 831.65,
        montant_tva: 158.014,
        montant_total: 989.664,
        statut: "EN_PREPARATION",
        priorite: "HAUTE",
        notes: "Commande urgente pour client prioritaire",
        lignes: [
          {
            id: "1",
            produit_id: "B1994471111",
            produit_nom: "PRODUIT DESIGN FUTURISTE",
            description: "Produit design moderne et futuriste",
            quantite: 10,
            prix_unitaire: 57.015,
            taux_tva: 19,
            montant_ht: 570.15,
            montant_tva: 108.329,
            montant_ttc: 678.479,
          },
          {
            id: "2",
            produit_id: "B1991720001",
            produit_nom: "ARTICLE PREMIER PLAN M...",
            description: "Article de première qualité",
            quantite: 5,
            prix_unitaire: 25.0,
            taux_tva: 19,
            montant_ht: 125.0,
            montant_tva: 23.75,
            montant_ttc: 148.75,
          },
          {
            id: "3",
            produit_id: "B1993360002",
            produit_nom: "SERVICE PLUS DANS ELEGANT",
            description: "Service élégant et professionnel",
            quantite: 3,
            prix_unitaire: 45.5,
            taux_tva: 19,
            montant_ht: 136.5,
            montant_tva: 25.935,
            montant_ttc: 162.435,
          },
        ],
        created_at: "2025-08-10T09:30:00Z",
        updated_at: "2025-08-12T14:20:00Z",
      };

      setOrder(mockOrder);
    } catch (error: any) {
      notify.error("Erreur lors du chargement de la commande");
      navigate("/orders");
    } finally {
      setLoading(false);
    }
  };

  const loadStatusHistory = async (orderId: string) => {
    try {
      // Simuler l'historique des statuts
      setStatusHistory([
        {
          statut: "BROUILLON",
          date: "2024-08-08T10:00:00Z",
          utilisateur: "Jean Dupont",
          commentaire: "Commande créée",
        },
        {
          statut: "CONFIRMEE",
          date: "2024-08-08T10:30:00Z",
          utilisateur: "Jean Dupont",
          commentaire: "Commande confirmée par le client",
        },
      ]);
    } catch (error: any) {
      console.error("Erreur chargement historique:", error);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!order) return;

    try {
      setActionLoading("status");
      const response = await OrderService.updateStatus(order.id, newStatus);

      if (response.success) {
        setOrder((prev) =>
          prev ? { ...prev, statut: newStatus as any } : null
        );
        notify.success("Statut mis à jour avec succès");
        loadStatusHistory(order.id);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      notify.error("Erreur lors de la mise à jour du statut");
    } finally {
      setActionLoading(null);
    }
  };

  const handleCreateInvoice = () => {
    if (!order) return;
    navigate(`/invoices/new?order=${order.id}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "BROUILLON":
        return "status-draft";
      case "CONFIRMEE":
        return "status-confirmed";
      case "EN_PREPARATION":
        return "status-preparing";
      case "EXPEDIEE":
        return "status-shipped";
      case "LIVREE":
        return "status-delivered";
      case "ANNULEE":
        return "status-cancelled";
      default:
        return "status-default";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "BROUILLON":
        return "Brouillon";
      case "CONFIRMEE":
        return "Confirmée";
      case "EN_PREPARATION":
        return "En préparation";
      case "EXPEDIEE":
        return "Expédiée";
      case "LIVREE":
        return "Livrée";
      case "ANNULEE":
        return "Annulée";
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "URGENTE":
        return "priority-urgent";
      case "HAUTE":
        return "priority-high";
      case "NORMALE":
        return "priority-normal";
      default:
        return "priority-default";
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "URGENTE":
        return "🔴 Urgente";
      case "HAUTE":
        return "🟡 Haute";
      case "NORMALE":
        return "🟢 Normale";
      default:
        return priority;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("fr-FR");
  };

  const isOverdue = () => {
    if (
      !order?.date_livraison_prevue ||
      order.statut === "LIVREE" ||
      order.statut === "ANNULEE"
    ) {
      return false;
    }
    return new Date(order.date_livraison_prevue) < new Date();
  };

  const getNextActions = () => {
    if (!order) return [];

    switch (order.statut) {
      case "BROUILLON":
        return [
          { label: "✅ Confirmer", action: "CONFIRMEE", color: "success" },
          { label: "❌ Annuler", action: "ANNULEE", color: "danger" },
        ];
      case "CONFIRMEE":
        return [
          { label: "📦 Préparer", action: "EN_PREPARATION", color: "warning" },
          { label: "❌ Annuler", action: "ANNULEE", color: "danger" },
        ];
      case "EN_PREPARATION":
        return [
          { label: "🚚 Expédier", action: "EXPEDIEE", color: "info" },
          { label: "❌ Annuler", action: "ANNULEE", color: "danger" },
        ];
      case "EXPEDIEE":
        return [
          { label: "✅ Marquer livrée", action: "LIVREE", color: "success" },
        ];
      default:
        return [];
    }
  };

  if (loading) {
    return (
      <div className="order-detail-loading">
        <div className="loading-spinner"></div>
        <p>Chargement de la commande...</p>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="order-not-found">
        <div className="error-icon">❌</div>
        <h3>Commande introuvable</h3>
        <p>La commande demandée n'existe pas ou a été supprimée</p>
        <button
          className="btn btn-primary"
          onClick={() => navigate("/orders")}>
          ← Retour aux commandes
        </button>
      </div>
    );
  }

  const nextActions = getNextActions();

  return (
    <div className="order-detail">
      {/* Header avec actions */}
      <div className="detail-header">
        <div className="header-info">
          <div className="order-title">
            <h1>🛒 {order.numero}</h1>
            <div className="order-badges">
              <span className={`status-badge ${getStatusColor(order.statut)}`}>
                {getStatusLabel(order.statut)}
              </span>
              <span
                className={`priority-badge ${getPriorityColor(
                  order.priorite
                )}`}>
                {getPriorityLabel(order.priorite)}
              </span>
              {isOverdue() && (
                <span className="overdue-badge">⚠️ En retard</span>
              )}
            </div>
          </div>
          <div className="order-summary">
            <span>Commandé le {formatDate(order.date_commande)}</span>
            <span>Client: {order.client_nom}</span>
            <span>Total: {formatCurrency(order.montant_total)}</span>
            {order.date_livraison_prevue && (
              <span>
                Livraison prévue: {formatDate(order.date_livraison_prevue)}
              </span>
            )}
          </div>
        </div>

        <div className="header-actions">
          <button
            className="btn btn-outline"
            onClick={() => navigate("/orders")}>
            ← Retour
          </button>

          <button
            className="btn btn-info"
            onClick={() => navigate(`/orders/${order.id}/edit`)}
            disabled={order.statut === "LIVREE" || order.statut === "ANNULEE"}>
            ✏️ Modifier
          </button>

          <button
            className="btn btn-primary"
            onClick={handleCreateInvoice}
            disabled={
              order.statut === "BROUILLON" || order.statut === "ANNULEE"
            }>
            📄 Créer facture
          </button>

          <div className="dropdown">
            <button className="btn btn-outline dropdown-toggle">⋮ Plus</button>
            <div className="dropdown-menu">
              <button
                className="dropdown-item"
                onClick={() => navigate(`/orders/${order.id}/duplicate`)}>
                📋 Dupliquer
              </button>
              <button
                className="dropdown-item"
                onClick={() =>
                  window.open(`/api/orders/${order.id}/export`, "_blank")
                }>
                📤 Exporter
              </button>
              <button
                className="dropdown-item"
                onClick={() => window.print()}>
                🖨️ Imprimer
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Actions de statut */}
      {nextActions.length > 0 && (
        <div className="status-actions">
          <h4>Actions disponibles</h4>
          <div className="status-buttons">
            {nextActions.map((action, index) => (
              <button
                key={index}
                className={`btn btn-${action.color}`}
                onClick={() => handleStatusChange(action.action)}
                disabled={actionLoading === "status"}>
                {actionLoading === "status" ? "⏳" : action.label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Informations client */}
      <div className="order-section">
        <h3>👤 Informations Client</h3>
        <div className="client-info-card">
          <div className="client-details">
            <h4>{order.client_nom}</h4>
            <p>📧 {order.client_email}</p>
            {order.client_telephone && <p>📞 {order.client_telephone}</p>}
            {order.client_adresse && <p>📍 {order.client_adresse}</p>}
          </div>
          <div className="client-actions">
            <button
              className="btn btn-outline btn-sm"
              onClick={() => navigate(`/clients/${order.client_id}`)}>
              👁️ Voir client
            </button>
          </div>
        </div>
      </div>

      {/* Lignes de commande */}
      <div className="order-section">
        <h3>📦 Produits Commandés</h3>
        <div className="order-lines-table">
          <table className="lines-table">
            <thead>
              <tr>
                <th>Produit</th>
                <th>Description</th>
                <th>Quantité</th>
                <th>Prix unitaire</th>
                <th>TVA</th>
                <th>Total HT</th>
                <th>Total TTC</th>
              </tr>
            </thead>
            <tbody>
              {order.lignes.map((ligne, index) => (
                <tr key={index}>
                  <td className="product-name">
                    <strong>{ligne.produit_nom}</strong>
                  </td>
                  <td className="product-description">{ligne.description}</td>
                  <td className="quantity">{ligne.quantite}</td>
                  <td className="unit-price">
                    {formatCurrency(ligne.prix_unitaire)}
                  </td>
                  <td className="vat-rate">{ligne.taux_tva}%</td>
                  <td className="amount-ht">
                    {formatCurrency(ligne.montant_ht)}
                  </td>
                  <td className="amount-ttc">
                    <strong>{formatCurrency(ligne.montant_ttc)}</strong>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Totaux */}
          <div className="order-totals">
            <div className="totals-table">
              <div className="total-row">
                <span>Total HT:</span>
                <span>{formatCurrency(order.montant_ht)}</span>
              </div>
              <div className="total-row">
                <span>TVA:</span>
                <span>{formatCurrency(order.montant_tva)}</span>
              </div>
              <div className="total-row final">
                <span>
                  <strong>Total TTC:</strong>
                </span>
                <span>
                  <strong>{formatCurrency(order.montant_total)}</strong>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Suivi de livraison */}
      <div className="order-section">
        <h3>🚚 Suivi de Livraison</h3>
        <div className="delivery-tracking">
          <div className="delivery-info">
            <div className="delivery-item">
              <label>Date de commande:</label>
              <span>{formatDate(order.date_commande)}</span>
            </div>
            {order.date_livraison_prevue && (
              <div className="delivery-item">
                <label>Livraison prévue:</label>
                <span className={isOverdue() ? "overdue" : ""}>
                  {formatDate(order.date_livraison_prevue)}
                  {isOverdue() && " ⚠️"}
                </span>
              </div>
            )}
            {order.date_livraison_reelle && (
              <div className="delivery-item">
                <label>Livraison réelle:</label>
                <span className="delivered">
                  {formatDate(order.date_livraison_reelle)}
                </span>
              </div>
            )}
          </div>

          {/* Timeline de statut */}
          <div className="status-timeline">
            <h4>📅 Historique</h4>
            <div className="timeline">
              {statusHistory.map((entry, index) => (
                <div
                  key={index}
                  className="timeline-item">
                  <div className="timeline-marker"></div>
                  <div className="timeline-content">
                    <div className="timeline-status">
                      <span
                        className={`status-badge ${getStatusColor(
                          entry.statut
                        )}`}>
                        {getStatusLabel(entry.statut)}
                      </span>
                    </div>
                    <div className="timeline-details">
                      <span className="timeline-date">
                        {formatDateTime(entry.date)}
                      </span>
                      <span className="timeline-user">
                        par {entry.utilisateur}
                      </span>
                      {entry.commentaire && (
                        <p className="timeline-comment">{entry.commentaire}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Notes */}
      {order.notes && (
        <div className="order-section">
          <h3>📝 Notes</h3>
          <div className="order-notes">
            <p>{order.notes}</p>
          </div>
        </div>
      )}

      {/* Actions rapides en bas */}
      <div className="detail-footer">
        <div className="footer-actions">
          <button
            className="btn btn-primary"
            onClick={handleCreateInvoice}
            disabled={
              order.statut === "BROUILLON" || order.statut === "ANNULEE"
            }>
            📄 Créer facture
          </button>

          <button
            className="btn btn-info"
            onClick={() => navigate(`/orders/${order.id}/edit`)}
            disabled={order.statut === "LIVREE" || order.statut === "ANNULEE"}>
            ✏️ Modifier
          </button>

          <button
            className="btn btn-secondary"
            onClick={() => navigate(`/orders/${order.id}/duplicate`)}>
            📋 Dupliquer
          </button>

          <button
            className="btn btn-outline"
            onClick={() =>
              window.open(`/api/orders/${order.id}/export`, "_blank")
            }>
            📤 Exporter
          </button>
        </div>

        <div className="footer-info">
          <span>Créée le {formatDateTime(order.created_at)}</span>
          <span>Modifiée le {formatDateTime(order.updated_at)}</span>
        </div>
      </div>
    </div>
  );
};

export default OrderDetail;
