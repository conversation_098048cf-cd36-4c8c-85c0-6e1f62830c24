/**
 * Paramètres système et sécurité
 */

import React, { useState, useEffect } from 'react';
import { useNotify } from '../Common/NotificationSystem';

interface SystemSettingsData {
  maintenance_mode: boolean;
  debug_mode: boolean;
  log_level: string;
  backup_auto: boolean;
  backup_frequence: string;
  retention_logs: number;
  max_file_size: number;
  allowed_file_types: string[];
  session_timeout: number;
  max_login_attempts: number;
  password_min_length: number;
  password_complexity: boolean;
  two_factor_auth: boolean;
}

const SystemSettings: React.FC = () => {
  const notify = useNotify();

  const [formData, setFormData] = useState<SystemSettingsData>({
    maintenance_mode: false,
    debug_mode: false,
    log_level: 'INFO',
    backup_auto: true,
    backup_frequence: 'daily',
    retention_logs: 30,
    max_file_size: 10 * 1024 * 1024, // 10MB
    allowed_file_types: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx'],
    session_timeout: 3600, // 1 heure
    max_login_attempts: 5,
    password_min_length: 8,
    password_complexity: true,
    two_factor_auth: false
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [systemInfo, setSystemInfo] = useState({
    version: '2.0.0',
    database_size: '125 MB',
    last_backup: '2024-08-10T08:00:00Z',
    uptime: '15 jours',
    active_users: 8
  });

  useEffect(() => {
    loadSystemSettings();
    loadSystemInfo();
  }, []);

  const loadSystemSettings = async () => {
    try {
      setLoading(true);
      // Simuler le chargement des paramètres
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error: any) {
      notify.error('Erreur lors du chargement des paramètres');
    } finally {
      setLoading(false);
    }
  };

  const loadSystemInfo = async () => {
    try {
      // Simuler le chargement des infos système
      setSystemInfo({
        version: '2.0.0',
        database_size: '125 MB',
        last_backup: '2024-08-10T08:00:00Z',
        uptime: '15 jours',
        active_users: 8
      });
    } catch (error: any) {
      console.error('Erreur chargement infos système:', error);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleFileTypesChange = (type: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      allowed_file_types: checked
        ? [...prev.allowed_file_types, type]
        : prev.allowed_file_types.filter(t => t !== type)
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (formData.retention_logs < 1 || formData.retention_logs > 365) {
      newErrors.retention_logs = 'Rétention entre 1 et 365 jours';
    }

    if (formData.max_file_size < 1024 || formData.max_file_size > 100 * 1024 * 1024) {
      newErrors.max_file_size = 'Taille entre 1KB et 100MB';
    }

    if (formData.session_timeout < 300 || formData.session_timeout > 86400) {
      newErrors.session_timeout = 'Timeout entre 5 minutes et 24h';
    }

    if (formData.max_login_attempts < 1 || formData.max_login_attempts > 20) {
      newErrors.max_login_attempts = 'Tentatives entre 1 et 20';
    }

    if (formData.password_min_length < 4 || formData.password_min_length > 50) {
      newErrors.password_min_length = 'Longueur entre 4 et 50 caractères';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notify.error('Veuillez corriger les erreurs du formulaire');
      return;
    }

    try {
      setSaving(true);
      
      // Simuler la sauvegarde
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      notify.success('Paramètres système mis à jour avec succès');
    } catch (error: any) {
      notify.error('Erreur lors de la sauvegarde');
    } finally {
      setSaving(false);
    }
  };

  const handleBackupNow = async () => {
    try {
      notify.info('Sauvegarde en cours...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      notify.success('Sauvegarde créée avec succès');
      loadSystemInfo();
    } catch (error: any) {
      notify.error('Erreur lors de la sauvegarde');
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}min`;
    }
    return `${minutes} minutes`;
  };

  if (loading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des paramètres...</p>
      </div>
    );
  }

  return (
    <div className="system-settings">
      {/* Informations système */}
      <div className="settings-section">
        <h3>ℹ️ Informations Système</h3>
        
        <div className="system-info">
          <div className="info-grid">
            <div className="info-card">
              <div className="info-icon">🚀</div>
              <div className="info-content">
                <h4>Version</h4>
                <span>{systemInfo.version}</span>
              </div>
            </div>
            
            <div className="info-card">
              <div className="info-icon">💾</div>
              <div className="info-content">
                <h4>Base de données</h4>
                <span>{systemInfo.database_size}</span>
              </div>
            </div>
            
            <div className="info-card">
              <div className="info-icon">⏰</div>
              <div className="info-content">
                <h4>Uptime</h4>
                <span>{systemInfo.uptime}</span>
              </div>
            </div>
            
            <div className="info-card">
              <div className="info-icon">👥</div>
              <div className="info-content">
                <h4>Utilisateurs actifs</h4>
                <span>{systemInfo.active_users}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Mode maintenance */}
        <div className="settings-section">
          <h3>🔧 Maintenance</h3>
          
          <div className="form-grid">
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.maintenance_mode}
                  onChange={(e) => handleInputChange('maintenance_mode', e.target.checked)}
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  🚧 Mode maintenance
                </span>
              </label>
              <small className="form-help">
                Désactiver l'accès pour maintenance
              </small>
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.debug_mode}
                  onChange={(e) => handleInputChange('debug_mode', e.target.checked)}
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  🐛 Mode debug
                </span>
              </label>
              <small className="form-help">
                Afficher les informations de débogage
              </small>
            </div>

            <div className="form-group">
              <label>Niveau de logs</label>
              <select
                value={formData.log_level}
                onChange={(e) => handleInputChange('log_level', e.target.value)}
                className="form-select"
              >
                <option value="DEBUG">🔍 DEBUG</option>
                <option value="INFO">ℹ️ INFO</option>
                <option value="WARNING">⚠️ WARNING</option>
                <option value="ERROR">❌ ERROR</option>
                <option value="CRITICAL">🚨 CRITICAL</option>
              </select>
            </div>

            <div className="form-group">
              <label>Rétention des logs (jours)</label>
              <input
                type="number"
                min="1"
                max="365"
                value={formData.retention_logs}
                onChange={(e) => handleInputChange('retention_logs', parseInt(e.target.value) || 30)}
                className={`form-input ${errors.retention_logs ? 'error' : ''}`}
              />
              {errors.retention_logs && <span className="error-text">{errors.retention_logs}</span>}
            </div>
          </div>
        </div>

        {/* Sauvegardes */}
        <div className="settings-section">
          <div className="section-header">
            <h3>💾 Sauvegardes</h3>
            <button
              type="button"
              className="btn btn-info btn-sm"
              onClick={handleBackupNow}
            >
              💾 Sauvegarder maintenant
            </button>
          </div>
          
          <div className="form-grid">
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.backup_auto}
                  onChange={(e) => handleInputChange('backup_auto', e.target.checked)}
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  🔄 Sauvegardes automatiques
                </span>
              </label>
            </div>

            <div className="form-group">
              <label>Fréquence</label>
              <select
                value={formData.backup_frequence}
                onChange={(e) => handleInputChange('backup_frequence', e.target.value)}
                className="form-select"
                disabled={!formData.backup_auto}
              >
                <option value="daily">📅 Quotidienne</option>
                <option value="weekly">📆 Hebdomadaire</option>
                <option value="monthly">🗓️ Mensuelle</option>
              </select>
            </div>

            <div className="form-group">
              <label>Dernière sauvegarde</label>
              <input
                type="text"
                value={new Date(systemInfo.last_backup).toLocaleString('fr-FR')}
                readOnly
                className="form-input readonly"
              />
            </div>
          </div>
        </div>

        {/* Sécurité */}
        <div className="settings-section">
          <h3>🔒 Sécurité</h3>
          
          <div className="form-grid">
            <div className="form-group">
              <label>Timeout de session (secondes)</label>
              <input
                type="number"
                min="300"
                max="86400"
                value={formData.session_timeout}
                onChange={(e) => handleInputChange('session_timeout', parseInt(e.target.value) || 3600)}
                className={`form-input ${errors.session_timeout ? 'error' : ''}`}
              />
              {errors.session_timeout && <span className="error-text">{errors.session_timeout}</span>}
              <small className="form-help">
                Actuellement: {formatDuration(formData.session_timeout)}
              </small>
            </div>

            <div className="form-group">
              <label>Tentatives de connexion max</label>
              <input
                type="number"
                min="1"
                max="20"
                value={formData.max_login_attempts}
                onChange={(e) => handleInputChange('max_login_attempts', parseInt(e.target.value) || 5)}
                className={`form-input ${errors.max_login_attempts ? 'error' : ''}`}
              />
              {errors.max_login_attempts && <span className="error-text">{errors.max_login_attempts}</span>}
            </div>

            <div className="form-group">
              <label>Longueur minimum mot de passe</label>
              <input
                type="number"
                min="4"
                max="50"
                value={formData.password_min_length}
                onChange={(e) => handleInputChange('password_min_length', parseInt(e.target.value) || 8)}
                className={`form-input ${errors.password_min_length ? 'error' : ''}`}
              />
              {errors.password_min_length && <span className="error-text">{errors.password_min_length}</span>}
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.password_complexity}
                  onChange={(e) => handleInputChange('password_complexity', e.target.checked)}
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  🔐 Complexité des mots de passe
                </span>
              </label>
              <small className="form-help">
                Exiger majuscules, minuscules, chiffres et caractères spéciaux
              </small>
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.two_factor_auth}
                  onChange={(e) => handleInputChange('two_factor_auth', e.target.checked)}
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  📱 Authentification à deux facteurs
                </span>
              </label>
              <small className="form-help">
                Activer l'A2F pour tous les utilisateurs
              </small>
            </div>
          </div>
        </div>

        {/* Fichiers */}
        <div className="settings-section">
          <h3>📁 Gestion des Fichiers</h3>
          
          <div className="form-grid">
            <div className="form-group">
              <label>Taille maximum (bytes)</label>
              <input
                type="number"
                min="1024"
                max={100 * 1024 * 1024}
                value={formData.max_file_size}
                onChange={(e) => handleInputChange('max_file_size', parseInt(e.target.value) || 10485760)}
                className={`form-input ${errors.max_file_size ? 'error' : ''}`}
              />
              {errors.max_file_size && <span className="error-text">{errors.max_file_size}</span>}
              <small className="form-help">
                Actuellement: {formatFileSize(formData.max_file_size)}
              </small>
            </div>

            <div className="form-group full-width">
              <label>Types de fichiers autorisés</label>
              <div className="file-types-grid">
                {['pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv', 'zip'].map(type => (
                  <label key={type} className="checkbox-label file-type">
                    <input
                      type="checkbox"
                      checked={formData.allowed_file_types.includes(type)}
                      onChange={(e) => handleFileTypesChange(type, e.target.checked)}
                      className="form-checkbox"
                    />
                    <span className="checkbox-text">.{type}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Actions système */}
        <div className="settings-section">
          <h3>⚡ Actions Système</h3>
          
          <div className="system-actions">
            <div className="action-card">
              <div className="action-info">
                <h4>🗑️ Nettoyer les logs</h4>
                <p>Supprimer les anciens fichiers de logs</p>
              </div>
              <button
                type="button"
                className="btn btn-warning btn-sm"
                onClick={() => {
                  if (window.confirm('Supprimer les anciens logs ?')) {
                    notify.success('Logs nettoyés avec succès');
                  }
                }}
              >
                🧹 Nettoyer
              </button>
            </div>

            <div className="action-card">
              <div className="action-info">
                <h4>🔄 Redémarrer l'application</h4>
                <p>Redémarrer pour appliquer certains changements</p>
              </div>
              <button
                type="button"
                className="btn btn-danger btn-sm"
                onClick={() => {
                  if (window.confirm('Redémarrer l\'application ?')) {
                    notify.info('Redémarrage en cours...');
                  }
                }}
              >
                🔄 Redémarrer
              </button>
            </div>

            <div className="action-card">
              <div className="action-info">
                <h4>📊 Optimiser la base</h4>
                <p>Optimiser les performances de la base de données</p>
              </div>
              <button
                type="button"
                className="btn btn-info btn-sm"
                onClick={() => {
                  notify.info('Optimisation en cours...');
                  setTimeout(() => notify.success('Base optimisée'), 2000);
                }}
              >
                ⚡ Optimiser
              </button>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="settings-actions">
          <button
            type="button"
            className="btn btn-outline"
            onClick={loadSystemSettings}
            disabled={saving}
          >
            🔄 Annuler
          </button>
          
          <button
            type="submit"
            className="btn btn-primary"
            disabled={saving}
          >
            {saving ? '⏳ Sauvegarde...' : '💾 Sauvegarder'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SystemSettings;
