"""
APIs intelligentes et avancées - Backend uniquement
"""
from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from datetime import datetime, timedelta
from django.utils import timezone
import json

from .models import SmartNotification, AIInsight, AuditLog, SystemHealth
from .services import IntelligentDashboardService, SmartNotificationService, AutomationService
from .utils import AIAnalyzer, SmartCache


# Vue HTML supprimée - Interface gérée par React
# Toutes les données sont disponibles via les APIs ci-dessous


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def smart_insights_api(request):
    """API pour récupérer les insights intelligents"""
    try:
        insights = IntelligentDashboardService.generate_smart_insights(request.user)
        return Response({
            'success': True,
            'insights': insights,
            'generated_at': timezone.now().isoformat()
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_stats_api(request):
    """API pour les statistiques principales du dashboard"""
    try:
        from django.db.models import Sum, Count, Q
        from Facturation.models import Facture
        from Clients.models import Client
        from Produits.models import Produit
        from Commandes.models import Commande
        from datetime import datetime, timedelta

        # Période actuelle
        now = timezone.now()
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        start_of_today = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Statistiques des factures
        factures_mois = Facture.objects.filter(
            date_creation__gte=start_of_month,
            statut__in=['VALIDEE', 'PAYEE']
        )

        # Chiffre d'affaires mensuel
        ca_mensuel = factures_mois.aggregate(
            total=Sum('montant_total_ttc')
        )['total'] or 0

        # Ventes aujourd'hui
        ventes_aujourd_hui = Facture.objects.filter(
            date_creation__gte=start_of_today,
            statut__in=['VALIDEE', 'PAYEE']
        ).aggregate(total=Sum('montant_total_ttc'))['total'] or 0

        # Nombre de factures ce mois
        nb_factures_mois = factures_mois.count()

        # Clients actifs (ayant une facture dans les 30 derniers jours)
        clients_actifs = Client.objects.filter(
            factures__date_creation__gte=now - timedelta(days=30)
        ).distinct().count()

        # Commandes en attente
        commandes_attente = Commande.objects.filter(
            statut__in=['EN_ATTENTE', 'CONFIRMEE']
        ).count()

        # Produits en stock faible
        from django.db import models
        produits_stock_faible = Produit.objects.filter(
            Q(stock_actuel__lte=models.F('stock_minimum')) |
            Q(stock_actuel=0)
        ).count()

        # Taux de conversion (factures payées / factures créées)
        factures_creees = Facture.objects.filter(date_creation__gte=start_of_month).count()
        factures_payees = Facture.objects.filter(
            date_creation__gte=start_of_month,
            statut='PAYEE'
        ).count()

        taux_conversion = (factures_payees / factures_creees * 100) if factures_creees > 0 else 0

        stats = {
            'chiffre_affaires_mensuel': float(ca_mensuel),
            'nombre_factures_mois': nb_factures_mois,
            'nombre_clients_actifs': clients_actifs,
            'commandes_en_attente': commandes_attente,
            'produits_stock_faible': produits_stock_faible,
            'ventes_aujourd_hui': float(ventes_aujourd_hui),
            'objectif_mensuel': 150000,  # À configurer dans les paramètres
            'taux_conversion': round(taux_conversion, 1),
            'periode': {
                'debut_mois': start_of_month.isoformat(),
                'maintenant': now.isoformat()
            }
        }

        return Response({
            'success': True,
            'data': stats,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def kpi_dashboard_api(request):
    """API pour les KPIs du dashboard"""
    try:
        kpis = IntelligentDashboardService.get_kpi_dashboard(request.user)
        return Response({
            'success': True,
            'kpis': kpis,
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_recent_data_api(request):
    """API pour les données récentes du dashboard"""
    try:
        from Facturation.models import Facture
        from Facturation.serializers import FactureSerializer
        from Clients.models import Client
        from Clients.serializers import ClientSerializer
        from Produits.models import Produit
        from Produits.serializers import ProduitSerializer
        from Commandes.models import Commande
        from Commandes.serializers import CommandeSerializer
        from django.db import models

        # Factures récentes (5 dernières)
        recent_invoices = Facture.objects.select_related('client').order_by('-date_creation')[:5]
        invoices_data = FactureSerializer(recent_invoices, many=True).data

        # Clients récents (5 derniers)
        recent_clients = Client.objects.order_by('-date_creation')[:5]
        clients_data = ClientSerializer(recent_clients, many=True).data

        # Produits les plus vendus (top 5)
        from django.db.models import Sum
        top_products = Produit.objects.annotate(
            total_vendu=Sum('lignes_facture__quantite')
        ).filter(total_vendu__isnull=False).order_by('-total_vendu')[:5]
        products_data = ProduitSerializer(top_products, many=True).data

        # Commandes récentes (5 dernières)
        recent_orders = Commande.objects.select_related('client').order_by('-date_creation')[:5]
        orders_data = CommandeSerializer(recent_orders, many=True).data

        # Alertes de stock
        stock_alerts = Produit.objects.filter(
            stock_actuel__lte=models.F('stock_minimum')
        ).order_by('stock_actuel')[:10]
        alerts_data = ProduitSerializer(stock_alerts, many=True).data

        # Activité récente (simulation basée sur les factures)
        recent_activity = []
        for invoice in recent_invoices:
            recent_activity.append({
                'type': 'facture',
                'title': f'Nouvelle facture #{invoice.numero}',
                'description': f'Facture créée pour {invoice.client.nom}',
                'timestamp': invoice.date_creation.isoformat(),
                'amount': float(invoice.montant_total_ttc) if invoice.montant_total_ttc else 0,
                'status': invoice.statut
            })

        return Response({
            'success': True,
            'data': {
                'recent_invoices': invoices_data,
                'recent_clients': clients_data,
                'top_products': products_data,
                'recent_orders': orders_data,
                'stock_alerts': alerts_data,
                'recent_activity': recent_activity
            },
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def smart_price_suggestion_api(request):
    """API pour suggestions de prix intelligentes"""
    try:
        product_name = request.data.get('product_name', '')
        category = request.data.get('category', '')
        
        if not product_name:
            return Response({
                'success': False,
                'error': 'Nom du produit requis'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        suggestion = AutomationService.smart_price_suggestion(product_name, category)
        
        return Response({
            'success': True,
            'suggestion': suggestion
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def auto_generate_invoice_number_api(request):
    """API pour génération automatique de numéros de facture"""
    try:
        client_code = request.data.get('client_code', '')
        invoice_number = AutomationService.auto_generate_invoice_number(client_code)
        
        return Response({
            'success': True,
            'invoice_number': invoice_number
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def notifications_api(request):
    """API pour les notifications intelligentes"""
    try:
        notifications = SmartNotification.objects.filter(
            user=request.user
        ).order_by('-created_at')
        
        # Filtres
        unread_only = request.GET.get('unread_only', 'false').lower() == 'true'
        if unread_only:
            notifications = notifications.filter(is_read=False)
        
        priority = request.GET.get('priority')
        if priority:
            notifications = notifications.filter(priority=priority.upper())
        
        # Pagination
        limit = int(request.GET.get('limit', 20))
        notifications = notifications[:limit]
        
        notifications_data = []
        for notif in notifications:
            notifications_data.append({
                'id': str(notif.id),
                'title': notif.title,
                'message': notif.message,
                'type': notif.type,
                'priority': notif.priority,
                'is_read': notif.is_read,
                'created_at': notif.created_at.isoformat(),
                'action_url': notif.action_url,
                'action_label': notif.action_label
            })
        
        return Response({
            'success': True,
            'notifications': notifications_data,
            'total_unread': SmartNotification.objects.filter(
                user=request.user, is_read=False
            ).count()
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read_api(request, notification_id):
    """API pour marquer une notification comme lue"""
    try:
        notification = SmartNotification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.mark_as_read()
        
        return Response({
            'success': True,
            'message': 'Notification marquée comme lue'
        })
    except SmartNotification.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Notification non trouvée'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def analyze_sales_trend_api(request):
    """API pour analyse des tendances de vente"""
    try:
        # Récupération des données de vente
        from Facturation.models import Facture
        
        days = int(request.data.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)
        
        sales_data = list(Facture.objects.filter(
            date_emission__gte=start_date.date()
        ).values('date_emission', 'montant_ttc'))
        
        # Conversion pour l'analyse
        analysis_data = [
            {'amount': float(item['montant_ttc'])}
            for item in sales_data
        ]
        
        analysis = AIAnalyzer.analyze_sales_trend(analysis_data)
        
        return Response({
            'success': True,
            'analysis': analysis,
            'period_days': days,
            'data_points': len(analysis_data)
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def predict_stock_api(request):
    """API pour prédiction de stock"""
    try:
        product_id = request.data.get('product_id')
        if not product_id:
            return Response({
                'success': False,
                'error': 'ID du produit requis'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        from Facturation.models import Produit, LigneFacture
        
        # Récupération du produit
        product = Produit.objects.get(id=product_id)
        
        # Historique des ventes (30 derniers jours)
        sales_history = list(LigneFacture.objects.filter(
            produit=product,
            facture__date_emission__gte=timezone.now().date() - timedelta(days=30)
        ).values('quantite', 'facture__date_emission'))
        
        product_data = {
            'stock': product.stock_actuel,
            'name': product.nom
        }
        
        prediction = AIAnalyzer.predict_stock_needs(product_data, sales_history)
        
        return Response({
            'success': True,
            'product': {
                'id': product.id,
                'name': product.nom,
                'current_stock': product.stock_actuel
            },
            'prediction': prediction
        })
    except Produit.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Produit non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def system_health_api(request):
    """API pour la santé du système"""
    try:
        # Vérification des composants
        health_checks = []
        
        # Base de données
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            health_checks.append({
                'component': 'DATABASE',
                'status': 'HEALTHY',
                'response_time': 0.001
            })
        except Exception as e:
            health_checks.append({
                'component': 'DATABASE',
                'status': 'CRITICAL',
                'error': str(e)
            })
        
        # Cache
        try:
            from django.core.cache import cache
            cache.set('health_check', 'ok', 10)
            cache.get('health_check')
            health_checks.append({
                'component': 'CACHE',
                'status': 'HEALTHY'
            })
        except Exception as e:
            health_checks.append({
                'component': 'CACHE',
                'status': 'WARNING',
                'error': str(e)
            })
        
        # Stockage des résultats
        for check in health_checks:
            SystemHealth.objects.create(**check)
        
        overall_status = 'HEALTHY'
        if any(check['status'] == 'CRITICAL' for check in health_checks):
            overall_status = 'CRITICAL'
        elif any(check['status'] == 'WARNING' for check in health_checks):
            overall_status = 'WARNING'
        
        return Response({
            'success': True,
            'overall_status': overall_status,
            'components': health_checks,
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
