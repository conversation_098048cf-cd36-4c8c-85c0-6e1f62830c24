{"name": "abm-frontend", "version": "2.0.0", "description": "ABM - Advanced Business Management Frontend", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@reduxjs/toolkit": "^2.0.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-toastify": "^4.0.2", "axios": "^1.6.2", "date-fns": "^2.30.0", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-redux": "^9.0.4", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.8.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1"}, "proxy": "http://localhost:8000"}