import React, { useState, useEffect } from 'react';

const PaymentReconciliation: React.FC = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setTimeout(() => setLoading(false), 500);
  }, []);

  if (loading) {
    return (
      <div className="loading-spinner">
        <div>Chargement du rapprochement bancaire...</div>
      </div>
    );
  }

  return (
    <div className="payment-reconciliation">
      <div className="coming-soon">
        <div className="coming-soon-icon">🏦</div>
        <h3>Rapprochement Bancaire</h3>
        <p>Module en cours de développement</p>
        <p>Fonctionnalités prévues :</p>
        <ul>
          <li>🔄 Import relevés bancaires</li>
          <li>🎯 Rapprochement automatique</li>
          <li>📊 Suivi des écarts</li>
          <li>📄 Export comptable</li>
        </ul>
      </div>
    </div>
  );
};

export default PaymentReconciliation;
