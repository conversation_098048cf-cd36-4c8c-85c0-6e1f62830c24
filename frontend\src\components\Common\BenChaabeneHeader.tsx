/**
 * Composant d'en-tête avec branding Ben Chaabène
 * Réutilisable dans toute l'application
 */

import React from 'react';
import './BenChaabeneHeader.css';

interface BenChaabeneHeaderProps {
  title?: string;
  subtitle?: string;
  showLogo?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const BenChaabeneHeader: React.FC<BenChaabeneHeaderProps> = ({
  title = "Société Ben Chaabène de Commerce",
  subtitle = "Système de Gestion d'Entreprise",
  showLogo = true,
  size = 'medium',
  className = ''
}) => {
  return (
    <div className={`ben-chaabene-header ${size} ${className}`}>
      {showLogo && (
        <div className="header-logo">
          <img 
            src="/logo_ben_chaabene.png" 
            alt="Ben Chaabène" 
            className="company-logo"
            onError={(e) => {
              // Fallback vers l'icône si le logo ne charge pas
              e.currentTarget.style.display = 'none';
              const fallback = e.currentTarget.nextElementSibling as HTMLElement;
              if (fallback) {
                fallback.style.display = 'block';
              }
            }}
          />
          <div className="logo-fallback" style={{ display: 'none' }}>
            🏢
          </div>
        </div>
      )}
      
      <div className="header-text">
        <h1 className="header-title">{title}</h1>
        {subtitle && <p className="header-subtitle">{subtitle}</p>}
      </div>
    </div>
  );
};

export default BenChaabeneHeader;
