"""
Service d'envoi d'emails pour l'authentification
"""

from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class EmailService:
    """Service pour l'envoi d'emails d'authentification"""
    
    @staticmethod
    def send_password_reset_code(user, code, email):
        """
        Envoie un code de récupération de mot de passe par email
        
        Args:
            user: L'utilisateur qui demande la récupération
            code: Le code de vérification à 6 chiffres
            email: L'adresse email de destination
        
        Returns:
            bool: True si l'email a été envoyé avec succès
        """
        try:
            subject = "Code de récupération de mot de passe - ABM"
            
            # Contexte pour le template
            context = {
                'user': user,
                'code': code,
                'company_name': 'ABM - Ste Ben Chaabène de Commerce',
                'support_email': settings.EMAIL_HOST_USER or '<EMAIL>',
                'validity_minutes': 15,
            }
            
            # Rendu du template HTML
            html_message = render_to_string(
                'authentication/password_reset_email.html',
                context
            )
            
            # Version texte (fallback)
            plain_message = strip_tags(html_message)
            
            # Envoi de l'email
            success = send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.EMAIL_HOST_USER or '<EMAIL>',
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False,
            )
            
            if success:
                logger.info(f"Code de récupération envoyé à {email} pour l'utilisateur {user.username}")
                return True
            else:
                logger.error(f"Échec de l'envoi du code de récupération à {email}")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de l'email de récupération: {str(e)}")
            return False
    
    @staticmethod
    def send_password_changed_notification(user, email):
        """
        Envoie une notification de changement de mot de passe
        
        Args:
            user: L'utilisateur dont le mot de passe a été changé
            email: L'adresse email de destination
        
        Returns:
            bool: True si l'email a été envoyé avec succès
        """
        try:
            subject = "Mot de passe modifié - ABM"
            
            # Contexte pour le template
            context = {
                'user': user,
                'company_name': 'ABM - Ste Ben Chaabène de Commerce',
                'support_email': settings.EMAIL_HOST_USER or '<EMAIL>',
            }
            
            # Rendu du template HTML
            html_message = render_to_string(
                'authentication/password_changed_email.html',
                context
            )
            
            # Version texte (fallback)
            plain_message = strip_tags(html_message)
            
            # Envoi de l'email
            success = send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.EMAIL_HOST_USER or '<EMAIL>',
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False,
            )
            
            if success:
                logger.info(f"Notification de changement de mot de passe envoyée à {email}")
                return True
            else:
                logger.error(f"Échec de l'envoi de la notification à {email}")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de la notification: {str(e)}")
            return False
    
    @staticmethod
    def send_welcome_email(user, email):
        """
        Envoie un email de bienvenue lors de l'inscription
        
        Args:
            user: Le nouvel utilisateur
            email: L'adresse email de destination
        
        Returns:
            bool: True si l'email a été envoyé avec succès
        """
        try:
            subject = "Bienvenue sur ABM - Votre compte a été créé"
            
            # Contexte pour le template
            context = {
                'user': user,
                'company_name': 'ABM - Ste Ben Chaabène de Commerce',
                'support_email': settings.EMAIL_HOST_USER or '<EMAIL>',
                'login_url': 'http://localhost:3000/login',
            }
            
            # Rendu du template HTML
            html_message = render_to_string(
                'authentication/welcome_email.html',
                context
            )
            
            # Version texte (fallback)
            plain_message = strip_tags(html_message)
            
            # Envoi de l'email
            success = send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.EMAIL_HOST_USER or '<EMAIL>',
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False,
            )
            
            if success:
                logger.info(f"Email de bienvenue envoyé à {email}")
                return True
            else:
                logger.error(f"Échec de l'envoi de l'email de bienvenue à {email}")
                return False
                
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de l'email de bienvenue: {str(e)}")
            return False
