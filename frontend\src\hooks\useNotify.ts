import { useContext } from 'react';
import { NotificationContext } from '../components/Common/NotificationSystem';

export const useNotify = () => {
  const context = useContext(NotificationContext);
  
  if (!context) {
    // Fallback si le contexte n'est pas disponible
    return {
      success: (message: string) => console.log('✅', message),
      error: (message: string) => console.error('❌', message),
      warning: (message: string) => console.warn('⚠️', message),
      info: (message: string) => console.info('ℹ️', message),
    };
  }
  
  return context;
};

export default useNotify;
