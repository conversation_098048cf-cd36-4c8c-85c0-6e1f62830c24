"""
Serializers pour l'API E-commerce
"""

from rest_framework import serializers
from .models import (
    CategorieEcommerce, ProduitEcommerce, ImageProduitEcommerce,
    Panier, ItemPanier, CommandeEcommerce, LigneCommandeEcommerce
)
from Produits.serializers import ProduitSerializer


class CategorieEcommerceSerializer(serializers.ModelSerializer):
    """Serializer pour les catégories e-commerce"""
    enfants = serializers.SerializerMethodField()
    chemin_complet = serializers.ReadOnlyField()
    
    class Meta:
        model = CategorieEcommerce
        fields = [
            'id', 'nom', 'slug', 'description', 'image', 'parent',
            'ordre', 'actif', 'meta_title', 'meta_description',
            'enfants', 'chemin_complet', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_enfants(self, obj):
        """Retourne les catégories enfants"""
        enfants = obj.enfants.filter(actif=True).order_by('ordre', 'nom')
        return CategorieEcommerceSerializer(enfants, many=True, context=self.context).data


class ImageProduitEcommerceSerializer(serializers.ModelSerializer):
    """Serializer pour les images de produits e-commerce"""
    
    class Meta:
        model = ImageProduitEcommerce
        fields = ['id', 'image', 'alt_text', 'ordre', 'principale']


class ProduitEcommerceSerializer(serializers.ModelSerializer):
    """Serializer pour les produits e-commerce"""
    produit = ProduitSerializer(read_only=True)
    images = ImageProduitEcommerceSerializer(many=True, read_only=True)
    categories_ecommerce = CategorieEcommerceSerializer(many=True, read_only=True)
    prix_actuel = serializers.ReadOnlyField()
    stock_disponible_reel = serializers.ReadOnlyField()
    peut_etre_commande = serializers.ReadOnlyField()
    
    class Meta:
        model = ProduitEcommerce
        fields = [
            'id', 'produit', 'slug', 'categories_ecommerce', 'images',
            'visible_en_ligne', 'en_vedette', 'nouveau', 'en_promotion',
            'prix_public', 'prix_promo', 'date_debut_promo', 'date_fin_promo',
            'prix_actuel', 'stock_disponible', 'stock_reserve', 'stock_disponible_reel',
            'gestion_stock', 'autoriser_commande_sans_stock', 'peut_etre_commande',
            'meta_title', 'meta_description', 'meta_keywords',
            'description_courte', 'description_longue', 'caracteristiques',
            'poids', 'dimensions_longueur', 'dimensions_largeur', 'dimensions_hauteur',
            'nombre_vues', 'nombre_ventes', 'note_moyenne', 'nombre_avis',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'nombre_vues', 'nombre_ventes', 'note_moyenne', 'nombre_avis',
            'created_at', 'updated_at'
        ]


class ProduitEcommerceCatalogueSerializer(serializers.ModelSerializer):
    """Serializer simplifié pour le catalogue (liste des produits)"""
    produit_nom = serializers.CharField(source='produit.nom', read_only=True)
    produit_code = serializers.CharField(source='produit.code_produit', read_only=True)
    image_principale = serializers.SerializerMethodField()
    prix_actuel = serializers.ReadOnlyField()
    peut_etre_commande = serializers.ReadOnlyField()
    
    class Meta:
        model = ProduitEcommerce
        fields = [
            'id', 'slug', 'produit_nom', 'produit_code', 'image_principale',
            'en_vedette', 'nouveau', 'en_promotion', 'prix_public', 'prix_promo',
            'prix_actuel', 'description_courte', 'note_moyenne', 'nombre_avis',
            'peut_etre_commande'
        ]
    
    def get_image_principale(self, obj):
        """Retourne l'image principale du produit"""
        image_principale = obj.images.filter(principale=True).first()
        if image_principale:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(image_principale.image.url)
            return image_principale.image.url
        return None


class ItemPanierSerializer(serializers.ModelSerializer):
    """Serializer pour les articles du panier"""
    produit_nom = serializers.CharField(source='produit_ecommerce.produit.nom', read_only=True)
    produit_slug = serializers.CharField(source='produit_ecommerce.slug', read_only=True)
    image_principale = serializers.SerializerMethodField()
    total_ht = serializers.ReadOnlyField()
    total_tva = serializers.ReadOnlyField()
    total_ttc = serializers.ReadOnlyField()
    
    class Meta:
        model = ItemPanier
        fields = [
            'id', 'produit_ecommerce', 'produit_nom', 'produit_slug',
            'image_principale', 'quantite', 'prix_unitaire',
            'total_ht', 'total_tva', 'total_ttc', 'created_at', 'updated_at'
        ]
        read_only_fields = ['prix_unitaire', 'created_at', 'updated_at']
    
    def get_image_principale(self, obj):
        """Retourne l'image principale du produit"""
        image_principale = obj.produit_ecommerce.images.filter(principale=True).first()
        if image_principale:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(image_principale.image.url)
            return image_principale.image.url
        return None


class PanierSerializer(serializers.ModelSerializer):
    """Serializer pour le panier"""
    items = ItemPanierSerializer(many=True, read_only=True)
    total_articles = serializers.ReadOnlyField()
    total_ht = serializers.ReadOnlyField()
    total_tva = serializers.ReadOnlyField()
    total_ttc = serializers.ReadOnlyField()
    
    class Meta:
        model = Panier
        fields = [
            'id', 'items', 'total_articles', 'total_ht', 'total_tva', 'total_ttc',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class AjouterAuPanierSerializer(serializers.Serializer):
    """Serializer pour ajouter un produit au panier"""
    produit_ecommerce_id = serializers.IntegerField()
    quantite = serializers.IntegerField(min_value=1)
    
    def validate_produit_ecommerce_id(self, value):
        """Valide que le produit existe et peut être commandé"""
        try:
            produit = ProduitEcommerce.objects.get(id=value)
            if not produit.peut_etre_commande:
                raise serializers.ValidationError("Ce produit ne peut pas être commandé actuellement.")
            return value
        except ProduitEcommerce.DoesNotExist:
            raise serializers.ValidationError("Produit non trouvé.")


class LigneCommandeEcommerceSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de commande e-commerce"""
    total_ht = serializers.ReadOnlyField()
    total_tva = serializers.ReadOnlyField()
    total_ttc = serializers.ReadOnlyField()
    
    class Meta:
        model = LigneCommandeEcommerce
        fields = [
            'id', 'produit_ecommerce', 'nom_produit', 'code_produit',
            'quantite', 'prix_unitaire', 'taux_tva',
            'total_ht', 'total_tva', 'total_ttc', 'created_at'
        ]
        read_only_fields = [
            'nom_produit', 'code_produit', 'prix_unitaire', 'taux_tva', 'created_at'
        ]


class CommandeEcommerceSerializer(serializers.ModelSerializer):
    """Serializer pour les commandes e-commerce"""
    lignes = LigneCommandeEcommerceSerializer(many=True, read_only=True)
    client_nom = serializers.CharField(source='client.nom_complet', read_only=True)
    
    class Meta:
        model = CommandeEcommerce
        fields = [
            'id', 'numero', 'client', 'client_nom', 'statut', 'lignes',
            'total_ht', 'total_tva', 'total_ttc', 'frais_livraison',
            'adresse_facturation', 'adresse_livraison',
            'transporteur', 'numero_suivi', 'date_expedition',
            'date_livraison_prevue', 'date_livraison_reelle',
            'methode_paiement', 'statut_paiement', 'reference_paiement',
            'notes_client', 'notes_interne', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'numero', 'total_ht', 'total_tva', 'total_ttc',
            'created_at', 'updated_at'
        ]


class CommandeEcommerceCreateSerializer(serializers.ModelSerializer):
    """Serializer pour créer une commande e-commerce"""
    
    class Meta:
        model = CommandeEcommerce
        fields = [
            'client', 'adresse_facturation', 'adresse_livraison',
            'notes_client', 'methode_paiement'
        ]
    
    def validate(self, data):
        """Validation des données de commande"""
        # Vérifier que le client a un panier avec des articles
        client = data.get('client')
        if client:
            panier = Panier.objects.filter(client=client).first()
            if not panier or not panier.items.exists():
                raise serializers.ValidationError("Le panier est vide.")
        
        return data


class StatistiquesEcommerceSerializer(serializers.Serializer):
    """Serializer pour les statistiques e-commerce"""
    total_commandes = serializers.IntegerField()
    chiffre_affaires = serializers.DecimalField(max_digits=15, decimal_places=2)
    commandes_en_attente = serializers.IntegerField()
    produits_en_ligne = serializers.IntegerField()
    paniers_actifs = serializers.IntegerField()
    top_produits = serializers.ListField()
    commandes_recentes = CommandeEcommerceSerializer(many=True, read_only=True)
