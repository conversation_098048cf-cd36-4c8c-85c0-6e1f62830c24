"""
Administration intelligente pour le module Core
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
import json

from .models import (
    AuditLog, SmartNotification, AIInsight,
    SmartTag, SystemHealth
)


class AuditLogAdmin(admin.ModelAdmin):
    """Administration des journaux d'audit"""
    list_display = ['timestamp', 'user', 'action', 'model_name', 'object_repr', 'ip_address']
    list_filter = ['action', 'model_name', 'timestamp']
    search_fields = ['user__username', 'model_name', 'object_repr']
    readonly_fields = ['timestamp', 'changes_display']
    date_hierarchy = 'timestamp'

    def changes_display(self, obj):
        """Affichage formaté des changements"""
        if obj.changes:
            return format_html('<pre>{}</pre>', json.dumps(obj.changes, indent=2))
        return "Aucun changement"
    changes_display.short_description = "Détails des changements"

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class SmartNotificationAdmin(admin.ModelAdmin):
    """Administration des notifications intelligentes"""
    list_display = ['title', 'user', 'type', 'priority', 'is_read', 'created_at']
    list_filter = ['type', 'priority', 'is_read', 'created_at']
    search_fields = ['title', 'message', 'user__username']
    readonly_fields = ['created_at', 'read_at']

    fieldsets = (
        ('Notification', {
            'fields': ('user', 'title', 'message', 'type', 'priority')
        }),
        ('Action', {
            'fields': ('action_url', 'action_label')
        }),
        ('Métadonnées', {
            'fields': ('is_read', 'read_at', 'expires_at', 'created_at'),
            'classes': ('collapse',)
        }),
        ('Liens', {
            'fields': ('related_object_type', 'related_object_id'),
            'classes': ('collapse',)
        })
    )

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        """Action pour marquer comme lu"""
        updated = queryset.update(is_read=True)
        self.message_user(request, f'{updated} notifications marquées comme lues.')
    mark_as_read.short_description = "Marquer comme lu"

    def mark_as_unread(self, request, queryset):
        """Action pour marquer comme non lu"""
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'{updated} notifications marquées comme non lues.')
    mark_as_unread.short_description = "Marquer comme non lu"


class AIInsightAdmin(admin.ModelAdmin):
    """Administration des insights IA"""
    list_display = ['title', 'type', 'confidence_score', 'is_actionable', 'generated_at']
    list_filter = ['type', 'is_actionable', 'generated_at']
    search_fields = ['title', 'description']
    readonly_fields = ['generated_at', 'data_display', 'analysis_display', 'recommendations_display']

    fieldsets = (
        ('Insight', {
            'fields': ('type', 'title', 'description', 'confidence_score')
        }),
        ('Données', {
            'fields': ('data_display', 'analysis_display'),
            'classes': ('collapse',)
        }),
        ('Recommandations', {
            'fields': ('recommendations_display', 'action_items', 'is_actionable')
        }),
        ('Métadonnées', {
            'fields': ('generated_at', 'valid_until'),
            'classes': ('collapse',)
        })
    )

    def data_display(self, obj):
        """Affichage formaté des données"""
        if obj.data:
            return format_html('<pre>{}</pre>', json.dumps(obj.data, indent=2))
        return "Aucune donnée"
    data_display.short_description = "Données brutes"

    def analysis_display(self, obj):
        """Affichage formaté de l'analyse"""
        if obj.analysis_result:
            return format_html('<pre>{}</pre>', json.dumps(obj.analysis_result, indent=2))
        return "Aucune analyse"
    analysis_display.short_description = "Résultat d'analyse"

    def recommendations_display(self, obj):
        """Affichage formaté des recommandations"""
        if obj.recommendations:
            recommendations_html = "<ul>"
            for rec in obj.recommendations:
                recommendations_html += f"<li>{rec}</li>"
            recommendations_html += "</ul>"
            return format_html(recommendations_html)
        return "Aucune recommandation"
    recommendations_display.short_description = "Recommandations"


class SmartTagAdmin(admin.ModelAdmin):
    """Administration des tags intelligents"""
    list_display = ['name', 'color_display', 'usage_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['usage_count', 'created_at']

    def color_display(self, obj):
        """Affichage de la couleur"""
        return format_html(
            '<span style="background-color: {}; padding: 2px 8px; border-radius: 3px; color: white;">{}</span>',
            obj.color,
            obj.color
        )
    color_display.short_description = "Couleur"


class SystemHealthAdmin(admin.ModelAdmin):
    """Administration de la santé du système"""
    list_display = ['component', 'status_display', 'response_time', 'checked_at']
    list_filter = ['component', 'status', 'checked_at']
    readonly_fields = ['checked_at', 'metrics_display']
    date_hierarchy = 'checked_at'

    def status_display(self, obj):
        """Affichage coloré du statut"""
        colors = {
            'HEALTHY': 'green',
            'WARNING': 'orange',
            'CRITICAL': 'red',
            'DOWN': 'darkred'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = "Statut"

    def metrics_display(self, obj):
        """Affichage formaté des métriques"""
        if obj.metrics:
            return format_html('<pre>{}</pre>', json.dumps(obj.metrics, indent=2))
        return "Aucune métrique"
    metrics_display.short_description = "Métriques"

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


# Enregistrement des modèles
admin.site.register(AuditLog, AuditLogAdmin)
admin.site.register(SmartNotification, SmartNotificationAdmin)
admin.site.register(AIInsight, AIInsightAdmin)
admin.site.register(SmartTag, SmartTagAdmin)
admin.site.register(SystemHealth, SystemHealthAdmin)

# Configuration de l'admin
admin.site.site_header = "Société Ben Chaabene de Commerce - Administration Intelligente"
admin.site.site_title = "ABM Admin"
admin.site.index_title = "Tableau de bord administrateur"


