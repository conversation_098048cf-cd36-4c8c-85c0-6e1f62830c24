from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

User = get_user_model()

class Categorie(models.Model):
    """Catégories de produits"""

    nom = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='sous_categories')
    actif = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Catégorie"
        verbose_name_plural = "Catégories"
        ordering = ['nom']

    def __str__(self):
        return self.nom


class Produit(models.Model):
    """Modèle Produit pour la gestion du catalogue"""

    TYPE_CHOICES = [
        ('PHYSIQUE', 'Produit physique'),
        ('SERVICE', 'Service'),
        ('NUMERIQUE', 'Produit numérique'),
        ('ABONNEMENT', 'Abonnement'),
    ]

    STATUT_CHOICES = [
        ('ACTIF', 'Actif'),
        ('INACTIF', 'Inactif'),
        ('RUPTURE', 'En rupture'),
        ('DISCONTINUE', 'Discontinué'),
    ]

    UNITE_CHOICES = [
        ('PIECE', 'Pièce'),
        ('KG', 'Kilogramme'),
        ('LITRE', 'Litre'),
        ('METRE', 'Mètre'),
        ('HEURE', 'Heure'),
        ('JOUR', 'Jour'),
        ('MOIS', 'Mois'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reference = models.CharField(max_length=50, unique=True, verbose_name="Référence")
    code_barre = models.CharField(max_length=50, blank=True, unique=True)

    # Informations de base
    nom = models.CharField(max_length=200, verbose_name="Nom du produit")
    description = models.TextField(blank=True)
    description_courte = models.CharField(max_length=500, blank=True)

    # Classification
    categorie = models.ForeignKey(Categorie, on_delete=models.SET_NULL, null=True, blank=True)
    type_produit = models.CharField(max_length=20, choices=TYPE_CHOICES, default='PHYSIQUE')
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='ACTIF')

    # Prix et coûts
    prix_achat = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    prix_vente = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))]
    )
    prix_promo = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00'))]
    )

    # TVA
    taux_tva = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('20.00'),
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('100.00'))]
    )

    # Stock
    unite = models.CharField(max_length=20, choices=UNITE_CHOICES, default='PIECE')
    stock_actuel = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    stock_minimum = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    stock_maximum = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    gestion_stock = models.BooleanField(default=True, verbose_name="Gérer le stock")

    # Dimensions et poids (pour les produits physiques)
    poids = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True, verbose_name="Poids (kg)")
    longueur = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name="Longueur (cm)")
    largeur = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name="Largeur (cm)")
    hauteur = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name="Hauteur (cm)")

    # E-commerce
    visible_ecommerce = models.BooleanField(default=False, verbose_name="Visible sur e-commerce")
    featured = models.BooleanField(default=False, verbose_name="Produit vedette")
    meta_title = models.CharField(max_length=200, blank=True)
    meta_description = models.CharField(max_length=500, blank=True)

    # Images
    image_principale = models.ImageField(upload_to='produits/', blank=True, null=True)

    # Métadonnées
    tags = models.CharField(max_length=500, blank=True, help_text="Tags séparés par des virgules")
    notes_internes = models.TextField(blank=True)

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='produits_created')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='produits_updated')

    class Meta:
        verbose_name = "Produit"
        verbose_name_plural = "Produits"
        ordering = ['nom']
        indexes = [
            models.Index(fields=['reference']),
            models.Index(fields=['statut']),
            models.Index(fields=['categorie']),
            models.Index(fields=['visible_ecommerce']),
        ]

    def __str__(self):
        return f"{self.reference} - {self.nom}"

    @property
    def prix_ttc(self):
        """Prix TTC calculé"""
        return self.prix_vente * (1 + self.taux_tva / 100)

    @property
    def marge_brute(self):
        """Marge brute en euros"""
        return self.prix_vente - self.prix_achat

    @property
    def taux_marge(self):
        """Taux de marge en pourcentage"""
        if self.prix_achat > 0:
            return ((self.prix_vente - self.prix_achat) / self.prix_achat) * 100
        return 0

    @property
    def en_rupture(self):
        """Vérifie si le produit est en rupture"""
        return self.gestion_stock and self.stock_actuel <= 0

    @property
    def alerte_stock(self):
        """Vérifie si le stock est en dessous du minimum"""
        return self.gestion_stock and self.stock_actuel <= self.stock_minimum

    def peut_vendre(self, quantite):
        """Vérifie si on peut vendre la quantité demandée"""
        if not self.gestion_stock:
            return True
        return self.stock_actuel >= quantite


class ImageProduit(models.Model):
    """Images supplémentaires pour les produits"""

    produit = models.ForeignKey(Produit, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='produits/')
    alt_text = models.CharField(max_length=200, blank=True)
    ordre = models.IntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Image produit"
        verbose_name_plural = "Images produits"
        ordering = ['ordre', 'created_at']

    def __str__(self):
        return f"Image {self.ordre} - {self.produit.nom}"
