/**
 * Styles pour SimpleLoginForm - Authentification <PERSON>
 */

.simple-login-form {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
  padding: 2rem;
}

.login-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.login-logo {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.login-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.login-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.login-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #0d9488;
  box-shadow: 0 0 0 3px rgba(13, 148, 136, 0.1);
}

.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.login-button {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.login-footer {
  padding: 1rem 2rem 2rem;
  text-align: center;
  border-top: 1px solid #f1f5f9;
}

.register-link {
  color: #0d9488;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.register-link:hover {
  color: #0f766e;
  text-decoration: underline;
}

.forgot-password {
  display: block;
  color: #6b7280;
  text-decoration: none;
  font-size: 0.9rem;
  margin-top: 1rem;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: #0d9488;
}

/* Responsive */
@media (max-width: 480px) {
  .simple-login-form {
    padding: 1rem;
  }

  .login-container {
    max-width: none;
  }

  .login-header,
  .login-form {
    padding: 1.5rem;
  }

  .login-footer {
    padding: 1rem 1.5rem 1.5rem;
  }
}

/* Comptes de démonstration */
.demo-accounts {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
}

.demo-accounts h3 {
  margin: 0 0 1rem 0;
  color: #0d9488;
  font-size: 1rem;
  text-align: center;
}

.demo-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.demo-account {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.demo-account:hover {
  border-color: #0d9488;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.15);
}

.demo-role {
  font-weight: 600;
  color: #374151;
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
}

.demo-email {
  font-size: 0.75rem;
  color: #6b7280;
}

.demo-note {
  text-align: center;
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
  font-style: italic;
}

.demo-info {
  text-align: center;
  font-size: 0.85rem;
  color: #0d9488;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
}

.password-hint {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .login-container {
    background: #1f2937;
    color: white;
  }

  .form-input {
    background: #374151;
    border-color: #4b5563;
    color: white;
  }

  .form-input:focus {
    border-color: #0d9488;
  }

  .form-group label {
    color: #d1d5db;
  }

  .login-footer {
    border-top-color: #374151;
  }

  .demo-accounts {
    background: #374151;
    border-color: #4b5563;
  }

  .demo-account {
    background: #1f2937;
    border-color: #4b5563;
  }

  .demo-account:hover {
    border-color: #0d9488;
  }

  .demo-role {
    color: #d1d5db;
  }

  .demo-email {
    color: #9ca3af;
  }
}
