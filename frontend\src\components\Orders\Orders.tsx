/**
 * Composant principal de gestion des commandes
 */

import React from "react";
import { Routes, Route, useNavigate } from "react-router-dom";
import OrdersList from "./OrdersList";
import OrderForm from "./OrderForm";
import OrderDetail from "./OrderDetail";
import OrderStats from "./OrderStats";
import { useAuth } from "../../contexts/AuthContext";
import "./Orders.css";

const Orders: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Vérifier les permissions
  const hasOrderAccess = () => {
    const allowedRoles = ['NORMAL', 'COMPTABLE', 'ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || 'NORMAL');
  };

  if (!hasOrderAccess()) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder à la gestion des commandes.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="orders-module">
      <Routes>
        {/* Liste des commandes */}
        <Route 
          path="/" 
          element={<OrdersList />} 
        />
        
        {/* Nouvelle commande */}
        <Route 
          path="/new" 
          element={<OrderForm />} 
        />
        
        {/* Détail d'une commande */}
        <Route 
          path="/:id" 
          element={<OrderDetail />} 
        />
        
        {/* Édition d'une commande */}
        <Route 
          path="/:id/edit" 
          element={<OrderForm />} 
        />
        
        {/* Statistiques commandes */}
        <Route 
          path="/stats" 
          element={<OrderStats />} 
        />
      </Routes>
    </div>
  );
};

export default Orders;
