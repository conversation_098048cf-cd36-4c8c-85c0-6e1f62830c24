/**
 * Panneau de vérification des clients en temps réel
 */

import React, { useState } from 'react';
import { useClientVerification, useClientMonitoring } from '../../hooks/useClientVerification';
import './ClientVerificationPanel.css';

interface ClientVerificationPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

const ClientVerificationPanel: React.FC<ClientVerificationPanelProps> = ({
  isVisible = false,
  onToggle
}) => {
  const {
    isVerifying,
    lastVerification,
    clientsCount,
    recentClients,
    verifyClient,
    refreshClients
  } = useClientVerification();

  const {
    monitoring,
    startMonitoring,
    stopMonitoring
  } = useClientMonitoring(5000); // Vérifier toutes les 5 secondes

  const [searchEmail, setSearchEmail] = useState('');

  const handleVerifyByEmail = async () => {
    if (!searchEmail.trim()) return;
    
    const client = recentClients.find(c => 
      c.email.toLowerCase() === searchEmail.toLowerCase()
    );
    
    if (client) {
      await verifyClient(client.id, client.nom_complet || client.nom);
    } else {
      alert(`Client avec l'email ${searchEmail} non trouvé dans les clients récents`);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return 'Jamais';
    return date.toLocaleString('fr-FR');
  };

  if (!isVisible) {
    return (
      <button 
        className="verification-toggle-btn"
        onClick={onToggle}
        title="Ouvrir le panneau de vérification"
      >
        🔍 Vérification
      </button>
    );
  }

  return (
    <div className="client-verification-panel">
      <div className="panel-header">
        <h3>🔍 Vérification des Clients</h3>
        <button 
          className="close-btn"
          onClick={onToggle}
          title="Fermer le panneau"
        >
          ✕
        </button>
      </div>

      <div className="panel-content">
        {/* Statistiques */}
        <div className="stats-section">
          <h4>📊 Statistiques</h4>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">Total clients:</span>
              <span className="stat-value">{clientsCount}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Dernière vérification:</span>
              <span className="stat-value">{formatDate(lastVerification)}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Surveillance:</span>
              <span className={`stat-value ${monitoring ? 'active' : 'inactive'}`}>
                {monitoring ? '🟢 Active' : '🔴 Inactive'}
              </span>
            </div>
          </div>
        </div>

        {/* Contrôles */}
        <div className="controls-section">
          <h4>🎛️ Contrôles</h4>
          <div className="controls-grid">
            <button 
              className="control-btn refresh"
              onClick={refreshClients}
              disabled={isVerifying}
            >
              🔄 Rafraîchir
            </button>
            
            <button 
              className={`control-btn monitor ${monitoring ? 'active' : ''}`}
              onClick={monitoring ? stopMonitoring : startMonitoring}
            >
              {monitoring ? '⏹️ Arrêter' : '👀 Surveiller'}
            </button>
          </div>
        </div>

        {/* Recherche par email */}
        <div className="search-section">
          <h4>🔍 Vérifier par Email</h4>
          <div className="search-controls">
            <input
              type="email"
              placeholder="<EMAIL>"
              value={searchEmail}
              onChange={(e) => setSearchEmail(e.target.value)}
              className="search-input"
            />
            <button 
              className="search-btn"
              onClick={handleVerifyByEmail}
              disabled={isVerifying || !searchEmail.trim()}
            >
              {isVerifying ? '⏳' : '🔍'} Vérifier
            </button>
          </div>
        </div>

        {/* Clients récents */}
        <div className="recent-section">
          <h4>🆕 Clients Récents ({recentClients.length})</h4>
          <div className="recent-list">
            {recentClients.length === 0 ? (
              <p className="no-clients">Aucun client récent</p>
            ) : (
              recentClients.slice(0, 5).map((client, index) => (
                <div key={client.id || index} className="recent-item">
                  <div className="client-info">
                    <span className="client-name">
                      {client.nom_complet || client.nom}
                    </span>
                    <span className="client-email">{client.email}</span>
                    <span className="client-date">
                      {new Date(client.created_at).toLocaleString('fr-FR')}
                    </span>
                  </div>
                  <button 
                    className="verify-btn"
                    onClick={() => verifyClient(client.id, client.nom_complet || client.nom)}
                    disabled={isVerifying}
                    title="Vérifier ce client en base"
                  >
                    {isVerifying ? '⏳' : '✅'}
                  </button>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Status */}
        {isVerifying && (
          <div className="status-section">
            <div className="loading-indicator">
              <span className="spinner">⏳</span>
              <span>Vérification en cours...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientVerificationPanel;
