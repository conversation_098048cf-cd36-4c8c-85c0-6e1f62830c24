from rest_framework import serializers
from .models import Client, ContactClient

class ContactClientSerializer(serializers.ModelSerializer):
    """Serializer pour les contacts clients"""
    
    class Meta:
        model = ContactClient
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class ClientSerializer(serializers.ModelSerializer):
    """Serializer pour les clients"""

    contacts = ContactClientSerializer(many=True, read_only=True)
    nom_complet = serializers.ReadOnlyField()
    adresse_complete = serializers.ReadOnlyField()
    segment_color = serializers.SerializerMethodField()
    ca_annuel = serializers.SerializerMethodField()
    nombre_factures = serializers.SerializerMethodField()
    chiffre_affaires_total = serializers.SerializerMethodField()
    date_creation = serializers.SerializerMethodField()

    class Meta:
        model = Client
        fields = '__all__'
        read_only_fields = (
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'total_achats', 'nb_commandes', 'derniere_commande'
        )

    def get_segment_color(self, obj):
        return obj.get_segment_color()

    def get_ca_annuel(self, obj):
        return float(obj.calculer_ca_annuel())

    def get_nombre_factures(self, obj):
        """Retourne le nombre de factures du client"""
        return obj.nb_commandes

    def get_chiffre_affaires_total(self, obj):
        """Retourne le chiffre d'affaires total"""
        return float(obj.calculer_ca_annuel())

    def get_date_creation(self, obj):
        """Retourne la date de création formatée"""
        return obj.created_at

class ClientStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques clients"""
    
    total_clients = serializers.IntegerField()
    clients_actifs = serializers.IntegerField()
    nouveaux_clients = serializers.IntegerField()
    taux_croissance = serializers.FloatField()
    segments = serializers.ListField()
    types = serializers.ListField()
    top_clients = ClientSerializer(many=True)
    evolution_mensuelle = serializers.ListField()

class ClientCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de clients"""
    
    contacts = ContactClientSerializer(many=True, required=False)
    
    class Meta:
        model = Client
        exclude = (
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'total_achats', 'nb_commandes', 'derniere_commande'
        )
    
    def create(self, validated_data):
        contacts_data = validated_data.pop('contacts', [])
        client = Client.objects.create(**validated_data)
        
        for contact_data in contacts_data:
            ContactClient.objects.create(client=client, **contact_data)
        
        return client
    
    def update(self, instance, validated_data):
        contacts_data = validated_data.pop('contacts', [])
        
        # Mettre à jour le client
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Mettre à jour les contacts
        if contacts_data:
            # Supprimer les anciens contacts
            instance.contacts.all().delete()
            
            # Créer les nouveaux contacts
            for contact_data in contacts_data:
                ContactClient.objects.create(client=instance, **contact_data)
        
        return instance
