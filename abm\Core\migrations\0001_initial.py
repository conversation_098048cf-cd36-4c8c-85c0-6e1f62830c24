# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AIInsight',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('SALES_TREND', 'Tendance des ventes'), ('STOCK_PREDICTION', 'Prédiction de stock'), ('CLIENT_BEHAVIOR', 'Comportement client'), ('REVENUE_FORECAST', 'Prévision de revenus'), ('COST_OPTIMIZATION', 'Optimisation des coûts'), ('RISK_ANALYSIS', 'Analyse des risques'), ('PERFORMANCE_KPI', 'KPI de performance')], max_length=50)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('data', models.JSONField(default=dict)),
                ('analysis_result', models.JSONField(default=dict)),
                ('confidence_score', models.FloatField(default=0.0)),
                ('recommendations', models.JSONField(default=list)),
                ('action_items', models.JSONField(default=list)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('valid_until', models.DateTimeField(blank=True, null=True)),
                ('is_actionable', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Insight IA',
                'verbose_name_plural': 'Insights IA',
                'ordering': ['-generated_at'],
            },
        ),
        migrations.CreateModel(
            name='SmartTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('color', models.CharField(default='#007bff', max_length=7)),
                ('description', models.TextField(blank=True)),
                ('auto_apply_rules', models.JSONField(default=list)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Tag intelligent',
                'verbose_name_plural': 'Tags intelligents',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SystemHealth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('component', models.CharField(choices=[('DATABASE', 'Base de données'), ('API', 'API REST'), ('STORAGE', 'Stockage'), ('CACHE', 'Cache'), ('EMAIL', 'Email'), ('PDF_GENERATOR', 'Générateur PDF'), ('BACKUP', 'Sauvegarde')], max_length=50)),
                ('status', models.CharField(choices=[('HEALTHY', 'Sain'), ('WARNING', 'Avertissement'), ('CRITICAL', 'Critique'), ('DOWN', 'Hors service')], max_length=20)),
                ('response_time', models.FloatField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('metrics', models.JSONField(default=dict)),
                ('checked_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Santé du système',
                'verbose_name_plural': 'Santé du système',
                'ordering': ['-checked_at'],
                'indexes': [models.Index(fields=['component', 'status'], name='Core_system_compone_944e97_idx')],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(choices=[('CREATE', 'Création'), ('UPDATE', 'Modification'), ('DELETE', 'Suppression'), ('VIEW', 'Consultation'), ('EXPORT', 'Export'), ('IMPORT', 'Import'), ('LOGIN', 'Connexion'), ('LOGOUT', 'Déconnexion')], max_length=20)),
                ('model_name', models.CharField(max_length=100)),
                ('object_id', models.CharField(blank=True, max_length=100, null=True)),
                ('object_repr', models.CharField(blank=True, max_length=200, null=True)),
                ('changes', models.JSONField(blank=True, default=dict)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': "Journal d'audit",
                'verbose_name_plural': "Journaux d'audit",
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='Core_auditl_user_id_09f296_idx'), models.Index(fields=['model_name', 'timestamp'], name='Core_auditl_model_n_054137_idx'), models.Index(fields=['action', 'timestamp'], name='Core_auditl_action_97f85e_idx')],
            },
        ),
        migrations.CreateModel(
            name='SmartNotification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('type', models.CharField(choices=[('INFO', 'Information'), ('SUCCESS', 'Succès'), ('WARNING', 'Avertissement'), ('ERROR', 'Erreur'), ('REMINDER', 'Rappel'), ('ALERT', 'Alerte')], default='INFO', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Basse'), ('NORMAL', 'Normale'), ('HIGH', 'Haute'), ('URGENT', 'Urgente')], default='NORMAL', max_length=20)),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('action_url', models.URLField(blank=True)),
                ('action_label', models.CharField(blank=True, max_length=100)),
                ('related_object_type', models.CharField(blank=True, max_length=100)),
                ('related_object_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification intelligente',
                'verbose_name_plural': 'Notifications intelligentes',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_read'], name='Core_smartn_user_id_22e0b3_idx'), models.Index(fields=['priority', 'created_at'], name='Core_smartn_priorit_7b37a7_idx')],
            },
        ),
    ]
