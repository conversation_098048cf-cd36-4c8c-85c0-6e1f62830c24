/**
 * Hook personnalisé pour vérifier l'ajout de clients en base
 */

import { useState, useEffect, useCallback } from "react";
import { ClientService } from "../services/apiService";
import { toast } from "react-toastify";

// Interface pour un client simplifié
interface ClientData {
  id: string;
  nom: string;
  nom_complet?: string;
  email: string;
  created_at: string;
  statut?: string;
}

interface ClientVerificationResult {
  isVerifying: boolean;
  lastVerification: Date | null;
  clientsCount: number;
  recentClients: ClientData[];
  verifyClient: (clientId: string, clientName: string) => Promise<boolean>;
  refreshClients: () => Promise<void>;
}

export const useClientVerification = (): ClientVerificationResult => {
  const [isVerifying, setIsVerifying] = useState(false);
  const [lastVerification, setLastVerification] = useState<Date | null>(null);
  const [clientsCount, setClientsCount] = useState(0);
  const [recentClients, setRecentClients] = useState<ClientData[]>([]);

  // Fonction pour vérifier un client spécifique
  const verifyClient = useCallback(
    async (clientId: string, clientName: string): Promise<boolean> => {
      setIsVerifying(true);

      try {
        console.log(
          `🔍 Vérification du client ${clientName} (ID: ${clientId})`
        );

        const response = await ClientService.getClient(clientId);

        if (response.success && response.data) {
          console.log("✅ Client trouvé en base:", response.data);

          toast.success(
            `✅ Client ${clientName} confirmé en base !\n` +
              `🆔 ID: ${clientId}\n` +
              `📧 Email: ${response.data.email}`,
            {
              position: "bottom-right",
              autoClose: 4000,
            }
          );

          setLastVerification(new Date());
          return true;
        } else {
          console.warn(`⚠️ Client ${clientName} non trouvé en base`);

          toast.warning(
            `⚠️ Client ${clientName} non trouvé en base\n` +
              `🆔 ID recherché: ${clientId}`,
            {
              position: "bottom-right",
              autoClose: 5000,
            }
          );

          return false;
        }
      } catch (error) {
        console.error("❌ Erreur lors de la vérification:", error);

        toast.error(
          `❌ Erreur lors de la vérification de ${clientName}\n` +
            `Détails: ${error}`,
          {
            position: "bottom-right",
            autoClose: 5000,
          }
        );

        return false;
      } finally {
        setIsVerifying(false);
      }
    },
    []
  );

  // Fonction pour rafraîchir la liste des clients
  const refreshClients = useCallback(async () => {
    try {
      console.log("🔄 Rafraîchissement de la liste des clients...");

      const response = await ClientService.getClients({
        ordering: "-created_at",
        limit: 10,
      });

      if (response.success && response.results) {
        setClientsCount(response.count || response.results.length);

        // Mapper les données avec le bon type
        const clientsData: ClientData[] = response.results.map(
          (client: any) => ({
            id: client.id || "",
            nom: client.nom || "",
            nom_complet: client.nom_complet || client.nom || "",
            email: client.email || "",
            created_at: client.created_at || "",
            statut: client.statut || "",
          })
        );

        setRecentClients(clientsData);
        setLastVerification(new Date());

        console.log(`✅ ${clientsData.length} clients récupérés`);

        // Afficher les 3 derniers clients créés
        const derniers = clientsData.slice(0, 3);
        if (derniers.length > 0) {
          const noms = derniers
            .map((c: ClientData) => c.nom_complet || c.nom || "Client sans nom")
            .join(", ");
          toast.info(`📊 Derniers clients: ${noms}`, {
            position: "bottom-left",
            autoClose: 3000,
          });
        }
      }
    } catch (error) {
      console.error("❌ Erreur lors du rafraîchissement:", error);
    }
  }, []);

  // Rafraîchissement automatique au montage du composant
  useEffect(() => {
    refreshClients();
  }, [refreshClients]);

  return {
    isVerifying,
    lastVerification,
    clientsCount,
    recentClients,
    verifyClient,
    refreshClients,
  };
};

// Hook pour surveiller les nouveaux clients en temps réel
export const useClientMonitoring = (intervalMs: number = 10000) => {
  const [monitoring, setMonitoring] = useState(false);
  const [lastCount, setLastCount] = useState(0);

  const startMonitoring = useCallback(() => {
    setMonitoring(true);
    console.log("👀 Surveillance des nouveaux clients démarrée");

    const interval = setInterval(async () => {
      try {
        const response = await ClientService.getClients({ limit: 1 });
        const currentCount = response.count || 0;

        if (lastCount > 0 && currentCount > lastCount) {
          const nouveauxClients = currentCount - lastCount;

          toast.success(
            `🎉 ${nouveauxClients} nouveau(x) client(s) détecté(s) !`,
            {
              position: "top-center",
              autoClose: 5000,
            }
          );

          console.log(`🎉 ${nouveauxClients} nouveau(x) client(s) ajouté(s)`);
        }

        setLastCount(currentCount);
      } catch (error) {
        console.error("Erreur lors de la surveillance:", error);
      }
    }, intervalMs);

    return () => {
      clearInterval(interval);
      setMonitoring(false);
      console.log("⏹️ Surveillance des nouveaux clients arrêtée");
    };
  }, [intervalMs, lastCount]);

  const stopMonitoring = useCallback(() => {
    setMonitoring(false);
  }, []);

  return {
    monitoring,
    startMonitoring,
    stopMonitoring,
  };
};

export default useClientVerification;
