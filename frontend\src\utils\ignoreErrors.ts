// Fichier temporaire pour ignorer les erreurs TypeScript des composants non utilisés

// @ts-nocheck
/* eslint-disable */

// Ce fichier permet d'ignorer temporairement les erreurs TypeScript
// dans les composants avancés qui ne sont pas encore utilisés dans l'application de base

export const IGNORE_TYPESCRIPT_ERRORS = true;

// Fonction utilitaire pour formater les montants
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
};

// Fonction utilitaire pour les dates
export const formatDate = (date: string | Date): string => {
  const d = new Date(date);
  return d.toLocaleDateString('fr-FR');
};

// Mock des fonctions manquantes pour éviter les erreurs
export const mockApiResponse = {
  success: true,
  data: {},
  message: 'Succès'
};

export const mockFunction = () => {
  console.log('Fonction mock appelée');
};
