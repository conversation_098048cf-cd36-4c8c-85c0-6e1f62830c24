/* Error <PERSON>ry */
.error-boundary {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  color: white;
}

.error-boundary-content {
  text-align: center;
  max-width: 600px;
  padding: 40px;
}

.error-icon {
  margin-bottom: 30px;
}

.error-icon-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 3rem;
  animation: pulse 2s infinite;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 20px 0;
  opacity: 0.95;
}

.error-message {
  font-size: 1.2rem;
  line-height: 1.6;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.error-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-btn {
  padding: 15px 30px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-btn.primary {
  background: white;
  color: #ff4757;
}

.error-btn.primary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.error-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.error-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.error-technical {
  margin-top: 40px;
  text-align: left;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 20px;
}

.error-technical summary {
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 15px;
  opacity: 0.8;
}

.error-stack {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  line-height: 1.4;
}

.error-stack h4 {
  margin: 15px 0 10px 0;
  color: #ffeb3b;
}

.error-stack pre {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Page Error */
.page-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.page-error-content {
  text-align: center;
  max-width: 500px;
}

.page-error-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.8;
}

.page-error-title {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.page-error-message {
  color: #7f8c8d;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 30px 0;
}

.page-error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Section Error */
.section-error {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  color: #c53030;
}

.section-error.compact {
  padding: 15px;
  font-size: 0.9rem;
}

.section-error-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.section-error-icon {
  font-size: 1.2rem;
}

.section-error-message {
  flex: 1;
}

.section-error-retry {
  background: none;
  border: none;
  color: #c53030;
  cursor: pointer;
  font-size: 1.1rem;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.section-error-retry:hover {
  background: rgba(197, 48, 48, 0.1);
}

/* Network Error */
.network-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
}

.network-error-content {
  text-align: center;
  max-width: 400px;
}

.network-error-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  opacity: 0.7;
}

.network-error h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin: 0 0 10px 0;
}

.network-error p {
  color: #7f8c8d;
  margin: 0 0 25px 0;
  line-height: 1.5;
}

/* Not Found */
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  padding: 40px 20px;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
}

.not-found-icon {
  font-size: 5rem;
  margin-bottom: 20px;
  opacity: 0.6;
}

.not-found h1 {
  font-size: 6rem;
  font-weight: 700;
  color: #667eea;
  margin: 0;
  line-height: 1;
}

.not-found h2 {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin: 10px 0 15px 0;
}

.not-found p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.not-found-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Unauthorized */
.unauthorized {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
  padding: 40px 20px;
}

.unauthorized-content {
  text-align: center;
  max-width: 500px;
}

.unauthorized-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.7;
}

.unauthorized h2 {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.unauthorized p {
  color: #7f8c8d;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 30px 0;
}

.unauthorized-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.error-icon-circle.shake {
  animation: shake 0.5s ease-in-out;
}

/* Responsive */
@media (max-width: 768px) {
  .error-boundary-content {
    padding: 20px;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-message {
    font-size: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
  
  .page-error-title {
    font-size: 1.5rem;
  }
  
  .not-found h1 {
    font-size: 4rem;
  }
  
  .not-found h2 {
    font-size: 1.5rem;
  }
  
  .not-found-actions,
  .page-error-actions,
  .unauthorized-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* Thème sombre */
@media (prefers-color-scheme: dark) {
  .page-error-title,
  .not-found h2,
  .unauthorized h2 {
    color: white;
  }
  
  .page-error-message,
  .not-found p,
  .unauthorized p,
  .network-error h3 {
    color: #bdc3c7;
  }
  
  .section-error {
    background: #2c1810;
    border-color: #8b4513;
    color: #ff6b6b;
  }
}

/* États de focus pour l'accessibilité */
.error-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.section-error-retry:focus {
  outline: 2px solid #c53030;
  outline-offset: 2px;
}

/* Styles pour les erreurs critiques */
.error-boundary.critical {
  background: linear-gradient(135deg, #8b0000 0%, #ff0000 100%);
}

.error-boundary.critical .error-icon-circle {
  animation: shake 0.5s ease-in-out infinite;
}

/* Styles pour les erreurs de maintenance */
.maintenance-error {
  background: linear-gradient(135deg, #ffa502 0%, #ff6348 100%);
}

.maintenance-error .error-icon-circle {
  background: rgba(255, 255, 255, 0.3);
}

/* Indicateur de statut du serveur */
.server-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
}

.server-status.online {
  background: rgba(46, 213, 115, 0.9);
}

.server-status.offline {
  background: rgba(255, 71, 87, 0.9);
}

.server-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}
