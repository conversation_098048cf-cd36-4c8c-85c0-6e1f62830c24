import React, { useState, useEffect, useCallback } from "react";
import { ClientService } from "../../services/apiService";
import { useAuth } from "../../contexts/AuthContext";
import { toast } from "react-toastify";
import { useClientAutoRefresh } from "../../hooks/useAutoRefresh";
import "./SimpleClients.css";

interface Client {
  id: string;
  nom: string;
  prenom?: string;
  email: string;
  telephone: string;
  ville: string;
  statut: string;
  chiffre_affaires_total?: number;
  type_client: string;
}

const SimpleClients: React.FC = () => {
  const { user, hasRole } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("tous");
  const [showAddForm, setShowAddForm] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Vérifier si l'utilisateur peut voir les chiffres d'affaires
  const canViewRevenue = hasRole("ADMIN");

  const loadClients = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = { page_size: 50 };
      if (searchTerm) params.search = searchTerm;
      if (selectedStatus !== "tous") params.statut = selectedStatus;

      const response = await ClientService.getClients(params);

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        const data: any = response.data;
        if (Array.isArray(data)) {
          setClients(data);
        } else if (data.results) {
          setClients(data.results);
        } else {
          setClients([]);
        }
      }
    } catch (err: any) {
      setError(err.message);
      toast.error(`Erreur lors du chargement des clients: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, selectedStatus]);

  useEffect(() => {
    loadClients();
  }, [loadClients]);

  // Utilisation du hook de rafraîchissement automatique
  const { forceRefresh } = useClientAutoRefresh(loadClients, {
    interval: 30000, // 30 secondes
    onFocus: true,
    onVisibilityChange: true,
    enabled: true,
  });

  // Données de démonstration supprimées - utilisation des vraies API

  const filteredClients = clients.filter((client) => {
    const matchesSearch =
      client.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (client.prenom &&
        client.prenom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      selectedStatus === "tous" || client.statut === selectedStatus;

    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case "ACTIF":
        return "#28a745";
      case "PROSPECT":
        return "#ffc107";
      case "INACTIF":
        return "#6c757d";
      default:
        return "#6c757d";
    }
  };

  const getStatusLabel = (statut: string) => {
    switch (statut) {
      case "ACTIF":
        return "Actif";
      case "PROSPECT":
        return "Prospect";
      case "INACTIF":
        return "Inactif";
      default:
        return statut;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="simple-clients">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Chargement des clients...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="simple-clients">
        <div className="error-container">
          <h2>Erreur</h2>
          <p>{error}</p>
          <button
            onClick={loadClients}
            className="retry-button">
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="simple-clients">
      {/* Header */}
      <div className="clients-header">
        <div className="header-left">
          <h1>👥 Gestion des Clients</h1>
          <p>Gérez vos clients et prospects</p>
        </div>
        <button
          className="btn-primary"
          onClick={() => setShowAddForm(true)}>
          ➕ Nouveau Client
        </button>
      </div>

      {/* Filters */}
      <div className="clients-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="Rechercher un client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="status-filter">
          <option value="tous">Tous les statuts</option>
          <option value="ACTIF">Actifs</option>
          <option value="PROSPECT">Prospects</option>
          <option value="INACTIF">Inactifs</option>
        </select>
      </div>

      {/* Stats */}
      <div className="clients-stats">
        <div className="stat-item">
          <span className="stat-number">{clients.length}</span>
          <span className="stat-label">Total Clients</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">
            {clients.filter((c) => c.statut === "ACTIF").length}
          </span>
          <span className="stat-label">Actifs</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">
            {clients.filter((c) => c.statut === "PROSPECT").length}
          </span>
          <span className="stat-label">Prospects</span>
        </div>
        {canViewRevenue && (
          <div className="stat-item">
            <span className="stat-number">
              {formatCurrency(
                clients.reduce(
                  (sum, c) => sum + (c.chiffre_affaires_total || 0),
                  0
                )
              )}
            </span>
            <span className="stat-label">CA Total</span>
          </div>
        )}
      </div>

      {/* Clients List */}
      <div className="clients-list">
        {filteredClients.length === 0 ? (
          <div className="empty-state">
            <p>Aucun client trouvé</p>
          </div>
        ) : (
          <div className="clients-grid">
            {filteredClients.map((client) => (
              <div
                key={client.id}
                className="client-card">
                <div className="client-header">
                  <div className="client-name">
                    <h3>
                      {client.nom} {client.prenom}
                    </h3>
                    <span
                      className="client-status"
                      style={{
                        backgroundColor: getStatusColor(client.statut),
                      }}>
                      {getStatusLabel(client.statut)}
                    </span>
                  </div>
                </div>

                <div className="client-details">
                  <div className="detail-item">
                    <span className="detail-icon">📧</span>
                    <span>{client.email}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-icon">📞</span>
                    <span>{client.telephone}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-icon">📍</span>
                    <span>{client.ville}</span>
                  </div>
                  {canViewRevenue && (
                    <div className="detail-item">
                      <span className="detail-icon">💰</span>
                      <span>
                        {formatCurrency(client.chiffre_affaires_total || 0)}
                      </span>
                    </div>
                  )}
                </div>

                <div className="client-actions">
                  <button className="btn-outline">👁️ Voir</button>
                  <button className="btn-outline">✏️ Modifier</button>
                  <button className="btn-outline">📄 Factures</button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Add Client Modal */}
      {showAddForm && (
        <NewClientModal
          onClose={() => setShowAddForm(false)}
          onSuccess={loadClients}
        />
      )}
    </div>
  );
};

// Composant Modal pour ajouter un client
interface NewClientModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

interface NewClientData {
  nom: string;
  email: string;
  telephone: string;
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
  type_client: "PARTICULIER" | "ENTREPRISE";
  siret?: string;
  tva_intracommunautaire?: string;
  notes: string;
}

const NewClientModal: React.FC<NewClientModalProps> = ({
  onClose,
  onSuccess,
}) => {
  const [clientData, setClientData] = useState<NewClientData>({
    nom: "",
    email: "",
    telephone: "",
    adresse: "",
    ville: "",
    code_postal: "",
    pays: "Tunisie",
    type_client: "PARTICULIER",
    siret: "",
    tva_intracommunautaire: "",
    notes: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!clientData.nom.trim()) {
      newErrors.nom = "Le nom est obligatoire";
    }

    if (!clientData.email.trim()) {
      newErrors.email = "L'email est obligatoire";
    } else if (!/\S+@\S+\.\S+/.test(clientData.email)) {
      newErrors.email = "Format d'email invalide";
    }

    if (!clientData.telephone.trim()) {
      newErrors.telephone = "Le téléphone est obligatoire";
    }

    if (!clientData.adresse.trim()) {
      newErrors.adresse = "L'adresse est obligatoire";
    }

    if (!clientData.ville.trim()) {
      newErrors.ville = "La ville est obligatoire";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof NewClientData, value: string) => {
    setClientData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast.error("Veuillez corriger les erreurs dans le formulaire");
      return;
    }

    setIsLoading(true);

    try {
      const response = await ClientService.createClient({
        ...clientData,
        prenom: clientData.type_client === "PARTICULIER" ? "Client" : "Société",
        chiffre_affaires_total: 0,
        statut: "ACTIF",
      });

      if (response.success || response.data) {
        toast.success(`Client ${clientData.nom} créé avec succès !`);
        onSuccess();
        onClose();
      } else if (response.error) {
        toast.error(response.error);
      }
    } catch (error) {
      console.error("Erreur lors de la création du client:", error);
      toast.error("Erreur lors de la création du client");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="modal-overlay"
      onClick={onClose}>
      <div
        className="modal-content large-modal"
        onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>👥 Nouveau Client</h2>
          <button
            className="modal-close"
            onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="modal-body">
          {/* Type de client */}
          <div className="form-section">
            <h4>🏷️ Type de Client</h4>
            <div className="client-type-selector">
              <label
                className={`type-option ${
                  clientData.type_client === "PARTICULIER" ? "selected" : ""
                }`}>
                <input
                  type="radio"
                  name="type_client"
                  value="PARTICULIER"
                  checked={clientData.type_client === "PARTICULIER"}
                  onChange={(e) =>
                    handleInputChange(
                      "type_client",
                      e.target.value as "PARTICULIER" | "ENTREPRISE"
                    )
                  }
                />
                <div className="type-content">
                  <span className="type-icon">👤</span>
                  <span className="type-label">Particulier</span>
                </div>
              </label>
              <label
                className={`type-option ${
                  clientData.type_client === "ENTREPRISE" ? "selected" : ""
                }`}>
                <input
                  type="radio"
                  name="type_client"
                  value="ENTREPRISE"
                  checked={clientData.type_client === "ENTREPRISE"}
                  onChange={(e) =>
                    handleInputChange(
                      "type_client",
                      e.target.value as "PARTICULIER" | "ENTREPRISE"
                    )
                  }
                />
                <div className="type-content">
                  <span className="type-icon">🏢</span>
                  <span className="type-label">Entreprise</span>
                </div>
              </label>
            </div>
          </div>

          {/* Informations générales */}
          <div className="form-section">
            <h4>📋 Informations Générales</h4>
            <div className="form-grid">
              <div className="form-group">
                <label>
                  {clientData.type_client === "ENTREPRISE"
                    ? "Raison sociale"
                    : "Nom complet"}{" "}
                  *
                </label>
                <input
                  type="text"
                  value={clientData.nom}
                  onChange={(e) => handleInputChange("nom", e.target.value)}
                  className={`form-input ${errors.nom ? "error" : ""}`}
                  placeholder={
                    clientData.type_client === "ENTREPRISE"
                      ? "Nom de l'entreprise"
                      : "Prénom et nom"
                  }
                />
                {errors.nom && (
                  <span className="error-message">{errors.nom}</span>
                )}
              </div>

              <div className="form-group">
                <label>Email *</label>
                <input
                  type="email"
                  value={clientData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className={`form-input ${errors.email ? "error" : ""}`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <span className="error-message">{errors.email}</span>
                )}
              </div>

              <div className="form-group">
                <label>Téléphone *</label>
                <input
                  type="tel"
                  value={clientData.telephone}
                  onChange={(e) =>
                    handleInputChange("telephone", e.target.value)
                  }
                  className={`form-input ${errors.telephone ? "error" : ""}`}
                  placeholder="+216 XX XXX XXX"
                />
                {errors.telephone && (
                  <span className="error-message">{errors.telephone}</span>
                )}
              </div>
            </div>
          </div>

          {/* Informations entreprise */}
          {clientData.type_client === "ENTREPRISE" && (
            <div className="form-section">
              <h4>🏢 Informations Entreprise</h4>
              <div className="form-grid">
                <div className="form-group">
                  <label>Numéro d'identification fiscale</label>
                  <input
                    type="text"
                    value={clientData.siret || ""}
                    onChange={(e) => handleInputChange("siret", e.target.value)}
                    className="form-input"
                    placeholder="Numéro d'identification fiscale (optionnel)"
                  />
                </div>

                <div className="form-group">
                  <label>TVA Intracommunautaire</label>
                  <input
                    type="text"
                    value={clientData.tva_intracommunautaire || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "tva_intracommunautaire",
                        e.target.value
                      )
                    }
                    className="form-input"
                    placeholder="Numéro TVA"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Adresse */}
          <div className="form-section">
            <h4>📍 Adresse</h4>
            <div className="form-group">
              <label>Adresse *</label>
              <textarea
                value={clientData.adresse}
                onChange={(e) => handleInputChange("adresse", e.target.value)}
                className={`form-textarea ${errors.adresse ? "error" : ""}`}
                rows={3}
                placeholder="Adresse complète"
              />
              {errors.adresse && (
                <span className="error-message">{errors.adresse}</span>
              )}
            </div>

            <div className="form-grid">
              <div className="form-group">
                <label>Ville *</label>
                <input
                  type="text"
                  value={clientData.ville}
                  onChange={(e) => handleInputChange("ville", e.target.value)}
                  className={`form-input ${errors.ville ? "error" : ""}`}
                  placeholder="Ville"
                />
                {errors.ville && (
                  <span className="error-message">{errors.ville}</span>
                )}
              </div>

              <div className="form-group">
                <label>Code postal</label>
                <input
                  type="text"
                  value={clientData.code_postal}
                  onChange={(e) =>
                    handleInputChange("code_postal", e.target.value)
                  }
                  className="form-input"
                  placeholder="Code postal"
                />
              </div>

              <div className="form-group">
                <label>Pays</label>
                <select
                  value={clientData.pays}
                  onChange={(e) => handleInputChange("pays", e.target.value)}
                  className="form-input">
                  <option value="Tunisie">Tunisie</option>
                  <option value="France">France</option>
                  <option value="Algérie">Algérie</option>
                  <option value="Maroc">Maroc</option>
                  <option value="Autre">Autre</option>
                </select>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="form-section">
            <h4>📝 Notes</h4>
            <textarea
              value={clientData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              className="form-textarea"
              rows={3}
              placeholder="Notes additionnelles sur le client..."
            />
          </div>
        </div>

        <div className="modal-footer">
          <button
            className="btn btn-secondary"
            onClick={onClose}>
            Annuler
          </button>
          <button
            className="btn btn-primary"
            onClick={handleSave}
            disabled={isLoading}>
            {isLoading ? "⏳ Enregistrement..." : "💾 Enregistrer"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleClients;
