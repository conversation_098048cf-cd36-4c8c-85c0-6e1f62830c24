#!/usr/bin/env node

/**
 * Script pour corriger les URLs des APIs dans apiService.ts
 * Remplace /facturation/api/ par /api/invoices/api/
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/services/apiService.ts');

console.log('🔧 Correction des URLs API dans apiService.ts...');

try {
  // Lire le fichier
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Compter les occurrences avant
  const beforeCount = (content.match(/\/facturation\/api\//g) || []).length;
  console.log(`📊 Trouvé ${beforeCount} occurrences de "/facturation/api/"`);
  
  // Remplacer toutes les occurrences
  content = content.replace(/\/facturation\/api\//g, '/api/invoices/api/');
  
  // Compter les occurrences après
  const afterCount = (content.match(/\/facturation\/api\//g) || []).length;
  const replacedCount = beforeCount - afterCount;
  
  // Écrire le fichier modifié
  fs.writeFileSync(filePath, content, 'utf8');
  
  console.log(`✅ ${replacedCount} URLs corrigées avec succès !`);
  console.log(`📝 Fichier mis à jour: ${filePath}`);
  
  if (afterCount === 0) {
    console.log('🎉 Toutes les URLs ont été corrigées !');
  } else {
    console.log(`⚠️  Il reste ${afterCount} occurrences non corrigées`);
  }
  
} catch (error) {
  console.error('❌ Erreur lors de la correction:', error.message);
  process.exit(1);
}

console.log('\n🔄 Redémarrez le serveur React pour appliquer les changements.');
