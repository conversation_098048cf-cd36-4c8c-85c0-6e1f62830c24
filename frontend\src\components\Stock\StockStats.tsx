/**
 * Composant StockStats - Statistiques des stocks
 */

import React, { useState, useEffect } from 'react';
import { StockService } from '../../services/apiService';
import { useNotify } from '../Common/NotificationSystem';

interface StockStatistics {
  total_produits: number;
  valeur_stock_total: number;
  produits_rupture: number;
  produits_alerte: number;
  mouvements_mois: number;
  rotation_moyenne: number;
  top_produits: Array<{
    nom: string;
    quantite: number;
    valeur: number;
  }>;
  evolution_mensuelle: Array<{
    mois: string;
    entrees: number;
    sorties: number;
    valeur: number;
  }>;
}

const StockStats: React.FC = () => {
  const notify = useNotify();
  const [stats, setStats] = useState<StockStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('month');

  useEffect(() => {
    loadStatistics();
  }, [period]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const response = await StockService.getStatistics({ period });
      if (response.success) {
        setStats(response.data);
      } else {
        notify.error('Erreur lors du chargement des statistiques');
      }
    } catch (error) {
      console.error('Erreur:', error);
      notify.error('Erreur lors du chargement des statistiques');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Chargement des statistiques...</p>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="error-state">
        <h3>❌ Erreur</h3>
        <p>Impossible de charger les statistiques</p>
        <button className="btn btn-primary" onClick={loadStatistics}>
          🔄 Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="stock-stats">
      <div className="stats-header">
        <h2>📊 Statistiques Stock</h2>
        <div className="period-selector">
          <select 
            value={period} 
            onChange={(e) => setPeriod(e.target.value)}
            className="form-select"
          >
            <option value="week">Cette semaine</option>
            <option value="month">Ce mois</option>
            <option value="quarter">Ce trimestre</option>
            <option value="year">Cette année</option>
          </select>
        </div>
      </div>

      {/* Métriques principales */}
      <div className="stats-grid">
        <div className="stat-card primary">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <h3>{stats.total_produits}</h3>
            <p>Produits en stock</p>
          </div>
        </div>

        <div className="stat-card success">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>{stats.valeur_stock_total.toFixed(3)} DT</h3>
            <p>Valeur totale du stock</p>
          </div>
        </div>

        <div className="stat-card warning">
          <div className="stat-icon">⚠️</div>
          <div className="stat-content">
            <h3>{stats.produits_alerte}</h3>
            <p>Produits en alerte</p>
          </div>
        </div>

        <div className="stat-card danger">
          <div className="stat-icon">🚨</div>
          <div className="stat-content">
            <h3>{stats.produits_rupture}</h3>
            <p>Produits en rupture</p>
          </div>
        </div>

        <div className="stat-card info">
          <div className="stat-icon">🔄</div>
          <div className="stat-content">
            <h3>{stats.mouvements_mois}</h3>
            <p>Mouvements ce mois</p>
          </div>
        </div>

        <div className="stat-card secondary">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <h3>{stats.rotation_moyenne.toFixed(1)}</h3>
            <p>Rotation moyenne</p>
          </div>
        </div>
      </div>

      {/* Top produits */}
      <div className="stats-section">
        <h3>🏆 Top Produits par Valeur</h3>
        <div className="top-products">
          {stats.top_produits.map((produit, index) => (
            <div key={index} className="product-item">
              <div className="product-rank">#{index + 1}</div>
              <div className="product-info">
                <h4>{produit.nom}</h4>
                <p>Quantité: {produit.quantite} | Valeur: {produit.valeur.toFixed(3)} DT</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Évolution mensuelle */}
      <div className="stats-section">
        <h3>📈 Évolution Mensuelle</h3>
        <div className="evolution-chart">
          <table className="evolution-table">
            <thead>
              <tr>
                <th>Mois</th>
                <th>Entrées</th>
                <th>Sorties</th>
                <th>Valeur</th>
              </tr>
            </thead>
            <tbody>
              {stats.evolution_mensuelle.map((item, index) => (
                <tr key={index}>
                  <td>{item.mois}</td>
                  <td className="positive">+{item.entrees}</td>
                  <td className="negative">-{item.sorties}</td>
                  <td>{item.valeur.toFixed(3)} DT</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="stats-actions">
        <button className="btn btn-primary">
          📊 Exporter les statistiques
        </button>
        <button className="btn btn-secondary">
          📧 Envoyer par email
        </button>
        <button className="btn btn-outline">
          🔄 Actualiser
        </button>
      </div>
    </div>
  );
};

export default StockStats;
