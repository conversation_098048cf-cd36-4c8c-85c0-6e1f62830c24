/* Dialog de confirmation */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.2s ease;
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 450px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

.confirm-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 25px 25px 20px 25px;
  border-bottom: 1px solid #ecf0f1;
}

.confirm-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.confirm-title {
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.confirm-content {
  padding: 20px 25px;
}

.confirm-message {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 0;
  font-size: 1rem;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  padding: 20px 25px 25px 25px;
  justify-content: flex-end;
  border-top: 1px solid #ecf0f1;
}

.confirm-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.95rem;
  min-width: 100px;
}

.confirm-btn-cancel {
  background: #f8f9fa;
  color: #2c3e50;
  border: 1px solid #dee2e6;
}

.confirm-btn-cancel:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.confirm-btn-confirm {
  color: white;
  border: none;
}

.confirm-btn-confirm:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Variantes de type */
.confirm-info .confirm-btn-confirm {
  background: #3742fa;
}

.confirm-info .confirm-btn-confirm:hover {
  background: #2f3af2;
}

.confirm-success .confirm-btn-confirm {
  background: #2ed573;
}

.confirm-success .confirm-btn-confirm:hover {
  background: #26d065;
}

.confirm-warning .confirm-btn-confirm {
  background: #ffa502;
}

.confirm-warning .confirm-btn-confirm:hover {
  background: #ff9500;
}

.confirm-danger .confirm-btn-confirm {
  background: #ff4757;
}

.confirm-danger .confirm-btn-confirm:hover {
  background: #ff3838;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .confirm-dialog {
    width: 95%;
    margin: 20px;
  }
  
  .confirm-header {
    padding: 20px 20px 15px 20px;
  }
  
  .confirm-content {
    padding: 15px 20px;
  }
  
  .confirm-actions {
    padding: 15px 20px 20px 20px;
    flex-direction: column;
  }
  
  .confirm-btn {
    width: 100%;
    justify-content: center;
  }
  
  .confirm-icon {
    font-size: 1.5rem;
  }
  
  .confirm-title {
    font-size: 1.1rem;
  }
  
  .confirm-message {
    font-size: 0.95rem;
  }
}

/* États de focus pour l'accessibilité */
.confirm-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.confirm-btn-confirm:focus {
  outline-color: currentColor;
}

/* Animation de sortie */
.confirm-overlay.closing {
  animation: fadeOut 0.2s ease;
}

.confirm-dialog.closing {
  animation: slideOut 0.3s ease;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
}

/* Styles pour les confirmations avec contenu riche */
.confirm-dialog.rich {
  max-width: 550px;
}

.confirm-dialog.rich .confirm-content {
  padding: 25px;
}

.confirm-dialog.rich .confirm-message {
  font-size: 1.05rem;
}

/* Indicateur de chargement pour les actions async */
.confirm-btn.loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.confirm-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Thème sombre */
@media (prefers-color-scheme: dark) {
  .confirm-dialog {
    background: #2c3e50;
    color: white;
  }
  
  .confirm-title {
    color: white;
  }
  
  .confirm-message {
    color: #bdc3c7;
  }
  
  .confirm-header,
  .confirm-actions {
    border-color: #34495e;
  }
  
  .confirm-btn-cancel {
    background: #34495e;
    color: white;
    border-color: #4a5f7a;
  }
  
  .confirm-btn-cancel:hover {
    background: #4a5f7a;
  }
}

/* Styles pour les confirmations critiques */
.confirm-dialog.critical {
  border: 2px solid #ff4757;
}

.confirm-dialog.critical .confirm-header {
  background: linear-gradient(135deg, #ff4757, #ff3838);
  color: white;
  border-bottom: none;
}

.confirm-dialog.critical .confirm-title {
  color: white;
}

.confirm-dialog.critical .confirm-icon {
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Amélioration de l'accessibilité */
.confirm-dialog {
  role: dialog;
  aria-modal: true;
}

.confirm-overlay {
  role: presentation;
}

/* Gestion du focus */
.confirm-dialog:focus {
  outline: none;
}

/* Styles pour les confirmations avec liste d'éléments */
.confirm-list {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  margin: 15px 0;
  max-height: 200px;
  overflow-y: auto;
}

.confirm-list-item {
  padding: 8px 0;
  border-bottom: 1px solid #dee2e6;
  color: #2c3e50;
  font-weight: 500;
}

.confirm-list-item:last-child {
  border-bottom: none;
}

.confirm-list-item::before {
  content: '• ';
  color: #7f8c8d;
  margin-right: 8px;
}
