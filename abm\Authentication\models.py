"""
Modèles pour l'authentification et la gestion des rôles
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
import secrets
import string


class UserRole(models.TextChoices):
    """Rôles disponibles dans le système"""
    NORMAL = 'NORMAL', _('Utilisateur Normal')
    COMPTABLE = 'COMPTABLE', _('Comptable')
    ADMIN = 'ADMIN', _('Administrateur')
    SUPERADMIN = 'SUPERADMIN', _('Super Administrateur')


class CustomUser(AbstractUser):
    """Modèle utilisateur personnalisé avec rôles"""
    
    role = models.CharField(
        max_length=20,
        choices=UserRole.choices,
        default=UserRole.NORMAL,
        verbose_name="Rôle"
    )
    
    phone = models.Char<PERSON><PERSON>(
        max_length=20,
        blank=True,
        null=True,
        verbose_name="Téléphone"
    )
    
    company = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Entreprise"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="Actif"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date de création"
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Date de modification"
    )
    
    class Meta:
        verbose_name = "Utilisateur"
        verbose_name_plural = "Utilisateurs"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
    
    @property
    def is_normal(self):
        """Vérifie si l'utilisateur a le rôle Normal"""
        return self.role == UserRole.NORMAL
    
    @property
    def is_comptable(self):
        """Vérifie si l'utilisateur a le rôle Comptable"""
        return self.role == UserRole.COMPTABLE
    
    @property
    def is_admin(self):
        """Vérifie si l'utilisateur a le rôle Admin"""
        return self.role == UserRole.ADMIN
    
    @property
    def is_superadmin(self):
        """Vérifie si l'utilisateur a le rôle SuperAdmin"""
        return self.role == UserRole.SUPERADMIN
    
    def has_permission(self, permission_level):
        """
        Vérifie si l'utilisateur a le niveau de permission requis
        
        Hiérarchie des permissions :
        NORMAL < COMPTABLE < ADMIN < SUPERADMIN
        """
        role_hierarchy = {
            UserRole.NORMAL: 1,
            UserRole.COMPTABLE: 2,
            UserRole.ADMIN: 3,
            UserRole.SUPERADMIN: 4
        }
        
        user_level = role_hierarchy.get(self.role, 0)
        required_level = role_hierarchy.get(permission_level, 0)
        
        return user_level >= required_level
    
    def get_accessible_modules(self):
        """Retourne la liste des modules accessibles selon le rôle"""
        modules = {
            UserRole.NORMAL: [
                'dashboard_basic',
                'profile'
            ],
            UserRole.COMPTABLE: [
                'dashboard_basic',
                'profile',
                'facturation',
                'clients',
                'produits',
                'comptabilite'
            ],
            UserRole.ADMIN: [
                'dashboard_basic',
                'dashboard_admin',
                'profile',
                'facturation',
                'clients',
                'produits',
                'comptabilite',
                'stock',
                'ecommerce',
                'statistics'
            ],
            UserRole.SUPERADMIN: [
                'dashboard_basic',
                'dashboard_admin',
                'dashboard_super',
                'profile',
                'facturation',
                'clients',
                'produits',
                'comptabilite',
                'stock',
                'ecommerce',
                'statistics',
                'users_management',
                'system_settings',
                'logs'
            ]
        }
        
        return modules.get(self.role, [])


class UserSession(models.Model):
    """Modèle pour tracker les sessions utilisateur"""
    
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='sessions',
        verbose_name="Utilisateur"
    )
    
    session_key = models.CharField(
        max_length=40,
        unique=True,
        verbose_name="Clé de session"
    )
    
    ip_address = models.GenericIPAddressField(
        verbose_name="Adresse IP"
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name="User Agent"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date de création"
    )
    
    last_activity = models.DateTimeField(
        auto_now=True,
        verbose_name="Dernière activité"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="Session active"
    )
    
    class Meta:
        verbose_name = "Session utilisateur"
        verbose_name_plural = "Sessions utilisateur"
        ordering = ['-last_activity']
    
    def __str__(self):
        return f"Session {self.user.username} - {self.ip_address}"


class LoginAttempt(models.Model):
    """Modèle pour tracker les tentatives de connexion"""
    
    username = models.CharField(
        max_length=150,
        verbose_name="Nom d'utilisateur"
    )
    
    ip_address = models.GenericIPAddressField(
        verbose_name="Adresse IP"
    )
    
    success = models.BooleanField(
        default=False,
        verbose_name="Succès"
    )
    
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date et heure"
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name="User Agent"
    )
    
    class Meta:
        verbose_name = "Tentative de connexion"
        verbose_name_plural = "Tentatives de connexion"
        ordering = ['-timestamp']
    
    def __str__(self):
        status = "Réussie" if self.success else "Échouée"
        return f"{self.username} - {status} - {self.timestamp}"


class PasswordResetCode(models.Model):
    """Modèle pour les codes de récupération de mot de passe"""

    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        verbose_name="Utilisateur"
    )

    code = models.CharField(
        max_length=6,
        verbose_name="Code de vérification"
    )

    email = models.EmailField(
        verbose_name="Email de récupération"
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Date de création"
    )

    expires_at = models.DateTimeField(
        verbose_name="Date d'expiration"
    )

    is_used = models.BooleanField(
        default=False,
        verbose_name="Utilisé"
    )

    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="Adresse IP"
    )

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = self.generate_code()
        if not self.expires_at:
            # Code valide pendant 15 minutes
            self.expires_at = timezone.now() + timezone.timedelta(minutes=15)
        super().save(*args, **kwargs)

    @staticmethod
    def generate_code():
        """Génère un code de vérification à 6 chiffres"""
        return ''.join(secrets.choice(string.digits) for _ in range(6))

    def is_valid(self):
        """Vérifie si le code est encore valide"""
        return not self.is_used and timezone.now() < self.expires_at

    def mark_as_used(self):
        """Marque le code comme utilisé"""
        self.is_used = True
        self.save()

    class Meta:
        verbose_name = "Code de récupération"
        verbose_name_plural = "Codes de récupération"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.code} - {self.created_at}"
