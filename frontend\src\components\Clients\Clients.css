/* Styles pour les composants Clients */

/* === LAYOUT GÉNÉRAL === */
.clients-list,
.client-form,
.client-detail {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.clients-list.embedded {
  padding: 0;
  background: transparent;
  min-height: auto;
}

/* === HEADERS === */
.clients-header,
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-title h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.header-title p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

/* === BOUTONS === */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn.btn-primary {
  background: #3498db;
  color: white;
}

.btn.btn-primary:hover {
  background: #2980b9;
}

.btn.btn-success {
  background: #27ae60;
  color: white;
}

.btn.btn-success:hover {
  background: #229954;
}

.btn.btn-warning {
  background: #f39c12;
  color: white;
}

.btn.btn-warning:hover {
  background: #e67e22;
}

.btn.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn.btn-danger:hover {
  background: #c0392b;
}

.btn.btn-info {
  background: #17a2b8;
  color: white;
}

.btn.btn-info:hover {
  background: #138496;
}

.btn.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn.btn-secondary:hover {
  background: #5a6268;
}

.btn.btn-outline {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn.btn-outline:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

.btn.btn-sm {
  padding: 8px 16px;
  font-size: 0.8rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* === STATISTIQUES === */
.client-stats {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.stats-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
}

.period-selector {
  display: flex;
  gap: 4px;
  background: #f1f5f9;
  padding: 4px;
  border-radius: 8px;
}

.period-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

.period-btn.active {
  background: white;
  color: #3498db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card.primary { border-left: 4px solid #3498db; }
.stat-card.success { border-left: 4px solid #27ae60; }
.stat-card.warning { border-left: 4px solid #f39c12; }
.stat-card.danger { border-left: 4px solid #e74c3c; }
.stat-card.info { border-left: 4px solid #17a2b8; }
.stat-card.secondary { border-left: 4px solid #6c757d; }

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-content h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.stat-subtitle {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0 0 8px 0;
}

.stat-progress {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s ease;
}

.progress-bar.primary { background: #3498db; }
.progress-bar.success { background: #27ae60; }
.progress-bar.warning { background: #f39c12; }
.progress-bar.danger { background: #e74c3c; }
.progress-bar.info { background: #17a2b8; }
.progress-bar.secondary { background: #6c757d; }

.stats-summary {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: 700;
}

.summary-value.success { color: #27ae60; }
.summary-value.warning { color: #f39c12; }
.summary-value.danger { color: #e74c3c; }

/* === TOP CLIENTS === */
.top-clients {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.top-clients h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.top-clients-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.top-client-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.client-rank {
  width: 32px;
  height: 32px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
}

.client-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.client-name {
  font-weight: 600;
  color: #1e293b;
}

.client-ca {
  font-weight: 700;
  color: #27ae60;
}

/* === FILTRES === */
.client-filters {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filters-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filters-title h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
}

.active-filters-badge {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.filters-actions {
  display: flex;
  gap: 8px;
}

.search-bar {
  margin-bottom: 20px;
}

.search-input-container {
  position: relative;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 1rem;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  font-size: 1rem;
  padding: 4px;
  border-radius: 4px;
}

.clear-search:hover {
  background: #f1f5f9;
  color: #475569;
}

.quick-filters {
  margin-bottom: 20px;
}

.quick-filters h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.quick-filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-filter-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

.quick-filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.quick-filter-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.advanced-filters {
  border-top: 1px solid #e2e8f0;
  padding-top: 20px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

.filter-input,
.filter-select {
  padding: 10px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.active-filters-summary {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.summary-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: block;
}

.active-filters-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #3498db;
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.filter-tag button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0;
  margin-left: 4px;
}

.filter-tag button:hover {
  opacity: 0.8;
}

/* === TABLEAU === */
.clients-table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.clients-table {
  width: 100%;
  border-collapse: collapse;
}

.clients-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e2e8f0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.clients-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background 0.3s ease;
}

.clients-table th.sortable:hover {
  background: #f1f5f9;
}

.clients-table th.sortable::after {
  content: '↕️';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8rem;
  opacity: 0.5;
}

.clients-table th.sorted-asc::after {
  content: '↑';
  opacity: 1;
  color: #3498db;
}

.clients-table th.sorted-desc::after {
  content: '↓';
  opacity: 1;
  color: #3498db;
}

.clients-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.9rem;
}

.client-row {
  transition: background 0.3s ease;
}

.client-row:hover {
  background: #f8fafc;
}

.client-row.selected {
  background: #eff6ff;
  border-left: 4px solid #3498db;
}

.checkbox-col {
  width: 40px;
}

.actions-col {
  width: 160px;
}

.client-name .clickable {
  color: #3498db;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
}

.client-name .clickable:hover {
  text-decoration: underline;
}

.client-notes {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 4px;
  font-style: italic;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-email {
  font-weight: 500;
  color: #1e293b;
}

.contact-phone {
  font-size: 0.8rem;
  color: #64748b;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.location-city {
  font-weight: 500;
  color: #1e293b;
}

.location-postal {
  font-size: 0.8rem;
  color: #64748b;
}

.amount {
  text-align: right;
}

.ca-amount {
  font-weight: 600;
  color: #1e293b;
}

.ca-details {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 4px;
}

/* === BADGES === */
.type-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.type-badge.entreprise {
  background: #dbeafe;
  color: #1d4ed8;
}

.type-badge.particulier {
  background: #f3e8ff;
  color: #7c3aed;
}

.segment-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.segment-badge.segment-vip {
  background: #fef3c7;
  color: #d97706;
}

.segment-badge.segment-premium {
  background: #e0e7ff;
  color: #4338ca;
}

.segment-badge.segment-standard {
  background: #f1f5f9;
  color: #64748b;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.status-active {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.status-inactive {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.status-prospect {
  background: #fef3c7;
  color: #d97706;
}

/* === ACTIONS === */
.actions {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-btn {
  padding: 6px 8px;
  background: none;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f8fafc;
  transform: translateY(-1px);
}

.action-btn.view-btn:hover { border-color: #3498db; }
.action-btn.edit-btn:hover { border-color: #f39c12; }
.action-btn.invoice-btn:hover { border-color: #27ae60; }
.action-btn.delete-btn:hover { border-color: #e74c3c; }

/* === ACTIONS GROUPÉES === */
.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #eff6ff;
  padding: 16px 24px;
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #bfdbfe;
}

.selected-count {
  font-weight: 600;
  color: #1e40af;
}

.bulk-buttons {
  display: flex;
  gap: 8px;
}

/* === PAGINATION === */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.pagination-btn {
  padding: 10px 20px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

/* === ÉTATS VIDES === */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3,
.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 1rem;
}

/* === CHARGEMENT === */
.clients-loading,
.client-detail-loading,
.client-stats-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === MESSAGES D'ERREUR === */
.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #fee2e2;
  color: #dc2626;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #fecaca;
}

.error-icon {
  font-size: 1.2rem;
}

.retry-btn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: auto;
}

.retry-btn:hover {
  background: #b91c1c;
}

/* === MODULE CLIENTS === */
.clients-module {
  min-height: 100vh;
  background: #f8fafc;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .clients-list,
  .client-form,
  .client-detail {
    padding: 15px;
  }

  .clients-header,
  .detail-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .quick-filter-buttons {
    justify-content: center;
  }

  .clients-table-container {
    overflow-x: auto;
  }

  .clients-table {
    min-width: 800px;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
