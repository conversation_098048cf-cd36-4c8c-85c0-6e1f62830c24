/**
 * Index des composants Paiements
 */

export { default as Payments } from './Payments';
export { default as PaymentsList } from './PaymentsList';
export { default as PaymentForm } from './PaymentForm';
export { default as PaymentDetail } from './PaymentDetail';
export { default as PaymentStats } from './PaymentStats';
export { default as PaymentReconciliation } from './PaymentReconciliation';

// Types
export interface Payment {
  id: string;
  montant: number;
  devise: string;
  mode_paiement: string;
  date_paiement: string;
  date_valeur?: string;
  reference: string;
  statut: 'EN_ATTENTE' | 'VALIDE' | 'REJETE' | 'REMBOURSE';
  type: 'ENCAISSEMENT' | 'DECAISSEMENT';
  facture_id?: string;
  client_id?: string;
  fournisseur_id?: string;
  description?: string;
  created_at: string;
}

// Routes
export const PAYMENTS_ROUTES = {
  MAIN: '/payments',
  NEW: '/payments/new',
  DETAIL: '/payments/:id',
  EDIT: '/payments/:id/edit',
  STATS: '/payments/stats',
  RECONCILIATION: '/payments/reconciliation'
} as const;
