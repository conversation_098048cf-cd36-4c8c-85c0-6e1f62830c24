/**
 * Export comptable vers logiciels externes
 */

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";

const ExportComptable: React.FC = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setTimeout(() => setLoading(false), 500);
  }, []);

  if (loading) {
    return (
      <div className="journal-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des exports...</p>
      </div>
    );
  }

  return (
    <div className="export-comptable">
      <div className="coming-soon">
        <div className="coming-soon-icon">📤</div>
        <h3>Export Comptable</h3>
        <p>Module en cours de développement</p>
        <p>Fonctionnalités prévues :</p>
        <ul>
          <li>📊 Export FEC</li>
          <li>💼 Export Sage</li>
          <li>📋 Export Ciel</li>
          <li>📤 Export Excel</li>
        </ul>
      </div>
    </div>
  );
};

export default ExportComptable;
