/**
 * Composants réutilisables pour le design system ABM
 */

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  user-select: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-300);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--success-dark);
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--warning-dark);
  transform: translateY(-1px);
}

.btn-error {
  background-color: var(--error-color);
  color: var(--text-white);
}

.btn-error:hover:not(:disabled) {
  background-color: var(--error-dark);
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
}

/* Cartes */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--bg-secondary);
}

.card-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.card-content {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background-color: var(--bg-secondary);
}

/* Formulaires */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  background-color: var(--bg-primary);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control:invalid {
  border-color: var(--error-color);
}

.form-control:disabled {
  background-color: var(--bg-tertiary);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-error {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

.form-help {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

.badge-success {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.badge-warning {
  background-color: var(--warning-light);
  color: var(--warning-dark);
}

.badge-error {
  background-color: var(--error-light);
  color: var(--error-dark);
}

.badge-secondary {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

/* Alertes */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  border: 1px solid;
  margin-bottom: var(--spacing-lg);
}

.alert-success {
  background-color: var(--success-light);
  border-color: var(--success-color);
  color: var(--success-dark);
}

.alert-warning {
  background-color: var(--warning-light);
  border-color: var(--warning-color);
  color: var(--warning-dark);
}

.alert-error {
  background-color: var(--error-light);
  border-color: var(--error-color);
  color: var(--error-dark);
}

.alert-info {
  background-color: var(--info-light);
  border-color: var(--info-color);
  color: var(--info-dark);
}

/* Tableaux */
.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th {
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  border-bottom: 2px solid var(--gray-200);
}

.table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  color: var(--text-secondary);
}

.table tbody tr:hover {
  background-color: var(--bg-secondary);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* Loading */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  gap: var(--spacing-md);
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* États vides */
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-muted);
}

.empty-state-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state-description {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-lg);
}
