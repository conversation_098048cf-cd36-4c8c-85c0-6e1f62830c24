#!/usr/bin/env node

/**
 * Script de test de compilation pour l'application ABM
 * Vérifie que tous les imports et composants sont corrects
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Test de compilation de l\'application ABM...\n');

// Vérification des fichiers critiques
const criticalFiles = [
  'src/index.tsx',
  'src/App.tsx',
  'src/components/Factures/FacturationProfessionnelle.tsx',
  'src/components/Auth/SimpleLoginForm.tsx',
  'src/components/Dashboard/SimpleDashboard.tsx',
  'src/components/Clients/SimpleClients.tsx',
  'src/services/apiService.ts',
  'public/index.html'
];

console.log('📁 Vérification des fichiers critiques:');
let allFilesExist = true;

criticalFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MANQUANT`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Des fichiers critiques sont manquants !');
  process.exit(1);
}

console.log('\n🔧 Test de compilation TypeScript...');

// Test de compilation
const tscProcess = spawn('npx', ['tsc', '--noEmit'], {
  cwd: __dirname,
  stdio: 'pipe',
  shell: true
});

let output = '';
let errorOutput = '';

tscProcess.stdout.on('data', (data) => {
  output += data.toString();
});

tscProcess.stderr.on('data', (data) => {
  errorOutput += data.toString();
});

tscProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Compilation TypeScript réussie !');
    console.log('\n🎉 Tous les tests sont passés !');
    console.log('\n📋 Résumé:');
    console.log('✅ Tous les fichiers critiques présents');
    console.log('✅ Compilation TypeScript sans erreur');
    console.log('✅ Application prête pour le démarrage');
    console.log('\n🚀 Vous pouvez maintenant lancer: npm start');
  } else {
    console.log('❌ Erreurs de compilation TypeScript:');
    console.log(errorOutput);
    console.log('\n🔧 Veuillez corriger les erreurs ci-dessus avant de continuer.');
  }
});

tscProcess.on('error', (error) => {
  console.log('❌ Erreur lors du test de compilation:', error.message);
  console.log('\n💡 Assurez-vous que TypeScript est installé: npm install -g typescript');
});
