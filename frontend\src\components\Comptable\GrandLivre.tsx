/**
 * Grand livre comptable
 */

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";

const GrandLivre: React.FC = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setTimeout(() => setLoading(false), 500);
  }, []);

  if (loading) {
    return (
      <div className="journal-loading">
        <div className="loading-spinner"></div>
        <p>Chargement du grand livre...</p>
      </div>
    );
  }

  return (
    <div className="grand-livre">
      <div className="coming-soon">
        <div className="coming-soon-icon">📚</div>
        <h3>Grand Livre</h3>
        <p>Module en cours de développement</p>
        <p>Fonctionnalités prévues :</p>
        <ul>
          <li>📊 Plan comptable complet</li>
          <li>💼 Écritures comptables</li>
          <li>🔍 Recherche par compte</li>
          <li>📤 Export FEC</li>
        </ul>
      </div>
    </div>
  );
};

export default GrandLivre;
