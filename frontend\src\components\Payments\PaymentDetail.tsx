import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useNotify } from "../Common/NotificationSystem";

interface Payment {
  id: string;
  numero: string;
  facture_numero: string;
  facture_id: string;
  client_nom: string;
  client_email: string;
  montant: number;
  methode_paiement: string;
  date_paiement: string;
  date_creation: string;
  reference: string;
  notes: string;
  statut: "EN_ATTENTE" | "VALIDE" | "REJETE" | "REMBOURSE";
  created_by: string;
  validated_by?: string;
  validation_date?: string;
}

const PaymentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const notify = useNotify();

  const [payment, setPayment] = useState<Payment | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadPayment(id);
    }
  }, [id]);

  const loadPayment = async (paymentId: string) => {
    try {
      setLoading(true);

      // Simulation de données de paiement
      const mockPayment: Payment = {
        id: paymentId,
        numero: "PAY-2025-001",
        facture_numero: "FAC-2025-5004",
        facture_id: "5004",
        client_nom: "CLIENT FOND FUTURISTE",
        client_email: "<EMAIL>",
        montant: 489.664,
        methode_paiement: "VIREMENT",
        date_paiement: "2025-08-12",
        date_creation: "2025-08-12T10:30:00Z",
        reference: "VIR-20250812-001",
        notes: "Paiement partiel de la facture 5004",
        statut: "VALIDE",
        created_by: "Admin Ben Chaabene",
        validated_by: "Comptable",
        validation_date: "2025-08-12T11:00:00Z",
      };

      setPayment(mockPayment);
    } catch (error: any) {
      notify.error("Erreur lors du chargement du paiement");
      navigate("/payments");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!payment) return;

    try {
      setActionLoading(newStatus);

      // Simulation de changement de statut
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setPayment((prev) =>
        prev
          ? {
              ...prev,
              statut: newStatus as any,
              validated_by: newStatus === "VALIDE" ? "Comptable" : undefined,
              validation_date:
                newStatus === "VALIDE" ? new Date().toISOString() : undefined,
            }
          : null
      );

      notify.success(`Paiement ${newStatus.toLowerCase()} avec succès`);
    } catch (error: any) {
      notify.error("Erreur lors de la mise à jour du statut");
    } finally {
      setActionLoading(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR");
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("fr-FR");
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case "VALIDE":
        return "success";
      case "EN_ATTENTE":
        return "warning";
      case "REJETE":
        return "danger";
      case "REMBOURSE":
        return "info";
      default:
        return "secondary";
    }
  };

  const getMethodeLabel = (methode: string) => {
    switch (methode) {
      case "VIREMENT":
        return "🏦 Virement bancaire";
      case "CHEQUE":
        return "📝 Chèque";
      case "ESPECES":
        return "💵 Espèces";
      case "CARTE":
        return "💳 Carte bancaire";
      case "MOBILE_MONEY":
        return "📱 Mobile Money";
      default:
        return methode;
    }
  };

  if (loading) {
    return (
      <div className="payment-detail-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des détails...</p>
      </div>
    );
  }

  if (!payment) {
    return (
      <div className="payment-detail-error">
        <div className="error-icon">❌</div>
        <h3>Paiement introuvable</h3>
        <p>Le paiement demandé n'existe pas ou a été supprimé</p>
        <button
          onClick={() => navigate("/payments")}
          className="btn-primary">
          ← Retour à la liste
        </button>
      </div>
    );
  }

  return (
    <div className="payment-detail">
      {/* Header avec actions */}
      <div className="detail-header">
        <div className="header-info">
          <div className="payment-title">
            <h1>💳 Paiement {payment.numero}</h1>
            <span className={`status-badge ${getStatusColor(payment.statut)}`}>
              {payment.statut}
            </span>
          </div>
          <div className="payment-meta">
            <span>Créé le {formatDateTime(payment.date_creation)}</span>
            <span>Par {payment.created_by}</span>
          </div>
        </div>

        <div className="header-actions">
          <button
            className="btn-secondary"
            onClick={() => navigate("/payments")}>
            ← Retour
          </button>
          <button
            className="btn-primary"
            onClick={() => navigate(`/factures/${payment.facture_id}`)}>
            📄 Voir facture
          </button>
          {payment.statut === "EN_ATTENTE" && (
            <>
              <button
                className="btn-success"
                onClick={() => handleStatusChange("VALIDE")}
                disabled={actionLoading === "VALIDE"}>
                {actionLoading === "VALIDE" ? "⏳" : "✅"} Valider
              </button>
              <button
                className="btn-danger"
                onClick={() => handleStatusChange("REJETE")}
                disabled={actionLoading === "REJETE"}>
                {actionLoading === "REJETE" ? "⏳" : "❌"} Rejeter
              </button>
            </>
          )}
        </div>
      </div>

      {/* Informations principales */}
      <div className="payment-info">
        <div className="info-sections">
          {/* Informations du paiement */}
          <div className="info-section">
            <h3>💳 Informations du paiement</h3>
            <div className="info-grid">
              <div className="info-item">
                <label>Numéro:</label>
                <span>{payment.numero}</span>
              </div>
              <div className="info-item">
                <label>Montant:</label>
                <span className="amount-value">
                  {formatCurrency(payment.montant)}
                </span>
              </div>
              <div className="info-item">
                <label>Méthode:</label>
                <span>{getMethodeLabel(payment.methode_paiement)}</span>
              </div>
              <div className="info-item">
                <label>Date de paiement:</label>
                <span>{formatDate(payment.date_paiement)}</span>
              </div>
              <div className="info-item">
                <label>Référence:</label>
                <span>{payment.reference || "Aucune"}</span>
              </div>
              <div className="info-item">
                <label>Statut:</label>
                <span
                  className={`status-badge ${getStatusColor(payment.statut)}`}>
                  {payment.statut}
                </span>
              </div>
            </div>
          </div>

          {/* Informations facture */}
          <div className="info-section">
            <h3>📄 Facture associée</h3>
            <div className="info-grid">
              <div className="info-item">
                <label>Numéro facture:</label>
                <span>
                  <a
                    href={`/factures/${payment.facture_id}`}
                    className="link">
                    {payment.facture_numero}
                  </a>
                </span>
              </div>
              <div className="info-item">
                <label>Client:</label>
                <span>{payment.client_nom}</span>
              </div>
              <div className="info-item">
                <label>Email client:</label>
                <span>
                  <a href={`mailto:${payment.client_email}`}>
                    {payment.client_email}
                  </a>
                </span>
              </div>
            </div>
          </div>

          {/* Validation */}
          {payment.validated_by && payment.validation_date && (
            <div className="info-section">
              <h3>✅ Validation</h3>
              <div className="info-grid">
                <div className="info-item">
                  <label>Validé par:</label>
                  <span>{payment.validated_by}</span>
                </div>
                <div className="info-item">
                  <label>Date de validation:</label>
                  <span>{formatDateTime(payment.validation_date)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {payment.notes && (
            <div className="info-section">
              <h3>📝 Notes</h3>
              <div className="notes-content">
                <p>{payment.notes}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentDetail;
