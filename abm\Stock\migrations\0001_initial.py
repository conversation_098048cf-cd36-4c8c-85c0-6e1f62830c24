# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Commandes', '0001_initial'),
        ('Facturation', '0001_initial'),
        ('Produits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Inventaire',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('numero', models.CharField(max_length=50, unique=True, verbose_name="Numéro d'inventaire")),
                ('nom', models.CharField(max_length=200, verbose_name="Nom de l'inventaire")),
                ('description', models.TextField(blank=True)),
                ('statut', models.CharField(choices=[('EN_COURS', 'En cours'), ('TERMINE', 'Terminé'), ('VALIDE', 'Validé'), ('ANNULE', 'Annulé')], default='EN_COURS', max_length=20)),
                ('date_debut', models.DateTimeField(verbose_name='Date de début')),
                ('date_fin', models.DateTimeField(blank=True, null=True, verbose_name='Date de fin')),
                ('date_validation', models.DateTimeField(blank=True, null=True, verbose_name='Date de validation')),
                ('nb_produits_comptes', models.IntegerField(default=0)),
                ('nb_ecarts_detectes', models.IntegerField(default=0)),
                ('valeur_ecarts', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('categories', models.ManyToManyField(blank=True, to='Produits.categorie')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventaires_created', to=settings.AUTH_USER_MODEL)),
                ('produits_specifiques', models.ManyToManyField(blank=True, to='Produits.produit')),
                ('validated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventaires_validated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Inventaire',
                'verbose_name_plural': 'Inventaires',
                'ordering': ['-date_debut'],
            },
        ),
        migrations.CreateModel(
            name='LigneInventaire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_theorique', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Stock théorique')),
                ('stock_physique', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Stock physique compté')),
                ('ecart', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10)),
                ('valeur_ecart', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('notes', models.TextField(blank=True, verbose_name='Notes de comptage')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('counted_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='comptages', to=settings.AUTH_USER_MODEL)),
                ('inventaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lignes', to='Stock.inventaire')),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='Produits.produit')),
            ],
            options={
                'verbose_name': "Ligne d'inventaire",
                'verbose_name_plural': "Lignes d'inventaire",
                'unique_together': {('inventaire', 'produit')},
            },
        ),
        migrations.CreateModel(
            name='MouvementStock',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reference', models.CharField(max_length=50, unique=True, verbose_name='Référence')),
                ('type_mouvement', models.CharField(choices=[('ENTREE', 'Entrée'), ('SORTIE', 'Sortie'), ('AJUSTEMENT', 'Ajustement'), ('INVENTAIRE', 'Inventaire'), ('TRANSFERT', 'Transfert'), ('RETOUR', 'Retour')], max_length=20)),
                ('quantite', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('quantite_avant', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('quantite_apres', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('prix_unitaire', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Prix unitaire')),
                ('valeur_totale', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='Valeur totale')),
                ('motif', models.CharField(max_length=200, verbose_name='Motif du mouvement')),
                ('reference_externe', models.CharField(blank=True, max_length=100, verbose_name='Référence externe')),
                ('date_mouvement', models.DateTimeField(auto_now_add=True)),
                ('commande', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mouvements_stock', to='Commandes.commande')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mouvements_created', to=settings.AUTH_USER_MODEL)),
                ('facture', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mouvements_stock', to='Facturation.facture')),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='mouvements_stock', to='Produits.produit')),
            ],
            options={
                'verbose_name': 'Mouvement de stock',
                'verbose_name_plural': 'Mouvements de stock',
                'ordering': ['-date_mouvement'],
                'indexes': [models.Index(fields=['reference'], name='Stock_mouve_referen_c55f51_idx'), models.Index(fields=['produit'], name='Stock_mouve_produit_059937_idx'), models.Index(fields=['type_mouvement'], name='Stock_mouve_type_mo_e372c5_idx'), models.Index(fields=['date_mouvement'], name='Stock_mouve_date_mo_37b660_idx')],
            },
        ),
    ]
