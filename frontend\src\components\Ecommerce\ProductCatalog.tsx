import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { EcommerceService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import "../Invoices/Invoice.css";

interface EcommerceProduct {
  id: string;
  nom: string;
  description?: string;
  prix_public: number;
  prix_promo?: number;
  stock_disponible: number;
  images: any[];
  categories_ecommerce: any[];
  visible_en_ligne: boolean;
  en_vedette: boolean;
  nouveau: boolean;
  en_promotion: boolean;
  note_moyenne?: number;
  nombre_avis?: number;
  slug: string;
}

interface Category {
  id: string;
  nom: string;
  description?: string;
  parent?: string;
  actif: boolean;
}

const ProductCatalog: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const [products, setProducts] = useState<EcommerceProduct[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [sortBy, setSortBy] = useState("nom");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const loadCategories = useCallback(async () => {
    try {
      const response = await EcommerceService.getCategories();
      if (response.error) {
        throw new Error(response.error);
      }
      if (response.data) {
        setCategories(response.data.results || response.data);
      }
    } catch (err: any) {
      console.error("Erreur lors du chargement des catégories:", err);
    }
  }, []);

  const loadProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        page: currentPage,
        visible_en_ligne: true,
        ordering: sortBy,
      };

      if (searchTerm) params.search = searchTerm;
      if (selectedCategory) params.categorie = selectedCategory;
      if (priceRange.min) params.prix_min = priceRange.min;
      if (priceRange.max) params.prix_max = priceRange.max;

      const response = await EcommerceService.getCatalog(params);

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        setProducts(response.data.results || response.data);
        setTotalPages(Math.ceil((response.data.count || 0) / 20));
      }
    } catch (err: any) {
      setError(err.message);
      notify.error(`Erreur lors du chargement du catalogue: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, selectedCategory, priceRange, sortBy, currentPage, notify]);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  const addToCart = async (productId: string, quantity: number = 1) => {
    try {
      const response = await EcommerceService.addToCart(productId, quantity);
      if (response.error) {
        throw new Error(response.error);
      }
      notify.success("Produit ajouté au panier");
    } catch (err: any) {
      notify.error(`Erreur: ${err.message}`);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(price);
  };

  if (loading) {
    return (
      <div className="product-catalog">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Chargement du catalogue...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="product-catalog">
        <div className="error-container">
          <h2>Erreur</h2>
          <p>{error}</p>
          <button onClick={loadProducts} className="retry-button">
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="product-catalog">
      <div className="catalog-header">
        <h1>Catalogue Produits</h1>
        <div className="catalog-filters">
          <div className="search-section">
            <input
              type="text"
              placeholder="Rechercher un produit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="filter-section">
            <select
              value={selectedCategory || ""}
              onChange={(e) => setSelectedCategory(e.target.value || null)}
              className="filter-select">
              <option value="">Toutes les catégories</option>
              {categories.map((cat) => (
                <option key={cat.id} value={cat.id}>
                  {cat.nom}
                </option>
              ))}
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="sort-select">
              <option value="nom">Nom</option>
              <option value="prix_public">Prix croissant</option>
              <option value="-prix_public">Prix décroissant</option>
              <option value="-note_moyenne">Mieux notés</option>
              <option value="-created_at">Plus récents</option>
            </select>
          </div>
        </div>
      </div>

      <div className="products-grid">
        {products.map((product) => (
          <div key={product.id} className="product-card">
            <div className="product-image">
              {product.images && product.images.length > 0 ? (
                <img
                  src={product.images[0].image || "/api/placeholder/300/300"}
                  alt={product.nom}
                />
              ) : (
                <div className="no-image">📦</div>
              )}
              {product.en_promotion && (
                <span className="promo-badge">PROMO</span>
              )}
              {product.nouveau && (
                <span className="new-badge">NOUVEAU</span>
              )}
            </div>

            <div className="product-info">
              <h3 className="product-name">{product.nom}</h3>
              <p className="product-description">{product.description}</p>

              <div className="product-price">
                {product.en_promotion && product.prix_promo ? (
                  <>
                    <span className="price-promo">{formatPrice(product.prix_promo)}</span>
                    <span className="price-original">{formatPrice(product.prix_public)}</span>
                  </>
                ) : (
                  <span className="price">{formatPrice(product.prix_public)}</span>
                )}
              </div>

              <div className="product-meta">
                <span className="stock">Stock: {product.stock_disponible}</span>
                {product.note_moyenne && (
                  <span className="rating">
                    ⭐ {product.note_moyenne.toFixed(1)} ({product.nombre_avis})
                  </span>
                )}
              </div>

              <div className="product-actions">
                <button
                  onClick={() => addToCart(product.id)}
                  disabled={product.stock_disponible <= 0}
                  className="btn-primary">
                  {product.stock_disponible > 0 ? "Ajouter au panier" : "Rupture"}
                </button>
                <button
                  onClick={() => navigate(`/ecommerce/products/${product.slug}`)}
                  className="btn-secondary">
                  Voir détails
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {products.length === 0 && !loading && (
        <div className="no-products">
          <h3>Aucun produit trouvé</h3>
          <p>Essayez de modifier vos critères de recherche</p>
        </div>
      )}

      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="pagination-btn">
            ← Précédent
          </button>
          <span className="pagination-info">
            Page {currentPage} sur {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="pagination-btn">
            Suivant →
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductCatalog;
    let filteredProducts = mockProducts;

    if (searchTerm) {
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory) {
      filteredProducts = filteredProducts.filter(
        (product) => product.categorie === selectedCategory
      );
    }

    if (priceRange.min) {
      filteredProducts = filteredProducts.filter(
        (product) => product.prix >= parseFloat(priceRange.min)
      );
    }

    if (priceRange.max) {
      filteredProducts = filteredProducts.filter(
        (product) => product.prix <= parseFloat(priceRange.max)
      );
    }

    // Tri
    filteredProducts.sort((a, b) => {
      switch (sortBy) {
        case "prix_asc":
          return a.prix - b.prix;
        case "prix_desc":
          return b.prix - a.prix;
        case "note":
          return (b.note_moyenne || 0) - (a.note_moyenne || 0);
        case "nom":
        default:
          return a.nom.localeCompare(b.nom);
      }
    });

    // Pagination
    const startIndex = (currentPage - 1) * 12;
    const paginatedProducts = filteredProducts.slice(
      startIndex,
      startIndex + 12
    );

    setProducts(paginatedProducts);
    setTotalPages(Math.ceil(filteredProducts.length / 12));
    setLoading(false);
  };

  const handleAddToCart = async (productId: number) => {
    const res = await EcommerceService.addToCart(productId, 1);
    if (res) {
      notify.success("Succès", "Produit ajouté au panier");
    } else {
      notify.error("Erreur", res.message || "Impossible d'ajouter au panier");
    }
  };

  const resetFilters = () => {
    setSearchTerm("");
    setSelectedCategory(null);
    setPriceRange({ min: "", max: "" });
    setSortBy("nom");
    setCurrentPage(1);
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Catalogue Produits</h1>
            <p className="page-subtitle">
              Découvrez notre sélection de produits
            </p>
          </div>
          <div className="page-actions">
            <button
              className="page-action"
              onClick={() => navigate("/dashboard/ecommerce/panier")}>
              🛒 Voir le panier
            </button>
          </div>
        </div>
      </div>

      <div className="page-content">
        {/* Filtres */}
        <div
          style={{
            marginBottom: "20px",
            background: "#f8f9fa",
            padding: "20px",
            borderRadius: "8px",
          }}>
          <div style={{ marginBottom: "15px" }}>
            <input
              type="text"
              placeholder="Rechercher des produits..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: "100%",
                maxWidth: "400px",
                padding: "10px 15px",
                border: "1px solid #dee2e6",
                borderRadius: "8px",
                fontSize: "14px",
              }}
            />
          </div>

          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: "wrap",
              alignItems: "center",
            }}>
            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Catégorie:
              </label>
              <select
                value={selectedCategory || ""}
                onChange={(e) =>
                  setSelectedCategory(
                    e.target.value ? parseInt(e.target.value) : null
                  )
                }
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="">Toutes les catégories</option>
                {categories.map((cat) => (
                  <option
                    key={cat.id}
                    value={cat.id}>
                    {cat.nom} ({cat.nombre_produits})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Prix min:
              </label>
              <input
                type="number"
                value={priceRange.min}
                onChange={(e) =>
                  setPriceRange((prev) => ({ ...prev, min: e.target.value }))
                }
                style={{
                  width: "100px",
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
                placeholder="0"
              />
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Prix max:
              </label>
              <input
                type="number"
                value={priceRange.max}
                onChange={(e) =>
                  setPriceRange((prev) => ({ ...prev, max: e.target.value }))
                }
                style={{
                  width: "100px",
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
                placeholder="1000"
              />
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Trier par:
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="nom">Nom</option>
                <option value="prix">Prix croissant</option>
                <option value="-prix">Prix décroissant</option>
                <option value="-note_moyenne">Mieux notés</option>
              </select>
            </div>

            <button
              onClick={resetFilters}
              style={{
                padding: "8px 12px",
                background: "#e74c3c",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
              }}>
              🗑️ Effacer
            </button>
          </div>
        </div>

        {/* Grille de produits */}
        {loading ? (
          <div style={{ textAlign: "center", padding: "40px" }}>
            <div className="loading-spinner"></div>
            <div className="loading-text">Chargement des produits...</div>
          </div>
        ) : (
          <>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fill, minmax(280px, 1fr))",
                gap: "20px",
                marginBottom: "30px",
              }}>
              {products.map((product) => (
                <div
                  key={product.id}
                  style={{
                    border: "1px solid #dee2e6",
                    borderRadius: "8px",
                    overflow: "hidden",
                    background: "white",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    transition: "transform 0.2s",
                    cursor: "pointer",
                  }}
                  onMouseEnter={(e) =>
                    (e.currentTarget.style.transform = "translateY(-2px)")
                  }
                  onMouseLeave={(e) =>
                    (e.currentTarget.style.transform = "translateY(0)")
                  }>
                  {/* Image du produit */}
                  <div
                    style={{
                      height: "200px",
                      background: product.image
                        ? `url(${product.image})`
                        : "#f8f9fa",
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "#666",
                    }}>
                    {!product.image && "📦"}
                  </div>

                  {/* Contenu */}
                  <div style={{ padding: "15px" }}>
                    <h3 style={{ margin: "0 0 8px 0", fontSize: "16px" }}>
                      {product.nom}
                    </h3>
                    <p
                      style={{
                        margin: "0 0 12px 0",
                        fontSize: "14px",
                        color: "#666",
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical" as any,
                        overflow: "hidden",
                      }}>
                      {product.description}
                    </p>

                    {/* Prix */}
                    <div style={{ marginBottom: "12px" }}>
                      {product.prix_promo ? (
                        <div>
                          <span
                            style={{
                              fontSize: "18px",
                              fontWeight: "bold",
                              color: "#e74c3c",
                            }}>
                            {EcommerceService.formatPrice(product.prix_promo)}
                          </span>
                          <span
                            style={{
                              fontSize: "14px",
                              color: "#666",
                              textDecoration: "line-through",
                              marginLeft: "8px",
                            }}>
                            {EcommerceService.formatPrice(product.prix)}
                          </span>
                        </div>
                      ) : (
                        <span style={{ fontSize: "18px", fontWeight: "bold" }}>
                          {EcommerceService.formatPrice(product.prix)}
                        </span>
                      )}
                    </div>

                    {/* Stock et note */}
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginBottom: "12px",
                        fontSize: "12px",
                        color: "#666",
                      }}>
                      <span>Stock: {product.stock}</span>
                      {product.note_moyenne && (
                        <span>
                          ⭐ {product.note_moyenne.toFixed(1)} (
                          {product.nombre_avis})
                        </span>
                      )}
                    </div>

                    {/* Actions */}
                    <div style={{ display: "flex", gap: "8px" }}>
                      <button
                        onClick={() =>
                          navigate(
                            `/dashboard/ecommerce/produits/${product.id}`
                          )
                        }
                        style={{
                          flex: 1,
                          padding: "8px 12px",
                          background: "#3498db",
                          color: "white",
                          border: "none",
                          borderRadius: "6px",
                          cursor: "pointer",
                          fontSize: "14px",
                        }}>
                        👁️ Voir
                      </button>
                      <button
                        onClick={() => handleAddToCart(product.id)}
                        disabled={product.stock === 0}
                        style={{
                          flex: 1,
                          padding: "8px 12px",
                          background: product.stock > 0 ? "#27ae60" : "#95a5a6",
                          color: "white",
                          border: "none",
                          borderRadius: "6px",
                          cursor: product.stock > 0 ? "pointer" : "not-allowed",
                          fontSize: "14px",
                        }}>
                        🛒 Ajouter
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: "10px",
                  alignItems: "center",
                }}>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  style={{
                    padding: "8px 12px",
                    background: currentPage === 1 ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: currentPage === 1 ? "not-allowed" : "pointer",
                  }}>
                  ← Précédent
                </button>

                <span style={{ margin: "0 15px" }}>
                  Page {currentPage} sur {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  style={{
                    padding: "8px 12px",
                    background:
                      currentPage === totalPages ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor:
                      currentPage === totalPages ? "not-allowed" : "pointer",
                  }}>
                  Suivant →
                </button>
              </div>
            )}
          </>
        )}

        {!loading && products.length === 0 && (
          <div
            style={{ textAlign: "center", padding: "40px", color: "#7f8c8d" }}>
            <p>Aucun produit trouvé.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductCatalog;
