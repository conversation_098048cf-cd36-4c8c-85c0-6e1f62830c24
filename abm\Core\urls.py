"""
URLs pour le module Core intelligent
"""
from django.urls import path, include
from . import views, api_views

app_name = 'core'

urlpatterns = [
    # APIs Dashboard
    path('futuristic-dashboard/', api_views.futuristic_dashboard_view, name='futuristic_dashboard'),
    
    # API intelligentes (sans re-préfixe)
    path('insights/', views.smart_insights_api, name='smart_insights_api'),
    path('dashboard/stats/', views.dashboard_stats_api, name='dashboard_stats_api'),
    path('dashboard/recent/', views.dashboard_recent_data_api, name='dashboard_recent_data_api'),
    path('kpis/', views.kpi_dashboard_api, name='kpi_dashboard_api'),
    path('price-suggestion/', views.smart_price_suggestion_api, name='smart_price_suggestion_api'),
    path('generate-invoice-number/', views.auto_generate_invoice_number_api, name='auto_generate_invoice_number_api'),

    # Notifications intelligentes
    path('notifications/', views.notifications_api, name='notifications_api'),
    path('notifications/<uuid:notification_id>/read/', views.mark_notification_read_api, name='mark_notification_read_api'),

    # Analyses IA
    path('analyze-sales/', views.analyze_sales_trend_api, name='analyze_sales_trend_api'),
    path('predict-stock/', views.predict_stock_api, name='predict_stock_api'),

    # Monitoring système
    path('system-health/', views.system_health_api, name='system_health_api'),

    # Nouvelles API Analytics
    path('dashboard/metrics/', api_views.dashboard_metrics, name='dashboard_metrics'),
    path('dashboard/top-products/', api_views.top_products, name='top_products'),
    path('dashboard/category-performance/', api_views.category_performance, name='category_performance'),
    path('dashboard/stock-alerts/', api_views.stock_alerts, name='stock_alerts'),
    path('dashboard/client-insights/', api_views.client_insights, name='client_insights'),
    path('dashboard/ai-insights/', api_views.ai_insights, name='ai_insights'),
    path('dashboard/generate-insights/', api_views.generate_ai_insights, name='generate_ai_insights'),

    # API Dashboard supplémentaires
    path('dashboard/monthly-revenue/', api_views.monthly_revenue, name='monthly_revenue'),
    path('dashboard/sales-overview/', api_views.sales_overview, name='sales_overview'),
    path('dashboard/recent-activities/', api_views.recent_activities, name='recent_activities'),
    path('dashboard/quick-stats/', api_views.quick_stats, name='quick_stats'),
    path('consolidation/', api_views.consolidate_data, name='consolidate_data'),
    path('health/', api_views.health_check, name='health_check'),
    path('system/stats/', api_views.system_stats, name='system_stats'),

    # API Monitoring avancé
    path('monitoring/health/', api_views.system_health_api, name='system_health_api'),
    path('monitoring/metrics/', api_views.system_metrics_api, name='system_metrics_api'),
    path('monitoring/performance/', api_views.performance_stats_api, name='performance_stats_api'),
    path('monitoring/health-check/', api_views.run_health_check_api, name='run_health_check_api'),
    path('monitoring/dashboard/', api_views.monitoring_dashboard_api, name='monitoring_dashboard_api'),

    # API Recommandations IA
    path('recommendations/', api_views.ai_recommendations_api, name='ai_recommendations_api'),
    path('recommendations/pricing/', api_views.pricing_recommendations_api, name='pricing_recommendations_api'),
    path('recommendations/inventory/', api_views.inventory_recommendations_api, name='inventory_recommendations_api'),
    path('recommendations/sales/', api_views.sales_recommendations_api, name='sales_recommendations_api'),
    path('recommendations/client/', api_views.client_recommendations_api, name='client_recommendations_api'),
    path('recommendations/generate/', api_views.generate_daily_recommendations_api, name='generate_daily_recommendations_api'),
    path('recommendations/summary/', api_views.recommendations_summary_api, name='recommendations_summary_api'),

    # API Recherche Intelligente
    path('search/', api_views.global_search_api, name='global_search_api'),
    path('search/products/', api_views.search_products_api, name='search_products_api'),
    path('search/clients/', api_views.search_clients_api, name='search_clients_api'),
    path('search/invoices/', api_views.search_invoices_api, name='search_invoices_api'),
    path('search/suggestions/', api_views.search_suggestions_api, name='search_suggestions_api'),
    path('search/analytics/', api_views.search_analytics_api, name='search_analytics_api'),

    # API Validation Intelligente
    path('validation/field/', api_views.validate_field_api, name='validate_field_api'),
    path('validation/form/', api_views.validate_form_api, name='validate_form_api'),
    path('validation/email/', api_views.validate_email_api, name='validate_email_api'),
    path('validation/phone/', api_views.validate_phone_api, name='validate_phone_api'),
    path('validation/price/', api_views.validate_price_api, name='validate_price_api'),
    path('validation/existing-data/', api_views.validate_existing_data_api, name='validate_existing_data_api'),
    path('validation/rules/', api_views.validation_rules_api, name='validation_rules_api'),

    # API Optimisation Système
    path('system/optimize/', api_views.system_optimize_api, name='system_optimize_api'),
]
