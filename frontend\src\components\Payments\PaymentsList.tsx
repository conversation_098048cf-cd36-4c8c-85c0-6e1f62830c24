import React, { useState, useEffect, useCallback } from "react";
import { PaymentService } from "../../services/apiService";
import { toast } from "react-toastify";
import PaymentDetails from "./PaymentDetails";
import "./Payments.css";

interface Payment {
  id: string;
  reference: string;
  type_paiement: "ENCAISSEMENT" | "DECAISSEMENT" | "VIREMENT" | "REMBOURSEMENT";
  mode_paiement:
    | "ESPECES"
    | "CHEQUE"
    | "VIREMENT"
    | "CARTE"
    | "PRELEVEMENT"
    | "PAYPAL"
    | "STRIPE";
  statut: "EN_ATTENTE" | "VALIDE" | "REJETE" | "REMBOURSE" | "ANNULE";
  montant: number;
  devise: string;
  date_paiement: string;
  date_valeur?: string;
  client?: string;
  client_nom?: string;
  fournisseur?: string;
  facture?: string;
  facture_numero?: string;
  description?: string;
  rapproche: boolean;
  created_at: string;
  updated_at: string;
}

const PaymentsList: React.FC = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [methodFilter, setMethodFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<any>(null);

  const loadPayments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {};
      if (methodFilter !== "all") params.mode_paiement = methodFilter;
      if (statusFilter !== "all") params.statut = statusFilter;
      if (searchTerm) params.search = searchTerm;
      if (dateFrom) params.date_debut = dateFrom;
      if (dateTo) params.date_fin = dateTo;

      // Données simulées pour les paiements
      const mockPayments = [
        {
          id: "1",
          reference: "PAY-2024-001",
          type_paiement: "ENCAISSEMENT" as const,
          mode_paiement: "CHEQUE" as const,
          statut: "VALIDE" as const,
          montant: 998.0,
          devise: "TND",
          date_paiement: "2024-03-16T10:30:00Z",
          client_nom: "CLIENT FOND FUTURISTE",
          facture_numero: "FAC-2024-5004",
          description: "Paiement reçu en temps et en heure",
          rapproche: true,
          created_at: "2024-03-16T10:30:00Z",
          updated_at: "2024-03-16T10:30:00Z",
        },
        {
          id: "2",
          reference: "PAY-2024-002",
          type_paiement: "ENCAISSEMENT" as const,
          mode_paiement: "VIREMENT" as const,
          statut: "EN_ATTENTE" as const,
          montant: 1020.0,
          devise: "TND",
          date_paiement: "2024-03-15T14:15:00Z",
          client_nom: "Société Innovation Tech",
          facture_numero: "FAC-2024-5003",
          description: "Virement en cours de traitement",
          rapproche: false,
          created_at: "2024-03-15T14:15:00Z",
          updated_at: "2024-03-15T14:15:00Z",
        },
        {
          id: "3",
          reference: "PAY-2024-003",
          type_paiement: "ENCAISSEMENT" as const,
          mode_paiement: "CARTE" as const,
          statut: "VALIDE" as const,
          montant: 1500.0,
          devise: "TND",
          date_paiement: "2024-03-14T09:45:00Z",
          client_nom: "Entreprise Moderne SARL",
          facture_numero: "DEV-2024-1001",
          description: "Paiement par carte bancaire validé",
          rapproche: true,
          created_at: "2024-03-14T09:45:00Z",
          updated_at: "2024-03-14T09:45:00Z",
        },
        {
          id: "4",
          reference: "PAY-2024-004",
          type_paiement: "ENCAISSEMENT" as const,
          mode_paiement: "ESPECES" as const,
          statut: "VALIDE" as const,
          montant: 151.0,
          devise: "TND",
          date_paiement: "2024-03-13T16:20:00Z",
          client_nom: "Cabinet Conseil Expert",
          facture_numero: "FAC-2024-5002",
          description: "Paiement en espèces",
          rapproche: true,
          created_at: "2024-03-13T16:20:00Z",
          updated_at: "2024-03-13T16:20:00Z",
        },
      ];

      // Simuler un délai de chargement
      await new Promise((resolve) => setTimeout(resolve, 600));

      setPayments(mockPayments);
    } catch (err: any) {
      setError(err.message);
      toast.error(`Erreur lors du chargement des paiements: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, methodFilter, statusFilter, dateFrom, dateTo]);

  const showPaymentDetailsModal = (payment: any) => {
    setSelectedPayment(payment);
    setShowPaymentDetails(true);
  };

  const generatePaymentReceipt = (payment: any) => {
    const receiptHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Reçu de Paiement ${payment.reference}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
          .header { text-align: center; margin-bottom: 30px; }
          .company-info h2 { color: #2c5aa0; margin: 0; }
          .receipt-title { background: #e8f4f8; padding: 15px; text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0; }
          .payment-details { border: 1px solid #333; padding: 15px; margin: 20px 0; }
          .detail-row { display: flex; justify-content: space-between; margin: 8px 0; }
          .detail-label { font-weight: bold; }
          .amount-section { background: #f0f0f0; padding: 15px; text-align: center; margin: 20px 0; }
          .amount-value { font-size: 24px; font-weight: bold; color: #2c5aa0; }
          .footer { margin-top: 50px; text-align: center; font-size: 10px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h2>✓ Société Chaabane de Commerce</h2>
          <p>10, Rue de la Commission<br>6000 - 06 287 297 - 06 287 863<br>MF: 1283508 W/AM/000</p>
        </div>

        <div class="receipt-title">
          <strong>REÇU DE PAIEMENT N°: ${payment.reference}</strong>
        </div>

        <div class="payment-details">
          <div class="detail-row">
            <span class="detail-label">Date de paiement:</span>
            <span>${new Date(payment.date_paiement).toLocaleDateString(
              "fr-FR"
            )}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Facture concernée:</span>
            <span>${payment.facture_numero}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Client:</span>
            <span>${payment.client_nom}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Mode de paiement:</span>
            <span>${payment.mode_paiement}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">N° Transaction:</span>
            <span>${payment.numero_transaction}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Banque:</span>
            <span>${payment.banque}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Statut:</span>
            <span>${payment.statut}</span>
          </div>
        </div>

        <div class="amount-section">
          <p>Montant payé:</p>
          <div class="amount-value">${payment.montant.toFixed(3)} TND</div>
        </div>

        <div class="footer">
          <p><strong>Notes:</strong> ${payment.notes}</p>
          <p>Document généré le ${new Date().toLocaleDateString(
            "fr-FR"
          )} à ${new Date().toLocaleTimeString("fr-FR")}</p>
        </div>
      </body>
      </html>
    `;

    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(receiptHTML);
      printWindow.document.close();
      printWindow.print();
    }
  };

  useEffect(() => {
    loadPayments();
  }, [loadPayments]);

  const handleDelete = async (id: string) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce paiement ?"))
      return;

    try {
      const response = await PaymentService.deletePayment(id);
      if (response.error) {
        throw new Error(response.error);
      }

      setPayments(payments.filter((p) => p.id !== id));
      toast.success("Paiement supprimé");
    } catch (error: any) {
      toast.error(error.message || "Impossible de supprimer le paiement");
    }
  };

  const getMethodeConfig = (methode: string) => {
    switch (methode) {
      case "ESPECES":
        return { color: "#27ae60", label: "Espèces", icon: "💵" };
      case "CHEQUE":
        return { color: "#3498db", label: "Chèque", icon: "📝" };
      case "VIREMENT":
        return { color: "#9b59b6", label: "Virement", icon: "🏦" };
      case "CARTE":
        return { color: "#e67e22", label: "Carte", icon: "💳" };
      case "AUTRE":
        return { color: "#95a5a6", label: "Autre", icon: "💰" };
      default:
        return { color: "#666", label: methode, icon: "💰" };
    }
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">Chargement des paiements...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Paiements</h1>
            <p className="page-subtitle">Suivi des paiements reçus</p>
          </div>
          <div className="page-actions">
            <button
              className="page-action"
              onClick={() => toast.info("Fonctionnalité en développement")}>
              ➕ Nouveau paiement
            </button>
          </div>
        </div>
      </div>

      <div className="page-content">
        {/* Filtres */}
        <div style={{ marginBottom: "20px" }}>
          <div style={{ marginBottom: "15px" }}>
            <input
              type="text"
              placeholder="Rechercher par facture, client..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: "100%",
                maxWidth: "400px",
                padding: "10px 15px",
                border: "1px solid #dee2e6",
                borderRadius: "8px",
                fontSize: "14px",
              }}
            />
          </div>

          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: "wrap",
              alignItems: "center",
            }}>
            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Méthode:
              </label>
              <select
                value={methodFilter}
                onChange={(e) => setMethodFilter(e.target.value)}
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="all">Toutes</option>
                <option value="ESPECES">Espèces</option>
                <option value="CHEQUE">Chèque</option>
                <option value="VIREMENT">Virement</option>
                <option value="CARTE">Carte</option>
                <option value="AUTRE">Autre</option>
              </select>
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Du:
              </label>
              <input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                style={{
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Au:
              </label>
              <input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                style={{
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </div>

            {(searchTerm || methodFilter !== "all" || dateFrom || dateTo) && (
              <button
                onClick={() => {
                  setSearchTerm("");
                  setMethodFilter("all");
                  setDateFrom("");
                  setDateTo("");
                }}
                style={{
                  padding: "6px 12px",
                  background: "#e74c3c",
                  color: "white",
                  border: "none",
                  borderRadius: "6px",
                  cursor: "pointer",
                  fontSize: "14px",
                }}>
                🗑️ Effacer
              </button>
            )}
          </div>
        </div>

        {/* Table des paiements */}
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Facture</th>
                <th>Client</th>
                <th>Montant</th>
                <th>Méthode</th>
                <th>Référence</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {payments.map((payment) => {
                const methodeConfig = getMethodeConfig(payment.mode_paiement);
                return (
                  <tr key={payment.id}>
                    <td>
                      <strong>
                        {new Date(payment.date_paiement).toLocaleDateString()}
                      </strong>
                    </td>
                    <td>
                      <strong>{payment.facture || payment.reference}</strong>
                      <div style={{ fontSize: "12px", color: "#666" }}>
                        Facture
                      </div>
                    </td>
                    <td>{payment.client_nom || "-"}</td>
                    <td>
                      <strong>{payment.montant.toFixed(2)} €</strong>
                    </td>
                    <td>
                      <span
                        style={{
                          padding: "4px 8px",
                          borderRadius: "4px",
                          fontSize: "12px",
                          background: methodeConfig.color + "20",
                          color: methodeConfig.color,
                        }}>
                        {methodeConfig.icon} {methodeConfig.label}
                      </span>
                    </td>
                    <td>
                      <code
                        style={{
                          background: "#f8f9fa",
                          padding: "2px 6px",
                          borderRadius: "4px",
                        }}>
                        {payment.reference || "-"}
                      </code>
                    </td>
                    <td>
                      <div style={{ display: "flex", gap: "8px" }}>
                        <button
                          style={{
                            background: "#3498db",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() => showPaymentDetailsModal(payment)}>
                          👁️ Détails
                        </button>
                        <button
                          style={{
                            background: "#27ae60",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() => generatePaymentReceipt(payment)}>
                          🧾 Reçu
                        </button>
                        <button
                          style={{
                            background: "#f39c12",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() =>
                            toast.info(`Modifier: ${payment.reference}`)
                          }>
                          ✏️ Modifier
                        </button>
                        <button
                          style={{
                            background: "#e74c3c",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() => handleDelete(payment.id)}>
                          🗑️ Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {payments.length === 0 && (
          <div
            style={{ textAlign: "center", padding: "40px", color: "#7f8c8d" }}>
            <p>Aucun paiement trouvé.</p>
          </div>
        )}

        {/* Modal des détails de paiement */}
        {showPaymentDetails && selectedPayment && (
          <div
            className="modal-overlay"
            onClick={() => setShowPaymentDetails(false)}>
            <div
              className="modal-content payment-details-modal"
              onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h3>💳 Détails du Paiement - {selectedPayment.reference}</h3>
                <div className="modal-actions">
                  <button
                    className="btn btn-secondary"
                    onClick={() => generatePaymentReceipt(selectedPayment)}>
                    🧾 Générer Reçu
                  </button>
                  <button
                    className="btn btn-danger"
                    onClick={() => setShowPaymentDetails(false)}>
                    ✕ Fermer
                  </button>
                </div>
              </div>
              <div className="modal-body">
                <div className="payment-details-grid">
                  <div className="detail-section">
                    <h4>📋 Informations Générales</h4>
                    <div className="detail-item">
                      <span className="detail-label">Référence:</span>
                      <span className="detail-value">
                        {selectedPayment.reference}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">Date de paiement:</span>
                      <span className="detail-value">
                        {new Date(
                          selectedPayment.date_paiement
                        ).toLocaleDateString("fr-FR")}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">Statut:</span>
                      <span
                        className={`status ${selectedPayment.statut.toLowerCase()}`}>
                        {selectedPayment.statut}
                      </span>
                    </div>
                  </div>

                  <div className="detail-section">
                    <h4>👤 Client</h4>
                    <div className="detail-item">
                      <span className="detail-label">Nom:</span>
                      <span className="detail-value">
                        {selectedPayment.client_nom}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">Facture:</span>
                      <span className="detail-value">
                        {selectedPayment.facture_numero}
                      </span>
                    </div>
                  </div>

                  <div className="detail-section">
                    <h4>💰 Montant</h4>
                    <div className="amount-display">
                      <span className="amount-value">
                        {selectedPayment.montant.toFixed(3)} TND
                      </span>
                    </div>
                  </div>

                  <div className="detail-section">
                    <h4>🏦 Informations Bancaires</h4>
                    <div className="detail-item">
                      <span className="detail-label">Mode de paiement:</span>
                      <span className="detail-value">
                        {selectedPayment.mode_paiement}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">N° Transaction:</span>
                      <span className="detail-value">
                        {selectedPayment.numero_transaction}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="detail-label">Banque:</span>
                      <span className="detail-value">
                        {selectedPayment.banque}
                      </span>
                    </div>
                  </div>

                  {selectedPayment.notes && (
                    <div className="detail-section full-width">
                      <h4>📝 Notes</h4>
                      <div className="notes-content">
                        {selectedPayment.notes}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modal de détails de paiement */}
        {showPaymentDetails && selectedPayment && (
          <PaymentDetails
            payment={selectedPayment}
            onClose={() => {
              setShowPaymentDetails(false);
              setSelectedPayment(null);
            }}
            onGenerateReceipt={() => generatePaymentReceipt(selectedPayment)}
          />
        )}
      </div>
    </div>
  );
};

export default PaymentsList;
