.order-details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.order-details-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.order-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 30px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.order-number {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.order-details-content {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.detail-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-row .label {
  font-weight: 600;
  color: #4b5563;
  flex: 0 0 40%;
}

.detail-row .value {
  color: #1f2937;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

.detail-row .value.amount {
  font-size: 1.1rem;
  font-weight: 700;
  color: #059669;
}

.status-badge,
.priority-badge {
  display: inline-block !important;
  text-align: center !important;
}

.notes-content {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  color: #374151;
  line-height: 1.6;
  white-space: pre-wrap;
}

.quick-actions-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.quick-actions-section h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.action-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.9rem;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.action-btn.confirm {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.action-btn.confirm:hover:not(:disabled) {
  background: #bbf7d0;
  transform: translateY(-1px);
}

.action-btn.prepare {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.action-btn.prepare:hover:not(:disabled) {
  background: #fde68a;
  transform: translateY(-1px);
}

.action-btn.ship {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

.action-btn.ship:hover:not(:disabled) {
  background: #bfdbfe;
  transform: translateY(-1px);
}

.action-btn.deliver {
  background: #e0e7ff;
  color: #3730a3;
  border: 1px solid #c7d2fe;
}

.action-btn.deliver:hover:not(:disabled) {
  background: #c7d2fe;
  transform: translateY(-1px);
}

.order-details-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px 30px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: #e5e7eb;
  color: #374151;
}

.btn-secondary:hover {
  background: #d1d5db;
}

/* Responsive */
@media (max-width: 768px) {
  .order-details-overlay {
    padding: 10px;
  }

  .order-details-modal {
    max-height: 95vh;
  }

  .order-details-header {
    padding: 20px;
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .header-left {
    width: 100%;
  }

  .close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
  }

  .order-details-content {
    padding: 20px;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-row .label {
    flex: none;
  }

  .detail-row .value {
    text-align: left;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .order-details-footer {
    padding: 20px;
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .order-details-header h2 {
    font-size: 1.3rem;
  }

  .detail-section {
    padding: 16px;
  }

  .detail-section h3 {
    font-size: 1rem;
  }

  .quick-actions-section {
    padding: 16px;
  }
}
