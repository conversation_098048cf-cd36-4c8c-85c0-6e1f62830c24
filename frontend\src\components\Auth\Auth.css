/**
 * Styles modernes pour les composants d'authentification
 */

/* === FORMULAIRES D'AUTHENTIFICATION === */

.auth-form {
  width: 100%;
}

.auth-form h2 {
  margin: 0 0 25px 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  text-align: center;
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-group {
  margin-bottom: 24px;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.form-group input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1),
    0 4px 12px rgba(102, 126, 234, 0.15);
  background: white;
  transform: translateY(-1px);
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label {
  color: #667eea;
}

.form-group input.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.form-group input.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1),
    0 4px 12px rgba(239, 68, 68, 0.15);
}

.auth-header p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error,
.form-group select.error {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding-right: 50px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #7f8c8d;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #2c3e50;
}

.error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 4px;
}

.general-error {
  background: #fdf2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  text-align: center;
}

.success-message {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  text-align: center;
  color: #166534;
}

.auth-button {
  width: 100%;
  padding: 18px 24px;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  margin-top: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auth-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s;
}

.auth-button:hover::before {
  left: 100%;
}

.auth-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.auth-button:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.auth-button:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 15px rgba(156, 163, 175, 0.2);
  opacity: 0.8;
}

.auth-button:disabled::before {
  display: none;
}

/* === CHAMPS DE MOT DE PASSE === */

.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  padding-right: 50px;
}

.password-toggle {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: #64748b;
  transition: all 0.3s ease;
  padding: 5px;
  border-radius: 6px;
}

.password-toggle:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.1);
}

/* === MESSAGES D'ERREUR === */

.error-message {
  color: #ef4444;
  font-size: 0.85rem;
  font-weight: 600;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: errorSlide 0.3s ease-out;
}

.error-message::before {
  content: "⚠️";
  font-size: 0.9rem;
}

@keyframes errorSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-message {
  color: #10b981;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: successSlide 0.3s ease-out;
}

.success-message::before {
  content: "✅";
  font-size: 0.9rem;
}

@keyframes successSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === CHECKBOX REMEMBER ME === */

.remember-me {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 15px 0;
}

.remember-me input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
  cursor: pointer;
}

.remember-me label {
  margin: 0;
  font-size: 0.95rem;
  color: #64748b;
  cursor: pointer;
  user-select: none;
}

/* === FORMULAIRES EN LIGNE (REGISTER) === */

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.form-row .form-group {
  margin-bottom: 0;
}

/* === ANIMATIONS D'ENTRÉE === */

.form-group {
  animation: fadeInUp 0.5s ease-out;
  animation-fill-mode: both;
}

.form-group:nth-child(1) {
  animation-delay: 0.1s;
}
.form-group:nth-child(2) {
  animation-delay: 0.2s;
}
.form-group:nth-child(3) {
  animation-delay: 0.3s;
}
.form-group:nth-child(4) {
  animation-delay: 0.4s;
}
.form-group:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === FOCUS EFFECTS === */

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1),
    0 4px 12px rgba(102, 126, 234, 0.15);
  background: white;
  transform: translateY(-1px);
}

.form-group input:focus + label {
  color: #667eea;
}

/* === VALIDATION VISUELLE === */

.form-group input:valid:not(:placeholder-shown) {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.form-group input:valid:not(:placeholder-shown):focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1),
    0 4px 12px rgba(16, 185, 129, 0.15);
}

.auth-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.auth-footer p {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0 0 10px 0;
}

.auth-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* Amélioration pour les champs du formulaire d'inscription */
.register-form .form-group {
  margin-bottom: 5px;
}

.register-form .form-row {
  gap: 20px;
  margin-bottom: 10px;
}

.register-form .form-group input {
  padding: 14px 16px;
  font-size: 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.register-form .form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

@media (max-width: 480px) {
  .auth-container {
    padding: 10px;
  }

  .auth-card {
    padding: 30px 20px;
  }

  .auth-card.register-card {
    padding: 30px 25px;
    max-width: 100%;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .register-form .form-row {
    gap: 15px;
  }
}

/* Styles pour les rôles */
.role-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.role-normal {
  background: #e3f2fd;
  color: #1976d2;
}

.role-comptable {
  background: #f3e5f5;
  color: #7b1fa2;
}

.role-admin {
  background: #fff3e0;
  color: #f57c00;
}

.role-superadmin {
  background: #ffebee;
  color: #d32f2f;
}

/* Styles spécifiques pour la récupération de mot de passe */
.code-input {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 8px;
  font-family: "Courier New", monospace;
}

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-container input {
  flex: 1;
  padding-right: 45px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #666;
  padding: 5px;
  border-radius: 3px;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: #333;
  background-color: #f5f5f5;
}

.link-button {
  background: none;
  border: none;
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  margin: 0 10px;
}

.link-button:hover {
  color: #0056b3;
}

.link-button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.auth-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.auth-links a,
.auth-links .link-button {
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.auth-links a:hover,
.auth-links .link-button:hover {
  color: #0056b3;
  text-decoration: underline;
}
