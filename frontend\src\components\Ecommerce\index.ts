/**
 * Index des composants E-commerce
 */

export { default as Ecommerce } from './Ecommerce';
export { default as ProductCatalog } from './ProductCatalog';
export { default as ShoppingCart } from './ShoppingCart';
export { default as OrdersList } from './OrdersList';
export { default as EcommerceStats } from './EcommerceStats';
export { default as EcommerceSettings } from './EcommerceSettings';

// Types
export interface EcommerceProduct {
  id: string;
  nom: string;
  description: string;
  prix: number;
  prix_promo?: number;
  stock: number;
  images: string[];
  categorie: string;
  tags: string[];
  visible: boolean;
  featured: boolean;
  created_at: string;
}

export interface CartItem {
  product_id: string;
  nom: string;
  prix: number;
  quantite: number;
  total: number;
}

// Routes
export const ECOMMERCE_ROUTES = {
  MAIN: '/ecommerce',
  CATALOG: '/ecommerce/catalog',
  CART: '/ecommerce/cart',
  ORDERS: '/ecommerce/orders',
  STATS: '/ecommerce/stats',
  SETTINGS: '/ecommerce/settings'
} as const;
