import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import "./SimpleLoginForm.css";

const SimpleLoginForm: React.FC = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [showRegister, setShowRegister] = useState(false);

  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const result = await login({ email: username, password });
      if (!result.success) {
        setError(result.message || "Erreur de connexion");
      }
    } catch (err) {
      setError("Erreur de connexion");
    } finally {
      setIsLoading(false);
    }
  };

  // Si on affiche l'inscription, afficher un message temporaire
  if (showRegister) {
    return (
      <div className="simple-login-form">
        <div className="login-container">
          <div className="login-header">
            <div className="login-logo">👤</div>
            <h1>Inscription</h1>
            <p>Fonctionnalité à venir</p>
          </div>
          <div className="login-form">
            <p>La fonctionnalité d'inscription sera bientôt disponible.</p>
            <button
              className="login-button"
              onClick={() => setShowRegister(false)}>
              ← Retour à la connexion
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="simple-login-form">
      <div className="login-container">
        <div className="login-header">
          <h1>🏢 ABM</h1>
          <h2>Connexion</h2>
          <p>Accédez à votre espace de gestion</p>
        </div>

        <form
          onSubmit={handleSubmit}
          className="login-form">
          {error && <div className="error-message">{error}</div>}

          <div className="form-group">
            <label htmlFor="username">Nom d'utilisateur</label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Votre nom d'utilisateur"
              required
              disabled={isLoading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Mot de passe</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Votre mot de passe"
              required
              disabled={isLoading}
            />
          </div>

          <button
            type="submit"
            className="login-button"
            disabled={isLoading}>
            {isLoading ? (
              <>
                <span className="spinner-small"></span>
                Connexion...
              </>
            ) : (
              "Se connecter"
            )}
          </button>
        </form>

        <div className="login-footer">
          <p>
            Première visite ? Utilisez n'importe quel nom d'utilisateur et mot
            de passe pour vous connecter en mode démo.
          </p>
          <p>
            Pas de compte ?{" "}
            <button
              type="button"
              onClick={() => setShowRegister(true)}
              className="auth-link-button">
              Créer un compte
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SimpleLoginForm;
