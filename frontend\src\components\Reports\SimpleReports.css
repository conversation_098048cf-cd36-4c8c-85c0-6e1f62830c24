.reports-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
}

.reports-header {
  text-align: center;
  margin-bottom: 40px;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.reports-header h1 {
  margin: 0 0 10px 0;
  color: #2d3748;
  font-size: 2.2rem;
  font-weight: 700;
}

.reports-header p {
  margin: 0;
  color: #718096;
  font-size: 1.1rem;
}

.reports-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.report-config,
.report-preview {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.config-section {
  margin-bottom: 30px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.config-section h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.report-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.report-type {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.report-type:hover {
  border-color: #3182ce;
  background: #ebf8ff;
  transform: translateY(-2px);
}

.report-type.selected {
  border-color: #3182ce;
  background: #ebf8ff;
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.15);
}

.type-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.type-info h4 {
  margin: 0 0 5px 0;
  color: #2d3748;
  font-weight: 600;
}

.type-info p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

.period-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.period-btn {
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-btn:hover {
  border-color: #3182ce;
  background: #ebf8ff;
}

.period-btn.active {
  border-color: #3182ce;
  background: #3182ce;
  color: white;
}

.report-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.export-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
}

.btn-primary {
  background: #3182ce;
  color: white;
  font-size: 1.1rem;
  padding: 16px 32px;
}

.btn-primary:hover {
  background: #2c5282;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
  transform: translateY(-1px);
}

.report-preview h3 {
  margin: 0 0 25px 0;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.preview-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.card-content h4 {
  margin: 0 0 5px 0;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 700;
}

.card-content p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
  font-weight: 500;
}

.growth {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 5px;
}

.growth.positive {
  background: #c6f6d5;
  color: #22543d;
}

.growth.negative {
  background: #fed7d7;
  color: #742a2a;
}

.preview-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.preview-placeholder h4 {
  margin: 0 0 10px 0;
  color: #4a5568;
  font-size: 1.2rem;
}

.preview-placeholder p {
  margin: 0;
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .reports-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .reports-container {
    padding: 15px;
  }

  .reports-header {
    padding: 20px;
  }

  .reports-header h1 {
    font-size: 1.8rem;
  }

  .report-config,
  .report-preview {
    padding: 20px;
  }

  .report-types {
    grid-template-columns: 1fr;
  }

  .period-selector {
    flex-direction: column;
  }

  .period-btn {
    width: 100%;
    text-align: center;
  }

  .export-options {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .preview-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .reports-header h1 {
    font-size: 1.5rem;
  }

  .config-section h3,
  .report-preview h3 {
    font-size: 1.1rem;
  }

  .report-type {
    flex-direction: column;
    text-align: center;
  }

  .type-icon {
    font-size: 1.5rem;
  }
}
