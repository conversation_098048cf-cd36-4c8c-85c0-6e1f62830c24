/**
 * Index des composants Fournisseurs
 */

export { default as Fournisseurs } from './Fournisseurs';
export { default as FournisseursList } from './FournisseursList';
export { default as FournisseurForm } from './FournisseurForm';
export { default as FournisseurDetail } from './FournisseurDetail';
export { default as FournisseurStats } from './FournisseurStats';

// Types
export interface Fournisseur {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse?: string;
  ville?: string;
  code_postal?: string;
  pays: string;
  siret?: string;
  tva_intracommunautaire?: string;
  statut: 'ACTIF' | 'INACTIF' | 'SUSPENDU';
  type: 'PRODUITS' | 'SERVICES' | 'MIXTE';
  conditions_paiement: string;
  delai_paiement: number;
  total_achats: number;
  derniere_commande?: string;
  created_at: string;
}

// Routes
export const FOURNISSEURS_ROUTES = {
  MAIN: '/fournisseurs',
  NEW: '/fournisseurs/new',
  DETAIL: '/fournisseurs/:id',
  EDIT: '/fournisseurs/:id/edit',
  STATS: '/fournisseurs/stats'
} as const;
