"""
Service d'analytics intelligent pour le tableau de bord
"""
from django.db.models import Sum, Count, Avg, Q, F
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from Produits.models import Produit, Categorie
from Clients.models import Client
from Facturation.models import Facture
from Ecommerce.models import ProduitEcommerce, CategorieEcommerce
from Core.models import AIInsight, SmartNotification
import json


class AnalyticsService:
    """Service d'analytics intelligent"""
    
    @staticmethod
    def get_dashboard_metrics():
        """Récupère les métriques principales du tableau de bord"""
        now = timezone.now()
        today = now.date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        # Métriques des produits
        total_produits = Produit.objects.filter(is_active=True).count()
        produits_en_stock = Produit.objects.filter(
            is_active=True,
            stock__quantite__gt=0
        ).distinct().count()
        
        produits_stock_faible = Produit.objects.filter(
            is_active=True
        ).annotate(
            stock_total=Sum('stock__quantite')
        ).filter(
            stock_total__lte=F('stock_minimum')
        ).count()
        
        # Métriques des clients
        total_clients = Client.objects.filter(is_active=True).count()
        nouveaux_clients_semaine = Client.objects.filter(
            is_active=True,
            created_at__gte=week_ago
        ).count()
        
        # Métriques des ventes
        factures_mois = Facture.objects.filter(
            date_emission__gte=month_ago,
            statut__in=['VALIDEE', 'PAYEE']
        )
        
        ca_mois = factures_mois.aggregate(
            total=Sum('montant_ttc')
        )['total'] or Decimal('0')
        
        nb_factures_mois = factures_mois.count()
        
        # Métriques e-commerce
        produits_en_ligne = ProduitEcommerce.objects.filter(
            visible_en_ligne=True,
            produit__is_active=True
        ).count()
        
        produits_promo = ProduitEcommerce.objects.filter(
            en_promotion=True,
            visible_en_ligne=True
        ).count()
        
        # Calcul des tendances
        ca_semaine_precedente = Facture.objects.filter(
            date_emission__gte=month_ago - timedelta(days=7),
            date_emission__lt=month_ago,
            statut__in=['VALIDEE', 'PAYEE']
        ).aggregate(total=Sum('montant_ttc'))['total'] or Decimal('0')
        
        tendance_ca = AnalyticsService._calculate_trend(ca_mois, ca_semaine_precedente)
        
        return {
            'produits': {
                'total': total_produits,
                'en_stock': produits_en_stock,
                'stock_faible': produits_stock_faible,
                'taux_disponibilite': (produits_en_stock / total_produits * 100) if total_produits > 0 else 0
            },
            'clients': {
                'total': total_clients,
                'nouveaux_semaine': nouveaux_clients_semaine,
                'croissance_semaine': (nouveaux_clients_semaine / total_clients * 100) if total_clients > 0 else 0
            },
            'ventes': {
                'ca_mois': float(ca_mois),
                'nb_factures_mois': nb_factures_mois,
                'panier_moyen': float(ca_mois / nb_factures_mois) if nb_factures_mois > 0 else 0,
                'tendance_ca': tendance_ca
            },
            'ecommerce': {
                'produits_en_ligne': produits_en_ligne,
                'produits_promo': produits_promo,
                'taux_promotion': (produits_promo / produits_en_ligne * 100) if produits_en_ligne > 0 else 0
            }
        }
    
    @staticmethod
    def get_top_products(limit=5):
        """Récupère les produits les plus performants"""
        # Top produits par ventes
        top_ventes = Produit.objects.filter(
            is_active=True,
            ventes_total__gt=0
        ).order_by('-ventes_total')[:limit]
        
        # Top produits par vues
        top_vues = Produit.objects.filter(
            is_active=True,
            vues_total__gt=0
        ).order_by('-vues_total')[:limit]
        
        # Top produits par marge
        top_marge = Produit.objects.filter(
            is_active=True,
            cout_achat__gt=0
        ).annotate(
            marge=F('prix_unitaire') - F('cout_achat')
        ).order_by('-marge')[:limit]
        
        return {
            'top_ventes': [
                {
                    'nom': p.nom,
                    'ventes': p.ventes_total,
                    'ca': float(p.ventes_total * p.prix_unitaire)
                } for p in top_ventes
            ],
            'top_vues': [
                {
                    'nom': p.nom,
                    'vues': p.vues_total,
                    'taux_conversion': (p.ventes_total / p.vues_total * 100) if p.vues_total > 0 else 0
                } for p in top_vues
            ],
            'top_marge': [
                {
                    'nom': p.nom,
                    'marge': float(p.prix_unitaire - (p.cout_achat or 0)),
                    'taux_marge': p.marge_beneficiaire
                } for p in top_marge
            ]
        }
    
    @staticmethod
    def get_category_performance():
        """Analyse la performance par catégorie"""
        categories = Categorie.objects.filter(is_active=True).annotate(
            nb_produits=Count('produits', filter=Q(produits__is_active=True)),
            ca_total=Sum('produits__ventes_total') * F('produits__prix_unitaire'),
            vues_total=Sum('produits__vues_total')
        ).order_by('-ca_total')
        
        return [
            {
                'nom': cat.nom,
                'nb_produits': cat.nb_produits,
                'ca_total': float(cat.ca_total or 0),
                'vues_total': cat.vues_total or 0,
                'color': cat.color,
                'icon': cat.icon
            } for cat in categories if cat.nb_produits > 0
        ]
    
    @staticmethod
    def get_stock_alerts():
        """Récupère les alertes de stock"""
        # Produits en rupture
        rupture_stock = Produit.objects.filter(
            is_active=True
        ).annotate(
            stock_total=Sum('stock__quantite')
        ).filter(
            stock_total__lte=0
        )
        
        # Produits en stock faible
        stock_faible = Produit.objects.filter(
            is_active=True
        ).annotate(
            stock_total=Sum('stock__quantite')
        ).filter(
            stock_total__gt=0,
            stock_total__lte=F('stock_minimum')
        )
        
        # Produits sans mouvement depuis 30 jours
        date_limite = timezone.now() - timedelta(days=30)
        sans_mouvement = Produit.objects.filter(
            is_active=True,
            updated_at__lt=date_limite
        ).distinct()
        
        return {
            'rupture_stock': [
                {
                    'nom': p.nom,
                    'code': p.code_produit,
                    'stock_actuel': p.stock_actuel,
                    'stock_minimum': p.stock_minimum
                } for p in rupture_stock[:10]
            ],
            'stock_faible': [
                {
                    'nom': p.nom,
                    'code': p.code_produit,
                    'stock_actuel': p.stock_actuel,
                    'stock_minimum': p.stock_minimum
                } for p in stock_faible[:10]
            ],
            'sans_mouvement': [
                {
                    'nom': p.nom,
                    'code': p.code_produit,
                    'derniere_activite': p.updated_at
                } for p in sans_mouvement[:10]
            ]
        }
    
    @staticmethod
    def get_client_insights():
        """Analyse des insights clients"""
        # Clients les plus actifs
        top_clients = Client.objects.filter(
            is_active=True,
            total_achats__gt=0
        ).order_by('-total_achats')[:5]
        
        # Répartition par type de client
        repartition_type = Client.objects.filter(
            is_active=True
        ).values('type_client').annotate(
            count=Count('id'),
            ca_total=Sum('total_achats')
        )
        
        # Clients par segment
        repartition_segment = Client.objects.filter(
            is_active=True,
            segment_client__isnull=False
        ).values('segment_client').annotate(
            count=Count('id')
        )
        
        return {
            'top_clients': [
                {
                    'nom': f"{c.prenom} {c.nom}".strip(),
                    'email': c.email,
                    'total_achats': float(c.total_achats),
                    'score_credit': c.score_credit,
                    'segment': c.segment_client
                } for c in top_clients
            ],
            'repartition_type': list(repartition_type),
            'repartition_segment': list(repartition_segment)
        }
    
    @staticmethod
    def generate_ai_insights():
        """Génère des insights IA basés sur les données"""
        insights = []
        
        # Analyse des tendances de vente
        metrics = AnalyticsService.get_dashboard_metrics()
        
        if metrics['ventes']['tendance_ca'] > 10:
            insights.append({
                'type': 'POSITIVE',
                'title': 'Croissance des ventes',
                'message': f"Excellente performance ! Le CA a augmenté de {metrics['ventes']['tendance_ca']:.1f}% ce mois.",
                'priority': 'HIGH',
                'action_recommandee': 'Maintenir la stratégie actuelle'
            })
        elif metrics['ventes']['tendance_ca'] < -10:
            insights.append({
                'type': 'WARNING',
                'title': 'Baisse des ventes',
                'message': f"Attention : le CA a diminué de {abs(metrics['ventes']['tendance_ca']):.1f}% ce mois.",
                'priority': 'HIGH',
                'action_recommandee': 'Analyser les causes et ajuster la stratégie'
            })
        
        # Analyse du stock
        if metrics['produits']['stock_faible'] > 0:
            insights.append({
                'type': 'WARNING',
                'title': 'Alerte stock',
                'message': f"{metrics['produits']['stock_faible']} produits ont un stock faible.",
                'priority': 'MEDIUM',
                'action_recommandee': 'Planifier les réapprovisionnements'
            })
        
        # Analyse e-commerce
        if metrics['ecommerce']['taux_promotion'] < 5:
            insights.append({
                'type': 'SUGGESTION',
                'title': 'Opportunité promotions',
                'message': f"Seulement {metrics['ecommerce']['taux_promotion']:.1f}% des produits sont en promotion.",
                'priority': 'LOW',
                'action_recommandee': 'Considérer plus de promotions pour stimuler les ventes'
            })
        
        # Sauvegarder les insights
        for insight_data in insights:
            AIInsight.objects.create(
                type=insight_data['type'],
                title=insight_data['title'],
                description=insight_data['message'],
                data={'analytics': metrics, 'priority': insight_data['priority']},
                recommendations=[insight_data['action_recommandee']],
                confidence_score=0.8  # Score par défaut
            )
        
        return insights
    
    @staticmethod
    def _calculate_trend(current_value, previous_value):
        """Calcule la tendance en pourcentage"""
        if previous_value == 0:
            return 100 if current_value > 0 else 0
        
        return ((current_value - previous_value) / previous_value) * 100
