/**
 * Types pour l'authentification
 */

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name?: string;
  last_name?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  role_display: string;
  permissions: string[];
  is_active: boolean;
  date_joined: string;
  last_login?: string;
}

export enum UserRole {
  SUPERADMIN = "SUPERADMIN",
  ADMIN = "ADMIN",
  COMPTABLE = "COMPTABLE",
  VENDEUR = "VENDEUR",
  UTILISATEUR = "UTILISATEUR",
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  token?: string;
  user?: User;
  errors?: Record<string, string[]>;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  logout: () => void;
  register: (data: RegisterData) => Promise<AuthResponse>;
  hasRole: (role: UserRole) => boolean;
  hasMinimumRole: (role: UserRole) => boolean;
  hasPermission: (permission: string) => boolean;
}

export interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: number;
}

export interface TokenPayload {
  user_id: number;
  username: string;
  role: UserRole;
  exp: number;
  iat: number;
}
