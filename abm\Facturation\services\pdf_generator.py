"""
Service de génération PDF professionnel pour les factures
Basé sur l'image fournie avec design moderne et personnalisable
"""

import os
import io
from decimal import Decimal
from django.conf import settings
from django.template.loader import render_to_string
from django.http import HttpResponse
from django.utils import timezone
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_LEFT, TA_RIGHT, TA_CENTER
import logging

logger = logging.getLogger(__name__)


class FacturePDFGenerator:
    """Générateur PDF professionnel pour les factures"""
    
    def __init__(self, facture):
        self.facture = facture
        self.config = self._get_configuration()
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _get_configuration(self):
        """Récupère la configuration de facturation"""
        from ..models_professional import ConfigurationFacturation
        config = ConfigurationFacturation.objects.first()
        if not config:
            # Configuration par défaut
            config = ConfigurationFacturation.objects.create(
                nom_societe="Société Commerciale de Comptabilité",
                adresse_ligne1="10, Rue de la Commission",
                code_postal="06000",
                ville="Nice",
                siret="12345678901234",
                numero_tva="FR12345678901234",
                telephone="+33 4 93 000 000",
                email="<EMAIL>"
            )
        return config
    
    def _setup_custom_styles(self):
        """Configure les styles personnalisés"""
        # Style pour l'en-tête société
        self.style_societe = ParagraphStyle(
            'SocieteStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_LEFT,
            spaceAfter=6
        )
        
        # Style pour le titre FACTURE
        self.style_titre = ParagraphStyle(
            'TitreStyle',
            parent=self.styles['Heading1'],
            fontSize=24,
            textColor=colors.HexColor('#3498db'),
            alignment=TA_CENTER,
            spaceAfter=20,
            fontName='Helvetica-Bold'
        )
        
        # Style pour les informations client
        self.style_client = ParagraphStyle(
            'ClientStyle',
            parent=self.styles['Normal'],
            fontSize=10,
            alignment=TA_LEFT,
            spaceAfter=4
        )
        
        # Style pour les totaux
        self.style_total = ParagraphStyle(
            'TotalStyle',
            parent=self.styles['Normal'],
            fontSize=11,
            fontName='Helvetica-Bold',
            alignment=TA_RIGHT
        )
    
    def generer_pdf(self):
        """Génère le PDF de la facture"""
        try:
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # Construction du contenu
            story = []
            
            # En-tête avec logo et informations société
            story.extend(self._build_header())
            
            # Titre et numéro de facture
            story.extend(self._build_title())
            
            # Informations client et facture
            story.extend(self._build_client_info())
            
            # Tableau des articles
            story.extend(self._build_articles_table())
            
            # Totaux
            story.extend(self._build_totals())
            
            # Pied de page avec conditions
            story.extend(self._build_footer())
            
            # Génération du PDF
            doc.build(story)
            
            pdf_content = buffer.getvalue()
            buffer.close()
            
            return pdf_content
            
        except Exception as e:
            logger.error(f"Erreur génération PDF facture {self.facture.numero}: {str(e)}")
            raise
    
    def _build_header(self):
        """Construit l'en-tête avec logo et informations société"""
        elements = []
        
        # Table pour logo et informations société
        data = []
        
        # Logo (si disponible)
        logo_cell = ""
        if self.config.logo and os.path.exists(self.config.logo.path):
            try:
                logo = Image(self.config.logo.path, width=3*cm, height=2*cm)
                logo_cell = logo
            except:
                logo_cell = ""
        
        # Informations société (style de l'image)
        societe_info = f"""
        <b>{self.config.nom_societe}</b><br/>
        {self.config.adresse_ligne1}<br/>
        {self.config.code_postal} {self.config.ville}<br/>
        Tél: {self.config.telephone}<br/>
        Email: {self.config.email}
        """
        
        # Informations légales (coin supérieur droit comme dans l'image)
        legal_info = f"""
        <b>SIRET:</b> {self.config.siret}<br/>
        <b>TVA:</b> {self.config.numero_tva}<br/>
        <b>APE:</b> {self.config.code_ape or 'N/A'}
        """
        
        data.append([
            logo_cell,
            Paragraph(societe_info, self.style_societe),
            Paragraph(legal_info, self.style_client)
        ])
        
        table = Table(data, colWidths=[3*cm, 8*cm, 6*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 1*cm))
        
        return elements
    
    def _build_title(self):
        """Construit le titre et les informations de facture"""
        elements = []
        
        # Titre FACTURE N°: XXXX (comme dans l'image)
        titre = f"FACTURE N°: {self.facture.numero}"
        elements.append(Paragraph(titre, self.style_titre))
        
        # Date (coin supérieur droit comme dans l'image)
        date_str = f"Date: {self.facture.date_facture.strftime('%d/%m/%Y')}"
        date_para = Paragraph(date_str, ParagraphStyle(
            'DateStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            alignment=TA_RIGHT,
            spaceAfter=20
        ))
        elements.append(date_para)
        
        return elements
    
    def _build_client_info(self):
        """Construit les informations client (structure de l'image)"""
        elements = []
        
        # Table pour client et informations facture
        data = []
        
        # Informations client (côté gauche)
        client_info = f"""
        <b>N° Client: {self.facture.client.id}</b><br/>
        <b>Nom:</b> {self.facture.client.nom}<br/>
        <b>Adresse:</b> {self.facture.client.adresse or 'N/A'}
        """
        
        # Informations facture (côté droit, comme dans l'image)
        facture_info = f"""
        <b>Matricule Fiscal:</b> MF 1234567890<br/>
        <b>Téléphone:</b> {self.facture.client.telephone or 'N/A'}
        """
        
        data.append([
            Paragraph(client_info, self.style_client),
            Paragraph(facture_info, self.style_client)
        ])
        
        table = Table(data, colWidths=[9*cm, 8*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
            ('INNERGRID', (0, 0), (-1, -1), 1, colors.black),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 0.5*cm))
        
        return elements

    def _build_articles_table(self):
        """Construit le tableau des articles (structure exacte de l'image)"""
        elements = []

        # En-têtes du tableau (exactement comme dans l'image)
        headers = [
            'Code Article',
            'Désignation',
            'Qté',
            'P.U H.T',
            'Rem %',
            'P.U T.T.C',
            'Mt Ht H.T',
            'TVA %'
        ]

        # Données du tableau
        data = [headers]

        # Lignes de la facture
        for ligne in self.facture.lignes.all():
            row = [
                ligne.code_article,
                ligne.designation,
                f"{ligne.quantite:g}",  # Supprime les zéros inutiles
                f"{ligne.prix_unitaire_ht:.2f}",
                f"{ligne.remise_pourcentage:.1f}" if ligne.remise_pourcentage > 0 else "0",
                f"{ligne.prix_unitaire_ttc:.2f}",
                f"{ligne.montant_ht:.2f}",
                f"{ligne.taux_tva:.0f}"
            ]
            data.append(row)

        # Création du tableau
        table = Table(data, colWidths=[
            2.2*cm,  # Code Article
            5*cm,    # Désignation
            1.2*cm,  # Qté
            1.8*cm,  # P.U H.T
            1.2*cm,  # Rem %
            1.8*cm,  # P.U T.T.C
            2*cm,    # Mt Ht H.T
            1.2*cm   # TVA %
        ])

        # Style du tableau (couleurs et bordures comme dans l'image)
        table.setStyle(TableStyle([
            # En-tête avec fond bleu foncé
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2c3e50')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

            # Corps du tableau
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),    # Code Article
            ('ALIGN', (1, 1), (1, -1), 'LEFT'),    # Désignation
            ('ALIGN', (2, 1), (-1, -1), 'RIGHT'),  # Autres colonnes

            # Bordures
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

            # Padding
            ('LEFTPADDING', (0, 0), (-1, -1), 4),
            ('RIGHTPADDING', (0, 0), (-1, -1), 4),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 0.5*cm))

        return elements

    def _build_totals(self):
        """Construit la section des totaux (structure de l'image)"""
        elements = []

        # Table pour les totaux (côté droit comme dans l'image)
        totals_data = []

        # Section Client et Cachet et Signature (côté gauche)
        client_section = """
        <b>Client</b><br/><br/><br/><br/><br/>
        """

        signature_section = """
        <b>Cachet et Signature</b><br/><br/><br/><br/><br/>
        """

        # Section totaux (côté droit, structure exacte de l'image)
        totaux_section = f"""
        <b>Montant Brut:</b><br/>
        <b>Remise (%):</b><br/>
        <b>Montant HT:</b><br/>
        <b>TVA (20%):</b><br/>
        <b>Timbre Fiscal:</b><br/>
        <b>TOTAL TTC:</b>
        """

        montants_section = f"""
        {self.facture.montant_ht + self.facture.remise_montant:.2f}<br/>
        {self.facture.remise_montant:.2f}<br/>
        {self.facture.montant_ht:.2f}<br/>
        {self.facture.montant_tva:.2f}<br/>
        0.00<br/>
        <b>{self.facture.montant_ttc:.2f}</b>
        """

        # Première ligne: Client et totaux
        totals_data.append([
            Paragraph(client_section, self.style_client),
            Paragraph(totaux_section, self.style_client),
            Paragraph(montants_section, self.style_total)
        ])

        # Deuxième ligne: Signature (vide pour l'espace)
        totals_data.append([
            Paragraph(signature_section, self.style_client),
            "",
            ""
        ])

        totals_table = Table(totals_data, colWidths=[6*cm, 5*cm, 4*cm])
        totals_table.setStyle(TableStyle([
            # Bordures pour les sections
            ('BOX', (0, 0), (0, 1), 1, colors.black),  # Client
            ('BOX', (1, 0), (2, 0), 1, colors.black),  # Totaux
            ('INNERGRID', (1, 0), (2, 0), 0.5, colors.gray),

            # Alignement
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('ALIGN', (2, 0), (2, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),

            # Padding
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(totals_table)
        elements.append(Spacer(1, 1*cm))

        return elements

    def _build_footer(self):
        """Construit le pied de page avec conditions et mentions légales"""
        elements = []

        # Ligne de séparation
        elements.append(HRFlowable(width="100%", thickness=1, color=colors.gray))
        elements.append(Spacer(1, 0.3*cm))

        # Conditions de paiement (comme dans l'image)
        conditions_text = f"""
        <b>Arrêtée la présente facture à la somme de:</b><br/>
        {self._nombre_en_lettres(self.facture.montant_ttc)} euros<br/><br/>

        <b>Six cent quatre-vingt-dix euros et quarante-cinq centimes</b><br/><br/>

        Payé en: ☐ Espèce ☐ Chèque ☐ Traite<br/><br/>

        {self.config.conditions_paiement}<br/>
        {self.config.mentions_legales}
        """

        footer_para = Paragraph(conditions_text, ParagraphStyle(
            'FooterStyle',
            parent=self.styles['Normal'],
            fontSize=8,
            alignment=TA_LEFT,
            spaceAfter=6
        ))

        elements.append(footer_para)

        # Informations société en bas (comme dans l'image)
        footer_info = f"""
        <b>Siège commercial:</b> {self.config.adresse_ligne1}, {self.config.code_postal} {self.config.ville}<br/>
        <b>SIRET:</b> {self.config.siret} - <b>APE:</b> {self.config.code_ape or 'N/A'}<br/>
        <b>Email:</b> {self.config.email} - <b>MF:</b> {self.config.numero_tva}
        """

        footer_final = Paragraph(footer_info, ParagraphStyle(
            'FooterFinalStyle',
            parent=self.styles['Normal'],
            fontSize=7,
            alignment=TA_CENTER,
            textColor=colors.gray
        ))

        elements.append(Spacer(1, 0.5*cm))
        elements.append(footer_final)

        return elements

    def _nombre_en_lettres(self, montant):
        """Convertit un montant en lettres (version simplifiée)"""
        # Version simplifiée - dans un vrai projet, utiliser une bibliothèque dédiée
        montant_int = int(montant)
        if montant_int < 1000:
            return f"{montant_int}"
        else:
            return f"{montant_int}"  # Simplification pour l'exemple

    def sauvegarder_pdf(self):
        """Sauvegarde le PDF dans le système de fichiers"""
        try:
            pdf_content = self.generer_pdf()

            # Créer le répertoire si nécessaire
            pdf_dir = os.path.join(settings.MEDIA_ROOT, 'facturation', 'pdf')
            os.makedirs(pdf_dir, exist_ok=True)

            # Nom du fichier
            filename = f"facture_{self.facture.numero}_{timezone.now().strftime('%Y%m%d')}.pdf"
            filepath = os.path.join(pdf_dir, filename)

            # Sauvegarder le fichier
            with open(filepath, 'wb') as f:
                f.write(pdf_content)

            # Mettre à jour le modèle
            self.facture.fichier_pdf.name = f'facturation/pdf/{filename}'
            self.facture.save(update_fields=['fichier_pdf'])

            return filepath

        except Exception as e:
            logger.error(f"Erreur sauvegarde PDF facture {self.facture.numero}: {str(e)}")
            raise

    def generer_response_http(self, filename=None):
        """Génère une réponse HTTP avec le PDF"""
        try:
            pdf_content = self.generer_pdf()

            if not filename:
                filename = f"facture_{self.facture.numero}.pdf"

            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            logger.error(f"Erreur génération réponse HTTP PDF: {str(e)}")
            raise


# Fonction utilitaire pour générer rapidement un PDF
def generer_facture_pdf(facture, sauvegarder=True):
    """
    Fonction utilitaire pour générer le PDF d'une facture

    Args:
        facture: Instance de Facture
        sauvegarder: Si True, sauvegarde le PDF sur le disque

    Returns:
        Contenu PDF en bytes ou chemin du fichier si sauvegardé
    """
    generator = FacturePDFGenerator(facture)

    if sauvegarder:
        return generator.sauvegarder_pdf()
    else:
        return generator.generer_pdf()
