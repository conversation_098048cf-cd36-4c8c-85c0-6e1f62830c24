import React, { useState, Suspense, useEffect } from "react";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { NotificationProvider } from "./components/Common/NotificationSystem";
import { ToastContainer } from "react-toastify";
import AuthPage from "./components/Auth/AuthPage";
import RouteGuard from "./components/Auth/RouteGuard";
import SimpleDashboard from "./components/Dashboard/SimpleDashboard";
import SimpleClients from "./components/Clients/SimpleClients";
import SimpleNavigation from "./components/Common/SimpleNavigation";
import ErrorBoundary from "./components/Common/ErrorBoundary";
import "./App.css";
import "./components/Common/BenChaabeneStyles.css";
import "react-toastify/dist/ReactToastify.css";

// Import des composants principaux (listes sans routing)
const ProductsList = React.lazy(
  () => import("./components/Products/ProductsList")
);
const FacturationProfessionnelle = React.lazy(
  () => import("./components/Factures/FacturationProfessionnelle")
);
const OrdersList = React.lazy(() => import("./components/Orders/OrdersList"));
const PaymentsList = React.lazy(
  () => import("./components/Payments/PaymentsList")
);
const StockOverview = React.lazy(
  () => import("./components/Stock/StockOverview")
);
const NewClient = React.lazy(() => import("./components/Clients/NewClient"));
const NewProduct = React.lazy(() => import("./components/Products/NewProduct"));
const ServiceTest = React.lazy(() => import("./components/Debug/ServiceTest"));
const SimpleReports = React.lazy(
  () => import("./components/Reports/SimpleReports")
);
const NewOrder = React.lazy(() => import("./components/Orders/NewOrder"));
const AppSettings = React.lazy(
  () => import("./components/Settings/AppSettings")
);

// Composant principal de l'application
const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading, user, hasModuleAccess } = useAuth();
  const [currentPage, setCurrentPage] = useState("dashboard");
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Forcer la mise à jour quand l'utilisateur change
  useEffect(() => {
    if (user) {
      console.log("Utilisateur connecté:", user);
    }
  }, [user]);

  // Gérer les changements d'URL
  useEffect(() => {
    const handleLocationChange = () => {
      const path = window.location.pathname;
      if (path === "/nouvelle-facture") {
        setCurrentPage("nouvelle-facture");
      } else if (path === "/nouveau-client") {
        setCurrentPage("nouveau-client");
      } else if (path === "/nouveau-produit") {
        setCurrentPage("nouveau-produit");
      } else if (path === "/nouvelle-commande") {
        setCurrentPage("nouvelle-commande");
      } else if (path === "/factures") {
        setCurrentPage("factures");
      } else if (path === "/clients") {
        setCurrentPage("clients");
      } else if (path === "/produits") {
        setCurrentPage("produits");
      } else if (path === "/commandes") {
        setCurrentPage("orders");
      } else if (path === "/paiements") {
        setCurrentPage("payments");
      } else if (path === "/stock") {
        setCurrentPage("stock");
      } else if (path === "/rapports") {
        setCurrentPage("rapports");
      } else {
        setCurrentPage("dashboard");
      }
    };

    // Écouter les changements d'URL
    window.addEventListener("popstate", handleLocationChange);
    handleLocationChange(); // Appeler une fois au chargement

    return () => {
      window.removeEventListener("popstate", handleLocationChange);
    };
  }, []);

  // Affichage pendant le chargement de l'authentification
  if (isLoading) {
    return (
      <div className="auth-loading">
        <div className="loading-spinner"></div>
        <p>Vérification de l'authentification...</p>
      </div>
    );
  }

  // Affichage de la page de connexion si non authentifié
  if (!isAuthenticated) {
    return <AuthPage />;
  }

  const renderPage = () => {
    switch (currentPage) {
      case "dashboard":
        return (
          <RouteGuard requiredModule="dashboard">
            <SimpleDashboard />
          </RouteGuard>
        );
      case "clients":
        return (
          <RouteGuard requiredModule="clients">
            <SimpleClients />
          </RouteGuard>
        );
      case "products":
        return (
          <RouteGuard requiredModule="produits">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <ProductsList />
            </Suspense>
          </RouteGuard>
        );
      case "factures":
      case "nouvelle-facture":
        return (
          <RouteGuard requiredModule="factures">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <FacturationProfessionnelle />
            </Suspense>
          </RouteGuard>
        );
      case "nouveau-client":
        return (
          <RouteGuard requiredModule="clients">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <NewClient />
            </Suspense>
          </RouteGuard>
        );
      case "nouveau-produit":
        return (
          <RouteGuard requiredModule="produits">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <NewProduct />
            </Suspense>
          </RouteGuard>
        );
      case "nouvelle-commande":
        return (
          <RouteGuard requiredModule="commandes">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <NewOrder />
            </Suspense>
          </RouteGuard>
        );
      case "orders":
        return (
          <RouteGuard requiredModule="commandes">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <OrdersList />
            </Suspense>
          </RouteGuard>
        );
      case "payments":
        return (
          <RouteGuard requiredModule="paiements">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <PaymentsList />
            </Suspense>
          </RouteGuard>
        );
      case "stock":
        return (
          <RouteGuard requiredModule="stock">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <StockOverview />
            </Suspense>
          </RouteGuard>
        );
      case "rapports":
        return (
          <RouteGuard requiredModule="rapports">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <SimpleReports />
            </Suspense>
          </RouteGuard>
        );
      case "comptable":
        return (
          <div className="page-placeholder">
            <h1>📈 Comptabilité</h1>
            <p>Module Comptabilité en cours de développement...</p>
          </div>
        );
      case "ecommerce":
        return (
          <div className="page-placeholder">
            <h1>🛒 E-commerce</h1>
            <p>Module E-commerce en cours de développement...</p>
          </div>
        );
      case "settings":
        return (
          <RouteGuard requiredModule="settings">
            <Suspense fallback={<div className="loading">Chargement...</div>}>
              <AppSettings />
            </Suspense>
          </RouteGuard>
        );
      case "debug":
        return (
          <Suspense fallback={<div className="loading">Chargement...</div>}>
            <ServiceTest />
          </Suspense>
        );
      default:
        return <SimpleDashboard />;
    }
  };

  return (
    <div
      className={`app-layout ${sidebarCollapsed ? "sidebar-collapsed" : ""}`}>
      {/* Sidebar avec navigation */}
      <aside className="app-sidebar">
        <SimpleNavigation
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          isCollapsed={sidebarCollapsed}
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
      </aside>

      {/* Contenu principal */}
      <main className="app-main">
        <div className="app-content">{renderPage()}</div>
      </main>
    </div>
  );
};

// Composant App principal avec providers
const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <NotificationProvider>
          <div className="App">
            <Suspense
              fallback={
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <p>Chargement de l'application...</p>
                </div>
              }>
              <AppContent />
            </Suspense>

            {/* Notifications Toast */}
            <ToastContainer
              position="top-right"
              autoClose={3000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
            />
          </div>
        </NotificationProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
};

export default App;
