/* Styles pour le module E-commerce */

.ecommerce-container {
  padding: 20px;
}

.ecommerce-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.ecommerce-title {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.ecommerce-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.ecommerce-tab {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.ecommerce-tab.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.ecommerce-tab:hover {
  color: #007bff;
}

.ecommerce-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.coming-soon h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #333;
}

.coming-soon p {
  margin-bottom: 10px;
}

.coming-soon ul {
  text-align: left;
  display: inline-block;
  margin-top: 20px;
}

.coming-soon li {
  margin-bottom: 8px;
  padding-left: 10px;
}

/* Responsive */
@media (max-width: 768px) {
  .ecommerce-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .ecommerce-tabs {
    flex-wrap: wrap;
  }
  
  .ecommerce-tab {
    flex: 1;
    min-width: 120px;
  }
}
