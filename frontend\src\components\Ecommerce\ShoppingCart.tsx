import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { EcommerceService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import "../Invoices/Invoice.css";

interface CartItem {
  id: string;
  produit_ecommerce: string;
  produit_nom: string;
  produit_slug: string;
  image_principale?: string;
  quantite: number;
  prix_unitaire: number;
  total_ht: number;
  total_tva: number;
  total_ttc: number;
}

interface Cart {
  id: string;
  items: CartItem[];
  total_items: number;
  total_ht: number;
  total_tva: number;
  total_ttc: number;
  session_id?: string;
}

const ShoppingCart: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState<string | null>(null);

  const loadCart = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const sessionId = localStorage.getItem("ecommerce_session_id");
      const response = await EcommerceService.getCart(sessionId || undefined);

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        setCart(response.data);
      }
    } catch (err: any) {
      setError(err.message);
      notify.error(`Erreur lors du chargement du panier: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [notify]);

  useEffect(() => {
    loadCart();
  }, [loadCart]);

  const updateQuantity = async (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      await removeItem(itemId);
      return;
    }

    try {
      setUpdating(itemId);
      const response = await EcommerceService.updateCartItem(
        itemId,
        newQuantity
      );
      if (response.error) {
        throw new Error(response.error);
      }

      await loadCart(); // Recharger le panier
      notify.success("Quantité mise à jour");
    } catch (err: any) {
      notify.error(`Erreur: ${err.message}`);
    } finally {
      setUpdating(null);
    }
  };

  const removeItem = async (itemId: string) => {
    try {
      setUpdating(itemId);
      const response = await EcommerceService.removeFromCart(itemId);
      if (response.error) {
        throw new Error(response.error);
      }

      await loadCart();
      notify.success("Produit retiré du panier");
    } catch (err: any) {
      notify.error(`Erreur: ${err.message}`);
    } finally {
      setUpdating(null);
    }
  };

  const clearCart = async () => {
    if (!window.confirm("Êtes-vous sûr de vouloir vider le panier ?")) return;

    try {
      // Supprimer tous les items un par un
      if (cart?.items) {
        for (const item of cart.items) {
          await EcommerceService.removeFromCart(item.id);
        }
      }
      await loadCart();
      notify.success("Panier vidé");
    } catch (err: any) {
      notify.error(`Erreur: ${err.message}`);
    }
  };

  const proceedToCheckout = () => {
    if (!cart || cart.items.length === 0) {
      notify.error("Erreur", "Votre panier est vide");
      return;
    }
    navigate("/dashboard/ecommerce/commande");
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">Chargement du panier...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Mon Panier</h1>
            <p className="page-subtitle">
              {cart?.total_items || 0} article(s) dans votre panier
            </p>
          </div>
          <div className="page-actions">
            <button
              className="page-action secondary"
              onClick={() => navigate("/dashboard/ecommerce/catalogue")}>
              ← Continuer les achats
            </button>
            {cart && cart.items.length > 0 && (
              <button
                className="page-action"
                onClick={proceedToCheckout}>
                🛒 Passer commande
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="page-content">
        {cart && cart.items.length > 0 ? (
          <div className="content-grid two-columns">
            {/* Articles du panier */}
            <div
              className="content-card"
              style={{ gridColumn: "1 / -1" }}>
              <div className="card-header">
                <h3 className="card-title">Articles</h3>
                <button
                  onClick={clearCart}
                  style={{
                    padding: "6px 12px",
                    background: "#e74c3c",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: "pointer",
                    fontSize: "14px",
                  }}>
                  🗑️ Vider le panier
                </button>
              </div>
              <div className="card-content">
                <div className="table-container">
                  <table className="data-table">
                    <thead>
                      <tr>
                        <th>Produit</th>
                        <th>Prix unitaire</th>
                        <th>Quantité</th>
                        <th>Total</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {cart.items.map((item) => (
                        <tr key={item.id}>
                          <td>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "12px",
                              }}>
                              <div
                                style={{
                                  width: "60px",
                                  height: "60px",
                                  background: item.image_principale
                                    ? `url(${item.image_principale})`
                                    : "#f8f9fa",
                                  backgroundSize: "cover",
                                  backgroundPosition: "center",
                                  borderRadius: "6px",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  color: "#666",
                                }}>
                                {!item.image_principale && "📦"}
                              </div>
                              <div>
                                <strong>{item.produit_nom}</strong>
                                <div
                                  style={{ fontSize: "12px", color: "#666" }}>
                                  Code: {item.produit_ecommerce}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td>
                            <strong>
                              {new Intl.NumberFormat("fr-TN", {
                                style: "currency",
                                currency: "TND",
                              }).format(item.prix_unitaire)}
                            </strong>
                          </td>
                          <td>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "8px",
                              }}>
                              <button
                                onClick={() =>
                                  updateQuantity(item.id, item.quantite - 1)
                                }
                                disabled={updating === item.id}
                                style={{
                                  width: "30px",
                                  height: "30px",
                                  background: "#e74c3c",
                                  color: "white",
                                  border: "none",
                                  borderRadius: "4px",
                                  cursor:
                                    updating === item.id
                                      ? "not-allowed"
                                      : "pointer",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}>
                                -
                              </button>
                              <span
                                style={{
                                  minWidth: "40px",
                                  textAlign: "center",
                                  fontWeight: "bold",
                                }}>
                                {item.quantite}
                              </span>
                              <button
                                onClick={() =>
                                  updateQuantity(item.id, item.quantite + 1)
                                }
                                disabled={
                                  updating === item.id ||
                                  item.quantite >= item.produit.stock
                                }
                                style={{
                                  width: "30px",
                                  height: "30px",
                                  background: "#27ae60",
                                  color: "white",
                                  border: "none",
                                  borderRadius: "4px",
                                  cursor:
                                    updating === item.id ||
                                    item.quantite >= item.produit.stock
                                      ? "not-allowed"
                                      : "pointer",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}>
                                +
                              </button>
                            </div>
                            {item.quantite >= item.produit.stock && (
                              <div
                                style={{
                                  fontSize: "11px",
                                  color: "#e74c3c",
                                  marginTop: "4px",
                                }}>
                                Stock limité
                              </div>
                            )}
                          </td>
                          <td>
                            <strong>
                              {EcommerceService.formatPrice(item.total)}
                            </strong>
                          </td>
                          <td>
                            <button
                              onClick={() => removeItem(item.id)}
                              disabled={updating === item.id}
                              style={{
                                padding: "6px 12px",
                                background: "#e74c3c",
                                color: "white",
                                border: "none",
                                borderRadius: "6px",
                                cursor:
                                  updating === item.id
                                    ? "not-allowed"
                                    : "pointer",
                                fontSize: "12px",
                              }}>
                              {updating === item.id ? "..." : "🗑️ Retirer"}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Résumé de la commande */}
            <div className="content-card">
              <div className="card-header">
                <h3 className="card-title">Résumé</h3>
              </div>
              <div className="card-content">
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "12px",
                  }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}>
                    <span>Sous-total HT:</span>
                    <span>{EcommerceService.formatPrice(cart.total_ht)}</span>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}>
                    <span>TVA:</span>
                    <span>{EcommerceService.formatPrice(cart.total_tva)}</span>
                  </div>
                  <hr
                    style={{
                      margin: "8px 0",
                      border: "none",
                      borderTop: "1px solid #dee2e6",
                    }}
                  />
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      fontSize: "18px",
                      fontWeight: "bold",
                    }}>
                    <span>Total TTC:</span>
                    <span>{EcommerceService.formatPrice(cart.total_ttc)}</span>
                  </div>

                  <button
                    onClick={proceedToCheckout}
                    style={{
                      width: "100%",
                      padding: "12px",
                      background: "#27ae60",
                      color: "white",
                      border: "none",
                      borderRadius: "8px",
                      cursor: "pointer",
                      fontSize: "16px",
                      fontWeight: "bold",
                      marginTop: "15px",
                    }}>
                    🛒 Passer commande
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div
            style={{ textAlign: "center", padding: "60px", color: "#7f8c8d" }}>
            <div style={{ fontSize: "48px", marginBottom: "20px" }}>🛒</div>
            <h3>Votre panier est vide</h3>
            <p>Découvrez nos produits et ajoutez-les à votre panier</p>
            <button
              onClick={() => navigate("/dashboard/ecommerce/catalogue")}
              style={{
                padding: "12px 24px",
                background: "#3498db",
                color: "white",
                border: "none",
                borderRadius: "8px",
                cursor: "pointer",
                fontSize: "16px",
                marginTop: "20px",
              }}>
              Voir le catalogue
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShoppingCart;
