import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useNotify } from "../Common/NotificationSystem";

interface Fournisseur {
  id: string;
  nom: string;
  email: string;
  telephone: string;
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
  matricule_fiscal: string;
  conditions_paiement: string;
  delai_paiement: number;
  remise_habituelle: number;
  contact_principal: string;
  secteur_activite: string;
  notes: string;
  created_at: string;
  updated_at: string;
  statut: string;
  total_commandes: number;
  montant_total_achats: number;
  derniere_commande: string;
}

const FournisseurDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const notify = useNotify();

  const [fournisseur, setFournisseur] = useState<Fournisseur | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("infos");

  useEffect(() => {
    if (id) {
      loadFournisseur(id);
    }
  }, [id]);

  const loadFournisseur = async (fournisseurId: string) => {
    try {
      setLoading(true);
      // Simulation de données fournisseur
      const mockFournisseur: Fournisseur = {
        id: fournisseurId,
        nom: "FOURNISSEUR DESIGN MODERNE SARL",
        email: "<EMAIL>",
        telephone: "+216 71 456 789",
        adresse: "123 Rue du Commerce Moderne",
        ville: "Tunis",
        code_postal: "1000",
        pays: "Tunisie",
        matricule_fiscal: "MF987654321",
        conditions_paiement: "Paiement à 45 jours",
        delai_paiement: 45,
        remise_habituelle: 7.5,
        contact_principal: "Ahmed Ben Salah",
        secteur_activite: "Commerce de gros",
        notes:
          "Fournisseur principal pour les produits design. Très fiable avec d'excellents délais de livraison.",
        created_at: "2024-01-15T10:30:00Z",
        updated_at: "2025-08-10T14:20:00Z",
        statut: "ACTIF",
        total_commandes: 45,
        montant_total_achats: 125000,
        derniere_commande: "2025-08-05T09:15:00Z",
      };

      setFournisseur(mockFournisseur);
    } catch (error: any) {
      notify.error("Erreur lors du chargement du fournisseur");
      navigate("/fournisseurs");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR");
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case "ACTIF":
        return "success";
      case "INACTIF":
        return "warning";
      case "SUSPENDU":
        return "danger";
      default:
        return "secondary";
    }
  };

  if (loading) {
    return (
      <div className="fournisseur-detail-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des détails...</p>
      </div>
    );
  }

  if (!fournisseur) {
    return (
      <div className="fournisseur-detail-error">
        <div className="error-icon">❌</div>
        <h3>Fournisseur introuvable</h3>
        <p>Le fournisseur demandé n'existe pas ou a été supprimé</p>
        <button
          onClick={() => navigate("/fournisseurs")}
          className="btn-primary">
          ← Retour à la liste
        </button>
      </div>
    );
  }

  return (
    <div className="fournisseur-detail">
      {/* Header avec actions */}
      <div className="detail-header">
        <div className="header-info">
          <div className="fournisseur-title">
            <h1>🏢 {fournisseur.nom}</h1>
            <span
              className={`status-badge ${getStatusColor(fournisseur.statut)}`}>
              {fournisseur.statut}
            </span>
          </div>
          <div className="fournisseur-meta">
            <span>Créé le {formatDate(fournisseur.created_at)}</span>
            <span>Modifié le {formatDate(fournisseur.updated_at)}</span>
          </div>
        </div>

        <div className="header-actions">
          <button
            className="btn-secondary"
            onClick={() => navigate("/fournisseurs")}>
            ← Retour
          </button>
          <button
            className="btn-primary"
            onClick={() => navigate(`/fournisseurs/${fournisseur.id}/edit`)}>
            ✏️ Modifier
          </button>
          <button
            className="btn-success"
            onClick={() =>
              navigate(`/commandes/new?fournisseur=${fournisseur.id}`)
            }>
            🛒 Nouvelle commande
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="stats-overview">
        <div className="stat-card">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <div className="stat-value">{fournisseur.total_commandes}</div>
            <div className="stat-label">Commandes</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <div className="stat-value">
              {formatCurrency(fournisseur.montant_total_achats)}
            </div>
            <div className="stat-label">Total achats</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">📅</div>
          <div className="stat-content">
            <div className="stat-value">
              {formatDate(fournisseur.derniere_commande)}
            </div>
            <div className="stat-label">Dernière commande</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">💸</div>
          <div className="stat-content">
            <div className="stat-value">{fournisseur.remise_habituelle}%</div>
            <div className="stat-label">Remise habituelle</div>
          </div>
        </div>
      </div>

      {/* Onglets */}
      <div className="detail-tabs">
        <div className="tabs-header">
          <button
            className={`tab-button ${activeTab === "infos" ? "active" : ""}`}
            onClick={() => setActiveTab("infos")}>
            📋 Informations
          </button>
          <button
            className={`tab-button ${
              activeTab === "commandes" ? "active" : ""
            }`}
            onClick={() => setActiveTab("commandes")}>
            📦 Commandes
          </button>
          <button
            className={`tab-button ${activeTab === "stats" ? "active" : ""}`}
            onClick={() => setActiveTab("stats")}>
            📊 Statistiques
          </button>
        </div>

        <div className="tab-content">
          {activeTab === "infos" && (
            <div className="info-tab">
              <div className="info-sections">
                {/* Informations générales */}
                <div className="info-section">
                  <h3>📋 Informations générales</h3>
                  <div className="info-grid">
                    <div className="info-item">
                      <label>Nom:</label>
                      <span>{fournisseur.nom}</span>
                    </div>
                    <div className="info-item">
                      <label>Secteur d'activité:</label>
                      <span>
                        {fournisseur.secteur_activite || "Non spécifié"}
                      </span>
                    </div>
                    <div className="info-item">
                      <label>Matricule fiscal:</label>
                      <span>
                        {fournisseur.matricule_fiscal || "Non spécifié"}
                      </span>
                    </div>
                    <div className="info-item">
                      <label>Contact principal:</label>
                      <span>
                        {fournisseur.contact_principal || "Non spécifié"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Coordonnées */}
                <div className="info-section">
                  <h3>📞 Coordonnées</h3>
                  <div className="info-grid">
                    <div className="info-item">
                      <label>Email:</label>
                      <span>
                        <a href={`mailto:${fournisseur.email}`}>
                          {fournisseur.email}
                        </a>
                      </span>
                    </div>
                    <div className="info-item">
                      <label>Téléphone:</label>
                      <span>
                        <a href={`tel:${fournisseur.telephone}`}>
                          {fournisseur.telephone}
                        </a>
                      </span>
                    </div>
                    <div className="info-item">
                      <label>Adresse:</label>
                      <span>{fournisseur.adresse}</span>
                    </div>
                    <div className="info-item">
                      <label>Ville:</label>
                      <span>
                        {fournisseur.ville} {fournisseur.code_postal}
                      </span>
                    </div>
                    <div className="info-item">
                      <label>Pays:</label>
                      <span>{fournisseur.pays}</span>
                    </div>
                  </div>
                </div>

                {/* Conditions commerciales */}
                <div className="info-section">
                  <h3>💼 Conditions commerciales</h3>
                  <div className="info-grid">
                    <div className="info-item">
                      <label>Conditions de paiement:</label>
                      <span>{fournisseur.conditions_paiement}</span>
                    </div>
                    <div className="info-item">
                      <label>Délai de paiement:</label>
                      <span>{fournisseur.delai_paiement} jours</span>
                    </div>
                    <div className="info-item">
                      <label>Remise habituelle:</label>
                      <span>{fournisseur.remise_habituelle}%</span>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                {fournisseur.notes && (
                  <div className="info-section">
                    <h3>📝 Notes</h3>
                    <div className="notes-content">
                      <p>{fournisseur.notes}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === "commandes" && (
            <div className="commandes-tab">
              <div className="commandes-header">
                <h3>📦 Historique des commandes</h3>
                <button
                  className="btn-primary"
                  onClick={() =>
                    navigate(`/commandes/new?fournisseur=${fournisseur.id}`)
                  }>
                  ➕ Nouvelle commande
                </button>
              </div>

              <div className="commandes-list">
                <div className="commande-item">
                  <div className="commande-info">
                    <h4>Commande #CMD-2025-001</h4>
                    <p>Date: 05/08/2025</p>
                    <p>
                      Statut:{" "}
                      <span className="status-badge success">Livrée</span>
                    </p>
                  </div>
                  <div className="commande-amount">
                    <span className="amount">{formatCurrency(15750)}</span>
                  </div>
                </div>

                <div className="commande-item">
                  <div className="commande-info">
                    <h4>Commande #CMD-2025-002</h4>
                    <p>Date: 28/07/2025</p>
                    <p>
                      Statut:{" "}
                      <span className="status-badge warning">En cours</span>
                    </p>
                  </div>
                  <div className="commande-amount">
                    <span className="amount">{formatCurrency(8900)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "stats" && (
            <div className="stats-tab">
              <h3>📊 Statistiques détaillées</h3>

              <div className="stats-grid">
                <div className="stat-detail-card">
                  <h4>📈 Performance</h4>
                  <div className="stat-items">
                    <div className="stat-item">
                      <span>Délai moyen de livraison:</span>
                      <span>5 jours</span>
                    </div>
                    <div className="stat-item">
                      <span>Taux de conformité:</span>
                      <span className="success">98%</span>
                    </div>
                    <div className="stat-item">
                      <span>Note qualité:</span>
                      <span className="success">4.8/5</span>
                    </div>
                  </div>
                </div>

                <div className="stat-detail-card">
                  <h4>💰 Financier</h4>
                  <div className="stat-items">
                    <div className="stat-item">
                      <span>Montant moyen par commande:</span>
                      <span>
                        {formatCurrency(
                          fournisseur.montant_total_achats /
                            fournisseur.total_commandes
                        )}
                      </span>
                    </div>
                    <div className="stat-item">
                      <span>Économies réalisées:</span>
                      <span className="success">
                        {formatCurrency(
                          fournisseur.montant_total_achats *
                            (fournisseur.remise_habituelle / 100)
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FournisseurDetail;
