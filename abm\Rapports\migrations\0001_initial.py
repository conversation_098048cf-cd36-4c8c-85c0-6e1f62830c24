# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RapportTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=200, verbose_name='Nom du rapport')),
                ('description', models.TextField(blank=True)),
                ('type_rapport', models.CharField(choices=[('VENTES', 'Rapport de ventes'), ('CLIENTS', 'Rapport clients'), ('PRODUITS', 'Rapport produits'), ('COMMANDES', 'Rapport commandes'), ('FINANCIER', 'Rapport financier'), ('STOCK', 'Rapport stock'), ('CUSTOM', 'Rapport personnalisé')], max_length=20)),
                ('format_export', models.CharField(choices=[('PDF', 'PDF'), ('EXCEL', 'Excel'), ('CSV', 'CSV'), ('JSON', 'JSON')], default='PDF', max_length=10)),
                ('colonnes', models.JSONField(default=list, verbose_name='Colonnes à inclure')),
                ('filtres', models.JSONField(default=dict, verbose_name='Filtres par défaut')),
                ('groupements', models.JSONField(default=list, verbose_name='Groupements')),
                ('totaux', models.JSONField(default=list, verbose_name='Colonnes de totaux')),
                ('automatique', models.BooleanField(default=False, verbose_name='Génération automatique')),
                ('frequence', models.CharField(blank=True, choices=[('QUOTIDIEN', 'Quotidien'), ('HEBDOMADAIRE', 'Hebdomadaire'), ('MENSUEL', 'Mensuel'), ('TRIMESTRIEL', 'Trimestriel'), ('ANNUEL', 'Annuel')], max_length=20)),
                ('prochaine_execution', models.DateTimeField(blank=True, null=True)),
                ('destinataires', models.JSONField(default=list, verbose_name='Emails destinataires')),
                ('public', models.BooleanField(default=False, verbose_name='Visible par tous')),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rapports_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Template de rapport',
                'verbose_name_plural': 'Templates de rapports',
                'ordering': ['nom'],
            },
        ),
        migrations.CreateModel(
            name='RapportGenere',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('statut', models.CharField(choices=[('EN_COURS', 'En cours de génération'), ('TERMINE', 'Terminé'), ('ERREUR', 'Erreur')], default='EN_COURS', max_length=20)),
                ('date_generation', models.DateTimeField(auto_now_add=True)),
                ('duree_generation', models.DurationField(blank=True, null=True)),
                ('periode_debut', models.DateField()),
                ('periode_fin', models.DateField()),
                ('filtres_appliques', models.JSONField(default=dict)),
                ('nb_lignes', models.IntegerField(default=0, verbose_name='Nombre de lignes')),
                ('taille_fichier', models.IntegerField(default=0, verbose_name='Taille du fichier (bytes)')),
                ('chemin_fichier', models.CharField(blank=True, max_length=500)),
                ('message_erreur', models.TextField(blank=True)),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='Rapports.rapporttemplate')),
            ],
            options={
                'verbose_name': 'Rapport généré',
                'verbose_name_plural': 'Rapports générés',
                'ordering': ['-date_generation'],
            },
        ),
        migrations.CreateModel(
            name='TableauBord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('widgets', models.JSONField(default=list, verbose_name='Configuration des widgets')),
                ('layout', models.JSONField(default=dict, verbose_name='Disposition des widgets')),
                ('public', models.BooleanField(default=False)),
                ('roles_autorises', models.JSONField(default=list, verbose_name='Rôles autorisés')),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Tableau de bord',
                'verbose_name_plural': 'Tableaux de bord',
                'ordering': ['nom'],
            },
        ),
    ]
