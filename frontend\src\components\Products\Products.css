/* Styles pour les composants Produits */

/* === LAYOUT GÉNÉRAL === */
.products-list,
.product-form,
.product-detail {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.products-list.embedded {
  padding: 0;
  background: transparent;
  min-height: auto;
}

/* === HEADERS === */
.products-header,
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-title h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.header-title p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

/* === BOUTONS === */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn.btn-primary {
  background: #3498db;
  color: white;
}

.btn.btn-primary:hover {
  background: #2980b9;
}

.btn.btn-success {
  background: #27ae60;
  color: white;
}

.btn.btn-success:hover {
  background: #229954;
}

.btn.btn-warning {
  background: #f39c12;
  color: white;
}

.btn.btn-warning:hover {
  background: #e67e22;
}

.btn.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn.btn-danger:hover {
  background: #c0392b;
}

.btn.btn-info {
  background: #17a2b8;
  color: white;
}

.btn.btn-info:hover {
  background: #138496;
}

.btn.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn.btn-secondary:hover {
  background: #5a6268;
}

.btn.btn-outline {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn.btn-outline:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

.btn.btn-sm {
  padding: 8px 16px;
  font-size: 0.8rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* === STATISTIQUES === */
.product-stats {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.stats-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
}

.period-selector {
  display: flex;
  gap: 4px;
  background: #f1f5f9;
  padding: 4px;
  border-radius: 8px;
}

.period-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

.period-btn.active {
  background: white;
  color: #3498db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
  border-left: 4px solid #3498db;
}
.stat-card.success {
  border-left: 4px solid #27ae60;
}
.stat-card.warning {
  border-left: 4px solid #f39c12;
}
.stat-card.danger {
  border-left: 4px solid #e74c3c;
}
.stat-card.info {
  border-left: 4px solid #17a2b8;
}
.stat-card.secondary {
  border-left: 4px solid #6c757d;
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-content h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.stat-subtitle {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0 0 8px 0;
}

.stat-progress {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s ease;
}

.progress-bar.primary {
  background: #3498db;
}
.progress-bar.success {
  background: #27ae60;
}
.progress-bar.warning {
  background: #f39c12;
}
.progress-bar.danger {
  background: #e74c3c;
}
.progress-bar.info {
  background: #17a2b8;
}
.progress-bar.secondary {
  background: #6c757d;
}

/* === GESTION DU STOCK === */
.stock-management {
  background: #f8fafc;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stock-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stock-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stock-item label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
}

.stock-value {
  font-size: 1.2rem;
  font-weight: 700;
}

.stock-value.ok {
  color: #27ae60;
}
.stock-value.faible {
  color: #f39c12;
}
.stock-value.rupture {
  color: #e74c3c;
}

.stock-actions {
  border-top: 1px solid #e2e8f0;
  padding-top: 20px;
}

.stock-update {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 300px;
}

.stock-update label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

.stock-controls {
  display: flex;
  gap: 8px;
}

.stock-input {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
}

.stock-input:focus {
  outline: none;
  border-color: #3498db;
}

/* === BADGES === */
.status-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.status-active {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.status-inactive {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.status-outofstock {
  background: #fef3c7;
  color: #d97706;
}

.stock-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
}

.stock-badge.success {
  background: #dcfce7;
  color: #16a34a;
}

.stock-badge.warning {
  background: #fef3c7;
  color: #d97706;
}

.stock-badge.danger {
  background: #fee2e2;
  color: #dc2626;
}

/* === TOP PRODUITS === */
.top-products {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

.top-products h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.top-products-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.top-product-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-rank {
  width: 32px;
  height: 32px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
}

.product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-weight: 600;
  color: #1e293b;
}

.product-sales {
  font-size: 0.8rem;
  color: #64748b;
}

.product-ca {
  font-weight: 700;
  color: #27ae60;
}

/* === CATÉGORIES === */
.categories-stats {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

.categories-stats h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.category-name {
  font-weight: 600;
  color: #1e293b;
}

.category-count {
  font-size: 0.8rem;
  color: #64748b;
}

.category-ca {
  font-weight: 700;
  color: #27ae60;
  margin-bottom: 8px;
}

.category-progress {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

/* === ALERTES STOCK === */
.stock-alerts {
  background: #fef2f2;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #fecaca;
  margin-bottom: 20px;
}

.stock-alerts h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #dc2626;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #dc2626;
}

.alert-icon {
  font-size: 1.5rem;
}

.alert-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.alert-product {
  font-weight: 600;
  color: #1e293b;
}

.alert-details {
  font-size: 0.8rem;
  color: #64748b;
}

/* === MOUVEMENTS DE STOCK === */
.movement-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.movement-type.entree {
  background: #dcfce7;
  color: #16a34a;
}

.movement-type.sortie {
  background: #fee2e2;
  color: #dc2626;
}

.positive {
  color: #27ae60;
  font-weight: 600;
}

.negative {
  color: #e74c3c;
  font-weight: 600;
}

/* === TABLEAUX === */
.products-table-container,
.sales-table-container,
.stock-table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.products-table,
.sales-table,
.stock-table {
  width: 100%;
  border-collapse: collapse;
}

.products-table th,
.sales-table th,
.stock-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e2e8f0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.products-table th.sortable,
.sales-table th.sortable,
.stock-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background 0.3s ease;
}

.products-table th.sortable:hover,
.sales-table th.sortable:hover,
.stock-table th.sortable:hover {
  background: #f1f5f9;
}

.products-table td,
.sales-table td,
.stock-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.9rem;
}

.product-row {
  transition: background 0.3s ease;
}

.product-row:hover {
  background: #f8fafc;
}

.product-row.selected {
  background: #eff6ff;
  border-left: 4px solid #3498db;
}

.product-name .clickable {
  color: #3498db;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
}

.product-name .clickable:hover {
  text-decoration: underline;
}

.price {
  font-weight: 600;
  color: #27ae60;
}

.amount {
  font-weight: 600;
  color: #1e293b;
  text-align: right;
}

/* === ACTIONS === */
.actions {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-btn {
  padding: 6px 8px;
  background: none;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f8fafc;
  transform: translateY(-1px);
}

.action-btn.view-btn:hover {
  border-color: #3498db;
}
.action-btn.edit-btn:hover {
  border-color: #f39c12;
}
.action-btn.stock-btn:hover {
  border-color: #27ae60;
}
.action-btn.delete-btn:hover {
  border-color: #e74c3c;
}

/* === SECTIONS === */
.product-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.product-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  border-bottom: 2px solid #f1f5f9;
  padding-bottom: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  border: none;
  padding: 0;
}

.product-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
}

.info-item span {
  font-size: 1rem;
  color: #1e293b;
}

/* === BADGES PRODUITS === */
.product-title {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.product-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.product-summary {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  font-size: 0.9rem;
  color: #64748b;
  margin-top: 8px;
}

/* === ÉTATS VIDES === */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3,
.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 1rem;
}

/* === CHARGEMENT === */
.products-loading,
.product-detail-loading,
.product-stats-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* === FOOTER === */
.detail-footer {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.footer-info {
  font-size: 0.9rem;
  color: #64748b;
}

/* === DROPDOWN === */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.9rem;
  color: #374151;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-item:hover {
  background: #f8fafc;
}

.dropdown-item.danger {
  color: #dc2626;
}

.dropdown-item.danger:hover {
  background: #fef2f2;
}

.dropdown-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 8px 0;
}

/* === MODULE PRODUITS === */
.products-module {
  min-height: 100vh;
  background: #f8fafc;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .products-list,
  .product-form,
  .product-detail {
    padding: 15px;
  }

  .products-header,
  .detail-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .product-info-grid {
    grid-template-columns: 1fr;
  }

  .stock-info {
    grid-template-columns: 1fr;
  }

  .detail-footer {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .product-details-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .detail-section {
    padding: 15px;
  }

  .stock-update-form {
    padding: 15px 0;
  }

  .stock-actions {
    flex-direction: column;
    gap: 8px;
  }
}

/* === MODALS PRODUITS === */
.product-details-modal {
  max-width: 800px;
  width: 90%;
}

.product-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.detail-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

.detail-value {
  color: #2d3748;
  font-weight: 500;
  text-align: right;
}

.detail-value.profit {
  color: #16a34a;
  font-weight: 700;
}

.detail-value.low-stock {
  color: #dc2626;
  font-weight: 700;
}

.description-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  color: #4a5568;
  line-height: 1.5;
}

/* Modal de mise à jour du stock */
.stock-update-modal {
  max-width: 500px;
  width: 90%;
}

.stock-update-form {
  padding: 20px 0;
}

.current-stock {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  margin-bottom: 24px;
}

.current-stock h4 {
  margin: 0;
  color: #0c4a6e;
  font-size: 1.2rem;
}

.stock-value {
  color: #0369a1;
  font-weight: 700;
  font-size: 1.3rem;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.stock-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.actif {
  background: #dcfce7;
  color: #16a34a;
}

.status.inactif {
  background: #f3f4f6;
  color: #6b7280;
}

.status.alerte {
  background: #fee2e2;
  color: #dc2626;
}
