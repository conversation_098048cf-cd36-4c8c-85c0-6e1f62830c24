/**
 * Composant de test des services API
 * Utile pour déboguer les problèmes de connexion
 */

import React, { useState } from 'react';
import { FacturationService, ClientService, ProductService } from '../../services/apiService';

const ServiceTest: React.FC = () => {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const testService = async (serviceName: string, serviceCall: () => Promise<any>) => {
    setLoading(true);
    try {
      console.log(`🧪 Test du service ${serviceName}...`);
      const result = await serviceCall();
      console.log(`✅ ${serviceName} résultat:`, result);
      
      setResults(prev => ({
        ...prev,
        [serviceName]: {
          success: true,
          data: result,
          timestamp: new Date().toLocaleTimeString()
        }
      }));
    } catch (error) {
      console.error(`❌ ${serviceName} erreur:`, error);
      
      setResults(prev => ({
        ...prev,
        [serviceName]: {
          success: false,
          error: error.message || 'Erreur inconnue',
          timestamp: new Date().toLocaleTimeString()
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  const testAllServices = async () => {
    await testService('FacturationService.getFactures', () => 
      FacturationService.getFactures()
    );
    
    await testService('ClientService.getClients', () => 
      ClientService.getClients()
    );
    
    await testService('ProductService.getProducts', () => 
      ProductService.getProducts()
    );
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🧪 Test des Services API</h2>
      <p>Utilisez ce composant pour tester la connectivité avec les services backend.</p>
      
      <div style={{ marginBottom: '2rem' }}>
        <button 
          onClick={testAllServices}
          disabled={loading}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#0d9488',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: loading ? 'not-allowed' : 'pointer',
            marginRight: '1rem'
          }}
        >
          {loading ? '🔄 Test en cours...' : '🚀 Tester tous les services'}
        </button>

        <button 
          onClick={() => testService('FacturationService.getFactures', () => FacturationService.getFactures())}
          disabled={loading}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#6366f1',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: loading ? 'not-allowed' : 'pointer',
            marginRight: '1rem'
          }}
        >
          📄 Test Factures
        </button>

        <button 
          onClick={() => testService('ClientService.getClients', () => ClientService.getClients())}
          disabled={loading}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#8b5cf6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: loading ? 'not-allowed' : 'pointer',
            marginRight: '1rem'
          }}
        >
          👥 Test Clients
        </button>

        <button 
          onClick={() => testService('ProductService.getProducts', () => ProductService.getProducts())}
          disabled={loading}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#f59e0b',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          📦 Test Produits
        </button>
      </div>

      <div>
        <h3>📊 Résultats des Tests</h3>
        {Object.keys(results).length === 0 ? (
          <p style={{ color: '#6b7280', fontStyle: 'italic' }}>
            Aucun test exécuté. Cliquez sur un bouton pour commencer.
          </p>
        ) : (
          Object.entries(results).map(([serviceName, result]: [string, any]) => (
            <div 
              key={serviceName}
              style={{
                border: `2px solid ${result.success ? '#10b981' : '#ef4444'}`,
                borderRadius: '8px',
                padding: '1rem',
                marginBottom: '1rem',
                backgroundColor: result.success ? '#f0fdf4' : '#fef2f2'
              }}
            >
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '0.5rem'
              }}>
                <h4 style={{ 
                  margin: 0, 
                  color: result.success ? '#059669' : '#dc2626' 
                }}>
                  {result.success ? '✅' : '❌'} {serviceName}
                </h4>
                <small style={{ color: '#6b7280' }}>
                  {result.timestamp}
                </small>
              </div>
              
              {result.success ? (
                <div>
                  <p style={{ color: '#059669', margin: '0.5rem 0' }}>
                    ✅ Service fonctionnel
                  </p>
                  <details>
                    <summary style={{ cursor: 'pointer', color: '#6b7280' }}>
                      Voir les données (cliquez pour développer)
                    </summary>
                    <pre style={{ 
                      backgroundColor: '#f9fafb',
                      padding: '1rem',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '0.8rem',
                      marginTop: '0.5rem'
                    }}>
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                </div>
              ) : (
                <div>
                  <p style={{ color: '#dc2626', margin: '0.5rem 0' }}>
                    ❌ Erreur: {result.error}
                  </p>
                  <p style={{ color: '#6b7280', fontSize: '0.9rem' }}>
                    Vérifiez que le serveur backend Django fonctionne sur le port 8000.
                  </p>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      <div style={{ 
        marginTop: '2rem', 
        padding: '1rem', 
        backgroundColor: '#f0f9ff', 
        borderRadius: '8px',
        border: '1px solid #0ea5e9'
      }}>
        <h4 style={{ color: '#0369a1', margin: '0 0 0.5rem 0' }}>
          💡 Informations de Débogage
        </h4>
        <ul style={{ color: '#0369a1', margin: 0, paddingLeft: '1.5rem' }}>
          <li>Backend Django doit être actif sur <code>http://localhost:8000</code></li>
          <li>Vérifiez la console du navigateur (F12) pour plus de détails</li>
          <li>Les erreurs CORS indiquent un problème de configuration backend</li>
          <li>Les erreurs 404 indiquent que les endpoints n'existent pas</li>
          <li>Les erreurs de réseau indiquent que le serveur n'est pas accessible</li>
        </ul>
      </div>
    </div>
  );
};

export default ServiceTest;
