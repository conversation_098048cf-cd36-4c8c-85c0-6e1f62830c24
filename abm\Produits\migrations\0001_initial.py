# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Categorie',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sous_categories', to='Produits.categorie')),
            ],
            options={
                'verbose_name': 'Catégorie',
                'verbose_name_plural': 'Catégories',
                'ordering': ['nom'],
            },
        ),
        migrations.CreateModel(
            name='Produit',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reference', models.CharField(max_length=50, unique=True, verbose_name='Référence')),
                ('code_barre', models.CharField(blank=True, max_length=50, unique=True)),
                ('nom', models.CharField(max_length=200, verbose_name='Nom du produit')),
                ('description', models.TextField(blank=True)),
                ('description_courte', models.CharField(blank=True, max_length=500)),
                ('type_produit', models.CharField(choices=[('PHYSIQUE', 'Produit physique'), ('SERVICE', 'Service'), ('NUMERIQUE', 'Produit numérique'), ('ABONNEMENT', 'Abonnement')], default='PHYSIQUE', max_length=20)),
                ('statut', models.CharField(choices=[('ACTIF', 'Actif'), ('INACTIF', 'Inactif'), ('RUPTURE', 'En rupture'), ('DISCONTINUE', 'Discontinué')], default='ACTIF', max_length=20)),
                ('prix_achat', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('prix_vente', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('prix_promo', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('taux_tva', models.DecimalField(decimal_places=2, default=Decimal('20.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00')), django.core.validators.MaxValueValidator(Decimal('100.00'))])),
                ('unite', models.CharField(choices=[('PIECE', 'Pièce'), ('KG', 'Kilogramme'), ('LITRE', 'Litre'), ('METRE', 'Mètre'), ('HEURE', 'Heure'), ('JOUR', 'Jour'), ('MOIS', 'Mois')], default='PIECE', max_length=20)),
                ('stock_actuel', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('stock_minimum', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('stock_maximum', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('gestion_stock', models.BooleanField(default=True, verbose_name='Gérer le stock')),
                ('poids', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='Poids (kg)')),
                ('longueur', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Longueur (cm)')),
                ('largeur', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Largeur (cm)')),
                ('hauteur', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Hauteur (cm)')),
                ('visible_ecommerce', models.BooleanField(default=False, verbose_name='Visible sur e-commerce')),
                ('featured', models.BooleanField(default=False, verbose_name='Produit vedette')),
                ('meta_title', models.CharField(blank=True, max_length=200)),
                ('meta_description', models.CharField(blank=True, max_length=500)),
                ('image_principale', models.ImageField(blank=True, null=True, upload_to='produits/')),
                ('tags', models.CharField(blank=True, help_text='Tags séparés par des virgules', max_length=500)),
                ('notes_internes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('categorie', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Produits.categorie')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='produits_created', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='produits_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Produit',
                'verbose_name_plural': 'Produits',
                'ordering': ['nom'],
            },
        ),
        migrations.CreateModel(
            name='ImageProduit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='produits/')),
                ('alt_text', models.CharField(blank=True, max_length=200)),
                ('ordre', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='Produits.produit')),
            ],
            options={
                'verbose_name': 'Image produit',
                'verbose_name_plural': 'Images produits',
                'ordering': ['ordre', 'created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='produit',
            index=models.Index(fields=['reference'], name='Produits_pr_referen_d1df10_idx'),
        ),
        migrations.AddIndex(
            model_name='produit',
            index=models.Index(fields=['statut'], name='Produits_pr_statut_4472e9_idx'),
        ),
        migrations.AddIndex(
            model_name='produit',
            index=models.Index(fields=['categorie'], name='Produits_pr_categor_1e901e_idx'),
        ),
        migrations.AddIndex(
            model_name='produit',
            index=models.Index(fields=['visible_ecommerce'], name='Produits_pr_visible_577d50_idx'),
        ),
    ]
