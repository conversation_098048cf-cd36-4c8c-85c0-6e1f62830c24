/**
 * Statistiques E-commerce
 */

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useNotify } from "../Common/NotificationSystem";

interface EcommerceStatsData {
  ventes_totales: number;
  commandes_totales: number;
  panier_moyen: number;
  taux_conversion: number;
  visiteurs_uniques: number;
  pages_vues: number;
  taux_abandon_panier: number;
  produits_populaires: Array<{
    id: string;
    nom: string;
    ventes: number;
    ca: number;
  }>;
  ventes_par_mois: Array<{
    mois: string;
    ventes: number;
    commandes: number;
  }>;
}

const EcommerceStats: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();

  const [stats, setStats] = useState<EcommerceStatsData>({
    ventes_totales: 0,
    commandes_totales: 0,
    panier_moyen: 0,
    taux_conversion: 0,
    visiteurs_uniques: 0,
    pages_vues: 0,
    taux_abandon_panier: 0,
    produits_populaires: [],
    ventes_par_mois: [],
  });

  const [loading, setLoading] = useState(true);
  const [periode, setPeriode] = useState("mois");

  useEffect(() => {
    loadEcommerceStats();
  }, [periode]);

  const loadEcommerceStats = async () => {
    try {
      setLoading(true);

      // Simuler le chargement des statistiques e-commerce
      const mockStats: EcommerceStatsData = {
        ventes_totales: 125680.5,
        commandes_totales: 342,
        panier_moyen: 367.54,
        taux_conversion: 3.2,
        visiteurs_uniques: 10680,
        pages_vues: 45230,
        taux_abandon_panier: 68.5,
        produits_populaires: [
          { id: "1", nom: "Ordinateur portable", ventes: 45, ca: 22500.0 },
          { id: "2", nom: "Souris sans fil", ventes: 128, ca: 3840.0 },
          { id: "3", nom: "Clavier mécanique", ventes: 67, ca: 6700.0 },
          { id: "4", nom: 'Écran 24"', ventes: 23, ca: 6900.0 },
          { id: "5", nom: "Webcam HD", ventes: 89, ca: 4450.0 },
        ],
        ventes_par_mois: [
          { mois: "Jan", ventes: 18500, commandes: 52 },
          { mois: "Fév", ventes: 22300, commandes: 61 },
          { mois: "Mar", ventes: 19800, commandes: 58 },
          { mois: "Avr", ventes: 25600, commandes: 72 },
          { mois: "Mai", ventes: 21400, commandes: 65 },
          { mois: "Jun", ventes: 18200, commandes: 54 },
        ],
      };

      setStats(mockStats);
    } catch (error: any) {
      notify.error("Erreur lors du chargement des statistiques");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("fr-FR").format(value);
  };

  if (loading) {
    return (
      <div className="ecommerce-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des statistiques e-commerce...</p>
      </div>
    );
  }

  return (
    <div className="ecommerce-stats">
      {/* Filtres de période */}
      <div className="periode-filters">
        <h3>📅 Période d'analyse</h3>
        <div className="filter-buttons">
          <button
            className={`filter-btn ${periode === "semaine" ? "active" : ""}`}
            onClick={() => setPeriode("semaine")}>
            📅 Cette semaine
          </button>
          <button
            className={`filter-btn ${periode === "mois" ? "active" : ""}`}
            onClick={() => setPeriode("mois")}>
            📊 Ce mois
          </button>
          <button
            className={`filter-btn ${periode === "trimestre" ? "active" : ""}`}
            onClick={() => setPeriode("trimestre")}>
            📈 Ce trimestre
          </button>
          <button
            className={`filter-btn ${periode === "annee" ? "active" : ""}`}
            onClick={() => setPeriode("annee")}>
            📆 Cette année
          </button>
        </div>
      </div>

      {/* KPI E-commerce */}
      <div className="ecommerce-kpi-grid">
        <div className="kpi-card sales">
          <div className="kpi-header">
            <h4>💰 Ventes Totales</h4>
            <span className="kpi-period">{periode}</span>
          </div>
          <div className="kpi-value">
            {formatCurrency(stats.ventes_totales)}
          </div>
          <div className="kpi-trend positive">
            <span className="trend-icon">📈</span>
            <span>+15.2% vs période précédente</span>
          </div>
        </div>

        <div className="kpi-card orders">
          <div className="kpi-header">
            <h4>📋 Commandes</h4>
            <span className="kpi-period">{periode}</span>
          </div>
          <div className="kpi-value">
            {formatNumber(stats.commandes_totales)}
          </div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>Panier moyen: {formatCurrency(stats.panier_moyen)}</span>
            </div>
          </div>
        </div>

        <div className="kpi-card conversion">
          <div className="kpi-header">
            <h4>🎯 Taux de Conversion</h4>
            <span className="kpi-period">Visiteurs → Acheteurs</span>
          </div>
          <div className="kpi-value">
            {formatPercentage(stats.taux_conversion)}
          </div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>Visiteurs: {formatNumber(stats.visiteurs_uniques)}</span>
            </div>
          </div>
        </div>

        <div className="kpi-card traffic">
          <div className="kpi-header">
            <h4>👁️ Trafic</h4>
            <span className="kpi-period">Pages vues</span>
          </div>
          <div className="kpi-value">{formatNumber(stats.pages_vues)}</div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>
                Abandon panier: {formatPercentage(stats.taux_abandon_panier)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Graphiques */}
      <div className="ecommerce-charts">
        <div className="chart-section">
          <h3>📈 Évolution des Ventes</h3>
          <div className="chart-placeholder">
            <div className="chart-info">
              <span>📊 Graphique d'évolution mensuelle</span>
              <p>Ventes et commandes par mois</p>
            </div>
          </div>
        </div>

        <div className="chart-section">
          <h3>🛒 Funnel de Conversion</h3>
          <div className="chart-placeholder">
            <div className="chart-info">
              <span>🎯 Entonnoir de conversion</span>
              <p>Visiteurs → Panier → Commande</p>
            </div>
          </div>
        </div>
      </div>

      {/* Produits populaires */}
      <div className="popular-products">
        <h3>🏆 Produits les Plus Vendus</h3>
        <div className="products-table-container">
          <table className="products-table">
            <thead>
              <tr>
                <th>Rang</th>
                <th>Produit</th>
                <th>Ventes</th>
                <th>Chiffre d'affaires</th>
                <th>Part du CA</th>
              </tr>
            </thead>
            <tbody>
              {stats.produits_populaires.map((produit, index) => (
                <tr key={produit.id}>
                  <td className="rank">
                    <span className="rank-badge">#{index + 1}</span>
                  </td>
                  <td className="product-name">
                    <strong>{produit.nom}</strong>
                  </td>
                  <td className="sales-count">
                    {formatNumber(produit.ventes)} unités
                  </td>
                  <td className="revenue">{formatCurrency(produit.ca)}</td>
                  <td className="percentage">
                    {formatPercentage(
                      (produit.ca / stats.ventes_totales) * 100
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Métriques détaillées */}
      <div className="detailed-metrics">
        <div className="metrics-section">
          <h3>📊 Métriques Détaillées</h3>
          <div className="metrics-grid">
            <div className="metric-item">
              <div className="metric-label">Taux de rebond</div>
              <div className="metric-value">45.2%</div>
              <div className="metric-trend negative">-2.1%</div>
            </div>
            <div className="metric-item">
              <div className="metric-label">Durée session moyenne</div>
              <div className="metric-value">4m 32s</div>
              <div className="metric-trend positive">+18s</div>
            </div>
            <div className="metric-item">
              <div className="metric-label">Pages par session</div>
              <div className="metric-value">3.8</div>
              <div className="metric-trend positive">+0.3</div>
            </div>
            <div className="metric-item">
              <div className="metric-label">Retours clients</div>
              <div className="metric-value">2.1%</div>
              <div className="metric-trend positive">-0.5%</div>
            </div>
          </div>
        </div>

        <div className="metrics-section">
          <h3>💳 Moyens de Paiement</h3>
          <div className="payment-methods">
            <div className="payment-item">
              <span className="payment-icon">💳</span>
              <span className="payment-label">Carte bancaire</span>
              <span className="payment-percentage">68%</span>
            </div>
            <div className="payment-item">
              <span className="payment-icon">🏦</span>
              <span className="payment-label">Virement</span>
              <span className="payment-percentage">22%</span>
            </div>
            <div className="payment-item">
              <span className="payment-icon">📱</span>
              <span className="payment-label">PayPal</span>
              <span className="payment-percentage">8%</span>
            </div>
            <div className="payment-item">
              <span className="payment-icon">💰</span>
              <span className="payment-label">Autres</span>
              <span className="payment-percentage">2%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="ecommerce-actions">
        <h3>⚡ Actions Rapides</h3>
        <div className="actions-grid">
          <button
            className="action-card"
            onClick={() => navigate("/products/new")}>
            <div className="action-icon">📦</div>
            <div className="action-label">Ajouter produit</div>
          </button>

          <button
            className="action-card"
            onClick={() => navigate("/orders")}>
            <div className="action-icon">📋</div>
            <div className="action-label">Voir commandes</div>
          </button>

          <button
            className="action-card"
            onClick={() => navigate("/clients")}>
            <div className="action-icon">👥</div>
            <div className="action-label">Gérer clients</div>
          </button>

          <button
            className="action-card"
            onClick={() => navigate("/factures")}>
            <div className="action-icon">📄</div>
            <div className="action-label">Facturation</div>
          </button>

          <button
            className="action-card"
            onClick={() => window.open("/ecommerce/catalog", "_blank")}>
            <div className="action-icon">🌐</div>
            <div className="action-label">Voir boutique</div>
          </button>

          <button
            className="action-card"
            onClick={() => navigate("/ecommerce/settings")}>
            <div className="action-icon">⚙️</div>
            <div className="action-label">Configuration</div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default EcommerceStats;
