from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import ProduitViewSet, CategorieViewSet, ImageProduitViewSet

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'produits', ProduitViewSet, basename='produit')
router.register(r'categories', CategorieViewSet, basename='categorie')
router.register(r'images', ImageProduitViewSet, basename='image-produit')

urlpatterns = [
    path('', include(router.urls)),
]
