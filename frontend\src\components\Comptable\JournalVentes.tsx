/**
 * Journal des ventes - Suivi des recettes
 */

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { InvoiceService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";

// Fonction utilitaire pour formater les montants
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency: "EUR",
  }).format(amount);
};

interface VenteEntry {
  id: string;
  date: string;
  numero_facture: string;
  client_nom: string;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
  taux_tva: number;
  statut: string;
  mode_paiement?: string;
}

const JournalVentes: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();

  const [ventes, setVentes] = useState<VenteEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    date_debut: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      .toISOString()
      .split("T")[0],
    date_fin: new Date().toISOString().split("T")[0],
    statut: "",
    client: "",
  });

  const [totaux, setTotaux] = useState({
    montant_ht: 0,
    montant_tva: 0,
    montant_ttc: 0,
    nombre_factures: 0,
  });

  useEffect(() => {
    loadVentes();
  }, [filters]);

  const loadVentes = async () => {
    try {
      setLoading(true);

      // Simuler le chargement du journal des ventes
      const mockVentes: VenteEntry[] = [
        {
          id: "1",
          date: "2024-08-10",
          numero_facture: "FAC-2024-001",
          client_nom: "Entreprise ABC",
          montant_ht: 1000.0,
          montant_tva: 200.0,
          montant_ttc: 1200.0,
          taux_tva: 20,
          statut: "PAYEE",
          mode_paiement: "Virement",
        },
        {
          id: "2",
          date: "2024-08-09",
          numero_facture: "FAC-2024-002",
          client_nom: "Société XYZ",
          montant_ht: 750.0,
          montant_tva: 150.0,
          montant_ttc: 900.0,
          taux_tva: 20,
          statut: "ENVOYEE",
          mode_paiement: "",
        },
        {
          id: "3",
          date: "2024-08-08",
          numero_facture: "FAC-2024-003",
          client_nom: "Client DEF",
          montant_ht: 500.0,
          montant_tva: 100.0,
          montant_ttc: 600.0,
          taux_tva: 20,
          statut: "PAYEE",
          mode_paiement: "Chèque",
        },
      ];

      setVentes(mockVentes);

      // Calculer les totaux
      const totaux = mockVentes.reduce(
        (acc, vente) => ({
          montant_ht: acc.montant_ht + vente.montant_ht,
          montant_tva: acc.montant_tva + vente.montant_tva,
          montant_ttc: acc.montant_ttc + vente.montant_ttc,
          nombre_factures: acc.nombre_factures + 1,
        }),
        { montant_ht: 0, montant_tva: 0, montant_ttc: 0, nombre_factures: 0 }
      );

      setTotaux(totaux);
    } catch (error: any) {
      notify.error("Erreur lors du chargement du journal des ventes");
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
  };

  const exportJournal = () => {
    // Simuler l'export
    notify.success("Export du journal des ventes en cours...");
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case "PAYEE":
        return "status-paid";
      case "ENVOYEE":
        return "status-sent";
      case "BROUILLON":
        return "status-draft";
      case "EN_RETARD":
        return "status-overdue";
      default:
        return "status-default";
    }
  };

  const getStatusLabel = (statut: string) => {
    switch (statut) {
      case "PAYEE":
        return "✅ Payée";
      case "ENVOYEE":
        return "📤 Envoyée";
      case "BROUILLON":
        return "📝 Brouillon";
      case "EN_RETARD":
        return "⚠️ En retard";
      default:
        return statut;
    }
  };

  return (
    <div className="journal-ventes">
      {/* Header avec filtres */}
      <div className="journal-header">
        <h3>💰 Journal des Ventes</h3>
        <div className="journal-filters">
          <div className="filter-group">
            <label>Du:</label>
            <input
              type="date"
              value={filters.date_debut}
              onChange={(e) => handleFilterChange("date_debut", e.target.value)}
              className="filter-input"
            />
          </div>
          <div className="filter-group">
            <label>Au:</label>
            <input
              type="date"
              value={filters.date_fin}
              onChange={(e) => handleFilterChange("date_fin", e.target.value)}
              className="filter-input"
            />
          </div>
          <div className="filter-group">
            <label>Statut:</label>
            <select
              value={filters.statut}
              onChange={(e) => handleFilterChange("statut", e.target.value)}
              className="filter-select">
              <option value="">Tous</option>
              <option value="PAYEE">Payées</option>
              <option value="ENVOYEE">Envoyées</option>
              <option value="EN_RETARD">En retard</option>
            </select>
          </div>
          <button
            className="btn btn-primary btn-sm"
            onClick={exportJournal}>
            📤 Exporter
          </button>
        </div>
      </div>

      {/* Totaux */}
      <div className="journal-totaux">
        <div className="total-item">
          <label>Nombre de factures:</label>
          <span>{totaux.nombre_factures}</span>
        </div>
        <div className="total-item">
          <label>Total HT:</label>
          <span>{formatCurrency(totaux.montant_ht)}</span>
        </div>
        <div className="total-item">
          <label>Total TVA:</label>
          <span>{formatCurrency(totaux.montant_tva)}</span>
        </div>
        <div className="total-item final">
          <label>
            <strong>Total TTC:</strong>
          </label>
          <span>
            <strong>{formatCurrency(totaux.montant_ttc)}</strong>
          </span>
        </div>
      </div>

      {/* Tableau des ventes */}
      <div className="journal-table-container">
        <table className="journal-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>N° Facture</th>
              <th>Client</th>
              <th>Montant HT</th>
              <th>TVA</th>
              <th>Montant TTC</th>
              <th>Statut</th>
              <th>Paiement</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {ventes.map((vente) => (
              <tr key={vente.id}>
                <td>{new Date(vente.date).toLocaleDateString("fr-FR")}</td>
                <td className="numero-facture">
                  <button
                    className="link-button"
                    onClick={() => navigate(`/factures/${vente.id}`)}>
                    {vente.numero_facture}
                  </button>
                </td>
                <td>{vente.client_nom}</td>
                <td className="montant">{formatCurrency(vente.montant_ht)}</td>
                <td className="montant">{formatCurrency(vente.montant_tva)}</td>
                <td className="montant total">
                  {formatCurrency(vente.montant_ttc)}
                </td>
                <td>
                  <span
                    className={`status-badge ${getStatusColor(vente.statut)}`}>
                    {getStatusLabel(vente.statut)}
                  </span>
                </td>
                <td className="paiement">{vente.mode_paiement || "-"}</td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => navigate(`/factures/${vente.id}`)}
                      title="Voir détail">
                      👁️
                    </button>
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() =>
                        window.open(`/api/factures/${vente.id}/pdf`, "_blank")
                      }
                      title="Télécharger PDF">
                      📄
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {loading && (
        <div className="journal-loading">
          <div className="loading-spinner"></div>
          <p>Chargement du journal des ventes...</p>
        </div>
      )}

      {!loading && ventes.length === 0 && (
        <div className="journal-empty">
          <div className="empty-icon">📊</div>
          <h4>Aucune vente trouvée</h4>
          <p>Aucune vente ne correspond aux critères sélectionnés</p>
        </div>
      )}
    </div>
  );
};

export default JournalVentes;
