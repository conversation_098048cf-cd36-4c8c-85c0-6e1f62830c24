{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\abm\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Configuration\nconst AUTH_CONFIG = {\n  STORAGE_KEY: \"abm_auth_token\",\n  USER_KEY: \"abm_user_data\",\n  REMEMBER_KEY: \"abm_remember_me\",\n  SESSION_DURATION: 8 * 60 * 60 * 1000 // 8 heures\n};\n\n// Hiérarchie des rôles\nconst ROLE_HIERARCHY = {\n  NORMAL: 1,\n  COMPTABLE: 2,\n  ADMIN: 3,\n  SUPERADMIN: 4\n};\n\n// Permissions par module\nconst MODULE_PERMISSIONS = {\n  dashboard: [\"NORMAL\", \"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  clients: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  produits: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  factures: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  commandes: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  paiements: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  stock: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  rapports: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  settings: [\"ADMIN\", \"SUPERADMIN\"],\n  users: [\"SUPERADMIN\"]\n};\n\n// Utilisateurs de démonstration\nconst demoUsers = {\n  \"<EMAIL>\": {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    nom: \"Admin\",\n    prenom: \"Super\",\n    role: \"SUPERADMIN\",\n    permissions: [\"read\", \"write\", \"delete\", \"admin\"],\n    modules: Object.keys(MODULE_PERMISSIONS),\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  },\n  \"<EMAIL>\": {\n    id: \"2\",\n    email: \"<EMAIL>\",\n    nom: \"Manager\",\n    prenom: \"Principal\",\n    role: \"ADMIN\",\n    permissions: [\"read\", \"write\", \"delete\"],\n    modules: [\"dashboard\", \"clients\", \"produits\", \"factures\", \"commandes\", \"paiements\", \"stock\", \"rapports\", \"settings\"],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  },\n  \"<EMAIL>\": {\n    id: \"3\",\n    email: \"<EMAIL>\",\n    nom: \"Comptable\",\n    prenom: \"Expert\",\n    role: \"COMPTABLE\",\n    permissions: [\"read\", \"write\"],\n    modules: [\"dashboard\", \"clients\", \"produits\", \"factures\", \"commandes\", \"paiements\", \"stock\", \"rapports\"],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  },\n  \"<EMAIL>\": {\n    id: \"4\",\n    email: \"<EMAIL>\",\n    nom: \"Utilisateur\",\n    prenom: \"Normal\",\n    role: \"NORMAL\",\n    permissions: [\"read\"],\n    modules: [\"dashboard\"],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  }\n};\nexport function AuthProvider({\n  children\n}) {\n  _s();\n  const [user, setUser] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [sessionExpiry, setSessionExpiry] = useState(null);\n\n  // Vérification de l'authentification au chargement\n  useEffect(() => {\n    const token = localStorage.getItem(AUTH_CONFIG.STORAGE_KEY);\n    const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);\n    if (token && userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        setUser(parsedUser);\n\n        // Vérifier l'expiration de la session\n        if (parsedUser.tokenExpiry) {\n          const expiry = new Date(parsedUser.tokenExpiry);\n          if (expiry > new Date()) {\n            setSessionExpiry(expiry);\n          } else {\n            // Session expirée\n            logout();\n          }\n        }\n      } catch (error) {\n        console.error(\"Erreur lors de la récupération des données utilisateur:\", error);\n        logout();\n      }\n    }\n  }, []);\n\n  // Auto-déconnexion à l'expiration\n  useEffect(() => {\n    if (sessionExpiry) {\n      const timeUntilExpiry = sessionExpiry.getTime() - Date.now();\n      if (timeUntilExpiry > 0) {\n        const timer = setTimeout(() => {\n          toast.warning(\"Votre session a expiré. Veuillez vous reconnecter.\");\n          logout();\n        }, timeUntilExpiry);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [user, sessionExpiry]);\n\n  // Fonction de connexion\n  const login = useCallback(async credentials => {\n    try {\n      setIsLoading(true);\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      const foundUser = demoUsers[credentials.email];\n      if (!foundUser || credentials.password !== \"123456\") {\n        return {\n          success: false,\n          message: \"Email ou mot de passe incorrect\",\n          errors: {\n            email: [\"Identifiants invalides\"]\n          }\n        };\n      }\n      if (foundUser.statut !== \"ACTIF\") {\n        return {\n          success: false,\n          message: \"Compte suspendu ou inactif\",\n          errors: {\n            account: [\"Compte non autorisé\"]\n          }\n        };\n      }\n      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n      const token = `abm_token_${foundUser.id}_${Date.now()}`;\n      const userWithToken = {\n        ...foundUser,\n        tokenExpiry: expiry.toISOString()\n      };\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));\n      if (credentials.remember) {\n        localStorage.setItem(AUTH_CONFIG.REMEMBER_KEY, \"true\");\n      }\n      setUser(foundUser);\n      setSessionExpiry(expiry);\n      toast.success(`Bienvenue ${foundUser.prenom} ${foundUser.nom} !`);\n      return {\n        success: true,\n        message: \"Connexion réussie\"\n      };\n    } catch (error) {\n      console.error(\"Erreur lors de la connexion:\", error);\n      return {\n        success: false,\n        message: \"Erreur de connexion. Veuillez réessayer.\",\n        errors: {\n          general: [\"Erreur système\"]\n        }\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Fonction d'inscription (simulation)\n  const register = useCallback(async credentials => {\n    try {\n      setIsLoading(true);\n\n      // Validation côté client\n      if (credentials.password !== credentials.confirmPassword) {\n        return {\n          success: false,\n          message: \"Les mots de passe ne correspondent pas\",\n          errors: {\n            confirmPassword: [\"Les mots de passe ne correspondent pas\"]\n          }\n        };\n      }\n      if (credentials.password.length < 6) {\n        return {\n          success: false,\n          message: \"Le mot de passe doit contenir au moins 6 caractères\",\n          errors: {\n            password: [\"Le mot de passe doit contenir au moins 6 caractères\"]\n          }\n        };\n      }\n\n      // Vérifier si l'email existe déjà\n      const existingUser = Object.values(demoUsers).find(user => user.email === credentials.email);\n      if (existingUser) {\n        return {\n          success: false,\n          message: \"Cet email est déjà utilisé\",\n          errors: {\n            email: [\"Cet email est déjà utilisé\"]\n          }\n        };\n      }\n\n      // Simulation d'une API d'inscription\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      // Créer un nouvel utilisateur\n      const newUser = {\n        id: String(Date.now()),\n        email: credentials.email,\n        nom: credentials.nom,\n        prenom: credentials.prenom,\n        role: credentials.role || \"NORMAL\",\n        permissions: [\"read\"],\n        modules: [\"dashboard\"],\n        statut: \"ACTIF\",\n        derniere_connexion: new Date().toISOString()\n      };\n\n      // Ajouter l'utilisateur aux utilisateurs de démo\n      demoUsers[credentials.email] = newUser;\n\n      // Créer automatiquement une session pour le nouvel utilisateur\n      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n      const token = `abm_token_${newUser.id}_${Date.now()}`;\n      const userWithToken = {\n        ...newUser,\n        tokenExpiry: expiry.toISOString()\n      };\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));\n      setUser(newUser);\n      setSessionExpiry(expiry);\n      toast.success(`Bienvenue ${newUser.prenom} ${newUser.nom} ! Votre compte a été créé avec succès.`);\n      return {\n        success: true,\n        message: \"Inscription réussie ! Vous êtes maintenant connecté.\",\n        user: newUser\n      };\n    } catch (error) {\n      console.error(\"Erreur lors de l'inscription:\", error);\n      return {\n        success: false,\n        message: \"Erreur lors de l'inscription. Veuillez réessayer.\",\n        errors: {\n          general: [\"Erreur système\"]\n        }\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Fonction de déconnexion\n  const logout = useCallback(() => {\n    localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\n    localStorage.removeItem(AUTH_CONFIG.USER_KEY);\n    localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\n    setUser(null);\n    setSessionExpiry(null);\n    toast.info(\"Vous avez été déconnecté.\");\n  }, []);\n\n  // Vérification des permissions\n  const hasPermission = useCallback(permission => {\n    return (user === null || user === void 0 ? void 0 : user.permissions.includes(permission)) || false;\n  }, [user]);\n\n  // Vérification du rôle\n  const hasRole = useCallback(role => {\n    return (user === null || user === void 0 ? void 0 : user.role) === role;\n  }, [user]);\n\n  // Vérification d'accès aux modules\n  const canAccessModule = useCallback(module => {\n    if (!user) return false;\n    const allowedRoles = MODULE_PERMISSIONS[module] || [];\n    return allowedRoles.includes(user.role);\n  }, [user]);\n\n  // Obtenir la hiérarchie du rôle\n  const getRoleHierarchy = useCallback(() => {\n    return user ? ROLE_HIERARCHY[user.role] : 0;\n  }, [user]);\n  const contextValue = useMemo(() => ({\n    user,\n    isAuthenticated: !!user,\n    isLoading,\n    sessionExpiry,\n    login,\n    register,\n    logout,\n    hasPermission,\n    hasRole,\n    canAccessModule,\n    getRoleHierarchy\n  }), [user, isLoading, sessionExpiry, login, register, logout, hasPermission, hasRole, canAccessModule, getRoleHierarchy]);\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 436,\n    columnNumber: 5\n  }, this);\n}\n_s(AuthProvider, \"Yja2piMZ2F4RaF4W6CKKHCBXKNQ=\");\n_c = AuthProvider;\nexport function useAuth() {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n}\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "useCallback", "useMemo", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "AUTH_CONFIG", "STORAGE_KEY", "USER_KEY", "REMEMBER_KEY", "SESSION_DURATION", "ROLE_HIERARCHY", "NORMAL", "COMPTABLE", "ADMIN", "SUPERADMIN", "MODULE_PERMISSIONS", "dashboard", "clients", "produits", "factures", "commandes", "paiements", "stock", "rapports", "settings", "users", "demoUsers", "id", "email", "nom", "prenom", "role", "permissions", "modules", "Object", "keys", "statut", "derniere_connexion", "Date", "toISOString", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "isLoading", "setIsLoading", "sessionExpiry", "setSessionExpiry", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "tokenExpiry", "expiry", "logout", "error", "console", "timeUntilExpiry", "getTime", "now", "timer", "setTimeout", "warning", "clearTimeout", "login", "credentials", "Promise", "resolve", "foundUser", "password", "success", "message", "errors", "account", "userWithToken", "setItem", "stringify", "remember", "general", "register", "confirmPassword", "length", "existingUser", "values", "find", "newUser", "String", "removeItem", "info", "hasPermission", "permission", "includes", "hasRole", "canAccessModule", "module", "allowedRoles", "getRoleHierarchy", "contextValue", "isAuthenticated", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/abm/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, {\n  createContext,\n  useContext,\n  useState,\n  useEffect,\n  useCallback,\n  useMemo,\n  ReactNode,\n} from \"react\";\nimport { toast } from \"react-toastify\";\n\nexport type UserRole = \"NORMAL\" | \"COMPTABLE\" | \"ADMIN\" | \"SUPERADMIN\";\n\ninterface User {\n  id: string;\n  email: string;\n  nom: string;\n  prenom: string;\n  role: UserRole;\n  permissions: string[];\n  modules: string[];\n  statut: \"ACTIF\" | \"INACTIF\" | \"SUSPENDU\";\n  derniere_connexion: string;\n}\n\ninterface LoginCredentials {\n  email: string;\n  password: string;\n  remember?: boolean;\n}\n\ninterface RegisterCredentials {\n  email: string;\n  password: string;\n  confirmPassword: string;\n  prenom: string;\n  nom: string;\n  role?: UserRole;\n  phone?: string;\n  company?: string;\n}\n\ninterface AuthResult {\n  success: boolean;\n  message: string;\n  errors?: Record<string, string[]>;\n  user?: User;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  sessionExpiry: Date | null;\n  login: (credentials: LoginCredentials) => Promise<AuthResult>;\n  register: (credentials: RegisterCredentials) => Promise<AuthResult>;\n  logout: () => void;\n  hasPermission: (permission: string) => boolean;\n  hasRole: (role: UserRole) => boolean;\n  canAccessModule: (module: string) => boolean;\n  getRoleHierarchy: () => number;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Configuration\nconst AUTH_CONFIG = {\n  STORAGE_KEY: \"abm_auth_token\",\n  USER_KEY: \"abm_user_data\",\n  REMEMBER_KEY: \"abm_remember_me\",\n  SESSION_DURATION: 8 * 60 * 60 * 1000, // 8 heures\n};\n\n// Hiérarchie des rôles\nconst ROLE_HIERARCHY: Record<UserRole, number> = {\n  NORMAL: 1,\n  COMPTABLE: 2,\n  ADMIN: 3,\n  SUPERADMIN: 4,\n};\n\n// Permissions par module\nconst MODULE_PERMISSIONS: Record<string, UserRole[]> = {\n  dashboard: [\"NORMAL\", \"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  clients: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  produits: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  factures: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  commandes: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  paiements: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  stock: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  rapports: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  settings: [\"ADMIN\", \"SUPERADMIN\"],\n  users: [\"SUPERADMIN\"],\n};\n\n// Utilisateurs de démonstration\nconst demoUsers: Record<string, User> = {\n  \"<EMAIL>\": {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    nom: \"Admin\",\n    prenom: \"Super\",\n    role: \"SUPERADMIN\",\n    permissions: [\"read\", \"write\", \"delete\", \"admin\"],\n    modules: Object.keys(MODULE_PERMISSIONS),\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString(),\n  },\n  \"<EMAIL>\": {\n    id: \"2\",\n    email: \"<EMAIL>\",\n    nom: \"Manager\",\n    prenom: \"Principal\",\n    role: \"ADMIN\",\n    permissions: [\"read\", \"write\", \"delete\"],\n    modules: [\n      \"dashboard\",\n      \"clients\",\n      \"produits\",\n      \"factures\",\n      \"commandes\",\n      \"paiements\",\n      \"stock\",\n      \"rapports\",\n      \"settings\",\n    ],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString(),\n  },\n  \"<EMAIL>\": {\n    id: \"3\",\n    email: \"<EMAIL>\",\n    nom: \"Comptable\",\n    prenom: \"Expert\",\n    role: \"COMPTABLE\",\n    permissions: [\"read\", \"write\"],\n    modules: [\n      \"dashboard\",\n      \"clients\",\n      \"produits\",\n      \"factures\",\n      \"commandes\",\n      \"paiements\",\n      \"stock\",\n      \"rapports\",\n    ],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString(),\n  },\n  \"<EMAIL>\": {\n    id: \"4\",\n    email: \"<EMAIL>\",\n    nom: \"Utilisateur\",\n    prenom: \"Normal\",\n    role: \"NORMAL\",\n    permissions: [\"read\"],\n    modules: [\"dashboard\"],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString(),\n  },\n};\n\nexport function AuthProvider({ children }: { children: ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);\n\n  // Vérification de l'authentification au chargement\n  useEffect(() => {\n    const token = localStorage.getItem(AUTH_CONFIG.STORAGE_KEY);\n    const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);\n\n    if (token && userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        setUser(parsedUser);\n\n        // Vérifier l'expiration de la session\n        if (parsedUser.tokenExpiry) {\n          const expiry = new Date(parsedUser.tokenExpiry);\n          if (expiry > new Date()) {\n            setSessionExpiry(expiry);\n          } else {\n            // Session expirée\n            logout();\n          }\n        }\n      } catch (error) {\n        console.error(\n          \"Erreur lors de la récupération des données utilisateur:\",\n          error\n        );\n        logout();\n      }\n    }\n  }, []);\n\n  // Auto-déconnexion à l'expiration\n  useEffect(() => {\n    if (sessionExpiry) {\n      const timeUntilExpiry = sessionExpiry.getTime() - Date.now();\n      if (timeUntilExpiry > 0) {\n        const timer = setTimeout(() => {\n          toast.warning(\"Votre session a expiré. Veuillez vous reconnecter.\");\n          logout();\n        }, timeUntilExpiry);\n\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [user, sessionExpiry]);\n\n  // Fonction de connexion\n  const login = useCallback(async (credentials: LoginCredentials) => {\n    try {\n      setIsLoading(true);\n      await new Promise((resolve) => setTimeout(resolve, 1000));\n\n      const foundUser = demoUsers[credentials.email];\n\n      if (!foundUser || credentials.password !== \"123456\") {\n        return {\n          success: false,\n          message: \"Email ou mot de passe incorrect\",\n          errors: { email: [\"Identifiants invalides\"] },\n        };\n      }\n\n      if (foundUser.statut !== \"ACTIF\") {\n        return {\n          success: false,\n          message: \"Compte suspendu ou inactif\",\n          errors: { account: [\"Compte non autorisé\"] },\n        };\n      }\n\n      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n      const token = `abm_token_${foundUser.id}_${Date.now()}`;\n\n      const userWithToken = {\n        ...foundUser,\n        tokenExpiry: expiry.toISOString(),\n      };\n\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));\n\n      if (credentials.remember) {\n        localStorage.setItem(AUTH_CONFIG.REMEMBER_KEY, \"true\");\n      }\n\n      setUser(foundUser);\n      setSessionExpiry(expiry);\n\n      toast.success(`Bienvenue ${foundUser.prenom} ${foundUser.nom} !`);\n\n      return {\n        success: true,\n        message: \"Connexion réussie\",\n      };\n    } catch (error) {\n      console.error(\"Erreur lors de la connexion:\", error);\n      return {\n        success: false,\n        message: \"Erreur de connexion. Veuillez réessayer.\",\n        errors: { general: [\"Erreur système\"] },\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Fonction d'inscription (simulation)\n  const register = useCallback(async (credentials: RegisterCredentials) => {\n    try {\n      setIsLoading(true);\n\n      // Validation côté client\n      if (credentials.password !== credentials.confirmPassword) {\n        return {\n          success: false,\n          message: \"Les mots de passe ne correspondent pas\",\n          errors: {\n            confirmPassword: [\"Les mots de passe ne correspondent pas\"],\n          },\n        };\n      }\n\n      if (credentials.password.length < 6) {\n        return {\n          success: false,\n          message: \"Le mot de passe doit contenir au moins 6 caractères\",\n          errors: {\n            password: [\"Le mot de passe doit contenir au moins 6 caractères\"],\n          },\n        };\n      }\n\n      // Vérifier si l'email existe déjà\n      const existingUser = Object.values(demoUsers).find(\n        (user) => user.email === credentials.email\n      );\n      if (existingUser) {\n        return {\n          success: false,\n          message: \"Cet email est déjà utilisé\",\n          errors: { email: [\"Cet email est déjà utilisé\"] },\n        };\n      }\n\n      // Simulation d'une API d'inscription\n      await new Promise((resolve) => setTimeout(resolve, 1500));\n\n      // Créer un nouvel utilisateur\n      const newUser: User = {\n        id: String(Date.now()),\n        email: credentials.email,\n        nom: credentials.nom,\n        prenom: credentials.prenom,\n        role: credentials.role || \"NORMAL\",\n        permissions: [\"read\"],\n        modules: [\"dashboard\"],\n        statut: \"ACTIF\",\n        derniere_connexion: new Date().toISOString(),\n      };\n\n      // Ajouter l'utilisateur aux utilisateurs de démo\n      demoUsers[credentials.email] = newUser;\n\n      // Créer automatiquement une session pour le nouvel utilisateur\n      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n      const token = `abm_token_${newUser.id}_${Date.now()}`;\n\n      const userWithToken = {\n        ...newUser,\n        tokenExpiry: expiry.toISOString(),\n      };\n\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));\n\n      setUser(newUser);\n      setSessionExpiry(expiry);\n\n      toast.success(\n        `Bienvenue ${newUser.prenom} ${newUser.nom} ! Votre compte a été créé avec succès.`\n      );\n\n      return {\n        success: true,\n        message: \"Inscription réussie ! Vous êtes maintenant connecté.\",\n        user: newUser,\n      };\n    } catch (error) {\n      console.error(\"Erreur lors de l'inscription:\", error);\n      return {\n        success: false,\n        message: \"Erreur lors de l'inscription. Veuillez réessayer.\",\n        errors: { general: [\"Erreur système\"] },\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Fonction de déconnexion\n  const logout = useCallback(() => {\n    localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\n    localStorage.removeItem(AUTH_CONFIG.USER_KEY);\n    localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\n    setUser(null);\n    setSessionExpiry(null);\n    toast.info(\"Vous avez été déconnecté.\");\n  }, []);\n\n  // Vérification des permissions\n  const hasPermission = useCallback(\n    (permission: string) => {\n      return user?.permissions.includes(permission) || false;\n    },\n    [user]\n  );\n\n  // Vérification du rôle\n  const hasRole = useCallback(\n    (role: UserRole) => {\n      return user?.role === role;\n    },\n    [user]\n  );\n\n  // Vérification d'accès aux modules\n  const canAccessModule = useCallback(\n    (module: string) => {\n      if (!user) return false;\n      const allowedRoles = MODULE_PERMISSIONS[module] || [];\n      return allowedRoles.includes(user.role);\n    },\n    [user]\n  );\n\n  // Obtenir la hiérarchie du rôle\n  const getRoleHierarchy = useCallback(() => {\n    return user ? ROLE_HIERARCHY[user.role] : 0;\n  }, [user]);\n\n  const contextValue = useMemo(\n    () => ({\n      user,\n      isAuthenticated: !!user,\n      isLoading,\n      sessionExpiry,\n      login,\n      register,\n      logout,\n      hasPermission,\n      hasRole,\n      canAccessModule,\n      getRoleHierarchy,\n    }),\n    [\n      user,\n      isLoading,\n      sessionExpiry,\n      login,\n      register,\n      logout,\n      hasPermission,\n      hasRole,\n      canAccessModule,\n      getRoleHierarchy,\n    ]\n  );\n\n  return (\n    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IACVC,aAAa,EACbC,UAAU,EACVC,QAAQ,EACRC,SAAS,EACTC,WAAW,EACXC,OAAO,QAEF,OAAO;AACd,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsDvC,MAAMC,WAAW,gBAAGT,aAAa,CAA8BU,SAAS,CAAC;;AAEzE;AACA,MAAMC,WAAW,GAAG;EAClBC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,eAAe;EACzBC,YAAY,EAAE,iBAAiB;EAC/BC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;AACxC,CAAC;;AAED;AACA,MAAMC,cAAwC,GAAG;EAC/CC,MAAM,EAAE,CAAC;EACTC,SAAS,EAAE,CAAC;EACZC,KAAK,EAAE,CAAC;EACRC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,MAAMC,kBAA8C,GAAG;EACrDC,SAAS,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EACzDC,OAAO,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC7CC,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC9CC,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC9CC,SAAS,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC/CC,SAAS,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC/CC,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC3CC,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC9CC,QAAQ,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;EACjCC,KAAK,EAAE,CAAC,YAAY;AACtB,CAAC;;AAED;AACA,MAAMC,SAA+B,GAAG;EACtC,cAAc,EAAE;IACdC,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,cAAc;IACrBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;IACjDC,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACpB,kBAAkB,CAAC;IACxCqB,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C,CAAC;EACD,gBAAgB,EAAE;IAChBZ,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,gBAAgB;IACvBC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;IACxCC,OAAO,EAAE,CACP,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACX,OAAO,EACP,UAAU,EACV,UAAU,CACX;IACDG,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C,CAAC;EACD,kBAAkB,EAAE;IAClBZ,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,kBAAkB;IACzBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;IAC9BC,OAAO,EAAE,CACP,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACX,OAAO,EACP,UAAU,CACX;IACDG,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C,CAAC;EACD,aAAa,EAAE;IACbZ,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,aAAa;IACpBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,CAAC,MAAM,CAAC;IACrBC,OAAO,EAAE,CAAC,WAAW,CAAC;IACtBG,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C;AACF,CAAC;AAED,OAAO,SAASC,YAAYA,CAAC;EAAEC;AAAkC,CAAC,EAAE;EAAAC,EAAA;EAClE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhD,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAc,IAAI,CAAC;;EAErE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC9C,WAAW,CAACC,WAAW,CAAC;IAC3D,MAAM8C,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC9C,WAAW,CAACE,QAAQ,CAAC;IAE3D,IAAI0C,KAAK,IAAIG,QAAQ,EAAE;MACrB,IAAI;QACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;QACvCR,OAAO,CAACS,UAAU,CAAC;;QAEnB;QACA,IAAIA,UAAU,CAACG,WAAW,EAAE;UAC1B,MAAMC,MAAM,GAAG,IAAInB,IAAI,CAACe,UAAU,CAACG,WAAW,CAAC;UAC/C,IAAIC,MAAM,GAAG,IAAInB,IAAI,CAAC,CAAC,EAAE;YACvBU,gBAAgB,CAACS,MAAM,CAAC;UAC1B,CAAC,MAAM;YACL;YACAC,MAAM,CAAC,CAAC;UACV;QACF;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CACX,yDAAyD,EACzDA,KACF,CAAC;QACDD,MAAM,CAAC,CAAC;MACV;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7D,SAAS,CAAC,MAAM;IACd,IAAIkD,aAAa,EAAE;MACjB,MAAMc,eAAe,GAAGd,aAAa,CAACe,OAAO,CAAC,CAAC,GAAGxB,IAAI,CAACyB,GAAG,CAAC,CAAC;MAC5D,IAAIF,eAAe,GAAG,CAAC,EAAE;QACvB,MAAMG,KAAK,GAAGC,UAAU,CAAC,MAAM;UAC7BjE,KAAK,CAACkE,OAAO,CAAC,oDAAoD,CAAC;UACnER,MAAM,CAAC,CAAC;QACV,CAAC,EAAEG,eAAe,CAAC;QAEnB,OAAO,MAAMM,YAAY,CAACH,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAACrB,IAAI,EAAEI,aAAa,CAAC,CAAC;;EAEzB;EACA,MAAMqB,KAAK,GAAGtE,WAAW,CAAC,MAAOuE,WAA6B,IAAK;IACjE,IAAI;MACFvB,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM,IAAIwB,OAAO,CAAEC,OAAO,IAAKN,UAAU,CAACM,OAAO,EAAE,IAAI,CAAC,CAAC;MAEzD,MAAMC,SAAS,GAAG9C,SAAS,CAAC2C,WAAW,CAACzC,KAAK,CAAC;MAE9C,IAAI,CAAC4C,SAAS,IAAIH,WAAW,CAACI,QAAQ,KAAK,QAAQ,EAAE;QACnD,OAAO;UACLC,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,iCAAiC;UAC1CC,MAAM,EAAE;YAAEhD,KAAK,EAAE,CAAC,wBAAwB;UAAE;QAC9C,CAAC;MACH;MAEA,IAAI4C,SAAS,CAACpC,MAAM,KAAK,OAAO,EAAE;QAChC,OAAO;UACLsC,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,4BAA4B;UACrCC,MAAM,EAAE;YAAEC,OAAO,EAAE,CAAC,qBAAqB;UAAE;QAC7C,CAAC;MACH;MAEA,MAAMpB,MAAM,GAAG,IAAInB,IAAI,CAACA,IAAI,CAACyB,GAAG,CAAC,CAAC,GAAG1D,WAAW,CAACI,gBAAgB,CAAC;MAClE,MAAMwC,KAAK,GAAG,aAAauB,SAAS,CAAC7C,EAAE,IAAIW,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAE;MAEvD,MAAMe,aAAa,GAAG;QACpB,GAAGN,SAAS;QACZhB,WAAW,EAAEC,MAAM,CAAClB,WAAW,CAAC;MAClC,CAAC;MAEDW,YAAY,CAAC6B,OAAO,CAAC1E,WAAW,CAACC,WAAW,EAAE2C,KAAK,CAAC;MACpDC,YAAY,CAAC6B,OAAO,CAAC1E,WAAW,CAACE,QAAQ,EAAE+C,IAAI,CAAC0B,SAAS,CAACF,aAAa,CAAC,CAAC;MAEzE,IAAIT,WAAW,CAACY,QAAQ,EAAE;QACxB/B,YAAY,CAAC6B,OAAO,CAAC1E,WAAW,CAACG,YAAY,EAAE,MAAM,CAAC;MACxD;MAEAoC,OAAO,CAAC4B,SAAS,CAAC;MAClBxB,gBAAgB,CAACS,MAAM,CAAC;MAExBzD,KAAK,CAAC0E,OAAO,CAAC,aAAaF,SAAS,CAAC1C,MAAM,IAAI0C,SAAS,CAAC3C,GAAG,IAAI,CAAC;MAEjE,OAAO;QACL6C,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QACLe,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,0CAA0C;QACnDC,MAAM,EAAE;UAAEM,OAAO,EAAE,CAAC,gBAAgB;QAAE;MACxC,CAAC;IACH,CAAC,SAAS;MACRpC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqC,QAAQ,GAAGrF,WAAW,CAAC,MAAOuE,WAAgC,IAAK;IACvE,IAAI;MACFvB,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,IAAIuB,WAAW,CAACI,QAAQ,KAAKJ,WAAW,CAACe,eAAe,EAAE;QACxD,OAAO;UACLV,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,wCAAwC;UACjDC,MAAM,EAAE;YACNQ,eAAe,EAAE,CAAC,wCAAwC;UAC5D;QACF,CAAC;MACH;MAEA,IAAIf,WAAW,CAACI,QAAQ,CAACY,MAAM,GAAG,CAAC,EAAE;QACnC,OAAO;UACLX,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,qDAAqD;UAC9DC,MAAM,EAAE;YACNH,QAAQ,EAAE,CAAC,qDAAqD;UAClE;QACF,CAAC;MACH;;MAEA;MACA,MAAMa,YAAY,GAAGpD,MAAM,CAACqD,MAAM,CAAC7D,SAAS,CAAC,CAAC8D,IAAI,CAC/C7C,IAAI,IAAKA,IAAI,CAACf,KAAK,KAAKyC,WAAW,CAACzC,KACvC,CAAC;MACD,IAAI0D,YAAY,EAAE;QAChB,OAAO;UACLZ,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,4BAA4B;UACrCC,MAAM,EAAE;YAAEhD,KAAK,EAAE,CAAC,4BAA4B;UAAE;QAClD,CAAC;MACH;;MAEA;MACA,MAAM,IAAI0C,OAAO,CAAEC,OAAO,IAAKN,UAAU,CAACM,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEzD;MACA,MAAMkB,OAAa,GAAG;QACpB9D,EAAE,EAAE+D,MAAM,CAACpD,IAAI,CAACyB,GAAG,CAAC,CAAC,CAAC;QACtBnC,KAAK,EAAEyC,WAAW,CAACzC,KAAK;QACxBC,GAAG,EAAEwC,WAAW,CAACxC,GAAG;QACpBC,MAAM,EAAEuC,WAAW,CAACvC,MAAM;QAC1BC,IAAI,EAAEsC,WAAW,CAACtC,IAAI,IAAI,QAAQ;QAClCC,WAAW,EAAE,CAAC,MAAM,CAAC;QACrBC,OAAO,EAAE,CAAC,WAAW,CAAC;QACtBG,MAAM,EAAE,OAAO;QACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAC7C,CAAC;;MAED;MACAb,SAAS,CAAC2C,WAAW,CAACzC,KAAK,CAAC,GAAG6D,OAAO;;MAEtC;MACA,MAAMhC,MAAM,GAAG,IAAInB,IAAI,CAACA,IAAI,CAACyB,GAAG,CAAC,CAAC,GAAG1D,WAAW,CAACI,gBAAgB,CAAC;MAClE,MAAMwC,KAAK,GAAG,aAAawC,OAAO,CAAC9D,EAAE,IAAIW,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAE;MAErD,MAAMe,aAAa,GAAG;QACpB,GAAGW,OAAO;QACVjC,WAAW,EAAEC,MAAM,CAAClB,WAAW,CAAC;MAClC,CAAC;MAEDW,YAAY,CAAC6B,OAAO,CAAC1E,WAAW,CAACC,WAAW,EAAE2C,KAAK,CAAC;MACpDC,YAAY,CAAC6B,OAAO,CAAC1E,WAAW,CAACE,QAAQ,EAAE+C,IAAI,CAAC0B,SAAS,CAACF,aAAa,CAAC,CAAC;MAEzElC,OAAO,CAAC6C,OAAO,CAAC;MAChBzC,gBAAgB,CAACS,MAAM,CAAC;MAExBzD,KAAK,CAAC0E,OAAO,CACX,aAAae,OAAO,CAAC3D,MAAM,IAAI2D,OAAO,CAAC5D,GAAG,yCAC5C,CAAC;MAED,OAAO;QACL6C,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,sDAAsD;QAC/DhC,IAAI,EAAE8C;MACR,CAAC;IACH,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QACLe,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,mDAAmD;QAC5DC,MAAM,EAAE;UAAEM,OAAO,EAAE,CAAC,gBAAgB;QAAE;MACxC,CAAC;IACH,CAAC,SAAS;MACRpC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,MAAM,GAAG5D,WAAW,CAAC,MAAM;IAC/BoD,YAAY,CAACyC,UAAU,CAACtF,WAAW,CAACC,WAAW,CAAC;IAChD4C,YAAY,CAACyC,UAAU,CAACtF,WAAW,CAACE,QAAQ,CAAC;IAC7C2C,YAAY,CAACyC,UAAU,CAACtF,WAAW,CAACG,YAAY,CAAC;IACjDoC,OAAO,CAAC,IAAI,CAAC;IACbI,gBAAgB,CAAC,IAAI,CAAC;IACtBhD,KAAK,CAAC4F,IAAI,CAAC,2BAA2B,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,aAAa,GAAG/F,WAAW,CAC9BgG,UAAkB,IAAK;IACtB,OAAO,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX,WAAW,CAAC+D,QAAQ,CAACD,UAAU,CAAC,KAAI,KAAK;EACxD,CAAC,EACD,CAACnD,IAAI,CACP,CAAC;;EAED;EACA,MAAMqD,OAAO,GAAGlG,WAAW,CACxBiC,IAAc,IAAK;IAClB,OAAO,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEZ,IAAI,MAAKA,IAAI;EAC5B,CAAC,EACD,CAACY,IAAI,CACP,CAAC;;EAED;EACA,MAAMsD,eAAe,GAAGnG,WAAW,CAChCoG,MAAc,IAAK;IAClB,IAAI,CAACvD,IAAI,EAAE,OAAO,KAAK;IACvB,MAAMwD,YAAY,GAAGpF,kBAAkB,CAACmF,MAAM,CAAC,IAAI,EAAE;IACrD,OAAOC,YAAY,CAACJ,QAAQ,CAACpD,IAAI,CAACZ,IAAI,CAAC;EACzC,CAAC,EACD,CAACY,IAAI,CACP,CAAC;;EAED;EACA,MAAMyD,gBAAgB,GAAGtG,WAAW,CAAC,MAAM;IACzC,OAAO6C,IAAI,GAAGjC,cAAc,CAACiC,IAAI,CAACZ,IAAI,CAAC,GAAG,CAAC;EAC7C,CAAC,EAAE,CAACY,IAAI,CAAC,CAAC;EAEV,MAAM0D,YAAY,GAAGtG,OAAO,CAC1B,OAAO;IACL4C,IAAI;IACJ2D,eAAe,EAAE,CAAC,CAAC3D,IAAI;IACvBE,SAAS;IACTE,aAAa;IACbqB,KAAK;IACLe,QAAQ;IACRzB,MAAM;IACNmC,aAAa;IACbG,OAAO;IACPC,eAAe;IACfG;EACF,CAAC,CAAC,EACF,CACEzD,IAAI,EACJE,SAAS,EACTE,aAAa,EACbqB,KAAK,EACLe,QAAQ,EACRzB,MAAM,EACNmC,aAAa,EACbG,OAAO,EACPC,eAAe,EACfG,gBAAgB,CAEpB,CAAC;EAED,oBACElG,OAAA,CAACC,WAAW,CAACoG,QAAQ;IAACC,KAAK,EAAEH,YAAa;IAAA5D,QAAA,EAAEA;EAAQ;IAAAgE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAEhF;AAAClE,EAAA,CAnReF,YAAY;AAAAqE,EAAA,GAAZrE,YAAY;AAqR5B,OAAO,SAASsE,OAAOA,CAAA,EAAG;EAAAC,GAAA;EACxB,MAAMC,OAAO,GAAGrH,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAI6G,OAAO,KAAK5G,SAAS,EAAE;IACzB,MAAM,IAAI6G,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB;AAACD,GAAA,CANeD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}