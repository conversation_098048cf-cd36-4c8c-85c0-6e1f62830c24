"""
Service WebSocket pour les notifications en temps réel
"""
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Set
from django.contrib.auth.models import AnonymousUser
from Core.models import SmartNotification, AIInsight
import logging

# Import optionnel de channels pour les WebSockets
try:
    from channels.generic.websocket import AsyncWebsocketConsumer
    from channels.db import database_sync_to_async
    CHANNELS_AVAILABLE = True
except ImportError:
    CHANNELS_AVAILABLE = False
    # Classes de substitution pour éviter les erreurs
    class AsyncWebsocketConsumer:
        pass

    def database_sync_to_async(func):
        return func

logger = logging.getLogger(__name__)


class NotificationConsumer(AsyncWebsocketConsumer):
    """Consumer WebSocket pour les notifications en temps réel"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_id = None
        self.group_name = None
    
    async def connect(self):
        """Connexion WebSocket"""
        # Vérifier l'authentification
        if isinstance(self.scope["user"], AnonymousUser):
            await self.close()
            return
        
        self.user_id = self.scope["user"].id
        self.group_name = f"notifications_{self.user_id}"
        
        # Rejoindre le groupe de notifications
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Envoyer les notifications non lues
        await self.send_unread_notifications()
        
        logger.info(f"WebSocket connecté pour l'utilisateur {self.user_id}")
    
    async def disconnect(self, close_code):
        """Déconnexion WebSocket"""
        if self.group_name:
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
        
        logger.info(f"WebSocket déconnecté pour l'utilisateur {self.user_id}")
    
    async def receive(self, text_data):
        """Réception d'un message du client"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'mark_read':
                await self.mark_notification_read(data.get('notification_id'))
            elif message_type == 'get_notifications':
                await self.send_unread_notifications()
            elif message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': datetime.now().isoformat()
                }))
            
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Format JSON invalide'
            }))
        except Exception as e:
            logger.error(f"Erreur dans receive: {e}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Erreur interne'
            }))
    
    async def send_unread_notifications(self):
        """Envoie les notifications non lues"""
        notifications = await self.get_unread_notifications()
        
        await self.send(text_data=json.dumps({
            'type': 'notifications_list',
            'notifications': notifications,
            'count': len(notifications)
        }))
    
    async def mark_notification_read(self, notification_id):
        """Marque une notification comme lue"""
        try:
            success = await self.mark_notification_as_read(notification_id)
            
            await self.send(text_data=json.dumps({
                'type': 'notification_marked_read',
                'notification_id': notification_id,
                'success': success
            }))
            
        except Exception as e:
            logger.error(f"Erreur lors du marquage de la notification: {e}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Erreur lors du marquage de la notification'
            }))
    
    # Handlers pour les messages de groupe
    async def notification_message(self, event):
        """Envoie une nouvelle notification"""
        await self.send(text_data=json.dumps({
            'type': 'new_notification',
            'notification': event['notification']
        }))
    
    async def insight_message(self, event):
        """Envoie un nouvel insight IA"""
        await self.send(text_data=json.dumps({
            'type': 'new_insight',
            'insight': event['insight']
        }))
    
    async def system_alert(self, event):
        """Envoie une alerte système"""
        await self.send(text_data=json.dumps({
            'type': 'system_alert',
            'alert': event['alert']
        }))
    
    # Méthodes d'accès à la base de données
    @database_sync_to_async
    def get_unread_notifications(self):
        """Récupère les notifications non lues"""
        notifications = SmartNotification.objects.filter(
            is_read=False
        ).order_by('-priority', '-created_at')[:20]

        return [
            {
                'id': str(notif.id),
                'type': notif.type,
                'title': notif.title,
                'message': notif.message,
                'priority': notif.priority,
                'created_at': notif.created_at.isoformat(),
                'action_url': notif.action_url
            } for notif in notifications
        ]
    
    @database_sync_to_async
    def mark_notification_as_read(self, notification_id):
        """Marque une notification comme lue"""
        try:
            notification = SmartNotification.objects.get(id=notification_id)
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()
            return True
        except SmartNotification.DoesNotExist:
            return False


class RealTimeNotificationService:
    """Service pour envoyer des notifications en temps réel"""
    
    @staticmethod
    async def send_notification_to_user(user_id: int, notification_data: dict):
        """Envoie une notification à un utilisateur spécifique"""
        if not CHANNELS_AVAILABLE:
            logger.warning("Channels non disponible - notification WebSocket ignorée")
            return

        try:
            from channels.layers import get_channel_layer

            channel_layer = get_channel_layer()
            group_name = f"notifications_{user_id}"

            await channel_layer.group_send(
                group_name,
                {
                    'type': 'notification_message',
                    'notification': notification_data
                }
            )

            logger.info(f"Notification envoyée à l'utilisateur {user_id}")
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de notification WebSocket: {e}")
    
    @staticmethod
    async def send_insight_to_all(insight_data: dict):
        """Envoie un insight IA à tous les utilisateurs connectés"""
        if not CHANNELS_AVAILABLE:
            logger.warning("Channels non disponible - insight WebSocket ignoré")
            return

        try:
            from channels.layers import get_channel_layer

            channel_layer = get_channel_layer()

            # Envoyer à tous les groupes de notifications
            # Note: Dans un vrai système, on maintiendrait une liste des utilisateurs connectés
            await channel_layer.group_send(
                "notifications_broadcast",
                {
                    'type': 'insight_message',
                    'insight': insight_data
                }
            )

            logger.info("Insight IA diffusé à tous les utilisateurs")
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi d'insight WebSocket: {e}")
    
    @staticmethod
    async def send_system_alert(alert_data: dict, priority: str = 'HIGH'):
        """Envoie une alerte système"""
        if not CHANNELS_AVAILABLE:
            logger.warning("Channels non disponible - alerte WebSocket ignorée")
            return

        try:
            from channels.layers import get_channel_layer

            channel_layer = get_channel_layer()

            await channel_layer.group_send(
                "notifications_broadcast",
                {
                    'type': 'system_alert',
                    'alert': {
                        **alert_data,
                        'priority': priority,
                        'timestamp': datetime.now().isoformat()
                    }
                }
            )

            logger.warning(f"Alerte système envoyée: {alert_data.get('title', 'Sans titre')}")
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi d'alerte WebSocket: {e}")


class NotificationManager:
    """Gestionnaire de notifications intelligent"""
    
    @staticmethod
    def create_smart_notification(
        type_notification: str,
        titre: str,
        message: str,
        priorite: str = 'MEDIUM',
        action_url: str = None,
        user_id: int = None,
        send_realtime: bool = True
    ):
        """Crée une notification intelligente"""
        from django.utils import timezone
        
        # Créer la notification en base
        from django.contrib.auth.models import User

        # Si aucun utilisateur spécifié, prendre le premier admin
        if not user_id:
            admin_user = User.objects.filter(is_staff=True).first()
            if not admin_user:
                # Créer un utilisateur admin par défaut si aucun n'existe
                admin_user = User.objects.create_user(
                    username='admin',
                    email='<EMAIL>',
                    is_staff=True,
                    is_superuser=True
                )
        else:
            admin_user = User.objects.get(id=user_id)

        notification = SmartNotification.objects.create(
            type=type_notification,
            title=titre,
            message=message,
            priority=priorite,
            action_url=action_url or '',
            user=admin_user
        )
        
        # Envoyer en temps réel si demandé
        if send_realtime and user_id:
            notification_data = {
                'id': str(notification.id),
                'type': type_notification,
                'titre': titre,
                'message': message,
                'priorite': priorite,
                'created_at': notification.created_at.isoformat(),
                'action_url': action_url
            }
            
            # Utiliser asyncio pour envoyer la notification
            try:
                loop = asyncio.get_event_loop()
                loop.create_task(
                    RealTimeNotificationService.send_notification_to_user(
                        user_id, notification_data
                    )
                )
            except RuntimeError:
                # Si pas de loop actif, créer une tâche pour plus tard
                asyncio.create_task(
                    RealTimeNotificationService.send_notification_to_user(
                        user_id, notification_data
                    )
                )
        
        return notification
    
    @staticmethod
    def create_stock_alert(produit_nom: str, stock_actuel: int, stock_minimum: int):
        """Crée une alerte de stock"""
        return NotificationManager.create_smart_notification(
            type_notification='STOCK_ALERT',
            titre='Alerte Stock',
            message=f'Stock faible pour {produit_nom}: {stock_actuel}/{stock_minimum}',
            priorite='HIGH',
            action_url='/facturation/produits/'
        )
    
    @staticmethod
    def create_sales_insight(ca_variation: float, periode: str):
        """Crée un insight de ventes"""
        type_notif = 'SALES_UP' if ca_variation > 0 else 'SALES_DOWN'
        priorite = 'HIGH' if abs(ca_variation) > 20 else 'MEDIUM'
        
        return NotificationManager.create_smart_notification(
            type_notification=type_notif,
            titre='Analyse des Ventes',
            message=f'CA {periode}: {"+" if ca_variation > 0 else ""}{ca_variation:.1f}%',
            priorite=priorite,
            action_url='/core/dashboard/'
        )
    
    @staticmethod
    def create_client_insight(client_nom: str, insight_type: str, details: str):
        """Crée un insight client"""
        return NotificationManager.create_smart_notification(
            type_notification='CLIENT_INSIGHT',
            titre=f'Insight Client: {client_nom}',
            message=details,
            priorite='MEDIUM',
            action_url='/facturation/clients/'
        )


# Tâches périodiques pour les notifications automatiques
class AutoNotificationTasks:
    """Tâches automatiques pour les notifications"""
    
    @staticmethod
    def check_stock_levels():
        """Vérifie les niveaux de stock et crée des alertes"""
        from Facturation.models import Produit
        from django.db.models import Sum, F
        
        # Produits en stock faible
        produits_stock_faible = Produit.objects.filter(
            is_active=True
        ).annotate(
            stock_total=Sum('stock__quantite')
        ).filter(
            stock_total__lte=F('stock_minimum'),
            stock_total__gt=0
        )
        
        for produit in produits_stock_faible:
            NotificationManager.create_stock_alert(
                produit.nom,
                produit.stock_actuel,
                produit.stock_minimum
            )
    
    @staticmethod
    def analyze_sales_trends():
        """Analyse les tendances de ventes et crée des insights"""
        from Core.analytics import AnalyticsService
        
        try:
            metrics = AnalyticsService.get_dashboard_metrics()
            ca_trend = metrics['ventes']['tendance_ca']
            
            if abs(ca_trend) > 10:  # Variation significative
                NotificationManager.create_sales_insight(ca_trend, 'ce mois')
                
        except Exception as e:
            logger.error(f"Erreur lors de l'analyse des tendances: {e}")
    
    @staticmethod
    def generate_daily_insights():
        """Génère des insights quotidiens"""
        from Core.analytics import AnalyticsService
        
        try:
            # Générer des insights IA
            insights = AnalyticsService.generate_ai_insights()
            
            # Créer des notifications pour les insights importants
            for insight in insights:
                if insight.get('priority') == 'HIGH':
                    NotificationManager.create_smart_notification(
                        type_notification='AI_INSIGHT',
                        titre=insight['title'],
                        message=insight['message'],
                        priorite='HIGH',
                        action_url='/core/dashboard/'
                    )
                    
        except Exception as e:
            logger.error(f"Erreur lors de la génération des insights: {e}")


# Configuration des routing WebSocket
websocket_urlpatterns = [
    # path('ws/notifications/', NotificationConsumer.as_asgi()),
]
