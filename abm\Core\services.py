"""
Services intelligents pour l'automatisation et l'IA
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.db.models import Sum, Count, Avg, Q
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from .models import SmartNotification, AIInsight, AuditLog
from .utils import AIAnalyzer, SmartCache


logger = logging.getLogger(__name__)


class IntelligentDashboardService:
    """Service pour tableau de bord intelligent avec IA"""
    
    @staticmethod
    def generate_smart_insights(user: User) -> List[Dict]:
        """Génère des insights intelligents personnalisés"""
        insights = []
        
        # Import dynamique pour éviter les dépendances circulaires
        from Facturation.models import Facture, Client, Produit
        
        # Analyse des ventes
        sales_data = Facture.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).values('date_emission', 'montant_ttc')
        
        if sales_data:
            sales_analysis = AIAnalyzer.analyze_sales_trend(list(sales_data))
            insights.append({
                'type': 'sales_trend',
                'title': 'Tendance des Ventes',
                'data': sales_analysis,
                'priority': 'high' if sales_analysis['trend'] == 'decreasing' else 'normal'
            })
        
        # Analyse des clients
        top_clients = Client.objects.annotate(
            total_spent=Sum('factures__montant_ttc')
        ).order_by('-total_spent')[:5]
        
        insights.append({
            'type': 'top_clients',
            'title': 'Top 5 Clients',
            'data': [
                {
                    'name': client.nom,
                    'total': float(client.total_spent or 0),
                    'invoices_count': client.factures.count()
                }
                for client in top_clients
            ],
            'priority': 'normal'
        })
        
        # Prédictions de stock
        low_stock_products = Produit.objects.filter(
            stock_actuel__lt=10
        )
        
        if low_stock_products.exists():
            insights.append({
                'type': 'stock_alert',
                'title': 'Alerte Stock Faible',
                'data': {
                    'count': low_stock_products.count(),
                    'products': [p.nom for p in low_stock_products[:5]]
                },
                'priority': 'urgent'
            })
        
        return insights
    
    @staticmethod
    def get_kpi_dashboard(user: User) -> Dict:
        """KPIs intelligents pour le dashboard"""
        from Facturation.models import Facture, Client, Produit
        
        # Période actuelle vs précédente
        now = timezone.now()
        current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        previous_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
        
        # Revenus
        current_revenue = Facture.objects.filter(
            date_emission__gte=current_month_start,
            statut='PAYEE'
        ).aggregate(total=Sum('montant_ttc'))['total'] or 0
        
        previous_revenue = Facture.objects.filter(
            date_emission__gte=previous_month_start,
            date_emission__lt=current_month_start,
            statut='PAYEE'
        ).aggregate(total=Sum('montant_ttc'))['total'] or 0
        
        revenue_growth = 0
        if previous_revenue > 0:
            revenue_growth = ((current_revenue - previous_revenue) / previous_revenue) * 100
        
        # Nouveaux clients
        new_clients = Client.objects.filter(
            created_at__gte=current_month_start
        ).count()
        
        # Factures en attente
        pending_invoices = Facture.objects.filter(
            statut='ENVOYEE',
            date_echeance__lt=now.date()
        ).count()
        
        return {
            'revenue': {
                'current': float(current_revenue),
                'growth': revenue_growth,
                'trend': 'up' if revenue_growth > 0 else 'down'
            },
            'clients': {
                'total': Client.objects.count(),
                'new_this_month': new_clients
            },
            'invoices': {
                'total': Facture.objects.count(),
                'pending': pending_invoices,
                'overdue': pending_invoices
            },
            'products': {
                'total': Produit.objects.count(),
                'low_stock': Produit.objects.filter(stock_actuel__lt=10).count()
            }
        }


class SmartNotificationService:
    """Service de notifications intelligentes"""
    
    @staticmethod
    def create_notification(
        user: User,
        title: str,
        message: str,
        type: str = 'INFO',
        priority: str = 'NORMAL',
        action_url: str = '',
        expires_in_days: int = 7
    ) -> SmartNotification:
        """Crée une notification intelligente"""
        expires_at = timezone.now() + timedelta(days=expires_in_days)
        
        notification = SmartNotification.objects.create(
            user=user,
            title=title,
            message=message,
            type=type,
            priority=priority,
            action_url=action_url,
            expires_at=expires_at
        )
        
        # Log de l'action
        AuditLog.objects.create(
            user=user,
            action='CREATE',
            model_name='SmartNotification',
            object_id=str(notification.id),
            object_repr=title
        )
        
        return notification
    
    @staticmethod
    def send_smart_reminders():
        """Envoie des rappels intelligents automatiques"""
        from Facturation.models import Facture
        
        # Factures en retard
        overdue_invoices = Facture.objects.filter(
            statut='ENVOYEE',
            date_echeance__lt=timezone.now().date()
        )
        
        for invoice in overdue_invoices:
            # Notification au créateur de la facture
            if invoice.created_by:
                SmartNotificationService.create_notification(
                    user=invoice.created_by,
                    title=f"Facture en retard: {invoice.numero}",
                    message=f"La facture {invoice.numero} pour {invoice.client.nom} est en retard de {(timezone.now().date() - invoice.date_echeance).days} jours.",
                    type='WARNING',
                    priority='HIGH',
                    action_url=f'/facturation/facture/{invoice.id}/'
                )
        
        # Stock faible
        from Facturation.models import Produit
        low_stock_products = Produit.objects.filter(stock_actuel__lt=10)
        
        if low_stock_products.exists():
            # Notification aux administrateurs
            admin_users = User.objects.filter(is_staff=True)
            for admin in admin_users:
                SmartNotificationService.create_notification(
                    user=admin,
                    title="Alerte Stock Faible",
                    message=f"{low_stock_products.count()} produits ont un stock faible.",
                    type='ALERT',
                    priority='HIGH',
                    action_url='/stock/low-stock/'
                )


class AutomationService:
    """Service d'automatisation intelligente"""
    
    @staticmethod
    def auto_categorize_expense(description: str, amount: float) -> str:
        """Catégorisation automatique des dépenses par IA"""
        description_lower = description.lower()
        
        # Règles simples d'IA pour la catégorisation
        if any(word in description_lower for word in ['essence', 'carburant', 'station']):
            return 'TRANSPORT'
        elif any(word in description_lower for word in ['restaurant', 'repas', 'déjeuner']):
            return 'REPAS'
        elif any(word in description_lower for word in ['bureau', 'fourniture', 'papier']):
            return 'FOURNITURES'
        elif any(word in description_lower for word in ['internet', 'téléphone', 'mobile']):
            return 'COMMUNICATION'
        elif amount > 1000:
            return 'EQUIPEMENT'
        else:
            return 'DIVERS'
    
    @staticmethod
    def auto_generate_invoice_number(client_code: str = None) -> str:
        """Génération automatique de numéros de facture intelligents"""
        from Facturation.models import Facture
        
        current_year = timezone.now().year
        current_month = timezone.now().month
        
        # Format: FACT-YYYY-MM-XXXX ou FACT-CLIENT-YYYY-MM-XXXX
        if client_code:
            prefix = f"FACT-{client_code}-{current_year:04d}-{current_month:02d}"
        else:
            prefix = f"FACT-{current_year:04d}-{current_month:02d}"
        
        # Trouver le prochain numéro
        last_invoice = Facture.objects.filter(
            numero__startswith=prefix
        ).order_by('-numero').first()
        
        if last_invoice:
            try:
                last_number = int(last_invoice.numero.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}-{next_number:04d}"
    
    @staticmethod
    def smart_price_suggestion(product_name: str, category: str = None) -> Dict:
        """Suggestion intelligente de prix basée sur l'historique"""
        from Facturation.models import Produit
        
        # Recherche de produits similaires
        similar_products = Produit.objects.filter(
            nom__icontains=product_name.split()[0]  # Premier mot
        )
        
        if category:
            similar_products = similar_products.filter(categorie__nom=category)
        
        if similar_products.exists():
            avg_price = similar_products.aggregate(
                avg=Avg('prix_unitaire')
            )['avg']
            
            min_price = similar_products.aggregate(
                min=Sum('prix_unitaire')
            )['min']
            
            max_price = similar_products.aggregate(
                max=Sum('prix_unitaire')
            )['max']
            
            return {
                'suggested_price': float(avg_price),
                'price_range': {
                    'min': float(min_price),
                    'max': float(max_price)
                },
                'confidence': min(similar_products.count() / 10, 1.0),
                'based_on': similar_products.count()
            }
        
        return {
            'suggested_price': 0.0,
            'price_range': {'min': 0.0, 'max': 0.0},
            'confidence': 0.0,
            'based_on': 0
        }


class ReportingService:
    """Service de reporting intelligent"""
    
    @staticmethod
    def generate_smart_report(report_type: str, date_range: Dict) -> Dict:
        """Génère des rapports intelligents avec insights"""
        from Facturation.models import Facture, Client, Produit
        
        start_date = date_range.get('start')
        end_date = date_range.get('end')
        
        if report_type == 'sales_performance':
            return ReportingService._generate_sales_report(start_date, end_date)
        elif report_type == 'client_analysis':
            return ReportingService._generate_client_report(start_date, end_date)
        elif report_type == 'product_performance':
            return ReportingService._generate_product_report(start_date, end_date)
        else:
            return {'error': 'Type de rapport non supporté'}
    
    @staticmethod
    def _generate_sales_report(start_date, end_date) -> Dict:
        """Rapport de performance des ventes"""
        from Facturation.models import Facture
        
        invoices = Facture.objects.filter(
            date_emission__range=[start_date, end_date]
        )
        
        total_revenue = invoices.aggregate(Sum('montant_ttc'))['total'] or 0
        total_invoices = invoices.count()
        avg_invoice_value = total_revenue / total_invoices if total_invoices > 0 else 0
        
        # Analyse par statut
        status_breakdown = invoices.values('statut').annotate(
            count=Count('id'),
            total=Sum('montant_ttc')
        )
        
        return {
            'period': {'start': start_date, 'end': end_date},
            'summary': {
                'total_revenue': float(total_revenue),
                'total_invoices': total_invoices,
                'average_invoice_value': float(avg_invoice_value)
            },
            'status_breakdown': list(status_breakdown),
            'insights': AIAnalyzer.analyze_sales_trend(
                list(invoices.values('date_emission', 'montant_ttc'))
            )
        }
