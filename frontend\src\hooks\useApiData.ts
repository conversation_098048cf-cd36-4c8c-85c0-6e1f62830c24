/**
 * Hook personnalisé pour gérer les données API de manière cohérente
 */

import { useState, useEffect, useCallback } from 'react';
import { useNotify } from '../components/Common/NotificationSystem';

interface UseApiDataOptions<T> {
  apiCall: () => Promise<{ data?: T; error?: string }>;
  dependencies?: any[];
  initialData?: T;
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
  autoLoad?: boolean;
}

interface UseApiDataReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  reload: () => Promise<void>;
  setData: (data: T | null) => void;
}

export function useApiData<T = any>({
  apiCall,
  dependencies = [],
  initialData = null,
  onSuccess,
  onError,
  autoLoad = true
}: UseApiDataOptions<T>): UseApiDataReturn<T> {
  const [data, setData] = useState<T | null>(initialData);
  const [loading, setLoading] = useState(autoLoad);
  const [error, setError] = useState<string | null>(null);
  const notify = useNotify();

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiCall();

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        setData(response.data);
        onSuccess?.(response.data);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erreur lors du chargement des données';
      setError(errorMessage);
      onError?.(errorMessage);
      notify.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [apiCall, onSuccess, onError, notify]);

  useEffect(() => {
    if (autoLoad) {
      loadData();
    }
  }, [loadData, autoLoad, ...dependencies]);

  return {
    data,
    loading,
    error,
    reload: loadData,
    setData
  };
}

/**
 * Hook pour gérer les listes paginées
 */
interface UsePaginatedDataOptions<T> extends Omit<UseApiDataOptions<T>, 'apiCall'> {
  apiCall: (params: any) => Promise<{ data?: any; error?: string }>;
  pageSize?: number;
  filters?: any;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface UsePaginatedDataReturn<T> extends UseApiDataReturn<T[]> {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  setCurrentPage: (page: number) => void;
  setFilters: (filters: any) => void;
  setSorting: (sortBy: string, sortOrder?: 'asc' | 'desc') => void;
}

export function usePaginatedData<T = any>({
  apiCall,
  pageSize = 20,
  filters = {},
  sortBy = 'created_at',
  sortOrder = 'desc',
  dependencies = [],
  onSuccess,
  onError,
  autoLoad = true
}: UsePaginatedDataOptions<T>): UsePaginatedDataReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(autoLoad);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [currentFilters, setCurrentFilters] = useState(filters);
  const [currentSortBy, setCurrentSortBy] = useState(sortBy);
  const [currentSortOrder, setCurrentSortOrder] = useState(sortOrder);
  const notify = useNotify();

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        page: currentPage,
        page_size: pageSize,
        ordering: currentSortOrder === 'desc' ? `-${currentSortBy}` : currentSortBy,
      };

      // Ajouter les filtres actifs
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value && value.toString().trim() !== '') {
          params[key] = value;
        }
      });

      const response = await apiCall(params);

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        const results = response.data.results || response.data;
        setData(results);
        setTotalCount(response.data.count || results.length);
        setTotalPages(Math.ceil((response.data.count || results.length) / pageSize));
        onSuccess?.(results);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erreur lors du chargement des données';
      setError(errorMessage);
      onError?.(errorMessage);
      notify.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [
    apiCall,
    currentPage,
    pageSize,
    currentFilters,
    currentSortBy,
    currentSortOrder,
    onSuccess,
    onError,
    notify,
    ...dependencies
  ]);

  useEffect(() => {
    if (autoLoad) {
      loadData();
    }
  }, [loadData, autoLoad]);

  const setFilters = useCallback((newFilters: any) => {
    setCurrentFilters(newFilters);
    setCurrentPage(1); // Reset à la première page lors du changement de filtres
  }, []);

  const setSorting = useCallback((newSortBy: string, newSortOrder: 'asc' | 'desc' = 'desc') => {
    setCurrentSortBy(newSortBy);
    setCurrentSortOrder(newSortOrder);
    setCurrentPage(1); // Reset à la première page lors du changement de tri
  }, []);

  return {
    data,
    loading,
    error,
    reload: loadData,
    setData,
    currentPage,
    totalPages,
    totalCount,
    setCurrentPage,
    setFilters,
    setSorting
  };
}

/**
 * Hook pour gérer les opérations CRUD
 */
interface UseCrudOptions<T> {
  getService: (id: string) => Promise<{ data?: T; error?: string }>;
  createService: (data: any) => Promise<{ data?: T; error?: string }>;
  updateService: (id: string, data: any) => Promise<{ data?: T; error?: string }>;
  deleteService: (id: string) => Promise<{ data?: any; error?: string }>;
}

export function useCrud<T = any>(options: UseCrudOptions<T>) {
  const notify = useNotify();

  const create = useCallback(async (data: any) => {
    try {
      const response = await options.createService(data);
      if (response.error) {
        throw new Error(response.error);
      }
      notify.success('Élément créé avec succès');
      return { success: true, data: response.data };
    } catch (err: any) {
      notify.error(`Erreur lors de la création: ${err.message}`);
      return { success: false, error: err.message };
    }
  }, [options.createService, notify]);

  const update = useCallback(async (id: string, data: any) => {
    try {
      const response = await options.updateService(id, data);
      if (response.error) {
        throw new Error(response.error);
      }
      notify.success('Élément modifié avec succès');
      return { success: true, data: response.data };
    } catch (err: any) {
      notify.error(`Erreur lors de la modification: ${err.message}`);
      return { success: false, error: err.message };
    }
  }, [options.updateService, notify]);

  const remove = useCallback(async (id: string) => {
    try {
      const response = await options.deleteService(id);
      if (response.error) {
        throw new Error(response.error);
      }
      notify.success('Élément supprimé avec succès');
      return { success: true };
    } catch (err: any) {
      notify.error(`Erreur lors de la suppression: ${err.message}`);
      return { success: false, error: err.message };
    }
  }, [options.deleteService, notify]);

  const get = useCallback(async (id: string) => {
    try {
      const response = await options.getService(id);
      if (response.error) {
        throw new Error(response.error);
      }
      return { success: true, data: response.data };
    } catch (err: any) {
      notify.error(`Erreur lors du chargement: ${err.message}`);
      return { success: false, error: err.message };
    }
  }, [options.getService, notify]);

  return {
    create,
    update,
    remove,
    get
  };
}
