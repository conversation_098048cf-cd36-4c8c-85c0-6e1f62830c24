/**
 * <PERSON><PERSON><PERSON> complet d'un client avec historique et actions
 */

import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ClientService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";

interface Client {
  id: string;
  nom: string;
  prenom?: string;
  email: string;
  telephone?: string;
  type_client: "PARTICULIER" | "ENTREPRISE" | "ASSOCIATION" | "ADMINISTRATION";
  statut: "ACTIF" | "INACTIF" | "SUSPENDU" | "PROSPECT";
  segment: "VIP" | "PREMIUM" | "STANDARD" | "NOUVEAU";
  adresse?: string;
  ville?: string;
  code_postal?: string;
  pays: string;
  siret?: string;
  tva_intracommunautaire?: string;
  remise_par_defaut: string;
  limite_credit: string;
  delai_paiement: number;
  total_achats: string;
  nb_commandes: number;
  derniere_commande?: string;
  notes?: string;
  tags?: string;
  created_at: string;
  updated_at: string;
  // Propriétés calculées
  nom_complet: string;
  adresse_complete: string;
  segment_color: string;
  ca_annuel: number;
  // Propriétés de compatibilité (mappées depuis les vraies propriétés)
  date_creation: string; // Mappé depuis created_at
  nombre_factures: number; // Mappé depuis nb_commandes ou calculé
  chiffre_affaires_total: number; // Mappé depuis ca_annuel ou total_achats
}

const ClientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const notify = useNotify();

  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [invoices, setInvoices] = useState<any[]>([]);
  const [orders, setOrders] = useState<any[]>([]);

  useEffect(() => {
    if (id) {
      loadClient(id);
      loadClientInvoices(id);
      loadClientOrders(id);
    }
  }, [id]);

  const loadClient = async (clientId: string) => {
    try {
      setLoading(true);
      const response = await ClientService.getClient(clientId);

      if (response && response.data) {
        setClient(response.data);
      } else {
        throw new Error("Erreur lors du chargement du client");
      }
    } catch (error: any) {
      notify.error("Erreur lors du chargement du client");
      navigate("/clients");
    } finally {
      setLoading(false);
    }
  };

  const loadClientInvoices = async (clientId: string) => {
    try {
      // Simulation de données pour le mode démo
      setInvoices([
        {
          id: 1,
          numero: "F2024-001",
          montant: 1500,
          statut: "PAYEE",
          date_emission: "2024-01-15",
        },
        {
          id: 2,
          numero: "F2024-002",
          montant: 2300,
          statut: "EN_ATTENTE",
          date_emission: "2024-01-20",
        },
      ]);
    } catch (error: any) {
      console.error("Erreur chargement factures:", error);
    }
  };

  const loadClientOrders = async (clientId: string) => {
    try {
      // Simulation de données pour le mode démo
      setOrders([
        {
          id: 1,
          numero: "C2024-001",
          montant: 850,
          statut: "LIVREE",
          date_commande: "2024-01-10",
        },
        {
          id: 2,
          numero: "C2024-002",
          montant: 1200,
          statut: "EN_COURS",
          date_commande: "2024-01-18",
        },
      ]);
    } catch (error: any) {
      console.error("Erreur chargement commandes:", error);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!client) return;

    try {
      setActionLoading("status");
      // Simulation de mise à jour pour le mode démo
      setClient((prev) =>
        prev ? { ...prev, statut: newStatus as any } : null
      );
      notify.success("Statut mis à jour avec succès");
    } catch (error: any) {
      notify.error("Erreur lors de la mise à jour du statut");
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async () => {
    if (!client) return;

    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce client ?")) {
      return;
    }

    try {
      setActionLoading("delete");
      const response = await ClientService.deleteClient(client.id);

      if (response) {
        notify.success("Client supprimé avec succès");
        navigate("/clients");
      } else {
        throw new Error("Erreur lors de la suppression");
      }
    } catch (error: any) {
      notify.error("Erreur lors de la suppression");
    } finally {
      setActionLoading(null);
    }
  };

  const getSegmentColor = (segment: string) => {
    switch (segment) {
      case "VIP":
        return "segment-vip";
      case "PREMIUM":
        return "segment-premium";
      case "STANDARD":
        return "segment-standard";
      default:
        return "segment-default";
    }
  };

  const getSegmentLabel = (segment: string) => {
    switch (segment) {
      case "VIP":
        return "⭐ VIP";
      case "PREMIUM":
        return "💎 Premium";
      case "STANDARD":
        return "👤 Standard";
      default:
        return segment;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIF":
        return "status-active";
      case "INACTIF":
        return "status-inactive";
      case "PROSPECT":
        return "status-prospect";
      default:
        return "status-default";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="client-detail-loading">
        <div className="loading-spinner"></div>
        <p>Chargement du client...</p>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="client-not-found">
        <div className="error-icon">❌</div>
        <h3>Client introuvable</h3>
        <p>Le client demandé n'existe pas ou a été supprimé</p>
        <button
          className="btn btn-primary"
          onClick={() => navigate("/clients")}>
          ← Retour aux clients
        </button>
      </div>
    );
  }

  return (
    <div className="client-detail">
      {/* Header avec actions */}
      <div className="detail-header">
        <div className="header-info">
          <div className="client-title">
            <h1>
              {client.type_client === "ENTREPRISE" ? "🏢" : "👤"} {client.nom}
            </h1>
            <div className="client-badges">
              <span
                className={`segment-badge ${getSegmentColor(client.segment)}`}>
                {getSegmentLabel(client.segment)}
              </span>
              <span className={`status-badge ${getStatusColor(client.statut)}`}>
                {client.statut}
              </span>
            </div>
          </div>
          <div className="client-summary">
            <span>Client depuis le {formatDate(client.date_creation)}</span>
            <span>
              {client.nombre_factures} factures •{" "}
              {formatCurrency(client.chiffre_affaires_total)}
            </span>
            {client.derniere_commande && (
              <span>
                Dernière commande: {formatDate(client.derniere_commande)}
              </span>
            )}
          </div>
        </div>

        <div className="header-actions">
          <button
            className="btn btn-outline"
            onClick={() => navigate("/clients")}>
            ← Retour
          </button>

          <button
            className="btn btn-info"
            onClick={() => navigate(`/clients/${client.id}/edit`)}>
            ✏️ Modifier
          </button>

          <button
            className="btn btn-primary"
            onClick={() => navigate(`/invoices/new?client=${client.id}`)}>
            📄 Nouvelle facture
          </button>

          <div className="dropdown">
            <button className="btn btn-outline dropdown-toggle">⋮ Plus</button>
            <div className="dropdown-menu">
              <button
                className="dropdown-item"
                onClick={() => navigate(`/orders/new?client=${client.id}`)}>
                🛒 Nouvelle commande
              </button>
              <button
                className="dropdown-item"
                onClick={() =>
                  window.open(`/api/clients/${client.id}/export`, "_blank")
                }>
                📤 Exporter données
              </button>
              <div className="dropdown-divider"></div>
              <button
                className="dropdown-item danger"
                onClick={handleDelete}
                disabled={actionLoading === "delete"}>
                🗑️ Supprimer
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Actions de statut */}
      {client.statut !== "ACTIF" && (
        <div className="status-actions">
          <h4>Actions rapides</h4>
          <div className="status-buttons">
            {client.statut === "PROSPECT" && (
              <button
                className="btn btn-success"
                onClick={() => handleStatusChange("ACTIF")}
                disabled={actionLoading === "status"}>
                ✅ Convertir en client
              </button>
            )}
            {client.statut === "INACTIF" && (
              <button
                className="btn btn-primary"
                onClick={() => handleStatusChange("ACTIF")}
                disabled={actionLoading === "status"}>
                🔄 Réactiver
              </button>
            )}
            <button
              className="btn btn-warning btn-outline"
              onClick={() => handleStatusChange("INACTIF")}
              disabled={actionLoading === "status"}>
              ⏸️ Désactiver
            </button>
          </div>
        </div>
      )}

      {/* Informations détaillées */}
      <div className="client-section">
        <h3>📋 Informations Détaillées</h3>
        <div className="client-info-grid">
          <div className="info-item">
            <label>Nom:</label>
            <span>{client.nom}</span>
          </div>
          <div className="info-item">
            <label>Email:</label>
            <span>{client.email}</span>
          </div>
          <div className="info-item">
            <label>Téléphone:</label>
            <span>{client.telephone || "Non renseigné"}</span>
          </div>
          <div className="info-item">
            <label>Type:</label>
            <span>
              {client.type_client === "ENTREPRISE"
                ? "🏢 Entreprise"
                : "👤 Particulier"}
            </span>
          </div>
          {client.siret && (
            <div className="info-item">
              <label>SIRET:</label>
              <span>{client.siret}</span>
            </div>
          )}
          {client.tva_intracommunautaire && (
            <div className="info-item">
              <label>TVA Intracommunautaire:</label>
              <span>{client.tva_intracommunautaire}</span>
            </div>
          )}
          <div className="info-item full-width">
            <label>Adresse:</label>
            <span>
              {client.adresse && <div>{client.adresse}</div>}
              {client.ville && client.code_postal && (
                <div>
                  {client.code_postal} {client.ville}
                </div>
              )}
              {client.pays && <div>{client.pays}</div>}
            </span>
          </div>
          {client.notes && (
            <div className="info-item full-width">
              <label>Notes:</label>
              <span>{client.notes}</span>
            </div>
          )}
        </div>
      </div>

      {/* Statistiques client */}
      <div className="client-section">
        <h3>📊 Statistiques</h3>
        <div className="stats-grid">
          <div className="stat-card primary">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h4>Chiffre d'affaires</h4>
              <div className="stat-value">
                {formatCurrency(client.chiffre_affaires_total)}
              </div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">📄</div>
            <div className="stat-content">
              <h4>Factures</h4>
              <div className="stat-value">{client.nombre_factures}</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">📈</div>
            <div className="stat-content">
              <h4>Panier moyen</h4>
              <div className="stat-value">
                {formatCurrency(
                  client.nombre_factures > 0
                    ? client.chiffre_affaires_total / client.nombre_factures
                    : 0
                )}
              </div>
            </div>
          </div>
          <div className="stat-card secondary">
            <div className="stat-icon">📅</div>
            <div className="stat-content">
              <h4>Client depuis</h4>
              <div className="stat-value">
                {formatDate(client.date_creation)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Factures récentes */}
      <div className="client-section">
        <div className="section-header">
          <h3>📄 Factures Récentes</h3>
          <button
            className="btn btn-primary btn-sm"
            onClick={() => navigate(`/invoices/new?client=${client.id}`)}>
            ➕ Nouvelle facture
          </button>
        </div>

        {invoices.length > 0 ? (
          <div className="invoices-table-container">
            <table className="invoices-table">
              <thead>
                <tr>
                  <th>Numéro</th>
                  <th>Date</th>
                  <th>Montant</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {invoices.slice(0, 5).map((invoice) => (
                  <tr key={invoice.id}>
                    <td>
                      <span
                        className="clickable"
                        onClick={() => navigate(`/invoices/${invoice.id}`)}>
                        {invoice.numero}
                      </span>
                    </td>
                    <td>{formatDate(invoice.date_emission)}</td>
                    <td className="amount">
                      {formatCurrency(invoice.montant_ttc)}
                    </td>
                    <td>
                      <span
                        className={`status-badge status-${invoice.statut.toLowerCase()}`}>
                        {invoice.statut}
                      </span>
                    </td>
                    <td>
                      <button
                        className="action-btn view-btn"
                        onClick={() => navigate(`/invoices/${invoice.id}`)}
                        title="Voir facture">
                        👁️
                      </button>
                      <button
                        className="action-btn pdf-btn"
                        onClick={() =>
                          window.open(
                            `/api/invoices/${invoice.id}/pdf`,
                            "_blank"
                          )
                        }
                        title="PDF">
                        📄
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {invoices.length > 5 && (
              <div className="see-more">
                <button
                  className="btn btn-outline"
                  onClick={() => navigate(`/invoices?client=${client.id}`)}>
                  Voir toutes les factures ({invoices.length})
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">📄</div>
            <h4>Aucune facture</h4>
            <p>Ce client n'a pas encore de factures</p>
            <button
              className="btn btn-primary"
              onClick={() => navigate(`/invoices/new?client=${client.id}`)}>
              ➕ Créer la première facture
            </button>
          </div>
        )}
      </div>

      {/* Commandes récentes */}
      <div className="client-section">
        <div className="section-header">
          <h3>🛒 Commandes Récentes</h3>
          <button
            className="btn btn-primary btn-sm"
            onClick={() => navigate(`/orders/new?client=${client.id}`)}>
            ➕ Nouvelle commande
          </button>
        </div>

        {orders.length > 0 ? (
          <div className="orders-table-container">
            <table className="orders-table">
              <thead>
                <tr>
                  <th>Numéro</th>
                  <th>Date</th>
                  <th>Montant</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {orders.slice(0, 5).map((order) => (
                  <tr key={order.id}>
                    <td>
                      <span
                        className="clickable"
                        onClick={() => navigate(`/orders/${order.id}`)}>
                        {order.numero}
                      </span>
                    </td>
                    <td>{formatDate(order.date_commande)}</td>
                    <td className="amount">
                      {formatCurrency(order.montant_total)}
                    </td>
                    <td>
                      <span
                        className={`status-badge status-${order.statut.toLowerCase()}`}>
                        {order.statut}
                      </span>
                    </td>
                    <td>
                      <button
                        className="action-btn view-btn"
                        onClick={() => navigate(`/orders/${order.id}`)}
                        title="Voir commande">
                        👁️
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {orders.length > 5 && (
              <div className="see-more">
                <button
                  className="btn btn-outline"
                  onClick={() => navigate(`/orders?client=${client.id}`)}>
                  Voir toutes les commandes ({orders.length})
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">🛒</div>
            <h4>Aucune commande</h4>
            <p>Ce client n'a pas encore passé de commandes</p>
            <button
              className="btn btn-primary"
              onClick={() => navigate(`/orders/new?client=${client.id}`)}>
              ➕ Créer la première commande
            </button>
          </div>
        )}
      </div>

      {/* Actions rapides en bas */}
      <div className="detail-footer">
        <div className="footer-actions">
          <button
            className="btn btn-primary"
            onClick={() => navigate(`/invoices/new?client=${client.id}`)}>
            📄 Nouvelle facture
          </button>

          <button
            className="btn btn-info"
            onClick={() => navigate(`/orders/new?client=${client.id}`)}>
            🛒 Nouvelle commande
          </button>

          <button
            className="btn btn-secondary"
            onClick={() => navigate(`/clients/${client.id}/edit`)}>
            ✏️ Modifier
          </button>

          <button
            className="btn btn-outline"
            onClick={() =>
              window.open(`/api/clients/${client.id}/export`, "_blank")
            }>
            📤 Exporter
          </button>
        </div>

        <div className="footer-info">
          <span>Dernière modification: {formatDate(client.updated_at)}</span>
        </div>
      </div>
    </div>
  );
};

export default ClientDetail;
