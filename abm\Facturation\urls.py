"""
URLs Facturation Professionnelles - Système Moderne
Basé sur l'image fournie avec génération PDF avancée
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ConfigurationFacturationViewSet,
    FactureViewSet,
    LigneFactureViewSet,
    PaiementFactureViewSet
)

app_name = 'facturation'

# Router pour les APIs REST
router = DefaultRouter()
router.register(r'configuration', ConfigurationFacturationViewSet, basename='configuration')
router.register(r'factures', FactureViewSet, basename='factures')
router.register(r'lignes', LigneFactureViewSet, basename='lignes')
router.register(r'paiements', PaiementFactureViewSet, basename='paiements')

urlpatterns = [
    # APIs REST
    path('api/', include(router.urls)),

    # URLs spécifiques
    path('api/factures/<int:pk>/pdf/', FactureViewSet.as_view({'get': 'pdf'}), name='facture-pdf'),
    path('api/factures/<int:pk>/dupliquer/', FactureViewSet.as_view({'post': 'dupliquer'}), name='facture-dupliquer'),
    path('api/factures/<int:pk>/marquer-payee/', FactureViewSet.as_view({'post': 'marquer_payee'}), name='facture-marquer-payee'),
    path('api/factures/statistiques/', FactureViewSet.as_view({'get': 'statistiques'}), name='factures-statistiques'),
    path('api/configuration/active/', ConfigurationFacturationViewSet.as_view({'get': 'active'}), name='configuration-active'),
]
