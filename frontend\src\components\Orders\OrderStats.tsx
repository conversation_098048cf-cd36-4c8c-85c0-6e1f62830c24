/**
 * Statistiques des commandes
 */

import React, { useState, useEffect } from "react";
import { OrderService } from "../../services/apiService";
import { toast } from "react-toastify";

interface OrderStats {
  total_commandes: number;
  commandes_confirmees: number;
  commandes_en_preparation: number;
  commandes_expediees: number;
  commandes_livrees: number;
  commandes_annulees: number;
  ca_total_commandes: number;
  panier_moyen: number;
  delai_moyen_livraison: number;
  taux_livraison_temps: number;
  commandes_urgentes: number;
  retards_livraison: number;
}

interface OrderStatsProps {
  embedded?: boolean;
  period?: "month" | "quarter" | "year";
}

const OrderStats: React.FC<OrderStatsProps> = ({
  embedded = false,
  period = "month",
}) => {
  const [stats, setStats] = useState<OrderStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState(period);

  useEffect(() => {
    loadStats();
  }, [selectedPeriod]);

  const loadStats = async () => {
    try {
      setLoading(true);
      // Simulation des statistiques
      const response = {
        success: true,
        data: {
          total_commandes: 89,
          commandes_confirmees: 25,
          commandes_en_preparation: 18,
          commandes_expediees: 12,
          commandes_livrees: 76,
          commandes_annulees: 4,
          ca_total_commandes: 32150.75,
          panier_moyen: 361.24,
          delai_moyen_livraison: 3.2,
          taux_livraison_temps: 94.5,
          commandes_urgentes: 7,
          retards_livraison: 2,
        },
      };

      if (response.success) {
        setStats(response.data);
      } else {
        // Données de démonstration
        setStats({
          total_commandes: 45,
          commandes_confirmees: 12,
          commandes_en_preparation: 8,
          commandes_expediees: 5,
          commandes_livrees: 18,
          commandes_annulees: 2,
          ca_total_commandes: 125000,
          panier_moyen: 2777,
          delai_moyen_livraison: 5.2,
          taux_livraison_temps: 89,
          commandes_urgentes: 6,
          retards_livraison: 3,
        });
      }
    } catch (error: any) {
      toast.error("Erreur lors du chargement des statistiques");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const calculatePercentage = (value: number, total: number) => {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  if (loading) {
    return (
      <div className="order-stats-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des statistiques...</p>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className={`order-stats ${embedded ? "embedded" : ""}`}>
      {!embedded && (
        <div className="stats-header">
          <h3>📊 Statistiques Commandes</h3>
          <div className="period-selector">
            <button
              className={`period-btn ${
                selectedPeriod === "month" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("month")}>
              Ce mois
            </button>
            <button
              className={`period-btn ${
                selectedPeriod === "quarter" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("quarter")}>
              Trimestre
            </button>
            <button
              className={`period-btn ${
                selectedPeriod === "year" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("year")}>
              Année
            </button>
          </div>
        </div>
      )}

      <div className="stats-grid">
        {/* Total commandes */}
        <div className="stat-card primary">
          <div className="stat-icon">🛒</div>
          <div className="stat-content">
            <h4>Total Commandes</h4>
            <div className="stat-value">{stats.total_commandes}</div>
            <div className="stat-subtitle">
              {stats.commandes_urgentes} urgentes
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar primary"
                style={{ width: "100%" }}></div>
            </div>
          </div>
        </div>

        {/* Commandes livrées */}
        <div className="stat-card success">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h4>Livrées</h4>
            <div className="stat-value">{stats.commandes_livrees}</div>
            <div className="stat-subtitle">
              {calculatePercentage(
                stats.commandes_livrees,
                stats.total_commandes
              )}
              % du total
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar success"
                style={{
                  width: `${calculatePercentage(
                    stats.commandes_livrees,
                    stats.total_commandes
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>

        {/* En cours */}
        <div className="stat-card warning">
          <div className="stat-icon">⏳</div>
          <div className="stat-content">
            <h4>En Cours</h4>
            <div className="stat-value">
              {stats.commandes_confirmees +
                stats.commandes_en_preparation +
                stats.commandes_expediees}
            </div>
            <div className="stat-subtitle">À traiter</div>
            <div className="stat-progress">
              <div
                className="progress-bar warning"
                style={{
                  width: `${calculatePercentage(
                    stats.commandes_confirmees +
                      stats.commandes_en_preparation +
                      stats.commandes_expediees,
                    stats.total_commandes
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>

        {/* CA Commandes */}
        <div className="stat-card info">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h4>CA Commandes</h4>
            <div className="stat-value">
              {formatCurrency(stats.ca_total_commandes)}
            </div>
            <div className="stat-subtitle">
              Panier: {formatCurrency(stats.panier_moyen)}
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar info"
                style={{ width: "75%" }}></div>
            </div>
          </div>
        </div>

        {/* Performance livraison */}
        <div className="stat-card secondary">
          <div className="stat-icon">🚚</div>
          <div className="stat-content">
            <h4>Livraison</h4>
            <div className="stat-value">{stats.taux_livraison_temps}%</div>
            <div className="stat-subtitle">
              {stats.delai_moyen_livraison.toFixed(1)} jours moy.
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar secondary"
                style={{ width: `${stats.taux_livraison_temps}%` }}></div>
            </div>
          </div>
        </div>

        {/* Retards */}
        <div className="stat-card danger">
          <div className="stat-icon">⚠️</div>
          <div className="stat-content">
            <h4>Retards</h4>
            <div className="stat-value">{stats.retards_livraison}</div>
            <div className="stat-subtitle">
              {calculatePercentage(
                stats.retards_livraison,
                stats.total_commandes
              )}
              % du total
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar danger"
                style={{
                  width: `${calculatePercentage(
                    stats.retards_livraison,
                    stats.total_commandes
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Résumé par statut */}
      <div className="stats-summary">
        <div className="summary-item">
          <div className="summary-label">✅ Livrées</div>
          <div className="summary-value success">{stats.commandes_livrees}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">📦 En préparation</div>
          <div className="summary-value warning">
            {stats.commandes_en_preparation}
          </div>
        </div>
        <div className="summary-item">
          <div className="summary-label">🚚 Expédiées</div>
          <div className="summary-value info">{stats.commandes_expediees}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">❌ Annulées</div>
          <div className="summary-value danger">{stats.commandes_annulees}</div>
        </div>
      </div>
    </div>
  );
};

export default OrderStats;
