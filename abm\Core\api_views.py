"""
API Views pour le tableau de bord intelligent
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.shortcuts import render
from Core.analytics import AnalyticsService
from Core.consolidation import ConsolidationService
from Core.models import SmartNotification, AIInsight, SystemHealth
# from Core.monitoring import system_monitor, performance_profiler, MonitoringTasks
# from Core.ai_recommendations import recommendation_engine, RecommendationTasks
# from Core.intelligent_search import search_engine, SearchTasks
# from Core.smart_validation import smart_validator, ValidationTasks
import json
import datetime
from dateutil.relativedelta import relativedelta


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_metrics(request):
    """API pour récupérer les métriques du tableau de bord"""
    try:
        metrics = AnalyticsService.get_dashboard_metrics()
        return Response({
            'success': True,
            'data': metrics,
            'timestamp': timezone.now()
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def top_products(request):
    """API pour récupérer les produits les plus performants"""
    try:
        limit = int(request.GET.get('limit', 5))
        products = AnalyticsService.get_top_products(limit)
        return Response({
            'success': True,
            'data': products
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def category_performance(request):
    """API pour l'analyse de performance par catégorie"""
    try:
        performance = AnalyticsService.get_category_performance()
        return Response({
            'success': True,
            'data': performance
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def stock_alerts(request):
    """API pour les alertes de stock"""
    try:
        alerts = AnalyticsService.get_stock_alerts()
        return Response({
            'success': True,
            'data': alerts
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def client_insights(request):
    """API pour les insights clients"""
    try:
        insights = AnalyticsService.get_client_insights()
        return Response({
            'success': True,
            'data': insights
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_ai_insights(request):
    """API pour générer des insights IA"""
    try:
        insights = AnalyticsService.generate_ai_insights()
        return Response({
            'success': True,
            'data': insights,
            'message': f'{len(insights)} nouveaux insights générés'
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def notifications(request):
    """API pour récupérer les notifications"""
    try:
        notifications = SmartNotification.objects.filter(
            is_active=True,
            lu=False
        ).order_by('-priorite', '-created_at')[:20]
        
        data = [
            {
                'id': str(notif.id),
                'type': notif.type_notification,
                'titre': notif.titre,
                'message': notif.message,
                'priorite': notif.priorite,
                'created_at': notif.created_at,
                'action_url': notif.action_url
            } for notif in notifications
        ]
        
        return Response({
            'success': True,
            'data': data,
            'count': len(data)
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read(request, notification_id):
    """API pour marquer une notification comme lue"""
    try:
        notification = SmartNotification.objects.get(id=notification_id)
        notification.lu = True
        notification.date_lecture = timezone.now()
        notification.save()
        
        return Response({
            'success': True,
            'message': 'Notification marquée comme lue'
        })
    except SmartNotification.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Notification non trouvée'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ai_insights(request):
    """API pour récupérer les insights IA"""
    try:
        insights = AIInsight.objects.filter(
            is_active=True
        ).order_by('-priorite', '-created_at')[:10]
        
        data = [
            {
                'id': str(insight.id),
                'type': insight.type_insight,
                'titre': insight.titre,
                'description': insight.description,
                'priorite': insight.priorite,
                'confiance': insight.score_confiance,
                'action_recommandee': insight.action_recommandee,
                'created_at': insight.created_at
            } for insight in insights
        ]
        
        return Response({
            'success': True,
            'data': data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def consolidate_data(request):
    """API pour lancer la consolidation des données"""
    try:
        action = request.data.get('action', 'analyze')
        
        if action == 'analyze':
            result = ConsolidationService.analyze_duplications()
        elif action == 'consolidate':
            result = ConsolidationService.consolidate_products()
        elif action == 'sync_prices':
            result = ConsolidationService.synchronize_prices()
        elif action == 'report':
            result = ConsolidationService.generate_consolidation_report()
        else:
            return Response({
                'success': False,
                'error': 'Action non reconnue'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'success': True,
            'data': result
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@require_http_methods(["GET"])
def health_check(request):
    """Endpoint de vérification de santé de l'API"""
    try:
        # Vérifications basiques
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        return JsonResponse({
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'version': '1.0.0',
            'services': {
                'database': 'ok',
                'analytics': 'ok',
                'consolidation': 'ok'
            }
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def system_stats(request):
    """API pour les statistiques système"""
    try:
        from django.db import connection
        from Facturation.models import Produit, Client, Facture
        from Ecommerce.models import ProduitEcommerce
        
        # Statistiques de base de données
        with connection.cursor() as cursor:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
        
        stats = {
            'database': {
                'tables_count': len(tables),
                'produits_count': Produit.objects.count(),
                'clients_count': Client.objects.count(),
                'factures_count': Facture.objects.count(),
                'produits_ecommerce_count': ProduitEcommerce.objects.count()
            },
            'cache': {
                'status': 'active',
                'hit_rate': 85.5  # Simulé pour l'exemple
            },
            'performance': {
                'avg_response_time': 120,  # ms
                'requests_per_minute': 45
            }
        }
        
        return Response({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def futuristic_dashboard_view(request):
    """Vue pour le tableau de bord futuriste"""
    return render(request, 'core/futuristic_dashboard.html')


# ===== MONITORING API VIEWS =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def system_health_api(request):
    """API pour l'état de santé du système"""
    try:
        # Temporairement désactivé
        health_check = {
            'status': 'healthy',
            'system_metrics': {'cpu_percent': 25.0, 'memory_percent': 45.0},
            'health_status': {'overall_status': 'healthy'}
        }
        return Response(health_check, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la vérification de santé: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def system_metrics_api(request):
    """API pour les métriques système"""
    try:
        # Temporairement désactivé
        metrics = {
            'system': {'cpu_percent': 25.0, 'memory_percent': 45.0, 'disk_usage': 60.0},
            'database': {'connections': 5, 'queries_per_second': 10},
            'application': {'active_users': 3, 'cache_hit_rate': 85.0}
        }
        return Response(metrics, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la collecte des métriques: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def performance_stats_api(request):
    """API pour les statistiques de performance"""
    try:
        stats = performance_profiler.get_performance_stats()
        return Response(stats, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la récupération des stats: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def run_health_check_api(request):
    """API pour déclencher une vérification de santé manuelle"""
    try:
        result = MonitoringTasks.run_periodic_health_check()
        return Response(result, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la vérification de santé: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def monitoring_dashboard_api(request):
    """API pour le tableau de bord de monitoring complet"""
    try:
        # Collecter toutes les données nécessaires
        current_metrics = system_monitor.collect_all_metrics()
        health_status = system_monitor.check_health_status(current_metrics)
        performance_stats = performance_profiler.get_performance_stats()

        # Récupérer les derniers enregistrements de santé
        recent_health = SystemHealth.objects.order_by('-created_at')[:10]
        health_history = []
        for record in recent_health:
            health_history.append({
                'timestamp': record.created_at.isoformat(),
                'status': record.status,
                'issues_count': len(record.issues),
                'warnings_count': len(record.warnings)
            })

        # Statistiques des notifications
        notifications_stats = {
            'total': SmartNotification.objects.count(),
            'unread': SmartNotification.objects.filter(is_read=False).count(),
            'recent': SmartNotification.objects.filter(
                created_at__gte=timezone.now() - timezone.timedelta(hours=24)
            ).count()
        }

        dashboard_data = {
            'current_metrics': current_metrics,
            'health_status': health_status,
            'performance_stats': performance_stats,
            'health_history': health_history,
            'notifications_stats': notifications_stats,
            'timestamp': timezone.now().isoformat()
        }

        return Response(dashboard_data, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération du tableau de bord: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# ===== AI RECOMMENDATIONS API VIEWS =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ai_recommendations_api(request):
    """API pour récupérer toutes les recommandations IA"""
    try:
        recommendations = recommendation_engine.generate_all_recommendations()
        return Response(recommendations, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération des recommandations: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pricing_recommendations_api(request):
    """API pour les recommandations de prix"""
    try:
        recommendations = recommendation_engine.generate_pricing_recommendations()
        return Response({
            'recommendations': recommendations,
            'count': len(recommendations),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération des recommandations de prix: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def inventory_recommendations_api(request):
    """API pour les recommandations de stock"""
    try:
        recommendations = recommendation_engine.generate_inventory_recommendations()
        return Response({
            'recommendations': recommendations,
            'count': len(recommendations),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération des recommandations de stock: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def sales_recommendations_api(request):
    """API pour les recommandations de vente"""
    try:
        recommendations = recommendation_engine.generate_sales_recommendations()
        return Response({
            'recommendations': recommendations,
            'count': len(recommendations),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération des recommandations de vente: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def client_recommendations_api(request):
    """API pour les recommandations client"""
    try:
        recommendations = recommendation_engine.generate_client_recommendations()
        return Response({
            'recommendations': recommendations,
            'count': len(recommendations),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération des recommandations client: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_daily_recommendations_api(request):
    """API pour générer les recommandations quotidiennes"""
    try:
        recommendations = RecommendationTasks.run_daily_recommendations()
        summary = RecommendationTasks.get_recommendations_summary()

        return Response({
            'message': 'Recommandations générées avec succès',
            'summary': summary,
            'recommendations': recommendations
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération des recommandations quotidiennes: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def recommendations_summary_api(request):
    """API pour le résumé des recommandations"""
    try:
        summary = RecommendationTasks.get_recommendations_summary()
        return Response(summary, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération du résumé: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# ===== INTELLIGENT SEARCH API VIEWS =====

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def global_search_api(request):
    """API pour la recherche globale intelligente"""
    try:
        query = request.GET.get('q', '').strip()
        limit = int(request.GET.get('limit', 10))

        if not query:
            return Response(
                {'error': 'Paramètre de recherche "q" requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = search_engine.global_search(query, limit)
        return Response(results, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la recherche: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_products_api(request):
    """API pour la recherche de produits"""
    try:
        query = request.GET.get('q', '').strip()
        limit = int(request.GET.get('limit', 20))

        if not query:
            return Response(
                {'error': 'Paramètre de recherche "q" requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = search_engine.search_products(query, limit)
        return Response({
            'results': results,
            'count': len(results),
            'query': query
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la recherche de produits: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_clients_api(request):
    """API pour la recherche de clients"""
    try:
        query = request.GET.get('q', '').strip()
        limit = int(request.GET.get('limit', 20))

        if not query:
            return Response(
                {'error': 'Paramètre de recherche "q" requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = search_engine.search_clients(query, limit)
        return Response({
            'results': results,
            'count': len(results),
            'query': query
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la recherche de clients: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_invoices_api(request):
    """API pour la recherche de factures"""
    try:
        query = request.GET.get('q', '').strip()
        limit = int(request.GET.get('limit', 20))

        if not query:
            return Response(
                {'error': 'Paramètre de recherche "q" requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = search_engine.search_invoices(query, limit)
        return Response({
            'results': results,
            'count': len(results),
            'query': query
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la recherche de factures: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_suggestions_api(request):
    """API pour les suggestions de recherche"""
    try:
        partial_query = request.GET.get('q', '').strip()
        limit = int(request.GET.get('limit', 5))

        suggestions = search_engine.get_search_suggestions(partial_query, limit)
        return Response({
            'suggestions': suggestions,
            'count': len(suggestions),
            'partial_query': partial_query
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la génération des suggestions: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_analytics_api(request):
    """API pour les analytics de recherche"""
    try:
        analytics = search_engine.get_search_analytics()
        return Response(analytics, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la récupération des analytics: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# ===== SMART VALIDATION API VIEWS =====

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_field_api(request):
    """API pour valider un champ spécifique"""
    try:
        field_type = request.data.get('type')
        field_value = request.data.get('value')

        if not field_type:
            return Response(
                {'error': 'Type de champ requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if field_type not in smart_validator.validation_rules:
            return Response(
                {'error': f'Type de validation non supporté: {field_type}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Appliquer la validation
        validation_result = smart_validator.validation_rules[field_type](field_value)

        return Response(validation_result, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la validation: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_form_api(request):
    """API pour valider un formulaire complet"""
    try:
        form_data = request.data.get('data', {})
        validation_schema = request.data.get('schema', {})

        if not form_data:
            return Response(
                {'error': 'Données du formulaire requises'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not validation_schema:
            return Response(
                {'error': 'Schéma de validation requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Valider le formulaire
        validation_result = smart_validator.validate_form_data(form_data, validation_schema)

        return Response(validation_result, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la validation du formulaire: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_email_api(request):
    """API spécialisée pour la validation d'email"""
    try:
        email = request.data.get('email', '')
        result = smart_validator.validate_email_smart(email)
        return Response(result, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la validation de l\'email: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_phone_api(request):
    """API spécialisée pour la validation de téléphone"""
    try:
        phone = request.data.get('phone', '')
        result = smart_validator.validate_phone_smart(phone)
        return Response(result, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la validation du téléphone: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_price_api(request):
    """API spécialisée pour la validation de prix"""
    try:
        price = request.data.get('price')
        result = smart_validator.validate_price_smart(price)
        return Response(result, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la validation du prix: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def validate_existing_data_api(request):
    """API pour valider les données existantes"""
    try:
        validation_report = ValidationTasks.validate_existing_data()
        return Response(validation_report, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la validation des données existantes: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def validation_rules_api(request):
    """API pour récupérer les règles de validation disponibles"""
    try:
        rules = {
            'available_types': list(smart_validator.validation_rules.keys()),
            'patterns': {
                'tunisian_phone': smart_validator.tunisian_patterns['phone'],
                'fiscal_number': smart_validator.tunisian_patterns['fiscal_number'],
                'postal_code': smart_validator.tunisian_patterns['postal_code']
            },
            'common_corrections': smart_validator.common_corrections
        }
        return Response(rules, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': f'Erreur lors de la récupération des règles: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# ===== SYSTEM OPTIMIZATION API VIEWS =====

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def system_optimize_api(request):
    """API pour exécuter l'optimisation système"""
    try:
        action = request.data.get('action', 'intelligent-maintenance')

        # Simuler l'exécution de la commande d'optimisation
        from django.core.management import call_command
        from io import StringIO
        import sys

        # Capturer la sortie de la commande
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()

        try:
            call_command('optimize_system', action=action)
            output = captured_output.getvalue()

            return Response({
                'status': 'completed',
                'action': action,
                'output': output,
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_200_OK)

        except Exception as cmd_error:
            return Response({
                'status': 'error',
                'action': action,
                'error': str(cmd_error),
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        finally:
            sys.stdout = old_stdout

    except Exception as e:
        return Response(
            {'error': f'Erreur lors de l\'optimisation: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def monthly_revenue(request):
    """API pour récupérer les revenus mensuels"""
    try:
        months = int(request.GET.get('months', 12))

        # Données de démonstration pour les revenus mensuels
        import datetime
        from dateutil.relativedelta import relativedelta

        current_date = timezone.now().date()
        revenue_data = []

        for i in range(months):
            month_date = current_date - relativedelta(months=i)
            revenue_data.append({
                'month': month_date.strftime('%Y-%m'),
                'month_name': month_date.strftime('%B %Y'),
                'revenue': 15000 + (i * 2000) + (i % 3 * 5000),  # Données simulées
                'orders': 45 + (i * 5),
                'growth': round((i % 4 - 1.5) * 10, 1)  # Croissance simulée
            })

        revenue_data.reverse()  # Ordre chronologique

        return Response({
            'success': True,
            'data': revenue_data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def sales_overview(request):
    """API pour récupérer l'aperçu des ventes"""
    try:
        # Données de démonstration
        overview_data = {
            'total_sales': 125000,
            'total_orders': 342,
            'average_order_value': 365.50,
            'conversion_rate': 3.2,
            'top_selling_category': 'Électronique',
            'growth_rate': 12.5
        }

        return Response({
            'success': True,
            'data': overview_data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def recent_activities(request):
    """API pour récupérer les activités récentes"""
    try:
        limit = int(request.GET.get('limit', 10))

        # Données de démonstration
        activities = [
            {
                'id': 1,
                'type': 'order',
                'title': 'Nouvelle commande #1234',
                'description': 'Commande de 450€ par Client ABC',
                'timestamp': timezone.now() - timezone.timedelta(minutes=15),
                'icon': 'shopping-cart',
                'color': 'success'
            },
            {
                'id': 2,
                'type': 'payment',
                'title': 'Paiement reçu',
                'description': 'Paiement de 1200€ pour la facture #5678',
                'timestamp': timezone.now() - timezone.timedelta(hours=2),
                'icon': 'credit-card',
                'color': 'info'
            },
            {
                'id': 3,
                'type': 'stock',
                'title': 'Stock faible',
                'description': 'Produit XYZ - Stock: 5 unités',
                'timestamp': timezone.now() - timezone.timedelta(hours=4),
                'icon': 'alert-triangle',
                'color': 'warning'
            }
        ][:limit]

        return Response({
            'success': True,
            'data': activities
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def quick_stats(request):
    """API pour récupérer les statistiques rapides"""
    try:
        # Données de démonstration
        stats = {
            'today_sales': 2450.00,
            'today_orders': 12,
            'pending_invoices': 8,
            'low_stock_items': 3,
            'new_customers': 5,
            'total_revenue_month': 45000.00
        }

        return Response({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
