from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count, Q, Avg
from django.utils import timezone

from .models import Four<PERSON><PERSON><PERSON>, CommandeFournisseur
from .serializers import FournisseurSerializer, CommandeFournisseurSerializer
from Authentication.permissions import HasModulePermission

class FournisseurViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des fournisseurs"""

    queryset = Fournisseur.objects.all()
    serializer_class = FournisseurSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'fournisseurs'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'type_fournisseur', 'pays']
    search_fields = ['nom', 'email', 'code_fournisseur', 'ville']
    ordering_fields = ['nom', 'created_at', 'total_achats']
    ordering = ['nom']

    def perform_create(self, serializer):
        # Générer un code fournisseur automatique
        code = self.generer_code_fournisseur()
        serializer.save(
            code_fournisseur=code,
            created_by=self.request.user
        )

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    def generer_code_fournisseur(self):
        """Génère un code fournisseur unique"""
        prefix = "FOUR"
        last_fournisseur = Fournisseur.objects.filter(
            code_fournisseur__startswith=prefix
        ).order_by('-code_fournisseur').first()

        if last_fournisseur:
            last_number = int(last_fournisseur.code_fournisseur.replace(prefix, ''))
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:06d}"

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Statistiques des fournisseurs"""

        # Statistiques de base
        total_fournisseurs = Fournisseur.objects.count()
        fournisseurs_actifs = Fournisseur.objects.filter(statut='ACTIF').count()

        # Répartition par type
        types = Fournisseur.objects.values('type_fournisseur').annotate(
            count=Count('id'),
            total_achats=Sum('total_achats')
        ).order_by('-count')

        # Top fournisseurs
        top_fournisseurs = Fournisseur.objects.filter(
            total_achats__gt=0
        ).order_by('-total_achats')[:10]

        # Notes moyennes
        notes_moyennes = Fournisseur.objects.exclude(
            note_qualite__isnull=True,
            note_delai__isnull=True,
            note_service__isnull=True
        ).aggregate(
            qualite=Avg('note_qualite'),
            delai=Avg('note_delai'),
            service=Avg('note_service')
        )

        data = {
            'total_fournisseurs': total_fournisseurs,
            'fournisseurs_actifs': fournisseurs_actifs,
            'types': list(types),
            'top_fournisseurs': FournisseurSerializer(top_fournisseurs, many=True).data,
            'notes_moyennes': notes_moyennes
        }

        return Response(data)


class CommandeFournisseurViewSet(viewsets.ModelViewSet):
    """ViewSet pour les commandes fournisseurs"""

    queryset = CommandeFournisseur.objects.all()
    serializer_class = CommandeFournisseurSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'fournisseurs'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'fournisseur']
    search_fields = ['numero', 'fournisseur__nom']
    ordering = ['-date_commande']

    def perform_create(self, serializer):
        # Générer un numéro de commande automatique
        numero = self.generer_numero_commande()
        serializer.save(
            numero=numero,
            created_by=self.request.user
        )

    def generer_numero_commande(self):
        """Génère un numéro de commande fournisseur unique"""
        today = timezone.now()
        prefix = f"ACH-{today.year}-{today.month:02d}-"

        last_commande = CommandeFournisseur.objects.filter(
            numero__startswith=prefix
        ).order_by('-numero').first()

        if last_commande:
            last_number = int(last_commande.numero.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:04d}"
