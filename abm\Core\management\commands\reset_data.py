"""
Commande pour réinitialiser les données avec les nouveaux modèles UUID
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth.models import User
from Facturation.models import Categorie, Produit, Client, Facture, Stock
from Ecommerce.models import CategorieEcommerce, ProduitEcommerce
from decimal import Decimal
import uuid


class Command(BaseCommand):
    help = 'Réinitialise les données avec les nouveaux modèles UUID'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirme la suppression des données existantes',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    '⚠️ Cette commande va supprimer toutes les données existantes!\n'
                    'Utilisez --confirm pour confirmer.'
                )
            )
            return

        self.stdout.write('🗑️ Suppression des données existantes...')
        
        with transaction.atomic():
            # Supprimer dans l'ordre des dépendances
            Facture.objects.all().delete()
            ProduitEcommerce.objects.all().delete()
            Produit.objects.all().delete()
            Client.objects.all().delete()
            CategorieEcommerce.objects.all().delete()
            Categorie.objects.all().delete()
            
            self.stdout.write('✅ Données supprimées')
            
            # Créer des données de démonstration
            self.stdout.write('📦 Création des données de démonstration...')
            self._create_demo_data()
            
        self.stdout.write(
            self.style.SUCCESS('✅ Réinitialisation terminée avec succès!')
        )

    def _create_demo_data(self):
        """Crée des données de démonstration"""
        
        # Créer des catégories
        cat_electronique = Categorie.objects.create(
            nom="Électronique",
            description="Produits électroniques et high-tech",
            color="#007bff",
            icon="fas fa-laptop"
        )
        
        cat_mobilier = Categorie.objects.create(
            nom="Mobilier",
            description="Meubles et décoration",
            color="#28a745",
            icon="fas fa-couch"
        )
        
        cat_vetements = Categorie.objects.create(
            nom="Vêtements",
            description="Mode et accessoires",
            color="#dc3545",
            icon="fas fa-tshirt"
        )
        
        # Créer des catégories e-commerce correspondantes
        cat_ecom_electronique = CategorieEcommerce.objects.create(
            nom="Électronique",
            slug="electronique",
            description="Produits électroniques et high-tech",
            ordre=1
        )
        
        cat_ecom_mobilier = CategorieEcommerce.objects.create(
            nom="Mobilier",
            slug="mobilier",
            description="Meubles et décoration",
            ordre=2
        )
        
        cat_ecom_vetements = CategorieEcommerce.objects.create(
            nom="Vêtements",
            slug="vetements",
            description="Mode et accessoires",
            ordre=3
        )
        
        # Créer des produits
        produits_data = [
            {
                'nom': 'Ordinateur Portable HP',
                'description': 'Ordinateur portable HP 15.6" avec processeur Intel i5',
                'prix': Decimal('1200.00'),
                'stock': 10,
                'categorie': cat_electronique,
                'cat_ecom': cat_ecom_electronique
            },
            {
                'nom': 'Smartphone Samsung Galaxy',
                'description': 'Smartphone Samsung Galaxy A54 128GB',
                'prix': Decimal('800.00'),
                'stock': 25,
                'categorie': cat_electronique,
                'cat_ecom': cat_ecom_electronique
            },
            {
                'nom': 'Canapé 3 Places',
                'description': 'Canapé confortable 3 places en tissu gris',
                'prix': Decimal('650.00'),
                'stock': 5,
                'categorie': cat_mobilier,
                'cat_ecom': cat_ecom_mobilier
            },
            {
                'nom': 'Table Basse Design',
                'description': 'Table basse moderne en verre et métal',
                'prix': Decimal('250.00'),
                'stock': 8,
                'categorie': cat_mobilier,
                'cat_ecom': cat_ecom_mobilier
            },
            {
                'nom': 'Chemise Homme',
                'description': 'Chemise homme coton blanc taille M',
                'prix': Decimal('45.00'),
                'stock': 50,
                'categorie': cat_vetements,
                'cat_ecom': cat_ecom_vetements
            },
            {
                'nom': 'Robe Femme',
                'description': 'Robe élégante femme taille 38',
                'prix': Decimal('85.00'),
                'stock': 30,
                'categorie': cat_vetements,
                'cat_ecom': cat_ecom_vetements
            }
        ]
        
        for prod_data in produits_data:
            # Créer le produit de facturation
            produit = Produit.objects.create(
                nom=prod_data['nom'],
                description=prod_data['description'],
                prix_unitaire=prod_data['prix'],
                stock_minimum=5,  # Stock minimum par défaut
                categorie=prod_data['categorie'],
                code_produit=self._generate_product_code(prod_data['categorie'], prod_data['nom'])
            )

            # Créer un mouvement de stock initial
            Stock.objects.create(
                produit=produit,
                type_mouvement='ENTREE',
                quantite=prod_data['stock'],
                motif=f'Stock initial - {produit.code_produit}',
                prix_unitaire=prod_data['prix']
            )
            
            # Créer le produit e-commerce correspondant
            ProduitEcommerce.objects.create(
                produit=produit,
                slug=self._generate_slug(prod_data['nom']),
                description_courte=prod_data['description'][:100],
                description_longue=prod_data['description'],
                prix_public=prod_data['prix'],
                prix_promo=prod_data['prix'] * Decimal('0.9'),  # 10% de réduction
                visible_en_ligne=True,
                gestion_stock=True,
                stock_disponible=prod_data['stock']
            )
            
            # Ajouter la catégorie e-commerce
            produit_ecom = ProduitEcommerce.objects.get(produit=produit)
            produit_ecom.categories_ecommerce.add(prod_data['cat_ecom'])
        
        # Créer des clients
        clients_data = [
            {
                'nom': 'Ben Ahmed',
                'prenom': 'Mohamed',
                'email': '<EMAIL>',
                'telephone': '+216 20 123 456',
                'type_client': 'PARTICULIER',
                'ville': 'Tunis',
                'adresse_ligne1': '15 Avenue Habib Bourguiba'
            },
            {
                'nom': 'Société TechnoTunisie',
                'prenom': '',
                'email': '<EMAIL>',
                'telephone': '+216 71 234 567',
                'type_client': 'ENTREPRISE',
                'ville': 'Sfax',
                'adresse_ligne1': '25 Rue de la République',
                'nom_entreprise': 'TechnoTunisie SARL',
                'matricule_fiscal': '1234567ABC123'
            },
            {
                'nom': 'Trabelsi',
                'prenom': 'Fatma',
                'email': '<EMAIL>',
                'telephone': '+216 22 345 678',
                'type_client': 'PARTICULIER',
                'ville': 'Sousse',
                'adresse_ligne1': '8 Rue Ibn Khaldoun'
            }
        ]
        
        for client_data in clients_data:
            Client.objects.create(
                nom=client_data['nom'],
                prenom=client_data['prenom'],
                email=client_data['email'],
                telephone=client_data['telephone'],
                type_client=client_data['type_client'],
                ville=client_data['ville'],
                adresse_ligne1=client_data['adresse_ligne1'],
                code_postal='1000',
                pays='Tunisie',
                nom_entreprise=client_data.get('nom_entreprise', ''),
                matricule_fiscal=client_data.get('matricule_fiscal', '')
            )
        
        self.stdout.write('✅ Données de démonstration créées:')
        self.stdout.write(f'  • {Categorie.objects.count()} catégories')
        self.stdout.write(f'  • {Produit.objects.count()} produits')
        self.stdout.write(f'  • {Client.objects.count()} clients')
        self.stdout.write(f'  • {ProduitEcommerce.objects.count()} produits e-commerce')

    def _generate_product_code(self, categorie, nom):
        """Génère un code produit intelligent"""
        prefix = categorie.nom[:3].upper()
        suffix = str(uuid.uuid4())[:8].upper()
        return f"{prefix}-{suffix}"

    def _generate_slug(self, nom):
        """Génère un slug pour l'e-commerce"""
        from django.utils.text import slugify
        return slugify(nom)
