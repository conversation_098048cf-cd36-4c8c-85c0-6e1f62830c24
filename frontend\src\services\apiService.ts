/**
 * Service API unifié et nettoyé - ABM
 * Architecture cohérente avec le backend Django
 * Supprime tous les doublons et incohérences
 */

import { API_CONFIG, getDefaultHeaders, getErrorMessage } from "../config/api";

// === TYPES ET INTERFACES ===

export interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  results?: T[];
  count?: number;
  next?: string;
  previous?: string;
  message?: string;
  error?: string;
}

// === CLASSE DE BASE API SERVICE ===

// Données de démonstration pour les produits
const DEMO_PRODUCTS = [
  {
    id: "1",
    code: "B1994471111",
    designation: "PRODUIT DESIGN FUTURISTE",
    description:
      "Produit innovant avec design moderne et fonctionnalités avancées",
    prix_unitaire: 57.015,
    prix_achat: 35.5,
    stock_actuel: 150,
    stock_minimum: 20,
    stock_maximum: 500,
    unite: "pièce",
    categorie: "Design",
    tva: 20.0,
    statut: "ACTIF",
    fournisseur: "Fournisseur Premium",
    date_creation: "2024-01-10",
  },
  {
    id: "2",
    code: "B1991720001",
    designation: "ARTICLE PREMIER PLAN",
    description: "Article de qualité supérieure pour usage professionnel",
    prix_unitaire: 25.0,
    prix_achat: 15.75,
    stock_actuel: 75,
    stock_minimum: 10,
    stock_maximum: 200,
    unite: "pièce",
    categorie: "Standard",
    tva: 20.0,
    statut: "ACTIF",
    fournisseur: "Fournisseur Standard",
    date_creation: "2024-02-15",
  },
  {
    id: "3",
    code: "B1993500002",
    designation: "SERVICE FUTURISME ELEGANT",
    description: "Service premium avec approche élégante et moderne",
    prix_unitaire: 45.5,
    prix_achat: 28.0,
    stock_actuel: 0,
    stock_minimum: 0,
    stock_maximum: 0,
    unite: "service",
    categorie: "Service",
    tva: 20.0,
    statut: "ACTIF",
    fournisseur: "Service Interne",
    date_creation: "2024-03-20",
  },
  {
    id: "4",
    code: "PACK001",
    designation: "Pack Solution Complète",
    description: "Pack complet incluant produits et services",
    prix_unitaire: 125.75,
    prix_achat: 85.0,
    stock_actuel: 25,
    stock_minimum: 5,
    stock_maximum: 100,
    unite: "pack",
    categorie: "Pack",
    tva: 20.0,
    statut: "ACTIF",
    fournisseur: "Assemblage Interne",
    date_creation: "2024-04-10",
  },
];

// Données de démonstration pour les commandes
const DEMO_ORDERS = [
  {
    id: "1",
    numero: "CMD-2024-001",
    client_id: "1",
    client_nom: "FOND FUTURISTE",
    date_commande: "2024-12-10",
    date_livraison_prevue: "2024-12-15",
    statut: "LIVREE",
    total_ht: 1250.75,
    total_tva: 250.15,
    total_ttc: 1500.9,
    lignes: [
      {
        id: "1",
        produit_id: "1",
        produit_nom: "PRODUIT DESIGN FUTURISTE",
        quantite: 10,
        prix_unitaire: 57.015,
        total: 570.15,
      },
      {
        id: "2",
        produit_id: "2",
        produit_nom: "ARTICLE PREMIER PLAN",
        quantite: 25,
        prix_unitaire: 25.0,
        total: 625.0,
      },
    ],
  },
  {
    id: "2",
    numero: "CMD-2024-002",
    client_id: "2",
    client_nom: "Innovation Tech",
    date_commande: "2024-12-08",
    date_livraison_prevue: "2024-12-12",
    statut: "EN_COURS",
    total_ht: 890.5,
    total_tva: 178.1,
    total_ttc: 1068.6,
    lignes: [
      {
        id: "3",
        produit_id: "3",
        produit_nom: "SERVICE FUTURISME ELEGANT",
        quantite: 15,
        prix_unitaire: 45.5,
        total: 682.5,
      },
    ],
  },
];

// Données de démonstration pour les factures
const DEMO_INVOICES = [
  {
    id: "1",
    numero: "FAC-2024-001",
    client_id: "1",
    client_nom: "FOND FUTURISTE",
    commande_id: "1",
    date_facture: "2024-12-10",
    date_echeance: "2024-12-25",
    statut: "PAYEE",
    total_ht: 1250.75,
    total_tva: 250.15,
    total_ttc: 1500.9,
    montant_paye: 1500.9,
    montant_restant: 0,
    lignes: [
      {
        id: "1",
        produit_id: "1",
        produit_nom: "PRODUIT DESIGN FUTURISTE",
        quantite: 10,
        prix_unitaire: 57.015,
        tva_taux: 20.0,
        total_ht: 570.15,
        total_tva: 114.03,
        total_ttc: 684.18,
      },
    ],
  },
  {
    id: "2",
    numero: "FAC-2024-002",
    client_id: "2",
    client_nom: "Innovation Tech",
    commande_id: "2",
    date_facture: "2024-12-08",
    date_echeance: "2024-12-23",
    statut: "EN_ATTENTE",
    total_ht: 890.5,
    total_tva: 178.1,
    total_ttc: 1068.6,
    montant_paye: 0,
    montant_restant: 1068.6,
    lignes: [
      {
        id: "2",
        produit_id: "3",
        produit_nom: "SERVICE FUTURISME ELEGANT",
        quantite: 15,
        prix_unitaire: 45.5,
        tva_taux: 20.0,
        total_ht: 682.5,
        total_tva: 136.5,
        total_ttc: 819.0,
      },
    ],
  },
];

// Données de démonstration pour les paiements
const DEMO_PAYMENTS = [
  {
    id: "1",
    facture_id: "1",
    facture_numero: "FAC-2024-001",
    client_id: "1",
    client_nom: "FOND FUTURISTE",
    montant: 1500.9,
    date_paiement: "2024-12-10",
    mode_paiement: "VIREMENT",
    statut: "VALIDE",
    reference: "VIR-2024-001",
    notes: "Paiement reçu par virement bancaire",
  },
  {
    id: "2",
    facture_id: "2",
    facture_numero: "FAC-2024-002",
    client_id: "2",
    client_nom: "Innovation Tech",
    montant: 500.0,
    date_paiement: "2024-12-09",
    mode_paiement: "CHEQUE",
    statut: "EN_ATTENTE",
    reference: "CHQ-2024-001",
    notes: "Acompte de 500€ reçu par chèque",
  },
];

// === CONFIGURATION DE BASE ===

const API_BASE_URL = API_CONFIG.BASE_URL;

/**
 * Classe de base pour tous les services API
 */
export class ApiService {
  private static baseURL = API_CONFIG.BASE_URL;

  static getHeaders(): Record<string, string> {
    return getDefaultHeaders();
  }

  private static async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    const defaultHeaders = getDefaultHeaders();

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      return { data };
    } catch (error: any) {
      console.error(`API Error [${endpoint}]:`, error);
      return { error: error.message };
    }
  }

  protected static async get<T = any>(
    endpoint: string,
    params?: any
  ): Promise<ApiResponse<T>> {
    const queryString = params
      ? "?" + new URLSearchParams(params).toString()
      : "";
    return this.request<T>(`${endpoint}${queryString}`);
  }

  protected static async post<T = any>(
    endpoint: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  protected static async put<T = any>(
    endpoint: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  protected static async patch<T = any>(
    endpoint: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  protected static async delete<T = any>(
    endpoint: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }
}

// === SERVICES SPÉCIALISÉS ===

/**
 * Service Authentification
 */
export class AuthService extends ApiService {
  static async login(username: string, password: string) {
    return this.post("/auth/token/", { username, password });
  }

  static async register(userData: any) {
    return this.post("/auth/register/", userData);
  }

  static async logout() {
    return this.post("/auth/logout/");
  }

  static async getProfile() {
    return this.get("/auth/profile/");
  }

  static async updateProfile(data: any) {
    return this.put("/auth/profile/", data);
  }

  static async changePassword(data: any) {
    return this.post("/auth/change-password/", data);
  }

  static async forgotPassword(email: string) {
    return this.post("/auth/forgot-password/", { email });
  }

  static async resetPassword(data: any) {
    return this.post("/auth/reset-password/", data);
  }

  static async verifyResetCode(email: string, code: string) {
    return this.post("/auth/verify-reset-code/", { email, code });
  }
}

/**
 * Service Clients - CRM avec données de démonstration
 */
export class ClientService extends ApiService {
  // Fonction utilitaire pour mapper les données du backend vers le frontend
  private static mapClientData(clientData: any): any {
    return {
      ...clientData,
      // Mapping des propriétés pour compatibilité
      date_creation: clientData.created_at,
      nombre_factures: clientData.nb_commandes || 0,
      chiffre_affaires_total:
        clientData.ca_annuel || parseFloat(clientData.total_achats || "0"),
    };
  }

  static async getClients(filters?: any): Promise<ApiResponse<any[]>> {
    try {
      const response = await this.get("/api/clients/", filters);
      const clients = (response.results || response.data || []).map(
        this.mapClientData
      );

      return {
        success: true,
        results: clients,
        count: response.count || 0,
        next: response.next,
        previous: response.previous,
      };
    } catch (error) {
      console.error("Erreur lors du chargement des clients:", error);
      return {
        success: false,
        results: [],
        count: 0,
        error: "Erreur lors du chargement des clients",
      };
    }
  }

  static async getClient(id: string): Promise<ApiResponse<any>> {
    try {
      const response = await this.get(`/api/clients/${id}/`);
      const clientData = response.data || response;

      return {
        success: true,
        data: this.mapClientData(clientData),
      };
    } catch (error) {
      return {
        success: false,
        error: "Client non trouvé",
      };
    }
  }

  static async createClient(data: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.post("/api/clients/", data);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      return {
        success: false,
        error: "Erreur lors de la création du client",
      };
    }
  }

  static async updateClient(id: string, data: any): Promise<ApiResponse<any>> {
    try {
      const response = await this.put(`/api/clients/${id}/`, data);
      return {
        success: true,
        data: response.data || response,
        message: "Client mis à jour avec succès",
      };
    } catch (error) {
      return {
        success: false,
        error: "Erreur lors de la mise à jour du client",
      };
    }
  }

  static async deleteClient(id: string): Promise<ApiResponse<any>> {
    try {
      await this.delete(`/api/clients/${id}/`);
      return {
        success: true,
        message: "Client supprimé avec succès",
      };
    } catch (error) {
      return {
        success: false,
        error: "Erreur lors de la suppression du client",
      };
    }
  }

  static async getClientStats(): Promise<ApiResponse<any>> {
    try {
      const response = await this.get("/api/clients/stats/");
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      return {
        success: false,
        error: "Erreur lors du chargement des statistiques clients",
      };
    }
  }

  static async getClientHistorique(id: string): Promise<ApiResponse<any>> {
    try {
      const response = await this.get(`/api/clients/${id}/historique/`);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      return {
        success: false,
        error: "Erreur lors du chargement de l'historique client",
      };
    }
  }
}

/**
 * Service Produits - Catalogue
 */
export class ProductService extends ApiService {
  static async getProducts(filters?: any): Promise<ApiResponse<any[]>> {
    try {
      const response = await this.get("/api/produits/", filters);
      return {
        success: true,
        data: response.results || response.data || response,
        count:
          response.count || (response.results ? response.results.length : 0),
        next: response.next || null,
        previous: response.previous || null,
      };
    } catch (error) {
      console.error("Erreur lors du chargement des produits:", error);
      return {
        success: false,
        data: [],
        count: 0,
        error: "Erreur lors du chargement des produits",
      };
    }
  }

  // Version de démonstration (sauvegardée)
  static async getProductsDemo(filters?: any): Promise<ApiResponse<any[]>> {
    await new Promise((resolve) => setTimeout(resolve, 500));

    let products = [...DEMO_PRODUCTS];

    // Appliquer les filtres
    if (filters) {
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        products = products.filter(
          (product) =>
            product.designation.toLowerCase().includes(searchTerm) ||
            product.code.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.categorie.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.categorie) {
        products = products.filter(
          (product) => product.categorie === filters.categorie
        );
      }

      if (filters.statut) {
        products = products.filter(
          (product) => product.statut === filters.statut
        );
      }

      if (filters.stock_faible) {
        products = products.filter(
          (product) => product.stock_actuel <= product.stock_minimum
        );
      }
    }

    return { data: products };
  }

  static async getProduct(id: string): Promise<ApiResponse<any>> {
    await new Promise((resolve) => setTimeout(resolve, 300));

    const product = DEMO_PRODUCTS.find((p) => p.id === id);
    if (!product) {
      return { error: "Produit non trouvé" };
    }

    return { data: product };
  }

  static async createProduct(data: any): Promise<ApiResponse<any>> {
    await new Promise((resolve) => setTimeout(resolve, 800));

    const newProduct = {
      id: String(Date.now()),
      code: data.code || `PRD${Date.now()}`,
      ...data,
      date_creation: new Date().toISOString().split("T")[0],
      statut: data.statut || "ACTIF",
    };

    DEMO_PRODUCTS.push(newProduct);
    return { data: newProduct, message: "Produit créé avec succès" };
  }

  static async updateProduct(id: string, data: any): Promise<ApiResponse<any>> {
    await new Promise((resolve) => setTimeout(resolve, 600));

    const index = DEMO_PRODUCTS.findIndex((p) => p.id === id);
    if (index === -1) {
      return { error: "Produit non trouvé" };
    }

    DEMO_PRODUCTS[index] = { ...DEMO_PRODUCTS[index], ...data };
    return {
      data: DEMO_PRODUCTS[index],
      message: "Produit mis à jour avec succès",
    };
  }

  static async deleteProduct(id: string): Promise<ApiResponse<any>> {
    await new Promise((resolve) => setTimeout(resolve, 400));

    const index = DEMO_PRODUCTS.findIndex((p) => p.id === id);
    if (index === -1) {
      return { error: "Produit non trouvé" };
    }

    DEMO_PRODUCTS.splice(index, 1);
    return { message: "Produit supprimé avec succès" };
  }

  static async getProductStats(): Promise<ApiResponse<any>> {
    await new Promise((resolve) => setTimeout(resolve, 400));

    const stats = {
      total_produits: DEMO_PRODUCTS.length,
      produits_actifs: DEMO_PRODUCTS.filter((p) => p.statut === "ACTIF").length,
      produits_inactifs: DEMO_PRODUCTS.filter((p) => p.statut === "INACTIF")
        .length,
      stock_total: DEMO_PRODUCTS.reduce((sum, p) => sum + p.stock_actuel, 0),
      valeur_stock: DEMO_PRODUCTS.reduce(
        (sum, p) => sum + p.stock_actuel * p.prix_achat,
        0
      ),
      produits_stock_faible: DEMO_PRODUCTS.filter(
        (p) => p.stock_actuel <= p.stock_minimum
      ).length,
      categories: Array.from(new Set(DEMO_PRODUCTS.map((p) => p.categorie))),
      prix_moyen:
        DEMO_PRODUCTS.length > 0
          ? DEMO_PRODUCTS.reduce((sum, p) => sum + p.prix_unitaire, 0) /
            DEMO_PRODUCTS.length
          : 0,
      marge_moyenne:
        DEMO_PRODUCTS.length > 0
          ? DEMO_PRODUCTS.reduce(
              (sum, p) =>
                sum +
                ((p.prix_unitaire - p.prix_achat) / p.prix_unitaire) * 100,
              0
            ) / DEMO_PRODUCTS.length
          : 0,
    };

    return { data: stats };
  }

  static async getStockAlerts(): Promise<ApiResponse<any[]>> {
    await new Promise((resolve) => setTimeout(resolve, 300));

    const alertes = DEMO_PRODUCTS.filter(
      (p) => p.stock_actuel <= p.stock_minimum
    ).map((p) => ({
      id: p.id,
      code: p.code,
      designation: p.designation,
      stock_actuel: p.stock_actuel,
      stock_minimum: p.stock_minimum,
      niveau_alerte: p.stock_actuel === 0 ? "RUPTURE" : "FAIBLE",
      date_alerte: new Date().toISOString(),
    }));

    return { data: alertes };
  }

  static async getCategories(): Promise<ApiResponse<string[]>> {
    await new Promise((resolve) => setTimeout(resolve, 200));

    const categories = Array.from(
      new Set(DEMO_PRODUCTS.map((p) => p.categorie))
    );
    return { data: categories };
  }

  static async updateStock(
    id: string,
    quantity: number,
    type: string
  ): Promise<ApiResponse<any>> {
    await new Promise((resolve) => setTimeout(resolve, 500));

    const index = DEMO_PRODUCTS.findIndex((p) => p.id === id);
    if (index === -1) {
      return { error: "Produit non trouvé" };
    }

    const product = DEMO_PRODUCTS[index];
    const stockAvant = product.stock_actuel;

    if (type === "ENTREE") {
      product.stock_actuel += quantity;
    } else if (type === "SORTIE") {
      if (product.stock_actuel < quantity) {
        return { error: "Stock insuffisant" };
      }
      product.stock_actuel -= quantity;
    } else if (type === "AJUSTEMENT") {
      product.stock_actuel = quantity;
    }

    const mouvement = {
      id: String(Date.now()),
      produit_id: id,
      type,
      quantite: quantity,
      stock_avant: stockAvant,
      stock_apres: product.stock_actuel,
      date: new Date().toISOString(),
      motif: `Mouvement ${type.toLowerCase()}`,
    };

    return {
      data: { product, mouvement },
      message: `Stock mis à jour: ${type.toLowerCase()} de ${quantity} unités`,
    };
  }
}

/**
 * Service Commandes - Ventes
 */
export class OrderService extends ApiService {
  static async getOrders(filters?: any) {
    return this.get("/api/orders/", filters);
  }

  static async getOrder(id: string) {
    return this.get(`/api/orders/${id}/`);
  }

  static async createOrder(data: any) {
    return this.post("/api/orders/", data);
  }

  static async updateOrder(id: string, data: any) {
    return this.put(`/api/orders/${id}/`, data);
  }

  static async deleteOrder(id: string) {
    return this.delete(`/api/orders/${id}/`);
  }

  static async getOrderStats() {
    return this.get("/api/orders/stats/");
  }

  static async changerStatut(id: string, statut: string, commentaire?: string) {
    return this.post(`/api/orders/${id}/changer-statut/`, {
      statut,
      commentaire,
    });
  }
}

/**
 * Service Facturation Professionnel - API Moderne
 */
export class FacturationService extends ApiService {
  // Configuration
  static async getConfiguration() {
    try {
      const response = await this.get("/api/configuration/active/");
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors du chargement de la configuration:", error);
      return {
        success: false,
        error: "Erreur lors du chargement de la configuration",
      };
    }
  }

  static async updateConfiguration(data: any) {
    try {
      const response = await this.put("/api/configuration/1/", data);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour de la configuration:",
        error
      );
      return {
        success: false,
        error: "Erreur lors de la mise à jour de la configuration",
      };
    }
  }

  // Factures
  static async getFactures(filters?: any) {
    try {
      const response = await this.get("/api/factures/", filters);
      return {
        success: true,
        data: response.results || response.data || response,
        count: response.count || 0,
        next: response.next,
        previous: response.previous,
      };
    } catch (error) {
      console.error("Erreur lors du chargement des factures:", error);
      return {
        success: false,
        data: [],
        count: 0,
        error: "Erreur lors du chargement des factures",
      };
    }
  }

  static async getFacture(id: string) {
    try {
      const response = await this.get(`/api/factures/${id}/`);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors du chargement de la facture:", error);
      return {
        success: false,
        error: "Facture non trouvée",
      };
    }
  }

  static async createFacture(data: any) {
    try {
      const response = await this.post("/api/factures/", data);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors de la création de la facture:", error);
      return {
        success: false,
        error: "Erreur lors de la création de la facture",
      };
    }
  }

  static async updateFacture(id: string, data: any) {
    try {
      const response = await this.put(`/api/factures/${id}/`, data);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la facture:", error);
      return {
        success: false,
        error: "Erreur lors de la mise à jour de la facture",
      };
    }
  }

  static async deleteFacture(id: string) {
    try {
      await this.delete(`/api/factures/${id}/`);
      return {
        success: true,
        message: "Facture supprimée avec succès",
      };
    } catch (error) {
      console.error("Erreur lors de la suppression de la facture:", error);
      return {
        success: false,
        error: "Erreur lors de la suppression de la facture",
      };
    }
  }

  static async dupliquerFacture(id: string) {
    try {
      const response = await this.post(`/api/factures/${id}/dupliquer/`);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors de la duplication de la facture:", error);
      return {
        success: false,
        error: "Erreur lors de la duplication de la facture",
      };
    }
  }

  static async marquerPayee(id: string, paiementData?: any) {
    try {
      const response = await this.post(
        `/api/factures/${id}/marquer-payee/`,
        paiementData
      );
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors du marquage comme payée:", error);
      return {
        success: false,
        error: "Erreur lors du marquage comme payée",
      };
    }
  }

  // Statistiques
  static async getStatistiques() {
    try {
      const response = await this.get("/api/factures/statistiques/");
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);
      return {
        success: false,
        error: "Erreur lors du chargement des statistiques",
      };
    }
  }

  // PDF
  static async generatePDF(id: string) {
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/invoices/api/factures/${id}/pdf/`,
        {
          method: "GET",
          headers: ApiService.getHeaders(),
        }
      );

      if (response.ok) {
        return {
          success: true,
          blob: await response.blob(),
        };
      } else {
        return {
          success: false,
          error: "Erreur lors de la génération du PDF",
        };
      }
    } catch (error) {
      console.error("Erreur lors de la génération du PDF:", error);
      return {
        success: false,
        error: "Erreur lors de la génération du PDF",
      };
    }
  }

  // Paiements
  static async getPaiements(factureId?: string) {
    try {
      const filters = factureId ? { facture: factureId } : {};
      const response = await this.get("/api/invoices/api/paiements/", filters);
      return {
        success: true,
        data: response.results || response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors du chargement des paiements:", error);
      return {
        success: false,
        data: [],
        error: "Erreur lors du chargement des paiements",
      };
    }
  }

  static async createPaiement(data: any) {
    try {
      const response = await this.post("/api/invoices/api/paiements/", data);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors de la création du paiement:", error);
      return {
        success: false,
        error: "Erreur lors de la création du paiement",
      };
    }
  }

  // Lignes de facture
  static async getLignes(factureId: string) {
    try {
      const response = await this.get("/api/invoices/api/lignes/", {
        facture: factureId,
      });
      return {
        success: true,
        data: response.results || response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors du chargement des lignes:", error);
      return {
        success: false,
        data: [],
        error: "Erreur lors du chargement des lignes",
      };
    }
  }

  static async createLigne(data: any) {
    try {
      const response = await this.post("/api/invoices/api/lignes/", data);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors de la création de la ligne:", error);
      return {
        success: false,
        error: "Erreur lors de la création de la ligne",
      };
    }
  }

  static async updateLigne(id: string, data: any) {
    try {
      const response = await this.put(`/api/invoices/api/lignes/${id}/`, data);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la ligne:", error);
      return {
        success: false,
        error: "Erreur lors de la mise à jour de la ligne",
      };
    }
  }

  static async deleteLigne(id: string) {
    try {
      await this.delete(`/api/invoices/api/lignes/${id}/`);
      return {
        success: true,
        message: "Ligne supprimée avec succès",
      };
    } catch (error) {
      console.error("Erreur lors de la suppression de la ligne:", error);
      return {
        success: false,
        error: "Erreur lors de la suppression de la ligne",
      };
    }
  }
}

// Alias pour compatibilité avec méthodes supplémentaires
export const InvoiceService = {
  ...FacturationService,

  // Méthodes pour compatibilité avec l'ancien code
  getInvoices: FacturationService.getFactures,
  getInvoiceStats: FacturationService.getStatistiques,
  getInvoice: FacturationService.getFacture,
  createInvoice: FacturationService.createFacture,
  updateInvoice: FacturationService.updateFacture,
  deleteInvoice: FacturationService.deleteFacture,
};

/**
 * Service Stock - Gestion des stocks
 */
export class StockService extends ApiService {
  static async getMouvements(filters?: any) {
    return this.get("/api/stock/mouvements/", filters);
  }

  static async createMouvement(data: any) {
    return this.post("/api/stock/mouvements/", data);
  }

  static async getInventaires(filters?: any) {
    return this.get("/api/stock/inventaires/", filters);
  }

  static async createInventaire(data: any) {
    return this.post("/api/stock/inventaires/", data);
  }

  static async getStockStats() {
    return this.get("/api/stock/stats/");
  }

  // Méthodes pour les inventaires
  static async getInventories(filters?: any) {
    try {
      const response = await this.get("/api/stock/inventaires/", filters);
      return {
        success: true,
        data: response.results || response.data || response,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error: "Erreur lors du chargement des inventaires",
      };
    }
  }

  static async validateInventoryItem(itemId: string) {
    try {
      const response = await this.post(
        `/api/stock/inventaires/${itemId}/validate/`
      );
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      return {
        success: false,
        error: "Erreur lors de la validation",
      };
    }
  }

  // Méthodes pour les statistiques
  static async getStatistics(filters?: any) {
    try {
      const response = await this.get("/api/stock/stats/", filters);
      return {
        success: true,
        data: response.data || response,
      };
    } catch (error) {
      return {
        success: false,
        error: "Erreur lors du chargement des statistiques",
      };
    }
  }

  // Méthodes pour les alertes
  static async getAlerts() {
    try {
      const response = await this.get("/api/stock/alerts/");
      return {
        success: true,
        data: response.results || response.data || response,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error: "Erreur lors du chargement des alertes",
      };
    }
  }
}

/**
 * Service Paiements - Trésorerie
 */
export class PaymentService extends ApiService {
  static async getPayments(filters?: any) {
    return this.get("/api/payments/", filters);
  }

  static async getPayment(id: string) {
    return this.get(`/api/payments/${id}/`);
  }

  static async createPayment(data: any) {
    return this.post("/api/payments/", data);
  }

  static async updatePayment(id: string, data: any) {
    return this.put(`/api/payments/${id}/`, data);
  }

  static async deletePayment(id: string) {
    return this.delete(`/api/payments/${id}/`);
  }

  static async getPaymentStats() {
    return this.get("/api/payments/stats/");
  }

  static async rapprochementBancaire(paiementIds: string[]) {
    return this.post("/api/payments/rapprochement/", {
      paiement_ids: paiementIds,
    });
  }
}

/**
 * Service Fournisseurs - Achats
 */
export class FournisseurService extends ApiService {
  static async getFournisseurs(filters?: any) {
    return this.get("/api/suppliers/", filters);
  }

  static async getFournisseur(id: string) {
    return this.get(`/api/suppliers/${id}/`);
  }

  static async createFournisseur(data: any) {
    return this.post("/api/suppliers/", data);
  }

  static async updateFournisseur(id: string, data: any) {
    return this.put(`/api/suppliers/${id}/`, data);
  }

  static async getFournisseurStats() {
    return this.get("/api/suppliers/stats/");
  }
}

/**
 * Service Comptabilité - Journaux et déclarations
 */
export class ComptabiliteService extends ApiService {
  static async getStats() {
    return this.get("/comptabilite/stats/");
  }

  static async getJournalVentes(filters?: any) {
    return this.get("/comptabilite/journal-ventes/", filters);
  }

  static async getJournalAchats(filters?: any) {
    return this.get("/comptabilite/journal-achats/", filters);
  }

  static async getTVADeclaration(periode: string) {
    return this.get(`/comptabilite/tva/${periode}/`);
  }
}

/**
 * Service Dashboard - Données du tableau de bord
 */
export class DashboardService extends ApiService {
  static async getStats() {
    try {
      const response = await this.get("/core/dashboard/stats/");
      return response.data;
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);
      // Retourner des données par défaut en cas d'erreur
      return {
        chiffre_affaires_mensuel: 0,
        nombre_factures_mois: 0,
        nombre_clients_actifs: 0,
        commandes_en_attente: 0,
        produits_stock_faible: 0,
        ventes_aujourd_hui: 0,
        objectif_mensuel: 150000,
        taux_conversion: 0,
      };
    }
  }

  static async getRecentData() {
    try {
      const response = await this.get("/core/dashboard/recent/");
      return response.data;
    } catch (error) {
      console.error("Erreur lors du chargement des données récentes:", error);
      return {
        recent_invoices: [],
        recent_clients: [],
        top_products: [],
        recent_orders: [],
        stock_alerts: [],
        recent_activity: [],
      };
    }
  }

  static async getRecentInvoices(limit = 5) {
    return this.get("/api/invoices/", { limit, ordering: "-date_creation" });
  }

  static async getRecentClients(limit = 5) {
    return this.get("/api/clients/", { limit, ordering: "-date_creation" });
  }

  static async getRecentOrders(limit = 5) {
    return this.get("/api/orders/", { limit, ordering: "-date_creation" });
  }

  static async getNotifications() {
    return this.get("/core/notifications/");
  }

  static async getInsights() {
    return this.get("/core/insights/");
  }

  static async getFuturisticDashboard() {
    return this.get("/core/futuristic-dashboard/");
  }

  static async getKPIs() {
    return this.get("/core/kpis/");
  }
}

/**
 * Service E-commerce - Boutique en ligne
 */
export class EcommerceService extends ApiService {
  static async getCatalog(filters?: any) {
    return this.get("/ecommerce/catalog/", filters);
  }

  static async getCart(sessionId?: string) {
    return this.get(
      "/ecommerce/cart/",
      sessionId ? { session_id: sessionId } : {}
    );
  }

  static async addToCart(
    productId: string,
    quantity: number,
    sessionId?: string
  ) {
    return this.post("/ecommerce/cart/", {
      produit_id: productId,
      quantite: quantity,
      session_id: sessionId,
    });
  }

  static async updateCartItem(itemId: string, quantity: number) {
    return this.put(`/ecommerce/cart/${itemId}/`, { quantite: quantity });
  }

  static async removeFromCart(itemId: string) {
    return this.delete(`/ecommerce/cart/${itemId}/`);
  }

  static async getEcommerceStats() {
    return this.get("/ecommerce/stats/");
  }

  static async getCategories() {
    return this.get("/ecommerce/categories/");
  }
}

/**
 * Service Rapports - Business Intelligence
 */
export class RapportService extends ApiService {
  static async getRapportVentes(filters?: any) {
    return this.get("/api/reports/ventes/", filters);
  }

  static async getRapportClients(filters?: any) {
    return this.get("/api/reports/api/clients/", filters);
  }

  static async getRapportProduits(filters?: any) {
    return this.get("/api/reports/produits/", filters);
  }

  static async getRapportFinancier(filters?: any) {
    return this.get("/api/reports/financier/", filters);
  }
}

// === EXPORT UNIFIÉ ===

export default {
  AuthService,
  ClientService,
  ProductService,
  OrderService,
  FacturationService,
  InvoiceService, // Alias pour compatibilité
  StockService,
  PaymentService,
  FournisseurService,
  ComptabiliteService,
  DashboardService,
  EcommerceService,
  RapportService,
  ApiService,
};
