import React from 'react';
import './LoadingSpinner.css';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  text?: string;
  overlay?: boolean;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color = '#667eea',
  text,
  overlay = false,
  className = '',
}) => {
  const sizeClasses = {
    small: 'spinner-small',
    medium: 'spinner-medium',
    large: 'spinner-large',
  };

  const spinnerContent = (
    <div className={`loading-spinner-container ${className}`}>
      <div 
        className={`loading-spinner ${sizeClasses[size]}`}
        style={{ borderTopColor: color }}
      />
      {text && <div className="loading-text">{text}</div>}
    </div>
  );

  if (overlay) {
    return (
      <div className="loading-overlay">
        {spinnerContent}
      </div>
    );
  }

  return spinnerContent;
};

// Composant de chargement pour les pages entières
export const PageLoader: React.FC<{ text?: string }> = ({ text = 'Chargement...' }) => (
  <div className="page-loader">
    <div className="page-loader-content">
      <div className="page-loader-spinner">
        <div className="spinner-ring"></div>
        <div className="spinner-ring"></div>
        <div className="spinner-ring"></div>
        <div className="spinner-ring"></div>
      </div>
      <div className="page-loader-text">{text}</div>
      <div className="page-loader-subtitle">Veuillez patienter</div>
    </div>
  </div>
);

// Composant de chargement pour les boutons
interface ButtonLoaderProps {
  loading: boolean;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export const ButtonLoader: React.FC<ButtonLoaderProps> = ({
  loading,
  children,
  className = '',
  disabled = false,
  onClick,
  type = 'button',
}) => (
  <button
    type={type}
    className={`btn-with-loader ${className} ${loading ? 'loading' : ''}`}
    disabled={disabled || loading}
    onClick={onClick}
  >
    {loading && (
      <div className="btn-spinner">
        <div className="btn-spinner-ring"></div>
      </div>
    )}
    <span className={loading ? 'btn-text-hidden' : 'btn-text-visible'}>
      {children}
    </span>
  </button>
);

// Composant de skeleton pour le chargement de contenu
interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: string | number;
  className?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '20px',
  borderRadius = '4px',
  className = '',
}) => (
  <div
    className={`skeleton ${className}`}
    style={{
      width,
      height,
      borderRadius,
    }}
  />
);

// Composant de skeleton pour les cartes
export const CardSkeleton: React.FC = () => (
  <div className="card-skeleton">
    <div className="card-skeleton-header">
      <Skeleton width="60%" height="24px" />
      <Skeleton width="80px" height="20px" />
    </div>
    <div className="card-skeleton-content">
      <Skeleton width="100%" height="16px" />
      <Skeleton width="80%" height="16px" />
      <Skeleton width="90%" height="16px" />
    </div>
    <div className="card-skeleton-footer">
      <Skeleton width="100px" height="32px" borderRadius="6px" />
      <Skeleton width="80px" height="32px" borderRadius="6px" />
    </div>
  </div>
);

// Composant de skeleton pour les listes
export const ListSkeleton: React.FC<{ items?: number }> = ({ items = 5 }) => (
  <div className="list-skeleton">
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="list-skeleton-item">
        <Skeleton width="40px" height="40px" borderRadius="50%" />
        <div className="list-skeleton-content">
          <Skeleton width="70%" height="18px" />
          <Skeleton width="50%" height="14px" />
        </div>
        <Skeleton width="80px" height="32px" borderRadius="6px" />
      </div>
    ))}
  </div>
);

// Hook pour gérer les états de chargement
export const useLoading = (initialState = false) => {
  const [loading, setLoading] = React.useState(initialState);

  const startLoading = React.useCallback(() => setLoading(true), []);
  const stopLoading = React.useCallback(() => setLoading(false), []);
  const toggleLoading = React.useCallback(() => setLoading(prev => !prev), []);

  const withLoading = React.useCallback(async (asyncFn: () => Promise<any>) => {
    startLoading();
    try {
      const result = await asyncFn();
      return result;
    } finally {
      stopLoading();
    }
  }, [startLoading, stopLoading]);

  return {
    loading,
    startLoading,
    stopLoading,
    toggleLoading,
    withLoading,
  };
};

export default LoadingSpinner;
