"""
URLs pour l'API E-commerce
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CategorieEcommerceViewSet, ProduitEcommerceViewSet,
    PanierViewSet, CommandeEcommerceViewSet
)

# Router pour l'API REST
router = DefaultRouter()
router.register(r'categories', CategorieEcommerceViewSet)
router.register(r'produits', ProduitEcommerceViewSet)
router.register(r'panier', PanierViewSet, basename='panier')
router.register(r'commandes', CommandeEcommerceViewSet)

urlpatterns = [
    # Expose directement sous /api/ecommerce/ depuis abm/urls.py
    path('', include(router.urls)),
]
