/**
 * Journal des achats - Suivi des dépenses
 */

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useNotify } from "../Common/NotificationSystem";

interface AchatEntry {
  id: string;
  date: string;
  numero_facture: string;
  fournisseur_nom: string;
  fournisseur_mf: string;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
  taux_tva: number;
  statut: string;
  mode_paiement?: string;
  date_echeance?: string;
  reference_interne?: string;
}

const JournalAchats: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();

  const [achats, setAchats] = useState<AchatEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    date_debut: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      .toISOString()
      .split("T")[0],
    date_fin: new Date().toISOString().split("T")[0],
    statut: "",
    fournisseur: "",
  });

  const [totaux, setTotaux] = useState({
    montant_ht: 0,
    montant_tva: 0,
    montant_ttc: 0,
    nombre_factures: 0,
  });

  useEffect(() => {
    loadAchats();
  }, [filters]);

  const loadAchats = async () => {
    try {
      setLoading(true);

      // Simulation de données d'achats
      const mockAchats: AchatEntry[] = [
        {
          id: "1",
          date: "2025-08-10",
          numero_facture: "FACH-2025-001",
          fournisseur_nom: "FOURNISSEUR DESIGN MODERNE",
          fournisseur_mf: "MF987654321",
          montant_ht: 15000,
          montant_tva: 2850,
          montant_ttc: 17850,
          taux_tva: 19,
          statut: "PAYEE",
          mode_paiement: "VIREMENT",
          date_echeance: "2025-09-10",
          reference_interne: "ACH-001",
        },
        {
          id: "2",
          date: "2025-08-08",
          numero_facture: "FACH-2025-002",
          fournisseur_nom: "SOCIÉTÉ TECH AVANCÉE",
          fournisseur_mf: "MF123789456",
          montant_ht: 8500,
          montant_tva: 1615,
          montant_ttc: 10115,
          taux_tva: 19,
          statut: "EN_ATTENTE",
          mode_paiement: "CHEQUE",
          date_echeance: "2025-09-08",
          reference_interne: "ACH-002",
        },
        {
          id: "3",
          date: "2025-08-05",
          numero_facture: "FACH-2025-003",
          fournisseur_nom: "COMMERCE FUTURISTE SARL",
          fournisseur_mf: "MF456123789",
          montant_ht: 12000,
          montant_tva: 2280,
          montant_ttc: 14280,
          taux_tva: 19,
          statut: "PAYEE",
          mode_paiement: "VIREMENT",
          date_echeance: "2025-09-05",
          reference_interne: "ACH-003",
        },
      ];

      setAchats(mockAchats);

      // Calculer les totaux
      const totauxCalcules = mockAchats.reduce(
        (acc, achat) => ({
          montant_ht: acc.montant_ht + achat.montant_ht,
          montant_tva: acc.montant_tva + achat.montant_tva,
          montant_ttc: acc.montant_ttc + achat.montant_ttc,
          nombre_factures: acc.nombre_factures + 1,
        }),
        { montant_ht: 0, montant_tva: 0, montant_ttc: 0, nombre_factures: 0 }
      );

      setTotaux(totauxCalcules);
    } catch (error: any) {
      notify.error("Erreur lors du chargement du journal des achats");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR");
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case "PAYEE":
        return "success";
      case "EN_ATTENTE":
        return "warning";
      case "RETARD":
        return "danger";
      case "ANNULEE":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const getStatusLabel = (statut: string) => {
    switch (statut) {
      case "PAYEE":
        return "✅ Payée";
      case "EN_ATTENTE":
        return "⏳ En attente";
      case "RETARD":
        return "🔴 En retard";
      case "ANNULEE":
        return "❌ Annulée";
      default:
        return statut;
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const exportToExcel = () => {
    notify.info("Export Excel en cours...");
    // Ici, implémenter l'export Excel
  };

  const exportToPDF = () => {
    notify.info("Export PDF en cours...");
    // Ici, implémenter l'export PDF
  };

  if (loading) {
    return (
      <div className="journal-loading">
        <div className="loading-spinner"></div>
        <p>Chargement du journal des achats...</p>
      </div>
    );
  }

  return (
    <div className="journal-achats">
      <div className="journal-header">
        <div className="header-content">
          <div className="company-info">
            <img
              src="/logo_ben_chaabene_moderne.png"
              alt="Logo"
              className="company-logo"
            />
            <div>
              <h1>🛒 Journal des Achats</h1>
              <h2>Société Ben Chaabene de Commerce</h2>
              <p>Suivi des factures fournisseurs et TVA déductible</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filtres */}
      <div className="journal-filters">
        <div className="filters-grid">
          <div className="filter-group">
            <label>Date début:</label>
            <input
              type="date"
              value={filters.date_debut}
              onChange={(e) => handleFilterChange("date_debut", e.target.value)}
            />
          </div>
          <div className="filter-group">
            <label>Date fin:</label>
            <input
              type="date"
              value={filters.date_fin}
              onChange={(e) => handleFilterChange("date_fin", e.target.value)}
            />
          </div>
          <div className="filter-group">
            <label>Statut:</label>
            <select
              value={filters.statut}
              onChange={(e) => handleFilterChange("statut", e.target.value)}>
              <option value="">Tous les statuts</option>
              <option value="PAYEE">Payées</option>
              <option value="EN_ATTENTE">En attente</option>
              <option value="RETARD">En retard</option>
              <option value="ANNULEE">Annulées</option>
            </select>
          </div>
          <div className="filter-group">
            <label>Fournisseur:</label>
            <input
              type="text"
              placeholder="Nom du fournisseur"
              value={filters.fournisseur}
              onChange={(e) =>
                handleFilterChange("fournisseur", e.target.value)
              }
            />
          </div>
        </div>
      </div>

      {/* Résumé des totaux */}
      <div className="journal-summary">
        <div className="summary-card">
          <div className="summary-icon">📊</div>
          <div className="summary-content">
            <div className="summary-value">{totaux.nombre_factures}</div>
            <div className="summary-label">Factures</div>
          </div>
        </div>
        <div className="summary-card">
          <div className="summary-icon">💰</div>
          <div className="summary-content">
            <div className="summary-value">
              {formatCurrency(totaux.montant_ht)}
            </div>
            <div className="summary-label">Total HT</div>
          </div>
        </div>
        <div className="summary-card">
          <div className="summary-icon">🧾</div>
          <div className="summary-content">
            <div className="summary-value">
              {formatCurrency(totaux.montant_tva)}
            </div>
            <div className="summary-label">TVA Déductible</div>
          </div>
        </div>
        <div className="summary-card">
          <div className="summary-icon">💳</div>
          <div className="summary-content">
            <div className="summary-value">
              {formatCurrency(totaux.montant_ttc)}
            </div>
            <div className="summary-label">Total TTC</div>
          </div>
        </div>
      </div>

      {/* Tableau des achats */}
      <div className="journal-table-container">
        <table className="journal-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>N° Facture</th>
              <th>Fournisseur</th>
              <th>M.F Fournisseur</th>
              <th>Montant HT</th>
              <th>TVA</th>
              <th>Montant TTC</th>
              <th>Statut</th>
              <th>Échéance</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {achats.map((achat) => (
              <tr key={achat.id}>
                <td>{formatDate(achat.date)}</td>
                <td className="numero-facture">{achat.numero_facture}</td>
                <td className="fournisseur-nom">{achat.fournisseur_nom}</td>
                <td className="matricule-fiscal">{achat.fournisseur_mf}</td>
                <td className="montant">{formatCurrency(achat.montant_ht)}</td>
                <td className="montant tva">
                  {formatCurrency(achat.montant_tva)}
                </td>
                <td className="montant total">
                  {formatCurrency(achat.montant_ttc)}
                </td>
                <td>
                  <span
                    className={`status-badge ${getStatusColor(achat.statut)}`}>
                    {getStatusLabel(achat.statut)}
                  </span>
                </td>
                <td>
                  {achat.date_echeance ? formatDate(achat.date_echeance) : "-"}
                </td>
                <td className="actions">
                  <button
                    className="btn-sm btn-primary"
                    onClick={() => navigate(`/achats/${achat.id}`)}
                    title="Voir détails">
                    👁️
                  </button>
                  <button
                    className="btn-sm btn-secondary"
                    onClick={() => navigate(`/achats/${achat.id}/edit`)}
                    title="Modifier">
                    ✏️
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="totals-row">
              <td colSpan={4}>
                <strong>TOTAUX</strong>
              </td>
              <td className="montant total">
                <strong>{formatCurrency(totaux.montant_ht)}</strong>
              </td>
              <td className="montant total">
                <strong>{formatCurrency(totaux.montant_tva)}</strong>
              </td>
              <td className="montant total">
                <strong>{formatCurrency(totaux.montant_ttc)}</strong>
              </td>
              <td colSpan={3}></td>
            </tr>
          </tfoot>
        </table>
      </div>

      {/* Actions */}
      <div className="journal-actions">
        <button
          className="btn-secondary"
          onClick={() => window.print()}>
          🖨️ Imprimer
        </button>
        <button
          className="btn-primary"
          onClick={exportToExcel}>
          📤 Exporter Excel
        </button>
        <button
          className="btn-success"
          onClick={exportToPDF}>
          📄 Exporter PDF
        </button>
        <button
          className="btn-info"
          onClick={() => navigate("/achats/new")}>
          ➕ Nouvelle facture achat
        </button>
      </div>
    </div>
  );
};

export default JournalAchats;
