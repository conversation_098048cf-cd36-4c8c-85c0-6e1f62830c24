from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count, Q, Avg
from django.utils import timezone
from datetime import datetime, timedelta

from .models import Client, ContactClient
from .serializers import ClientSerializer, ContactClientSerializer, ClientStatsSerializer
from Authentication.permissions import HasModulePermission

@method_decorator(csrf_exempt, name='dispatch')
class ClientViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des clients"""

    queryset = Client.objects.all()
    serializer_class = ClientSerializer
    permission_classes = [AllowAny]  # Temporaire pour les tests
    required_permission = 'clients'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'segment', 'type_client', 'pays']
    search_fields = ['nom', 'prenom', 'email', 'telephone', 'ville']
    ordering_fields = ['nom', 'created_at', 'total_achats', 'derniere_commande']
    ordering = ['-created_at']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Statistiques globales des clients"""

        # Statistiques de base
        total_clients = Client.objects.count()
        clients_actifs = Client.objects.filter(statut='ACTIF').count()
        nouveaux_clients = Client.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count()

        # Répartition par segment
        segments = Client.objects.values('segment').annotate(
            count=Count('id'),
            ca_total=Sum('total_achats')
        ).order_by('-ca_total')

        # Répartition par type
        types = Client.objects.values('type_client').annotate(
            count=Count('id')
        ).order_by('-count')

        # Top clients par CA
        top_clients = Client.objects.filter(
            total_achats__gt=0
        ).order_by('-total_achats')[:10]

        # Évolution mensuelle
        evolution = []
        for i in range(12):
            date_debut = timezone.now().replace(day=1) - timedelta(days=30*i)
            date_fin = date_debut.replace(day=28) + timedelta(days=4)

            nb_clients = Client.objects.filter(
                created_at__gte=date_debut,
                created_at__lt=date_fin
            ).count()

            evolution.append({
                'mois': date_debut.strftime('%Y-%m'),
                'nouveaux_clients': nb_clients
            })

        data = {
            'total_clients': total_clients,
            'clients_actifs': clients_actifs,
            'nouveaux_clients': nouveaux_clients,
            'taux_croissance': (nouveaux_clients / total_clients * 100) if total_clients > 0 else 0,
            'segments': list(segments),
            'types': list(types),
            'top_clients': ClientSerializer(top_clients, many=True).data,
            'evolution_mensuelle': list(reversed(evolution))
        }

        return Response(data)

    @action(detail=True, methods=['get'])
    def historique(self, request, pk=None):
        """Historique des commandes et factures du client"""
        client = self.get_object()

        # Commandes récentes
        commandes = client.commandes.order_by('-date_commande')[:10]

        # Factures récentes
        factures = client.factures.order_by('-date_facture')[:10]

        # Paiements récents
        paiements = client.paiements.order_by('-date_paiement')[:10]

        data = {
            'commandes': [
                {
                    'id': str(cmd.id),
                    'numero': cmd.numero,
                    'date': cmd.date_commande,
                    'statut': cmd.statut,
                    'montant': cmd.montant_total
                } for cmd in commandes
            ],
            'factures': [
                {
                    'id': str(fact.id),
                    'numero': fact.numero,
                    'date': fact.date_facture,
                    'statut': fact.statut,
                    'montant': fact.montant_total
                } for fact in factures
            ],
            'paiements': [
                {
                    'id': str(paie.id),
                    'reference': paie.reference,
                    'date': paie.date_paiement,
                    'montant': paie.montant,
                    'mode': paie.mode_paiement
                } for paie in paiements
            ]
        }

        return Response(data)

    @action(detail=True, methods=['post'])
    def changer_segment(self, request, pk=None):
        """Change le segment d'un client"""
        client = self.get_object()
        nouveau_segment = request.data.get('segment')

        if nouveau_segment in dict(Client.SEGMENT_CHOICES):
            ancien_segment = client.segment
            client.segment = nouveau_segment
            client.updated_by = request.user
            client.save()

            return Response({
                'message': f'Segment changé de {ancien_segment} à {nouveau_segment}',
                'ancien_segment': ancien_segment,
                'nouveau_segment': nouveau_segment
            })

        return Response(
            {'error': 'Segment invalide'},
            status=status.HTTP_400_BAD_REQUEST
        )


class ContactClientViewSet(viewsets.ModelViewSet):
    """ViewSet pour les contacts clients"""

    queryset = ContactClient.objects.all()
    serializer_class = ContactClientSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'clients'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['client', 'principal']
    search_fields = ['nom', 'prenom', 'email', 'fonction']
