import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { OrderService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import "../Invoices/Invoice.css";

interface Order {
  id: string;
  numero: string;
  client_nom: string;
  date_commande: string;
  statut: string;
  montant_total: number;
  items: any[];
}

const OrdersList: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const loadOrders = useCallback(async () => {
    try {
      setLoading(true);

      const params: any = {
        page: currentPage,
        page_size: 20,
      };

      if (statusFilter !== "all") params.statut = statusFilter;
      if (dateFrom) params.date_from = dateFrom;
      if (dateTo) params.date_to = dateTo;

      const response = await OrderService.getOrders(params);

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.data) {
        setOrders(response.data.results || response.data);
        setTotalPages(Math.ceil((response.data.count || 0) / 20));
      }
    } catch (err: any) {
      notify.error(`Erreur lors du chargement des commandes: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [statusFilter, dateFrom, dateTo, currentPage, notify]);

  useEffect(() => {
    loadOrders();
  }, [loadOrders]);

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      const response = await OrderService.updateOrder(orderId, { statut: newStatus });
      if (response.error) {
        throw new Error(response.error);
      }

      await loadOrders(); // Recharger la liste
      notify.success("Statut de commande mis à jour");
    } catch (err: any) {
      notify.error(`Erreur: ${err.message}`);
    }
  };
      {
        id: 2,
        numero: "ECOM-2025-002",
        client_nom: "ENTREPRISE MODERNE SARL",
        client_email: "<EMAIL>",
        date_commande: "2025-08-11T14:20:00Z",
        statut: "LIVREE",
        total_ht: 320.0,
        total_tva: 60.8,
        total_ttc: 380.8,
        items: [
          {
            id: 3,
            produit: {
              id: 4,
              nom: 'ÉCRAN PROFESSIONNEL 27"',
              prix: 320.0,
              code_produit: "B2025001001",
            },
            quantite: 1,
            prix_unitaire: 320.0,
            total: 320.0,
          },
        ],
        adresse_livraison: "456 Rue de l'Innovation, Tunis",
        mode_paiement: "VIREMENT",
        notes: "",
      },
      {
        id: 3,
        numero: "ECOM-2025-003",
        client_nom: "SOCIÉTÉ TECHNOLOGIE AVANCÉE",
        client_email: "<EMAIL>",
        date_commande: "2025-08-10T09:15:00Z",
        statut: "CONFIRMEE",
        total_ht: 154.5,
        total_tva: 29.355,
        total_ttc: 183.855,
        items: [
          {
            id: 4,
            produit: {
              id: 5,
              nom: "CLAVIER MÉCANIQUE RGB",
              prix: 89.5,
              code_produit: "B2025001002",
            },
            quantite: 1,
            prix_unitaire: 89.5,
            total: 89.5,
          },
          {
            id: 5,
            produit: {
              id: 6,
              nom: "SOURIS GAMING PRECISION",
              prix: 65.0,
              code_produit: "B2025001003",
            },
            quantite: 1,
            prix_unitaire: 65.0,
            total: 65.0,
          },
        ],
        adresse_livraison: "789 Boulevard du Progrès, Sfax",
        mode_paiement: "CHEQUE",
        notes: "Commande groupée pour bureau",
      },
    ];

    // Appliquer les filtres
    let filteredOrders = mockOrders;

    if (statusFilter !== "all") {
      filteredOrders = filteredOrders.filter(
        (order) => order.statut === statusFilter
      );
    }

    if (dateFrom) {
      filteredOrders = filteredOrders.filter(
        (order) => new Date(order.date_commande) >= new Date(dateFrom)
      );
    }

    if (dateTo) {
      filteredOrders = filteredOrders.filter(
        (order) => new Date(order.date_commande) <= new Date(dateTo)
      );
    }

    // Pagination
    const startIndex = (currentPage - 1) * 10;
    const paginatedOrders = filteredOrders.slice(startIndex, startIndex + 10);

    setOrders(paginatedOrders);
    setTotalPages(Math.ceil(filteredOrders.length / 10));
    setLoading(false);
  };

  const updateOrderStatus = async (orderId: number, newStatus: string) => {
    const res = await EcommerceService.updateOrderStatus(orderId, newStatus);
    if (res) {
      await loadOrders();
      notify.success("Succès", "Statut de la commande mis à jour");
    } else {
      notify.error(
        "Erreur",
        res.message || "Impossible de mettre à jour le statut"
      );
    }
  };

  const resetFilters = () => {
    setStatusFilter("all");
    setDateFrom("");
    setDateTo("");
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">Chargement des commandes...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Commandes</h1>
            <p className="page-subtitle">Gestion des commandes e-commerce</p>
          </div>
          <div className="page-actions">
            <button
              className="page-action"
              onClick={() => navigate("/dashboard/ecommerce/catalogue")}>
              🛒 Nouvelle commande
            </button>
          </div>
        </div>
      </div>

      <div className="page-content">
        {/* Filtres */}
        <div style={{ marginBottom: "20px" }}>
          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: "wrap",
              alignItems: "center",
            }}>
            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Statut:
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="all">Tous les statuts</option>
                <option value="EN_ATTENTE">En attente</option>
                <option value="CONFIRMEE">Confirmée</option>
                <option value="EN_PREPARATION">En préparation</option>
                <option value="EXPEDIEE">Expédiée</option>
                <option value="LIVREE">Livrée</option>
                <option value="ANNULEE">Annulée</option>
              </select>
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Du:
              </label>
              <input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                style={{
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Au:
              </label>
              <input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                style={{
                  padding: "6px 10px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </div>

            {(statusFilter !== "all" || dateFrom || dateTo) && (
              <button
                onClick={resetFilters}
                style={{
                  padding: "6px 12px",
                  background: "#e74c3c",
                  color: "white",
                  border: "none",
                  borderRadius: "6px",
                  cursor: "pointer",
                  fontSize: "14px",
                }}>
                🗑️ Effacer
              </button>
            )}
          </div>
        </div>

        {/* Table des commandes */}
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>N° Commande</th>
                <th>Date</th>
                <th>Client</th>
                <th>Montant</th>
                <th>Statut</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {orders.map((order) => (
                <tr key={order.id}>
                  <td>
                    <strong>{order.numero}</strong>
                  </td>
                  <td>
                    {new Date(order.date_commande).toLocaleDateString("fr-FR")}
                  </td>
                  <td>
                    <div>
                      <strong>{order.client_nom}</strong>
                      <div style={{ fontSize: "12px", color: "#666" }}>
                        {order.client_email}
                      </div>
                    </div>
                  </td>
                  <td>
                    <strong>
                      {EcommerceService.formatPrice(order.total_ttc)}
                    </strong>
                    <div style={{ fontSize: "12px", color: "#666" }}>
                      HT: {EcommerceService.formatPrice(order.total_ht)}
                    </div>
                  </td>
                  <td>
                    <span
                      style={{
                        padding: "4px 8px",
                        borderRadius: "4px",
                        fontSize: "12px",
                        background:
                          EcommerceService.getStatusColor(order.statut) + "20",
                        color: EcommerceService.getStatusColor(order.statut),
                      }}>
                      {EcommerceService.getStatusLabel(order.statut)}
                    </span>
                  </td>
                  <td>
                    <div
                      style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
                      <button
                        style={{
                          background: "#3498db",
                          color: "white",
                          border: "none",
                          padding: "6px 12px",
                          borderRadius: "6px",
                          cursor: "pointer",
                          fontSize: "0.8rem",
                        }}
                        onClick={() =>
                          navigate(`/dashboard/ecommerce/commandes/${order.id}`)
                        }>
                        👁️ Voir
                      </button>

                      {/* Actions rapides selon le statut */}
                      {order.statut === "EN_ATTENTE" && (
                        <button
                          style={{
                            background: "#27ae60",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() =>
                            updateOrderStatus(order.id, "CONFIRMEE")
                          }>
                          ✅ Confirmer
                        </button>
                      )}

                      {order.statut === "CONFIRMEE" && (
                        <button
                          style={{
                            background: "#9b59b6",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() =>
                            updateOrderStatus(order.id, "EN_PREPARATION")
                          }>
                          📦 Préparer
                        </button>
                      )}

                      {order.statut === "EN_PREPARATION" && (
                        <button
                          style={{
                            background: "#e67e22",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() =>
                            updateOrderStatus(order.id, "EXPEDIEE")
                          }>
                          🚚 Expédier
                        </button>
                      )}

                      {order.statut === "EXPEDIEE" && (
                        <button
                          style={{
                            background: "#27ae60",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() => updateOrderStatus(order.id, "LIVREE")}>
                          ✅ Livrer
                        </button>
                      )}

                      {!["LIVREE", "ANNULEE"].includes(order.statut) && (
                        <button
                          style={{
                            background: "#e74c3c",
                            color: "white",
                            border: "none",
                            padding: "6px 12px",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "0.8rem",
                          }}
                          onClick={() => {
                            if (
                              window.confirm(
                                "Êtes-vous sûr de vouloir annuler cette commande ?"
                              )
                            ) {
                              updateOrderStatus(order.id, "ANNULEE");
                            }
                          }}>
                          ❌ Annuler
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "10px",
              alignItems: "center",
              marginTop: "20px",
            }}>
            <button
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              style={{
                padding: "8px 12px",
                background: currentPage === 1 ? "#95a5a6" : "#3498db",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: currentPage === 1 ? "not-allowed" : "pointer",
              }}>
              ← Précédent
            </button>

            <span style={{ margin: "0 15px" }}>
              Page {currentPage} sur {totalPages}
            </span>

            <button
              onClick={() =>
                setCurrentPage((prev) => Math.min(totalPages, prev + 1))
              }
              disabled={currentPage === totalPages}
              style={{
                padding: "8px 12px",
                background: currentPage === totalPages ? "#95a5a6" : "#3498db",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: currentPage === totalPages ? "not-allowed" : "pointer",
              }}>
              Suivant →
            </button>
          </div>
        )}

        {orders.length === 0 && (
          <div
            style={{ textAlign: "center", padding: "40px", color: "#7f8c8d" }}>
            <p>Aucune commande trouvée.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrdersList;
