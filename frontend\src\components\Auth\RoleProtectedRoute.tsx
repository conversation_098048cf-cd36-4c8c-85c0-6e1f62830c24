/**
 * Composant pour protéger les routes basé sur les rôles utilisateur
 */

import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth, UserRole } from "../../contexts/AuthContext";

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredModule?: string;
  minimumRole?: UserRole;
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredModule,
  minimumRole,
  fallbackPath = "/dashboard",
  showUnauthorized = false,
}) => {
  const {
    isAuthenticated,
    isLoading,
    hasRole,
    hasModuleAccess,
    hasMinimumRole,
    user,
  } = useAuth();

  // Affichage de chargement
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          color: "white",
        }}>
        <div style={{ textAlign: "center" }}>
          <div
            style={{
              width: "50px",
              height: "50px",
              border: "3px solid rgba(255,255,255,0.3)",
              borderTop: "3px solid white",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              margin: "0 auto 20px",
            }}
          />
          <p>Chargement...</p>
        </div>
      </div>
    );
  }

  // Redirection si non authentifié
  if (!isAuthenticated) {
    return (
      <Navigate
        to="/login"
        replace
      />
    );
  }

  // Vérification des permissions
  let hasPermission = true;

  if (requiredRole && !hasRole(requiredRole)) {
    hasPermission = false;
  }

  if (requiredModule && !hasModuleAccess(requiredModule)) {
    hasPermission = false;
  }

  if (minimumRole && !hasMinimumRole(minimumRole)) {
    hasPermission = false;
  }

  // Si l'utilisateur n'a pas les permissions
  if (!hasPermission) {
    if (showUnauthorized) {
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
            background: "linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)",
            color: "white",
            textAlign: "center",
            padding: "20px",
          }}>
          <h1 style={{ fontSize: "48px", marginBottom: "20px" }}>🚫</h1>
          <h2 style={{ marginBottom: "10px" }}>Accès non autorisé</h2>
          <p style={{ marginBottom: "20px", maxWidth: "500px" }}>
            Vous n'avez pas les permissions nécessaires pour accéder à cette
            page.
            {user && (
              <>
                <br />
                <strong>Votre rôle :</strong> {user.role}
              </>
            )}
          </p>
          <button
            onClick={() => window.history.back()}
            style={{
              padding: "12px 24px",
              background: "rgba(255,255,255,0.2)",
              border: "2px solid white",
              borderRadius: "8px",
              color: "white",
              cursor: "pointer",
              fontSize: "16px",
              marginRight: "10px",
            }}>
            Retour
          </button>
          <button
            onClick={() => (window.location.href = fallbackPath)}
            style={{
              padding: "12px 24px",
              background: "white",
              border: "2px solid white",
              borderRadius: "8px",
              color: "#ee5a24",
              cursor: "pointer",
              fontSize: "16px",
            }}>
            Tableau de bord
          </button>
        </div>
      );
    } else {
      return (
        <Navigate
          to={fallbackPath}
          replace
        />
      );
    }
  }

  // L'utilisateur a les permissions, afficher le contenu
  return <>{children}</>;
};

export default RoleProtectedRoute;
