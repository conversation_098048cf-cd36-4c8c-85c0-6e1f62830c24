"""
Service de consolidation pour éliminer les duplications
"""
from django.db import transaction
from django.core.exceptions import ValidationError
from Produits.models import Produit
from Ecommerce.models import ProduitEcommerce
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class ConsolidationService:
    """Service pour consolider les données et éliminer les duplications"""
    
    @staticmethod
    @transaction.atomic
    def consolidate_products():
        """
        Consolide les produits entre Facturation et Ecommerce
        Élimine les duplications et synchronise les données
        """
        try:
            # Récupérer tous les produits de facturation
            produits_facturation = Produit.objects.all()
            
            # Récupérer tous les produits e-commerce
            produits_ecommerce = ProduitEcommerce.objects.select_related('produit').all()
            
            # Identifier les duplications potentielles
            duplications = []
            
            for prod_fact in produits_facturation:
                # Chercher des produits similaires dans e-commerce
                similaires = produits_ecommerce.filter(
                    produit__nom__iexact=prod_fact.nom
                ).exclude(produit=prod_fact)
                
                if similaires.exists():
                    duplications.append({
                        'produit_facturation': prod_fact,
                        'produits_similaires': list(similaires)
                    })
            
            # Traiter les duplications
            for duplication in duplications:
                ConsolidationService._merge_duplicate_products(duplication)
            
            # Créer des liens manquants entre Facturation et Ecommerce
            ConsolidationService._create_missing_ecommerce_links()
            
            logger.info(f"Consolidation terminée. {len(duplications)} duplications traitées.")
            
            return {
                'success': True,
                'duplications_found': len(duplications),
                'message': f'Consolidation réussie. {len(duplications)} duplications traitées.'
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la consolidation: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def _merge_duplicate_products(duplication):
        """Fusionne les produits dupliqués"""
        produit_principal = duplication['produit_facturation']
        produits_similaires = duplication['produits_similaires']
        
        for prod_ecom in produits_similaires:
            # Transférer les données importantes
            if not produit_principal.description and prod_ecom.description_longue:
                produit_principal.description = prod_ecom.description_longue
            
            # Transférer les images si nécessaire
            if not produit_principal.image and prod_ecom.image_principale:
                produit_principal.image = prod_ecom.image_principale
            
            # Mettre à jour les statistiques
            if hasattr(produit_principal, 'vues_total'):
                produit_principal.vues_total += prod_ecom.nombre_vues
            
            if hasattr(produit_principal, 'ventes_total'):
                produit_principal.ventes_total += prod_ecom.nombre_ventes
            
            # Sauvegarder les modifications
            produit_principal.save()
            
            # Supprimer le produit e-commerce dupliqué
            prod_ecom.delete()
    
    @staticmethod
    def _create_missing_ecommerce_links():
        """Crée les liens manquants entre produits Facturation et Ecommerce"""
        produits_sans_ecommerce = Produit.objects.filter(
            ecommerce__isnull=True,
            statut='ACTIF'
        )
        
        for produit in produits_sans_ecommerce:
            # Créer automatiquement un produit e-commerce
            ProduitEcommerce.objects.get_or_create(
                produit=produit,
                defaults={
                    'slug': ConsolidationService._generate_slug(produit.nom),
                    'visible_en_ligne': False,  # Désactivé par défaut
                    'description_courte': produit.description[:200] if produit.description else '',
                    'description_longue': produit.description or '',
                    'prix_promo': produit.prix_unitaire * 0.9,  # 10% de réduction par défaut
                }
            )
    
    @staticmethod
    def _generate_slug(nom):
        """Génère un slug unique pour un produit"""
        import re
        from django.utils.text import slugify
        
        base_slug = slugify(nom)
        slug = base_slug
        counter = 1
        
        while ProduitEcommerce.objects.filter(slug=slug).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1
        
        return slug
    
    @staticmethod
    def analyze_duplications():
        """Analyse les duplications sans les traiter"""
        try:
            # Analyser les produits
            produits_facturation = Produit.objects.all()
            produits_ecommerce = ProduitEcommerce.objects.select_related('produit').all()
            
            # Identifier les duplications par nom
            duplications_nom = {}
            for prod in produits_facturation:
                nom_lower = prod.nom.lower()
                if nom_lower in duplications_nom:
                    duplications_nom[nom_lower].append(prod)
                else:
                    duplications_nom[nom_lower] = [prod]
            
            # Filtrer les vrais duplications
            vraies_duplications = {
                nom: produits for nom, produits in duplications_nom.items() 
                if len(produits) > 1
            }
            
            # Analyser les produits orphelins (sans lien e-commerce)
            produits_orphelins = Produit.objects.filter(
                ecommerce__isnull=True
            ).count()
            
            # Analyser les incohérences de prix
            incoherences_prix = []
            for prod_ecom in produits_ecommerce:
                prix_ecom = prod_ecom.prix_public or prod_ecom.produit.prix_unitaire
                if prix_ecom != prod_ecom.produit.prix_unitaire:
                    incoherences_prix.append({
                        'produit': prod_ecom.produit.nom,
                        'prix_facturation': prod_ecom.produit.prix_unitaire,
                        'prix_ecommerce': prix_ecom
                    })
            
            return {
                'duplications_nom': len(vraies_duplications),
                'produits_orphelins': produits_orphelins,
                'incoherences_prix': len(incoherences_prix),
                'details': {
                    'duplications': vraies_duplications,
                    'incoherences_prix': incoherences_prix
                }
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de l'analyse: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    @transaction.atomic
    def synchronize_prices():
        """Synchronise les prix entre Facturation et Ecommerce"""
        try:
            produits_ecommerce = ProduitEcommerce.objects.select_related('produit').all()
            updated_count = 0
            
            for prod_ecom in produits_ecommerce:
                prix_ecom = prod_ecom.prix_public or prod_ecom.produit.prix_unitaire
                if prix_ecom != prod_ecom.produit.prix_unitaire:
                    prod_ecom.prix_public = prod_ecom.produit.prix_unitaire
                    # Recalculer le prix promo (10% de réduction)
                    prod_ecom.prix_promo = prod_ecom.produit.prix_unitaire * Decimal('0.9')
                    prod_ecom.save()
                    updated_count += 1
            
            return {
                'success': True,
                'updated_count': updated_count,
                'message': f'{updated_count} prix synchronisés.'
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la synchronisation: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def generate_consolidation_report():
        """Génère un rapport complet de consolidation"""
        analysis = ConsolidationService.analyze_duplications()
        
        # Statistiques générales
        total_produits_facturation = Produit.objects.count()
        total_produits_ecommerce = ProduitEcommerce.objects.count()
        produits_actifs = Produit.objects.filter(statut='ACTIF').count()
        
        report = {
            'timestamp': timezone.now(),
            'statistiques_generales': {
                'total_produits_facturation': total_produits_facturation,
                'total_produits_ecommerce': total_produits_ecommerce,
                'produits_actifs': produits_actifs,
                'taux_activation': (produits_actifs / total_produits_facturation * 100) if total_produits_facturation > 0 else 0
            },
            'problemes_detectes': analysis,
            'recommandations': ConsolidationService._generate_recommendations(analysis)
        }
        
        return report
    
    @staticmethod
    def _generate_recommendations(analysis):
        """Génère des recommandations basées sur l'analyse"""
        recommendations = []
        
        if analysis.get('duplications_nom', 0) > 0:
            recommendations.append({
                'type': 'DUPLICATION',
                'priority': 'HIGH',
                'message': f"{analysis['duplications_nom']} duplications de noms détectées. Consolidation recommandée.",
                'action': 'consolidate_products'
            })
        
        if analysis.get('produits_orphelins', 0) > 0:
            recommendations.append({
                'type': 'ORPHAN',
                'priority': 'MEDIUM',
                'message': f"{analysis['produits_orphelins']} produits sans lien e-commerce. Création de liens recommandée.",
                'action': 'create_missing_ecommerce_links'
            })
        
        if analysis.get('incoherences_prix', 0) > 0:
            recommendations.append({
                'type': 'PRICE_SYNC',
                'priority': 'HIGH',
                'message': f"{analysis['incoherences_prix']} incohérences de prix détectées. Synchronisation recommandée.",
                'action': 'synchronize_prices'
            })
        
        return recommendations


# Import nécessaire pour timezone
from django.utils import timezone
