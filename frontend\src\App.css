/* Import du design system */
@import "./styles/variables.css";
@import "./styles/components.css";

/* Styles pour l'authentification */
.auth-loading {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.auth-loading .loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.auth-loading p {
  font-size: 1.1rem;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Reset et styles de base */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
}

.App {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

/* Styles globaux pour les liens */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Styles pour les titres */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-sm);
}

h1 {
  font-size: var(--font-size-3xl);
}
h2 {
  font-size: var(--font-size-2xl);
}
h3 {
  font-size: var(--font-size-xl);
}
h4 {
  font-size: var(--font-size-lg);
}
h5 {
  font-size: var(--font-size-base);
}
h6 {
  font-size: var(--font-size-sm);
}

/* Styles pour les paragraphes */
p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

/* Styles pour le code */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
  background-color: var(--bg-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
}

/* Styles pour les conteneurs */
.container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  h1 {
    font-size: var(--font-size-2xl);
  }
  h2 {
    font-size: var(--font-size-xl);
  }
  h3 {
    font-size: var(--font-size-lg);
  }
}

/* Styles spécifiques pour la page d'accueil */
.App-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 20px;
  color: white;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.App-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
  color: white;
}

.App-header > p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  color: white;
}

.status-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
}

.status-info p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
  color: white;
}

/* Layout principal de l'application */
.app-layout {
  display: flex;
  min-height: 100vh;
}

.app-sidebar {
  width: 280px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: width 0.3s ease;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-collapsed .app-sidebar {
  width: 70px;
}

.app-main {
  flex: 1;
  margin-left: 280px;
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

.sidebar-collapsed .app-main {
  margin-left: 70px;
}

.app-content {
  min-height: 100vh;
}

/* Loading */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f8f9fa;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner p {
  color: #6c757d;
  font-size: 1.1rem;
}

/* Page placeholder */
.page-placeholder {
  padding: 3rem;
  text-align: center;
  background: white;
  margin: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.page-placeholder h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.page-placeholder p {
  font-size: 1.2rem;
  color: #6c757d;
}

/* Responsive pour l'application */
@media (max-width: 768px) {
  .app-sidebar {
    width: 280px;
    transform: translateX(-100%);
  }

  .app-sidebar.mobile-open {
    transform: translateX(0);
  }

  .app-main {
    margin-left: 0;
  }

  .sidebar-collapsed .app-main {
    margin-left: 0;
  }
}
