/**
 * Paramètres utilisateur
 */

import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useNotify } from "../Common/NotificationSystem";

interface UserData {
  first_name: string;
  last_name: string;
  email: string;
  telephone: string;
  poste: string;
  avatar_url: string;
  langue: string;
  fuseau_horaire: string;
  format_date: string;
  format_heure: string;
  notifications_email: boolean;
  notifications_desktop: boolean;
  theme: "light" | "dark" | "auto";
}

interface PasswordData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

const UserSettings: React.FC = () => {
  const { user } = useAuth();
  const notify = useNotify();

  const [formData, setFormData] = useState<UserData>({
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    telephone: "+216 72 287 863",
    poste: "Directeur <PERSON>",
    avatar_url: "/api/placeholder/150/150",
    langue: "fr",
    fuseau_horaire: "Africa/Tunis",
    format_date: "DD/MM/YYYY",
    format_heure: "24h",
    notifications_email: true,
    notifications_desktop: true,
    theme: "light",
  });

  const [passwordData, setPasswordData] = useState<PasswordData>({
    current_password: "",
    new_password: "",
    confirm_password: "",
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [passwordErrors, setPasswordErrors] = useState<Record<string, string>>(
    {}
  );
  const [showPasswordForm, setShowPasswordForm] = useState(false);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);

      // Utiliser les données de l'utilisateur connecté
      if (user) {
        setFormData((prev) => ({
          ...prev,
          first_name: user.prenom || "",
          last_name: user.nom || "",
          email: user.email || "",
          telephone: "",
          poste: "",
          avatar_url: "",
          langue: "fr",
          fuseau_horaire: "Europe/Paris",
          format_date: "DD/MM/YYYY",
          format_heure: "24h",
          notifications_email: true,
          notifications_desktop: true,
          theme: "light",
        }));
      }
    } catch (error: any) {
      notify.error("Erreur lors du chargement des paramètres");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData((prev) => ({ ...prev, [field]: value }));
    if (passwordErrors[field]) {
      setPasswordErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.first_name.trim()) {
      newErrors.first_name = "Prénom requis";
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = "Nom requis";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email requis";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Format email invalide";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePassword = () => {
    const newErrors: Record<string, string> = {};

    if (!passwordData.current_password) {
      newErrors.current_password = "Mot de passe actuel requis";
    }

    if (!passwordData.new_password) {
      newErrors.new_password = "Nouveau mot de passe requis";
    } else if (passwordData.new_password.length < 8) {
      newErrors.new_password = "Minimum 8 caractères";
    }

    if (passwordData.new_password !== passwordData.confirm_password) {
      newErrors.confirm_password = "Les mots de passe ne correspondent pas";
    }

    setPasswordErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notify.error("Veuillez corriger les erreurs du formulaire");
      return;
    }

    try {
      setSaving(true);

      // Simuler la sauvegarde
      await new Promise((resolve) => setTimeout(resolve, 1000));

      notify.success("Paramètres utilisateur mis à jour avec succès");
    } catch (error: any) {
      notify.error("Erreur lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePassword()) {
      notify.error("Veuillez corriger les erreurs du formulaire");
      return;
    }

    try {
      setChangingPassword(true);

      // Simuler le changement de mot de passe
      await new Promise((resolve) => setTimeout(resolve, 1000));

      notify.success("Mot de passe modifié avec succès");
      setPasswordData({
        current_password: "",
        new_password: "",
        confirm_password: "",
      });
      setShowPasswordForm(false);
    } catch (error: any) {
      notify.error("Erreur lors du changement de mot de passe");
    } finally {
      setChangingPassword(false);
    }
  };

  if (loading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des paramètres...</p>
      </div>
    );
  }

  return (
    <div className="user-settings">
      {/* Informations personnelles */}
      <form onSubmit={handleSubmit}>
        <div className="settings-section">
          <h3>👤 Informations Personnelles</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Prénom *</label>
              <input
                type="text"
                value={formData.first_name}
                onChange={(e) =>
                  handleInputChange("first_name", e.target.value)
                }
                className={`form-input ${errors.first_name ? "error" : ""}`}
                placeholder="Jean"
              />
              {errors.first_name && (
                <span className="error-text">{errors.first_name}</span>
              )}
            </div>

            <div className="form-group">
              <label>Nom *</label>
              <input
                type="text"
                value={formData.last_name}
                onChange={(e) => handleInputChange("last_name", e.target.value)}
                className={`form-input ${errors.last_name ? "error" : ""}`}
                placeholder="Dupont"
              />
              {errors.last_name && (
                <span className="error-text">{errors.last_name}</span>
              )}
            </div>

            <div className="form-group">
              <label>Email *</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`form-input ${errors.email ? "error" : ""}`}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <span className="error-text">{errors.email}</span>
              )}
            </div>

            <div className="form-group">
              <label>Téléphone</label>
              <input
                type="tel"
                value={formData.telephone}
                onChange={(e) => handleInputChange("telephone", e.target.value)}
                className="form-input"
                placeholder="06 12 34 56 78"
              />
            </div>

            <div className="form-group">
              <label>Poste</label>
              <input
                type="text"
                value={formData.poste}
                onChange={(e) => handleInputChange("poste", e.target.value)}
                className="form-input"
                placeholder="Responsable commercial"
              />
            </div>

            <div className="form-group">
              <label>Avatar (URL)</label>
              <input
                type="url"
                value={formData.avatar_url}
                onChange={(e) =>
                  handleInputChange("avatar_url", e.target.value)
                }
                className="form-input"
                placeholder="https://example.com/avatar.jpg"
              />
            </div>
          </div>
        </div>

        {/* Préférences */}
        <div className="settings-section">
          <h3>🎨 Préférences</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Langue</label>
              <select
                value={formData.langue}
                onChange={(e) => handleInputChange("langue", e.target.value)}
                className="form-select">
                <option value="fr">🇫🇷 Français</option>
                <option value="en">🇬🇧 English</option>
                <option value="es">🇪🇸 Español</option>
              </select>
            </div>

            <div className="form-group">
              <label>Fuseau horaire</label>
              <select
                value={formData.fuseau_horaire}
                onChange={(e) =>
                  handleInputChange("fuseau_horaire", e.target.value)
                }
                className="form-select">
                <option value="Europe/Paris">Europe/Paris (UTC+1)</option>
                <option value="Europe/London">Europe/London (UTC+0)</option>
                <option value="America/New_York">
                  America/New_York (UTC-5)
                </option>
                <option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</option>
              </select>
            </div>

            <div className="form-group">
              <label>Format de date</label>
              <select
                value={formData.format_date}
                onChange={(e) =>
                  handleInputChange("format_date", e.target.value)
                }
                className="form-select">
                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
              </select>
            </div>

            <div className="form-group">
              <label>Format d'heure</label>
              <select
                value={formData.format_heure}
                onChange={(e) =>
                  handleInputChange("format_heure", e.target.value)
                }
                className="form-select">
                <option value="24h">24h (14:30)</option>
                <option value="12h">12h (2:30 PM)</option>
              </select>
            </div>

            <div className="form-group">
              <label>Thème</label>
              <select
                value={formData.theme}
                onChange={(e) => handleInputChange("theme", e.target.value)}
                className="form-select">
                <option value="light">☀️ Clair</option>
                <option value="dark">🌙 Sombre</option>
                <option value="auto">🔄 Automatique</option>
              </select>
            </div>
          </div>
        </div>

        {/* Notifications */}
        <div className="settings-section">
          <h3>🔔 Notifications</h3>

          <div className="form-grid">
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.notifications_email}
                  onChange={(e) =>
                    handleInputChange("notifications_email", e.target.checked)
                  }
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  📧 Notifications par email
                </span>
              </label>
              <small className="form-help">
                Recevoir les notifications importantes par email
              </small>
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.notifications_desktop}
                  onChange={(e) =>
                    handleInputChange("notifications_desktop", e.target.checked)
                  }
                  className="form-checkbox"
                />
                <span className="checkbox-text">🖥️ Notifications desktop</span>
              </label>
              <small className="form-help">
                Afficher les notifications dans le navigateur
              </small>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="settings-actions">
          <button
            type="button"
            className="btn btn-outline"
            onClick={loadUserData}
            disabled={saving}>
            🔄 Annuler
          </button>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={saving}>
            {saving ? "⏳ Sauvegarde..." : "💾 Sauvegarder"}
          </button>
        </div>
      </form>

      {/* Changement de mot de passe */}
      <div className="settings-section">
        <div className="section-header">
          <h3>🔒 Sécurité</h3>
          <button
            className="btn btn-warning btn-sm"
            onClick={() => setShowPasswordForm(!showPasswordForm)}>
            {showPasswordForm ? "❌ Annuler" : "🔑 Changer mot de passe"}
          </button>
        </div>

        {showPasswordForm && (
          <form onSubmit={handlePasswordSubmit}>
            <div className="form-grid">
              <div className="form-group">
                <label>Mot de passe actuel *</label>
                <input
                  type="password"
                  value={passwordData.current_password}
                  onChange={(e) =>
                    handlePasswordChange("current_password", e.target.value)
                  }
                  className={`form-input ${
                    passwordErrors.current_password ? "error" : ""
                  }`}
                  placeholder="••••••••"
                />
                {passwordErrors.current_password && (
                  <span className="error-text">
                    {passwordErrors.current_password}
                  </span>
                )}
              </div>

              <div className="form-group">
                <label>Nouveau mot de passe *</label>
                <input
                  type="password"
                  value={passwordData.new_password}
                  onChange={(e) =>
                    handlePasswordChange("new_password", e.target.value)
                  }
                  className={`form-input ${
                    passwordErrors.new_password ? "error" : ""
                  }`}
                  placeholder="••••••••"
                />
                {passwordErrors.new_password && (
                  <span className="error-text">
                    {passwordErrors.new_password}
                  </span>
                )}
                <small className="form-help">
                  Minimum 8 caractères avec lettres et chiffres
                </small>
              </div>

              <div className="form-group">
                <label>Confirmer le mot de passe *</label>
                <input
                  type="password"
                  value={passwordData.confirm_password}
                  onChange={(e) =>
                    handlePasswordChange("confirm_password", e.target.value)
                  }
                  className={`form-input ${
                    passwordErrors.confirm_password ? "error" : ""
                  }`}
                  placeholder="••••••••"
                />
                {passwordErrors.confirm_password && (
                  <span className="error-text">
                    {passwordErrors.confirm_password}
                  </span>
                )}
              </div>
            </div>

            <div className="password-actions">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => {
                  setShowPasswordForm(false);
                  setPasswordData({
                    current_password: "",
                    new_password: "",
                    confirm_password: "",
                  });
                  setPasswordErrors({});
                }}
                disabled={changingPassword}>
                ❌ Annuler
              </button>

              <button
                type="submit"
                className="btn btn-warning"
                disabled={changingPassword}>
                {changingPassword
                  ? "⏳ Modification..."
                  : "🔑 Changer le mot de passe"}
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Informations du compte */}
      <div className="settings-section">
        <h3>ℹ️ Informations du Compte</h3>

        <div className="account-info">
          <div className="info-item">
            <label>Nom d'utilisateur:</label>
            <span>{user?.email}</span>
          </div>
          <div className="info-item">
            <label>Rôle:</label>
            <span className={`role-badge role-${user?.role?.toLowerCase()}`}>
              {user?.role === "SUPERADMIN"
                ? "👑 Super Admin"
                : user?.role === "ADMIN"
                ? "🛡️ Administrateur"
                : user?.role === "COMPTABLE"
                ? "📊 Comptable"
                : "👤 Utilisateur"}
            </span>
          </div>
          <div className="info-item">
            <label>Membre depuis:</label>
            <span>
              {user?.derniere_connexion
                ? new Date(user.derniere_connexion).toLocaleDateString("fr-FR")
                : "N/A"}
            </span>
          </div>
          <div className="info-item">
            <label>Dernière connexion:</label>
            <span>
              {user?.derniere_connexion
                ? new Date(user.derniere_connexion).toLocaleString("fr-FR")
                : "Jamais"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserSettings;
