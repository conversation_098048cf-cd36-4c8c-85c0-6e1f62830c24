from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count, Q, Avg, F
from django.utils import timezone
from datetime import datetime, timedelta

from .models import Produit, Categorie, ImageProduit
from .serializers import ProduitSerializer, CategorieSerializer, ImageProduitSerializer
from Authentication.permissions import HasModulePermission

class CategorieViewSet(viewsets.ModelViewSet):
    """ViewSet pour les catégories de produits"""

    queryset = Categorie.objects.filter(actif=True)
    serializer_class = CategorieSerializer
    permission_classes = [AllowAny]  # Temporaire pour les tests
    required_permission = 'produits'

    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['nom', 'description']
    ordering = ['nom']

class ProduitViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des produits"""

    queryset = Produit.objects.all()
    serializer_class = ProduitSerializer
    permission_classes = [AllowAny]  # Temporaire pour les tests
    required_permission = 'produits'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'categorie', 'type_produit', 'visible_ecommerce']
    search_fields = ['nom', 'reference', 'description', 'tags']
    ordering_fields = ['nom', 'prix_vente', 'stock_actuel', 'created_at']
    ordering = ['nom']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Statistiques des produits"""

        # Statistiques de base
        total_produits = Produit.objects.count()
        produits_actifs = Produit.objects.filter(statut='ACTIF').count()
        produits_rupture = Produit.objects.filter(
            gestion_stock=True,
            stock_actuel__lte=0
        ).count()
        produits_alerte = Produit.objects.filter(
            gestion_stock=True,
            stock_actuel__lte=F('stock_minimum'),
            stock_actuel__gt=0
        ).count()

        # Valeur du stock
        valeur_stock = Produit.objects.filter(
            gestion_stock=True
        ).aggregate(
            valeur_achat=Sum(F('stock_actuel') * F('prix_achat')),
            valeur_vente=Sum(F('stock_actuel') * F('prix_vente'))
        )

        # Répartition par catégorie
        categories = Produit.objects.values(
            'categorie__nom'
        ).annotate(
            count=Count('id'),
            valeur_stock=Sum(F('stock_actuel') * F('prix_vente'))
        ).order_by('-count')

        # Top produits par valeur stock
        top_produits = Produit.objects.filter(
            gestion_stock=True,
            stock_actuel__gt=0
        ).annotate(
            valeur_stock=F('stock_actuel') * F('prix_vente')
        ).order_by('-valeur_stock')[:10]

        data = {
            'total_produits': total_produits,
            'produits_actifs': produits_actifs,
            'produits_rupture': produits_rupture,
            'produits_alerte': produits_alerte,
            'valeur_stock_achat': valeur_stock['valeur_achat'] or 0,
            'valeur_stock_vente': valeur_stock['valeur_vente'] or 0,
            'categories': list(categories),
            'top_produits': ProduitSerializer(top_produits, many=True).data
        }

        return Response(data)

    @action(detail=False, methods=['get'])
    def alertes_stock(self, request):
        """Produits en alerte de stock"""

        # Produits en rupture
        ruptures = Produit.objects.filter(
            gestion_stock=True,
            stock_actuel__lte=0,
            statut='ACTIF'
        )

        # Produits en alerte (stock <= minimum)
        alertes = Produit.objects.filter(
            gestion_stock=True,
            stock_actuel__lte=F('stock_minimum'),
            stock_actuel__gt=0,
            statut='ACTIF'
        )

        data = {
            'ruptures': ProduitSerializer(ruptures, many=True).data,
            'alertes': ProduitSerializer(alertes, many=True).data,
            'total_ruptures': ruptures.count(),
            'total_alertes': alertes.count()
        }

        return Response(data)

    @action(detail=True, methods=['post'])
    def ajuster_stock(self, request, pk=None):
        """Ajuste le stock d'un produit"""
        produit = self.get_object()

        nouveau_stock = request.data.get('nouveau_stock')
        motif = request.data.get('motif', 'Ajustement manuel')

        if nouveau_stock is None:
            return Response(
                {'error': 'nouveau_stock requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            nouveau_stock = float(nouveau_stock)
            if nouveau_stock < 0:
                return Response(
                    {'error': 'Le stock ne peut pas être négatif'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            ancien_stock = produit.stock_actuel
            produit.stock_actuel = nouveau_stock
            produit.updated_by = request.user
            produit.save()

            # Créer un mouvement de stock
            from Stock.models import MouvementStock
            MouvementStock.objects.create(
                produit=produit,
                type_mouvement='AJUSTEMENT',
                quantite=nouveau_stock - ancien_stock,
                quantite_avant=ancien_stock,
                quantite_apres=nouveau_stock,
                motif=motif,
                created_by=request.user
            )

            return Response({
                'message': 'Stock ajusté avec succès',
                'ancien_stock': ancien_stock,
                'nouveau_stock': nouveau_stock,
                'difference': nouveau_stock - ancien_stock
            })

        except ValueError:
            return Response(
                {'error': 'Valeur de stock invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )


class ImageProduitViewSet(viewsets.ModelViewSet):
    """ViewSet pour les images de produits"""

    queryset = ImageProduit.objects.all()
    serializer_class = ImageProduitSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'produits'

    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['produit']
