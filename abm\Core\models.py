"""
Modèles de base et fonctionnalités communes intelligentes
"""
from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid
from datetime import datetime, timedelta
from django.utils import timezone


class BaseModel(models.Model):
    """Modèle de base avec fonctionnalités communes intelligentes"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='+')
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='+')
    
    # Métadonnées intelligentes
    version = models.PositiveIntegerField(default=1)
    is_active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)  # Stockage flexible de données
    
    class Meta:
        abstract = True
        
    def save(self, *args, **kwargs):
        """Sauvegarde intelligente avec versioning"""
        if self.pk:
            self.version += 1
        super().save(*args, **kwargs)
        
    def soft_delete(self):
        """Suppression logique"""
        self.is_active = False
        self.save()
        
    def restore(self):
        """Restauration"""
        self.is_active = True
        self.save()


class AuditLog(models.Model):
    """Journal d'audit intelligent pour traçabilité complète"""
    ACTION_CHOICES = [
        ('CREATE', 'Création'),
        ('UPDATE', 'Modification'),
        ('DELETE', 'Suppression'),
        ('VIEW', 'Consultation'),
        ('EXPORT', 'Export'),
        ('IMPORT', 'Import'),
        ('LOGIN', 'Connexion'),
        ('LOGOUT', 'Déconnexion'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    model_name = models.CharField(max_length=100)
    object_id = models.CharField(max_length=100, null=True, blank=True)
    object_repr = models.CharField(max_length=200, null=True, blank=True)
    changes = models.JSONField(default=dict, blank=True)  # Détail des changements
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Journal d'audit"
        verbose_name_plural = "Journaux d'audit"
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['model_name', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.user} - {self.action} - {self.model_name} - {self.timestamp}"


class SmartNotification(models.Model):
    """Système de notifications intelligentes"""
    TYPE_CHOICES = [
        ('INFO', 'Information'),
        ('SUCCESS', 'Succès'),
        ('WARNING', 'Avertissement'),
        ('ERROR', 'Erreur'),
        ('REMINDER', 'Rappel'),
        ('ALERT', 'Alerte'),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', 'Basse'),
        ('NORMAL', 'Normale'),
        ('HIGH', 'Haute'),
        ('URGENT', 'Urgente'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=200)
    message = models.TextField()
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='INFO')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='NORMAL')
    
    # Métadonnées
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Liens et actions
    action_url = models.URLField(blank=True)
    action_label = models.CharField(max_length=100, blank=True)
    related_object_type = models.CharField(max_length=100, blank=True)
    related_object_id = models.CharField(max_length=100, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Notification intelligente"
        verbose_name_plural = "Notifications intelligentes"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['priority', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.user.username}"
    
    def mark_as_read(self):
        """Marquer comme lu"""
        self.is_read = True
        self.read_at = timezone.now()
        self.save()
    
    @property
    def is_expired(self):
        """Vérifier si la notification a expiré"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False


class AIInsight(models.Model):
    """Insights et analyses IA pour l'entreprise"""
    INSIGHT_TYPES = [
        ('SALES_TREND', 'Tendance des ventes'),
        ('STOCK_PREDICTION', 'Prédiction de stock'),
        ('CLIENT_BEHAVIOR', 'Comportement client'),
        ('REVENUE_FORECAST', 'Prévision de revenus'),
        ('COST_OPTIMIZATION', 'Optimisation des coûts'),
        ('RISK_ANALYSIS', 'Analyse des risques'),
        ('PERFORMANCE_KPI', 'KPI de performance'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=50, choices=INSIGHT_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    
    # Données d'analyse
    data = models.JSONField(default=dict)  # Données brutes
    analysis_result = models.JSONField(default=dict)  # Résultats d'analyse
    confidence_score = models.FloatField(default=0.0)  # Score de confiance (0-1)
    
    # Recommandations
    recommendations = models.JSONField(default=list)
    action_items = models.JSONField(default=list)
    
    # Métadonnées
    generated_at = models.DateTimeField(auto_now_add=True)
    valid_until = models.DateTimeField(null=True, blank=True)
    is_actionable = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Insight IA"
        verbose_name_plural = "Insights IA"
        ordering = ['-generated_at']
    
    def __str__(self):
        return f"{self.get_type_display()} - {self.title}"
    
    @property
    def is_valid(self):
        """Vérifier si l'insight est encore valide"""
        if self.valid_until:
            return timezone.now() <= self.valid_until
        return True


class SmartTag(models.Model):
    """Système de tags intelligents pour catégorisation automatique"""
    name = models.CharField(max_length=100, unique=True)
    color = models.CharField(max_length=7, default='#007bff')  # Couleur hex
    description = models.TextField(blank=True)
    
    # IA et automatisation
    auto_apply_rules = models.JSONField(default=list)  # Règles d'application automatique
    usage_count = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Tag intelligent"
        verbose_name_plural = "Tags intelligents"
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def increment_usage(self):
        """Incrémenter le compteur d'utilisation"""
        self.usage_count += 1
        self.save()


class SystemHealth(models.Model):
    """Monitoring de la santé du système"""
    COMPONENT_CHOICES = [
        ('DATABASE', 'Base de données'),
        ('API', 'API REST'),
        ('STORAGE', 'Stockage'),
        ('CACHE', 'Cache'),
        ('EMAIL', 'Email'),
        ('PDF_GENERATOR', 'Générateur PDF'),
        ('BACKUP', 'Sauvegarde'),
    ]
    
    STATUS_CHOICES = [
        ('HEALTHY', 'Sain'),
        ('WARNING', 'Avertissement'),
        ('CRITICAL', 'Critique'),
        ('DOWN', 'Hors service'),
    ]
    
    component = models.CharField(max_length=50, choices=COMPONENT_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    response_time = models.FloatField(null=True, blank=True)  # en millisecondes
    error_message = models.TextField(blank=True)
    metrics = models.JSONField(default=dict)
    
    checked_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Santé du système"
        verbose_name_plural = "Santé du système"
        ordering = ['-checked_at']
        indexes = [
            models.Index(fields=['component', 'status']),
        ]
    
    def __str__(self):
        return f"{self.get_component_display()} - {self.get_status_display()}"
