from rest_framework import serializers
from .models import Commande, <PERSON>gne<PERSON>ommand<PERSON>, StatutCommande

class LigneCommandeSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de commande"""
    
    produit_nom = serializers.CharField(source='produit.nom', read_only=True)
    produit_reference = serializers.CharField(source='produit.reference', read_only=True)
    
    class Meta:
        model = LigneCommande
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'total_ht', 'montant_tva', 'total_ttc')

class StatutCommandeSerializer(serializers.ModelSerializer):
    """Serializer pour l'historique des statuts"""
    
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = StatutCommande
        fields = '__all__'
        read_only_fields = ('created_at',)

class CommandeSerializer(serializers.ModelSerializer):
    """Serializer pour les commandes"""
    
    lignes = LigneCommandeSerializer(many=True, read_only=True)
    historique_statuts = StatutCommandeSerializer(many=True, read_only=True)
    client_nom = serializers.CharField(source='client.nom_complet', read_only=True)
    peut_modifier = serializers.ReadOnlyField()
    peut_annuler = serializers.ReadOnlyField()
    
    class Meta:
        model = Commande
        fields = '__all__'
        read_only_fields = (
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'sous_total', 'montant_tva', 'montant_total'
        )

class CommandeCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de commandes"""
    
    lignes = LigneCommandeSerializer(many=True, required=False)
    
    class Meta:
        model = Commande
        exclude = (
            'numero', 'created_at', 'updated_at', 'created_by', 'updated_by',
            'sous_total', 'montant_tva', 'montant_total'
        )
    
    def create(self, validated_data):
        lignes_data = validated_data.pop('lignes', [])
        commande = Commande.objects.create(**validated_data)
        
        for ligne_data in lignes_data:
            LigneCommande.objects.create(commande=commande, **ligne_data)
        
        # Recalculer les totaux
        commande.calculer_totaux()
        
        return commande
    
    def update(self, instance, validated_data):
        lignes_data = validated_data.pop('lignes', [])
        
        # Mettre à jour la commande
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Mettre à jour les lignes
        if lignes_data:
            # Supprimer les anciennes lignes
            instance.lignes.all().delete()
            
            # Créer les nouvelles lignes
            for ligne_data in lignes_data:
                LigneCommande.objects.create(commande=instance, **ligne_data)
            
            # Recalculer les totaux
            instance.calculer_totaux()
        
        return instance
