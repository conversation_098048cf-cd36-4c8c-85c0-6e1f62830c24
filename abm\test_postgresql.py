#!/usr/bin/env python
"""
Script de test pour vérifier la configuration PostgreSQL
"""

import os
import sys
import django
from django.conf import settings

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'abm.settings')
django.setup()

from django.db import connection
from Authentication.models import CustomUser
from Clients.models import Client
from Produits.models import Produit

def test_database_connection():
    """Test de la connexion à la base de données"""
    print("🔍 Test de la connexion à PostgreSQL...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            print(f"✅ Connexion réussie à PostgreSQL")
            print(f"📊 Version: {version}")
            return True
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

def test_database_info():
    """Affichage des informations de la base de données"""
    print("\n📋 Informations de la base de données:")
    print(f"   - Nom: {settings.DATABASES['default']['NAME']}")
    print(f"   - Utilisateur: {settings.DATABASES['default']['USER']}")
    print(f"   - Hôte: {settings.DATABASES['default']['HOST']}")
    print(f"   - Port: {settings.DATABASES['default']['PORT']}")
    return True

def test_models():
    """Test des modèles Django"""
    print("\n🧪 Test des modèles Django...")
    
    try:
        # Test du modèle CustomUser
        user_count = CustomUser.objects.count()
        print(f"✅ Utilisateurs: {user_count}")

        # Test du modèle Client
        client_count = Client.objects.count()
        print(f"✅ Clients: {client_count}")

        # Test du modèle Produit
        produit_count = Produit.objects.count()
        print(f"✅ Produits: {produit_count}")

        return True
    except Exception as e:
        print(f"❌ Erreur avec les modèles: {e}")
        return False

def create_test_data():
    """Création de données de test"""
    print("\n🔧 Création de données de test...")
    
    try:
        # Créer un client de test
        client, created = Client.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                'nom': 'Client Test PostgreSQL',
                'prenom': 'Test',
                'telephone': '0123456789',
                'type_client': 'PARTICULIER',
                'statut': 'ACTIF'
            }
        )
        
        if created:
            print(f"✅ Client créé: {client.nom} ({client.email})")
        else:
            print(f"ℹ️ Client existant: {client.nom} ({client.email})")
        
        # Créer un produit de test
        produit, created = Produit.objects.get_or_create(
            nom="Produit Test PostgreSQL",
            defaults={
                'description': 'Produit de test pour PostgreSQL',
                'prix_vente': 99.99,
                'stock_actuel': 10,
                'statut': 'ACTIF'
            }
        )
        
        if created:
            print(f"✅ Produit créé: {produit.nom} - {produit.prix_vente}€")
        else:
            print(f"ℹ️ Produit existant: {produit.nom} - {produit.prix_vente}€")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la création des données: {e}")
        return False

def test_queries():
    """Test de requêtes complexes"""
    print("\n🔍 Test de requêtes PostgreSQL...")
    
    try:
        # Requête avec jointure
        clients_with_orders = Client.objects.select_related().all()[:5]
        print(f"✅ Requête avec jointure: {len(clients_with_orders)} clients")
        
        # Requête d'agrégation
        from django.db.models import Count, Sum
        stats = Client.objects.aggregate(
            total=Count('id'),
            total_ca=Sum('total_achats')
        )
        print(f"✅ Agrégation: {stats['total']} clients, CA total: {stats['total_ca'] or 0}€")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors des requêtes: {e}")
        return False

def main():
    """Fonction principale"""
    print("🐘 TEST DE CONFIGURATION POSTGRESQL POUR ABM")
    print("=" * 50)
    
    # Tests
    tests = [
        ("Connexion à la base", test_database_connection),
        ("Informations de la base", test_database_info),
        ("Modèles Django", test_models),
        ("Création de données", create_test_data),
        ("Requêtes complexes", test_queries),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 TOUS LES TESTS SONT RÉUSSIS ! PostgreSQL est correctement configuré.")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez la configuration.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
