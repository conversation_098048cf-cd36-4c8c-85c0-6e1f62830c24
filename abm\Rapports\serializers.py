from rest_framework import serializers
from .models import RapportTemplate, RapportGenere, TableauBord

class RapportTemplateSerializer(serializers.ModelSerializer):
    """Serializer pour les templates de rapports"""
    
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    nb_executions = serializers.SerializerMethodField()
    derniere_execution = serializers.SerializerMethodField()
    
    class Meta:
        model = RapportTemplate
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by')
    
    def get_nb_executions(self, obj):
        return obj.executions.count()
    
    def get_derniere_execution(self, obj):
        derniere = obj.executions.order_by('-date_generation').first()
        if derniere:
            return derniere.date_generation
        return None

class RapportGenereSerializer(serializers.ModelSerializer):
    """Serializer pour les rapports générés"""
    
    template_nom = serializers.CharField(source='template.nom', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    taille_fichier_mb = serializers.SerializerMethodField()
    
    class Meta:
        model = RapportGenere
        fields = '__all__'
        read_only_fields = ('date_generation',)
    
    def get_taille_fichier_mb(self, obj):
        if obj.taille_fichier:
            return round(obj.taille_fichier / (1024 * 1024), 2)
        return 0

class TableauBordSerializer(serializers.ModelSerializer):
    """Serializer pour les tableaux de bord"""
    
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    nb_widgets = serializers.SerializerMethodField()
    
    class Meta:
        model = TableauBord
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by')
    
    def get_nb_widgets(self, obj):
        return len(obj.widgets) if obj.widgets else 0

class RapportVentesSerializer(serializers.Serializer):
    """Serializer pour le rapport de ventes"""
    
    periode = serializers.DictField()
    resume = serializers.DictField()
    evolution_quotidienne = serializers.ListField()
    top_clients = serializers.ListField()
    top_produits = serializers.ListField()

class RapportClientsSerializer(serializers.Serializer):
    """Serializer pour le rapport clients"""
    
    total_clients = serializers.IntegerField()
    clients_actifs = serializers.IntegerField()
    taux_activite = serializers.FloatField()
    segments = serializers.ListField()
    evolution_nouveaux = serializers.ListField()

class RapportProduitsSerializer(serializers.Serializer):
    """Serializer pour le rapport produits"""
    
    total_produits = serializers.IntegerField()
    produits_actifs = serializers.IntegerField()
    produits_rupture = serializers.IntegerField()
    valeur_stock_achat = serializers.FloatField()
    valeur_stock_vente = serializers.FloatField()

class RapportFinancierSerializer(serializers.Serializer):
    """Serializer pour le rapport financier"""
    
    periode = serializers.DictField()
    ca_facture = serializers.FloatField()
    ca_encaisse = serializers.FloatField()
    factures_impayees = serializers.FloatField()
    taux_recouvrement = serializers.FloatField()
