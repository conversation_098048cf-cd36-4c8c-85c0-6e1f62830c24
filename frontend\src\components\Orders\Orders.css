/* Styles pour les composants Commandes */

/* === LAYOUT GÉNÉRAL === */
.orders-list,
.order-form,
.order-detail {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.orders-list.embedded {
  padding: 0;
  background: transparent;
  min-height: auto;
}

/* === HEADERS === */
.orders-header,
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-title h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.header-title p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

/* === BOUTONS === */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn.btn-primary {
  background: #3498db;
  color: white;
}

.btn.btn-primary:hover {
  background: #2980b9;
}

.btn.btn-success {
  background: #27ae60;
  color: white;
}

.btn.btn-success:hover {
  background: #229954;
}

.btn.btn-warning {
  background: #f39c12;
  color: white;
}

.btn.btn-warning:hover {
  background: #e67e22;
}

.btn.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn.btn-danger:hover {
  background: #c0392b;
}

.btn.btn-info {
  background: #17a2b8;
  color: white;
}

.btn.btn-info:hover {
  background: #138496;
}

.btn.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn.btn-secondary:hover {
  background: #5a6268;
}

.btn.btn-outline {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn.btn-outline:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

.btn.btn-sm {
  padding: 8px 16px;
  font-size: 0.8rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* === STATISTIQUES === */
.order-stats {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.stats-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
}

.period-selector {
  display: flex;
  gap: 4px;
  background: #f1f5f9;
  padding: 4px;
  border-radius: 8px;
}

.period-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

.period-btn.active {
  background: white;
  color: #3498db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
  border-left: 4px solid #3498db;
}
.stat-card.success {
  border-left: 4px solid #27ae60;
}
.stat-card.warning {
  border-left: 4px solid #f39c12;
}
.stat-card.danger {
  border-left: 4px solid #e74c3c;
}
.stat-card.info {
  border-left: 4px solid #17a2b8;
}
.stat-card.secondary {
  border-left: 4px solid #6c757d;
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-content h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.stat-subtitle {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0 0 8px 0;
}

.stat-progress {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s ease;
}

.progress-bar.primary {
  background: #3498db;
}
.progress-bar.success {
  background: #27ae60;
}
.progress-bar.warning {
  background: #f39c12;
}
.progress-bar.danger {
  background: #e74c3c;
}
.progress-bar.info {
  background: #17a2b8;
}
.progress-bar.secondary {
  background: #6c757d;
}

.stats-summary {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.summary-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: 700;
}

.summary-value.success {
  color: #27ae60;
}
.summary-value.warning {
  color: #f39c12;
}
.summary-value.danger {
  color: #e74c3c;
}
.summary-value.info {
  color: #17a2b8;
}

/* === TABLEAU === */
.orders-table-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
}

.orders-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e2e8f0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.orders-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background 0.3s ease;
}

.orders-table th.sortable:hover {
  background: #f1f5f9;
}

.orders-table th.sortable::after {
  content: "↕️";
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.8rem;
  opacity: 0.5;
}

.orders-table th.sorted-asc::after {
  content: "↑";
  opacity: 1;
  color: #3498db;
}

.orders-table th.sorted-desc::after {
  content: "↓";
  opacity: 1;
  color: #3498db;
}

.orders-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.9rem;
}

.order-row {
  transition: background 0.3s ease;
}

.order-row:hover {
  background: #f8fafc;
}

.order-row.selected {
  background: #eff6ff;
  border-left: 4px solid #3498db;
}

.checkbox-col {
  width: 40px;
}

.actions-col {
  width: 160px;
}

.order-number .clickable {
  color: #3498db;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
}

.order-number .clickable:hover {
  text-decoration: underline;
}

.client-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.client-name {
  font-weight: 600;
  color: #1e293b;
}

.client-email {
  font-size: 0.8rem;
  color: #64748b;
}

.delivery-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.delivery-planned {
  font-size: 0.8rem;
  color: #64748b;
}

.delivery-actual {
  font-size: 0.8rem;
  color: #27ae60;
  font-weight: 600;
}

.no-delivery {
  font-size: 0.8rem;
  color: #94a3b8;
  font-style: italic;
}

.amount {
  font-weight: 600;
  color: #1e293b;
  text-align: right;
}

/* === BADGES === */
.status-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.status-draft {
  background: #f3f4f6;
  color: #6b7280;
}

.status-badge.status-confirmed {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-badge.status-preparing {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.status-shipped {
  background: #e0e7ff;
  color: #4338ca;
}

.status-badge.status-delivered {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.status-cancelled {
  background: #fee2e2;
  color: #dc2626;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.priority-urgent {
  background: #fee2e2;
  color: #dc2626;
}

.priority-badge.priority-high {
  background: #fef3c7;
  color: #d97706;
}

.priority-badge.priority-normal {
  background: #dcfce7;
  color: #16a34a;
}

/* === ACTIONS === */
.actions {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-btn {
  padding: 6px 8px;
  background: none;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f8fafc;
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.action-btn.view-btn:hover {
  border-color: #3498db;
}
.action-btn.edit-btn:hover {
  border-color: #f39c12;
}
.action-btn.invoice-btn:hover {
  border-color: #27ae60;
}
.action-btn.delete-btn:hover {
  border-color: #e74c3c;
}

/* === ACTIONS GROUPÉES === */
.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #eff6ff;
  padding: 16px 24px;
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #bfdbfe;
}

.selected-count {
  font-weight: 600;
  color: #1e40af;
}

.bulk-buttons {
  display: flex;
  gap: 8px;
}

/* === PAGINATION === */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.pagination-btn {
  padding: 10px 20px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

/* === ÉTATS VIDES === */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 1rem;
}

/* === CHARGEMENT === */
.orders-loading,
.order-detail-loading,
.order-stats-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* === MESSAGES D'ERREUR === */
.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #fee2e2;
  color: #dc2626;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #fecaca;
}

.error-icon {
  font-size: 1.2rem;
}

.retry-btn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: auto;
}

.retry-btn:hover {
  background: #b91c1c;
}

/* === MODULE COMMANDES === */
.orders-module {
  min-height: 100vh;
  background: #f8fafc;
}

/* === ACCÈS REFUSÉ === */
.access-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px;
}

.access-denied-content {
  text-align: center;
  max-width: 400px;
}

.access-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.6;
}

.access-denied h2 {
  margin: 0 0 16px 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
}

.access-denied p {
  margin: 0 0 24px 0;
  color: #64748b;
  line-height: 1.6;
}

/* === FORMULAIRE === */
.order-form {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-steps {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.step {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 20px;
  margin: 0 8px;
  transition: all 0.3s ease;
}

.step.active {
  background: #3498db;
  color: white;
}

.step-number {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
}

.step.active .step-number {
  background: rgba(255, 255, 255, 0.3);
}

.step-label {
  font-weight: 600;
  font-size: 0.9rem;
}

.form-section {
  background: white;
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-section h3 {
  margin: 0 0 24px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  border-bottom: 2px solid #f1f5f9;
  padding-bottom: 12px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #e74c3c;
}

.form-input.readonly {
  background: #f8fafc;
  color: #64748b;
  cursor: not-allowed;
}

.form-input.readonly.total {
  font-weight: 600;
  color: #1e293b;
}

.error-text {
  color: #e74c3c;
  font-size: 0.8rem;
  font-weight: 500;
}

.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.final-actions {
  display: flex;
  gap: 12px;
}

/* === LIGNES DE COMMANDE === */
.lines-container {
  margin-bottom: 24px;
}

.order-line {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.line-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.line-number {
  font-weight: 600;
  color: #3498db;
}

.line-form {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  align-items: end;
}

.empty-lines {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
}

.empty-lines .empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-lines h4 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #374151;
}

.empty-lines p {
  margin: 0 0 24px 0;
  font-size: 1rem;
}

/* === RÉSUMÉ COMMANDE === */
.order-summary {
  background: #f8fafc;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.order-summary h4 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
}

.summary-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.summary-item label {
  font-weight: 600;
  color: #64748b;
}

.summary-lines {
  margin-bottom: 20px;
}

.summary-lines h5 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid #e2e8f0;
}

.line-product {
  font-weight: 600;
  color: #1e293b;
}

.line-quantity {
  font-size: 0.9rem;
  color: #64748b;
}

.line-total {
  font-weight: 600;
  color: #27ae60;
}

.summary-totals {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.total-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.total-line:last-child {
  border-bottom: none;
}

.total-line.final {
  border-top: 2px solid #e2e8f0;
  margin-top: 8px;
  padding-top: 12px;
  font-size: 1.1rem;
}

/* === DÉTAIL COMMANDE === */
.client-info-card {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.client-details h4 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.client-details p {
  margin: 4px 0;
  color: #64748b;
  font-size: 0.9rem;
}

.order-lines-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.lines-table {
  width: 100%;
  border-collapse: collapse;
}

.lines-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e2e8f0;
  font-size: 0.9rem;
}

.lines-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.9rem;
}

.product-name {
  color: #1e293b;
}

.product-description {
  color: #64748b;
  font-style: italic;
}

.quantity,
.unit-price,
.vat-rate,
.amount-ht,
.amount-ttc {
  text-align: right;
}

.amount-ttc {
  color: #27ae60;
}

.order-totals {
  padding: 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.totals-table {
  max-width: 300px;
  margin-left: auto;
}

/* === SUIVI LIVRAISON === */
.delivery-tracking {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.delivery-info {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.delivery-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.delivery-item:last-child {
  border-bottom: none;
}

.delivery-item label {
  font-weight: 600;
  color: #64748b;
}

.overdue {
  color: #e74c3c;
  font-weight: 600;
}

.delivered {
  color: #27ae60;
  font-weight: 600;
}

/* === TIMELINE === */
.status-timeline {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.status-timeline h4 {
  margin: 0 0 16px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.timeline {
  position: relative;
}

.timeline::before {
  content: "";
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e2e8f0;
}

.timeline-item {
  position: relative;
  padding-left: 40px;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: 6px;
  top: 8px;
  width: 12px;
  height: 12px;
  background: #3498db;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #3498db;
}

.timeline-content {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.timeline-status {
  margin-bottom: 8px;
}

.timeline-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.timeline-date {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
}

.timeline-user {
  font-size: 0.8rem;
  color: #64748b;
}

.timeline-comment {
  margin: 8px 0 0 0;
  font-size: 0.9rem;
  color: #374151;
  font-style: italic;
}

/* === BADGES SPÉCIAUX === */
.overdue-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  background: #fee2e2;
  color: #dc2626;
  text-transform: uppercase;
}

/* === NOTES === */
.order-notes {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.order-notes p {
  margin: 0;
  color: #374151;
  line-height: 1.6;
  font-style: italic;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .orders-list,
  .order-form,
  .order-detail {
    padding: 15px;
  }

  .orders-header,
  .detail-header,
  .form-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .line-form {
    grid-template-columns: 1fr;
  }

  .delivery-tracking {
    grid-template-columns: 1fr;
  }

  .orders-table-container {
    overflow-x: auto;
  }

  .orders-table {
    min-width: 800px;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .step-actions,
  .detail-footer {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .final-actions {
    justify-content: center;
  }

  .order-details-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .detail-section {
    padding: 15px;
  }

  .quick-actions {
    gap: 8px;
  }

  .quick-action-btn {
    padding: 10px 16px;
    font-size: 0.8rem;
    min-width: 100px;
  }

  .amount-display {
    padding: 12px;
  }

  .amount-value {
    font-size: 1.2rem;
  }
}

/* === MODAL DÉTAILS DE COMMANDE === */
.order-details-modal {
  max-width: 900px;
  width: 90%;
}

.order-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.detail-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

.detail-value {
  color: #2d3748;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-wrap: break-word;
}

.amount-display {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  margin-top: 12px;
}

.amount-label {
  font-size: 0.9rem;
  display: block;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 1.5rem;
  font-weight: 700;
  display: block;
}

.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.quick-action-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s;
  min-width: 120px;
}

.quick-action-btn:hover:not(:disabled) {
  background: #2c5282;
  transform: translateY(-1px);
}

.quick-action-btn:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  opacity: 0.6;
}

.urgency.urgent {
  color: #e53e3e;
  font-weight: 700;
}

.urgency.normal {
  color: #38a169;
  font-weight: 600;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.confirmee {
  background: #bee3f8;
  color: #2b6cb0;
}

.status.en-preparation {
  background: #fef3c7;
  color: #d97706;
}

.status.expediee {
  background: #e6fffa;
  color: #319795;
}

.status.livree {
  background: #dcfce7;
  color: #16a34a;
}

.status.annulee {
  background: #fee2e2;
  color: #dc2626;
}
