import React, { createContext, useContext, useState, useCallback } from "react";
import "./NotificationSystem.css";

export interface Notification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, "id">) => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export { NotificationContext };

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const removeNotification = useCallback((id: string) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== id)
    );
  }, []);

  const addNotification = useCallback(
    (notification: Omit<Notification, "id">) => {
      const id =
        Date.now().toString() + Math.random().toString(36).substr(2, 9);
      const duration = notification.duration || 5000;
      const newNotification: Notification = {
        ...notification,
        id,
        duration,
      };

      setNotifications((prev) => [...prev, newNotification]);

      // Auto-remove after duration
      if (duration > 0) {
        setTimeout(() => {
          removeNotification(id);
        }, duration);
      }
    },
    [removeNotification]
  );

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        addNotification,
        removeNotification,
        clearAll,
      }}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  );
};

const NotificationContainer: React.FC = () => {
  const { notifications, removeNotification } = useNotifications();

  const getNotificationConfig = (type: Notification["type"]) => {
    switch (type) {
      case "success":
        return {
          icon: "✅",
          className: "notification-success",
          color: "#2ed573",
        };
      case "error":
        return {
          icon: "❌",
          className: "notification-error",
          color: "#ff4757",
        };
      case "warning":
        return {
          icon: "⚠️",
          className: "notification-warning",
          color: "#ffa502",
        };
      case "info":
        return {
          icon: "ℹ️",
          className: "notification-info",
          color: "#3742fa",
        };
      default:
        return {
          icon: "ℹ️",
          className: "notification-info",
          color: "#3742fa",
        };
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="notification-container">
      {notifications.map((notification) => {
        const config = getNotificationConfig(notification.type);

        return (
          <div
            key={notification.id}
            className={`notification ${config.className}`}>
            <div className="notification-content">
              <div className="notification-header">
                <div className="notification-icon">{config.icon}</div>
                <div className="notification-title">{notification.title}</div>
                <button
                  className="notification-close"
                  onClick={() => removeNotification(notification.id)}>
                  ✕
                </button>
              </div>

              {notification.message && (
                <div className="notification-message">
                  {notification.message}
                </div>
              )}

              {notification.action && (
                <div className="notification-actions">
                  <button
                    className="notification-action-btn"
                    onClick={() => {
                      notification.action!.onClick();
                      removeNotification(notification.id);
                    }}>
                    {notification.action.label}
                  </button>
                </div>
              )}
            </div>

            <div
              className="notification-progress"
              style={{
                animationDuration: `${notification.duration}ms`,
                backgroundColor: config.color,
              }}
            />
          </div>
        );
      })}
    </div>
  );
};

// Hook utilitaire pour les notifications courantes
export const useNotify = () => {
  const { addNotification } = useNotifications();

  return {
    success: (title: string, message?: string) =>
      addNotification({ type: "success", title, message }),

    error: (title: string, message?: string) =>
      addNotification({ type: "error", title, message }),

    warning: (title: string, message?: string) =>
      addNotification({ type: "warning", title, message }),

    info: (title: string, message?: string) =>
      addNotification({ type: "info", title, message }),

    custom: (notification: Omit<Notification, "id">) =>
      addNotification(notification),
  };
};
