import React, { useState } from "react";
import { toast } from "react-toastify";
import { ClientService } from "../../services/apiService";
import "./NewClient.css";

interface NewClientData {
  nom: string;
  email: string;
  telephone: string;
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
  type_client: "PARTICULIER" | "ENTREPRISE";
  siret?: string;
  tva_intracommunautaire?: string;
  notes: string;
}

interface NewClientProps {
  onSuccess?: () => void;
}

const NewClient: React.FC<NewClientProps> = ({ onSuccess }) => {
  const navigateBack = () => {
    // Déclencher le callback de rafraîchissement si fourni
    if (onSuccess) {
      onSuccess();
    }

    // Navigation avec rafraîchissement forcé
    window.history.pushState({}, "", "/clients");
    window.location.reload();
  };
  const [clientData, setClientData] = useState<NewClientData>({
    nom: "",
    email: "",
    telephone: "",
    adresse: "",
    ville: "",
    code_postal: "",
    pays: "Tunisie",
    type_client: "PARTICULIER",
    siret: "",
    tva_intracommunautaire: "",
    notes: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!clientData.nom.trim()) {
      newErrors.nom = "Le nom est obligatoire";
    }

    if (!clientData.email.trim()) {
      newErrors.email = "L'email est obligatoire";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(clientData.email)) {
      newErrors.email = "Format d'email invalide";
    }

    if (!clientData.telephone.trim()) {
      newErrors.telephone = "Le téléphone est obligatoire";
    }

    if (!clientData.adresse.trim()) {
      newErrors.adresse = "L'adresse est obligatoire";
    }

    if (!clientData.ville.trim()) {
      newErrors.ville = "La ville est obligatoire";
    }

    // SIRET supprimé car non applicable en Tunisie

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof NewClientData, value: string) => {
    setClientData((prev) => ({ ...prev, [field]: value }));
    // Supprimer l'erreur si le champ est maintenant valide
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast.error("Veuillez corriger les erreurs dans le formulaire");
      return;
    }

    setIsLoading(true);

    try {
      const response = await ClientService.createClient({
        ...clientData,
        prenom: clientData.type_client === "PARTICULIER" ? "Client" : "Société",
        chiffre_affaires_total: 0,
        statut: "ACTIF",
      });

      if (response.data) {
        const clientCree = response.data;

        // Notification de succès avec détails
        toast.success(
          `✅ Client créé avec succès !\n` +
            `📝 Nom: ${clientData.nom}\n` +
            `📧 Email: ${clientData.email}\n` +
            `🆔 ID: ${clientCree.id || "N/A"}`,
          {
            autoClose: 5000,
            position: "top-right",
          }
        );

        // Vérification immédiate en base
        setTimeout(async () => {
          try {
            const verification = await ClientService.getClient(clientCree.id);
            if (verification.success && verification.data) {
              console.log("✅ Client vérifié en base:", verification.data);
              toast.info(
                `🔍 Client ${clientData.nom} confirmé en base de données`,
                {
                  autoClose: 3000,
                }
              );
            } else {
              console.warn("⚠️ Client non trouvé lors de la vérification");
              toast.warning("⚠️ Client créé mais vérification échouée");
            }
          } catch (verificationError) {
            console.error("Erreur lors de la vérification:", verificationError);
          }
        }, 1000);

        navigateBack();
      } else if (response.error) {
        toast.error(`❌ Erreur: ${response.error}`);
      }
    } catch (error) {
      console.error("Erreur lors de la création du client:", error);
      toast.error("❌ Erreur lors de la création du client");
    } finally {
      setIsLoading(false);
    }
  };

  const goBack = () => {
    navigateBack();
  };

  return (
    <div className="new-client-container">
      <div className="new-client-header">
        <div className="header-left">
          <button
            className="btn-back"
            onClick={goBack}>
            ← Retour
          </button>
          <h1>👥 Nouveau Client</h1>
        </div>
        <div className="header-actions">
          <button
            className="btn btn-secondary"
            onClick={goBack}>
            Annuler
          </button>
          <button
            className="btn btn-primary"
            onClick={handleSave}
            disabled={isLoading}>
            {isLoading ? "⏳ Enregistrement..." : "💾 Enregistrer"}
          </button>
        </div>
      </div>

      <div className="new-client-content">
        <div className="client-form">
          {/* Type de client */}
          <div className="form-section">
            <h3>🏷️ Type de Client</h3>
            <div className="client-type-selector">
              <label
                className={`type-option ${
                  clientData.type_client === "PARTICULIER" ? "selected" : ""
                }`}>
                <input
                  type="radio"
                  name="type_client"
                  value="PARTICULIER"
                  checked={clientData.type_client === "PARTICULIER"}
                  onChange={(e) =>
                    handleInputChange(
                      "type_client",
                      e.target.value as "PARTICULIER" | "ENTREPRISE"
                    )
                  }
                />
                <div className="type-content">
                  <span className="type-icon">👤</span>
                  <span className="type-label">Particulier</span>
                </div>
              </label>
              <label
                className={`type-option ${
                  clientData.type_client === "ENTREPRISE" ? "selected" : ""
                }`}>
                <input
                  type="radio"
                  name="type_client"
                  value="ENTREPRISE"
                  checked={clientData.type_client === "ENTREPRISE"}
                  onChange={(e) =>
                    handleInputChange(
                      "type_client",
                      e.target.value as "PARTICULIER" | "ENTREPRISE"
                    )
                  }
                />
                <div className="type-content">
                  <span className="type-icon">🏢</span>
                  <span className="type-label">Entreprise</span>
                </div>
              </label>
            </div>
          </div>

          {/* Informations générales */}
          <div className="form-section">
            <h3>📋 Informations Générales</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>
                  {clientData.type_client === "ENTREPRISE"
                    ? "Raison sociale"
                    : "Nom complet"}{" "}
                  *
                </label>
                <input
                  type="text"
                  value={clientData.nom}
                  onChange={(e) => handleInputChange("nom", e.target.value)}
                  className={`form-input ${errors.nom ? "error" : ""}`}
                  placeholder={
                    clientData.type_client === "ENTREPRISE"
                      ? "Nom de l'entreprise"
                      : "Prénom et nom"
                  }
                />
                {errors.nom && (
                  <span className="error-message">{errors.nom}</span>
                )}
              </div>

              <div className="form-group">
                <label>Email *</label>
                <input
                  type="email"
                  value={clientData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className={`form-input ${errors.email ? "error" : ""}`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <span className="error-message">{errors.email}</span>
                )}
              </div>

              <div className="form-group">
                <label>Téléphone *</label>
                <input
                  type="tel"
                  value={clientData.telephone}
                  onChange={(e) =>
                    handleInputChange("telephone", e.target.value)
                  }
                  className={`form-input ${errors.telephone ? "error" : ""}`}
                  placeholder="+216 XX XXX XXX"
                />
                {errors.telephone && (
                  <span className="error-message">{errors.telephone}</span>
                )}
              </div>
            </div>
          </div>

          {/* Informations entreprise */}
          {clientData.type_client === "ENTREPRISE" && (
            <div className="form-section">
              <h3>🏢 Informations Entreprise</h3>
              <div className="form-grid">
                <div className="form-group">
                  <label>Numéro d'identification fiscale</label>
                  <input
                    type="text"
                    value={clientData.siret || ""}
                    onChange={(e) => handleInputChange("siret", e.target.value)}
                    className="form-input"
                    placeholder="Numéro d'identification fiscale (optionnel)"
                  />
                  <small className="form-help">
                    Numéro d'identification fiscale de l'entreprise (optionnel)
                  </small>
                </div>

                <div className="form-group">
                  <label>TVA Intracommunautaire</label>
                  <input
                    type="text"
                    value={clientData.tva_intracommunautaire || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "tva_intracommunautaire",
                        e.target.value
                      )
                    }
                    className="form-input"
                    placeholder="Numéro TVA"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Adresse */}
          <div className="form-section">
            <h3>📍 Adresse</h3>
            <div className="form-grid">
              <div className="form-group full-width">
                <label>Adresse *</label>
                <textarea
                  value={clientData.adresse}
                  onChange={(e) => handleInputChange("adresse", e.target.value)}
                  className={`form-textarea ${errors.adresse ? "error" : ""}`}
                  rows={3}
                  placeholder="Adresse complète"
                />
                {errors.adresse && (
                  <span className="error-message">{errors.adresse}</span>
                )}
              </div>

              <div className="form-group">
                <label>Ville *</label>
                <input
                  type="text"
                  value={clientData.ville}
                  onChange={(e) => handleInputChange("ville", e.target.value)}
                  className={`form-input ${errors.ville ? "error" : ""}`}
                  placeholder="Ville"
                />
                {errors.ville && (
                  <span className="error-message">{errors.ville}</span>
                )}
              </div>

              <div className="form-group">
                <label>Code postal</label>
                <input
                  type="text"
                  value={clientData.code_postal}
                  onChange={(e) =>
                    handleInputChange("code_postal", e.target.value)
                  }
                  className="form-input"
                  placeholder="Code postal"
                />
              </div>

              <div className="form-group">
                <label>Pays</label>
                <select
                  value={clientData.pays}
                  onChange={(e) => handleInputChange("pays", e.target.value)}
                  className="form-input">
                  <option value="Tunisie">Tunisie</option>
                  <option value="France">France</option>
                  <option value="Algérie">Algérie</option>
                  <option value="Maroc">Maroc</option>
                  <option value="Autre">Autre</option>
                </select>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="form-section">
            <h3>📝 Notes</h3>
            <textarea
              value={clientData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              className="form-textarea"
              rows={4}
              placeholder="Notes additionnelles sur le client..."
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewClient;
