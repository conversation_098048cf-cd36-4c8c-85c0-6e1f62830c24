"""
Moteur de workflow intelligent pour automatiser les processus métier
"""
from enum import Enum
from typing import Dict, List, Any, Callable, Optional
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from Core.models import BaseModel, SmartNotification, AIInsight
from Core.websocket_service import NotificationManager
import json
import logging

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """États des workflows"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"
    PAUSED = "PAUSED"


class WorkflowPriority(Enum):
    """Priorités des workflows"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class WorkflowStep:
    """Étape d'un workflow"""
    
    def __init__(
        self,
        name: str,
        action: Callable,
        conditions: List[Callable] = None,
        retry_count: int = 3,
        timeout: int = 300,
        rollback_action: Callable = None
    ):
        self.name = name
        self.action = action
        self.conditions = conditions or []
        self.retry_count = retry_count
        self.timeout = timeout
        self.rollback_action = rollback_action
        self.executed = False
        self.result = None
        self.error = None
        self.execution_time = None
    
    def can_execute(self, context: Dict[str, Any]) -> bool:
        """Vérifie si l'étape peut être exécutée"""
        for condition in self.conditions:
            if not condition(context):
                return False
        return True
    
    def execute(self, context: Dict[str, Any]) -> Any:
        """Exécute l'étape"""
        start_time = timezone.now()
        
        try:
            logger.info(f"Exécution de l'étape: {self.name}")
            self.result = self.action(context)
            self.executed = True
            self.execution_time = (timezone.now() - start_time).total_seconds()
            logger.info(f"Étape {self.name} terminée en {self.execution_time:.2f}s")
            return self.result
            
        except Exception as e:
            self.error = str(e)
            self.execution_time = (timezone.now() - start_time).total_seconds()
            logger.error(f"Erreur dans l'étape {self.name}: {e}")
            raise
    
    def rollback(self, context: Dict[str, Any]):
        """Annule les effets de l'étape"""
        if self.rollback_action and self.executed:
            try:
                logger.info(f"Rollback de l'étape: {self.name}")
                self.rollback_action(context)
            except Exception as e:
                logger.error(f"Erreur lors du rollback de {self.name}: {e}")


class Workflow:
    """Workflow intelligent"""
    
    def __init__(
        self,
        name: str,
        description: str = "",
        priority: WorkflowPriority = WorkflowPriority.MEDIUM,
        max_execution_time: int = 3600,
        auto_retry: bool = True
    ):
        self.name = name
        self.description = description
        self.priority = priority
        self.max_execution_time = max_execution_time
        self.auto_retry = auto_retry
        self.steps: List[WorkflowStep] = []
        self.status = WorkflowStatus.PENDING
        self.context: Dict[str, Any] = {}
        self.start_time = None
        self.end_time = None
        self.current_step_index = 0
        self.execution_log = []
        self.error_message = None
    
    def add_step(self, step: WorkflowStep):
        """Ajoute une étape au workflow"""
        self.steps.append(step)
    
    def set_context(self, context: Dict[str, Any]):
        """Définit le contexte d'exécution"""
        self.context.update(context)
    
    def execute(self) -> bool:
        """Exécute le workflow"""
        self.status = WorkflowStatus.RUNNING
        self.start_time = timezone.now()
        
        try:
            logger.info(f"Démarrage du workflow: {self.name}")
            
            with transaction.atomic():
                for i, step in enumerate(self.steps):
                    self.current_step_index = i
                    
                    # Vérifier les conditions
                    if not step.can_execute(self.context):
                        self._log(f"Étape {step.name} ignorée (conditions non remplies)")
                        continue
                    
                    # Exécuter l'étape avec retry
                    retry_count = 0
                    while retry_count <= step.retry_count:
                        try:
                            result = step.execute(self.context)
                            self.context[f"step_{i}_result"] = result
                            self._log(f"Étape {step.name} réussie")
                            break
                            
                        except Exception as e:
                            retry_count += 1
                            if retry_count > step.retry_count:
                                raise
                            
                            self._log(f"Étape {step.name} échouée, retry {retry_count}/{step.retry_count}")
                            if not self.auto_retry:
                                raise
            
            self.status = WorkflowStatus.COMPLETED
            self.end_time = timezone.now()
            self._log("Workflow terminé avec succès")
            
            # Créer une notification de succès
            NotificationManager.create_smart_notification(
                type_notification='WORKFLOW_SUCCESS',
                titre=f'Workflow Terminé',
                message=f'Le workflow "{self.name}" s\'est terminé avec succès',
                priorite='MEDIUM'
            )
            
            return True
            
        except Exception as e:
            self.status = WorkflowStatus.FAILED
            self.end_time = timezone.now()
            self.error_message = str(e)
            self._log(f"Workflow échoué: {e}")
            
            # Rollback des étapes exécutées
            self._rollback()
            
            # Créer une notification d'erreur
            NotificationManager.create_smart_notification(
                type_notification='WORKFLOW_ERROR',
                titre=f'Workflow Échoué',
                message=f'Le workflow "{self.name}" a échoué: {str(e)}',
                priorite='HIGH'
            )
            
            return False
    
    def _rollback(self):
        """Annule les étapes exécutées"""
        logger.info(f"Rollback du workflow: {self.name}")
        
        # Rollback dans l'ordre inverse
        for step in reversed(self.steps[:self.current_step_index + 1]):
            if step.executed:
                step.rollback(self.context)
    
    def _log(self, message: str):
        """Ajoute un message au log d'exécution"""
        log_entry = {
            'timestamp': timezone.now().isoformat(),
            'message': message
        }
        self.execution_log.append(log_entry)
        logger.info(f"[{self.name}] {message}")
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Récupère un résumé de l'exécution"""
        duration = None
        if self.start_time and self.end_time:
            duration = (self.end_time - self.start_time).total_seconds()
        
        return {
            'name': self.name,
            'description': self.description,
            'status': self.status.value,
            'priority': self.priority.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': duration,
            'steps_total': len(self.steps),
            'steps_executed': sum(1 for step in self.steps if step.executed),
            'error_message': self.error_message,
            'execution_log': self.execution_log
        }


class WorkflowEngine:
    """Moteur de workflow intelligent"""
    
    def __init__(self):
        self.workflows: Dict[str, Workflow] = {}
        self.running_workflows: List[str] = []
        self.workflow_history: List[Dict[str, Any]] = []
    
    def register_workflow(self, workflow: Workflow):
        """Enregistre un workflow"""
        self.workflows[workflow.name] = workflow
        logger.info(f"Workflow enregistré: {workflow.name}")
    
    def execute_workflow(self, workflow_name: str, context: Dict[str, Any] = None) -> bool:
        """Exécute un workflow"""
        if workflow_name not in self.workflows:
            raise ValueError(f"Workflow non trouvé: {workflow_name}")
        
        workflow = self.workflows[workflow_name]
        
        if context:
            workflow.set_context(context)
        
        self.running_workflows.append(workflow_name)
        
        try:
            success = workflow.execute()
            
            # Ajouter à l'historique
            self.workflow_history.append(workflow.get_execution_summary())
            
            return success
            
        finally:
            if workflow_name in self.running_workflows:
                self.running_workflows.remove(workflow_name)
    
    def get_workflow_status(self, workflow_name: str) -> Dict[str, Any]:
        """Récupère le statut d'un workflow"""
        if workflow_name not in self.workflows:
            return {'error': 'Workflow non trouvé'}
        
        workflow = self.workflows[workflow_name]
        return workflow.get_execution_summary()
    
    def get_running_workflows(self) -> List[str]:
        """Récupère la liste des workflows en cours"""
        return self.running_workflows.copy()
    
    def get_workflow_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Récupère l'historique des workflows"""
        return self.workflow_history[-limit:]


# Instance globale du moteur de workflow
workflow_engine = WorkflowEngine()


# Workflows prédéfinis pour l'application ABM
class ABMWorkflows:
    """Workflows spécifiques à l'application ABM"""
    
    @staticmethod
    def create_invoice_processing_workflow():
        """Workflow de traitement des factures"""
        workflow = Workflow(
            name="invoice_processing",
            description="Traitement automatique des factures",
            priority=WorkflowPriority.HIGH
        )
        
        # Étape 1: Validation des données
        def validate_invoice_data(context):
            facture = context.get('facture')
            if not facture:
                raise ValueError("Facture manquante")
            
            if not facture.client:
                raise ValueError("Client manquant")
            
            if facture.montant_total <= 0:
                raise ValueError("Montant invalide")
            
            return True
        
        workflow.add_step(WorkflowStep(
            name="validate_data",
            action=validate_invoice_data
        ))
        
        # Étape 2: Calcul des taxes
        def calculate_taxes(context):
            facture = context['facture']
            # Logique de calcul des taxes
            facture.montant_tva = facture.montant_ht * 0.19  # TVA 19%
            facture.montant_total = facture.montant_ht + facture.montant_tva
            facture.save()
            return facture.montant_tva
        
        workflow.add_step(WorkflowStep(
            name="calculate_taxes",
            action=calculate_taxes
        ))
        
        # Étape 3: Mise à jour du stock
        def update_stock(context):
            facture = context['facture']
            for ligne in facture.lignes.all():
                produit = ligne.produit
                # Décrémenter le stock
                stock_entry = produit.stock.first()
                if stock_entry:
                    stock_entry.quantite -= ligne.quantite
                    stock_entry.save()
            return True
        
        workflow.add_step(WorkflowStep(
            name="update_stock",
            action=update_stock,
            rollback_action=lambda ctx: ABMWorkflows._rollback_stock_update(ctx)
        ))
        
        # Étape 4: Notification client
        def notify_client(context):
            facture = context['facture']
            NotificationManager.create_smart_notification(
                type_notification='INVOICE_CREATED',
                titre='Nouvelle Facture',
                message=f'Facture {facture.numero} créée pour {facture.client.nom}',
                priorite='MEDIUM'
            )
            return True
        
        workflow.add_step(WorkflowStep(
            name="notify_client",
            action=notify_client
        ))
        
        return workflow
    
    @staticmethod
    def create_stock_replenishment_workflow():
        """Workflow de réapprovisionnement automatique"""
        workflow = Workflow(
            name="stock_replenishment",
            description="Réapprovisionnement automatique des stocks",
            priority=WorkflowPriority.MEDIUM
        )
        
        # Étape 1: Identifier les produits à réapprovisionner
        def identify_low_stock_products(context):
            from Facturation.models import Produit
            from django.db.models import Sum, F
            
            low_stock_products = Produit.objects.filter(
                is_active=True
            ).annotate(
                stock_total=Sum('stock__quantite')
            ).filter(
                stock_total__lte=F('stock_minimum')
            )
            
            context['low_stock_products'] = list(low_stock_products)
            return len(low_stock_products)
        
        workflow.add_step(WorkflowStep(
            name="identify_low_stock",
            action=identify_low_stock_products
        ))
        
        # Étape 2: Créer les commandes de réapprovisionnement
        def create_replenishment_orders(context):
            products = context.get('low_stock_products', [])
            orders_created = 0
            
            for product in products:
                # Logique de création de commande
                quantity_to_order = max(
                    product.stock_minimum * 2 - product.stock_actuel,
                    product.stock_minimum
                )
                
                # Ici, on créerait une commande fournisseur
                # Pour l'exemple, on crée juste une notification
                NotificationManager.create_smart_notification(
                    type_notification='STOCK_ORDER',
                    titre='Commande de Réapprovisionnement',
                    message=f'Commander {quantity_to_order} unités de {product.nom}',
                    priorite='MEDIUM'
                )
                
                orders_created += 1
            
            return orders_created
        
        workflow.add_step(WorkflowStep(
            name="create_orders",
            action=create_replenishment_orders,
            conditions=[lambda ctx: len(ctx.get('low_stock_products', [])) > 0]
        ))
        
        return workflow
    
    @staticmethod
    def create_daily_analytics_workflow():
        """Workflow d'analyse quotidienne"""
        workflow = Workflow(
            name="daily_analytics",
            description="Analyse quotidienne des performances",
            priority=WorkflowPriority.LOW
        )
        
        # Étape 1: Générer les métriques
        def generate_daily_metrics(context):
            from Core.analytics import AnalyticsService
            
            metrics = AnalyticsService.get_dashboard_metrics()
            context['daily_metrics'] = metrics
            return metrics
        
        workflow.add_step(WorkflowStep(
            name="generate_metrics",
            action=generate_daily_metrics
        ))
        
        # Étape 2: Générer les insights IA
        def generate_ai_insights(context):
            from Core.analytics import AnalyticsService
            
            insights = AnalyticsService.generate_ai_insights()
            context['ai_insights'] = insights
            return len(insights)
        
        workflow.add_step(WorkflowStep(
            name="generate_insights",
            action=generate_ai_insights
        ))
        
        # Étape 3: Créer le rapport quotidien
        def create_daily_report(context):
            metrics = context['daily_metrics']
            insights = context['ai_insights']
            
            report = {
                'date': timezone.now().date().isoformat(),
                'metrics': metrics,
                'insights_count': len(insights),
                'key_insights': [i for i in insights if i.get('priority') == 'HIGH']
            }
            
            # Sauvegarder le rapport (ici on crée juste une notification)
            NotificationManager.create_smart_notification(
                type_notification='DAILY_REPORT',
                titre='Rapport Quotidien',
                message=f'Rapport du {report["date"]} généré avec {len(insights)} insights',
                priorite='LOW'
            )
            
            return report
        
        workflow.add_step(WorkflowStep(
            name="create_report",
            action=create_daily_report
        ))
        
        return workflow
    
    @staticmethod
    def _rollback_stock_update(context):
        """Rollback de la mise à jour du stock"""
        facture = context['facture']
        for ligne in facture.lignes.all():
            produit = ligne.produit
            stock_entry = produit.stock.first()
            if stock_entry:
                stock_entry.quantite += ligne.quantite
                stock_entry.save()


# Initialisation des workflows ABM
def initialize_abm_workflows():
    """Initialise les workflows ABM"""
    workflows = [
        ABMWorkflows.create_invoice_processing_workflow(),
        ABMWorkflows.create_stock_replenishment_workflow(),
        ABMWorkflows.create_daily_analytics_workflow()
    ]
    
    for workflow in workflows:
        workflow_engine.register_workflow(workflow)
    
    logger.info(f"{len(workflows)} workflows ABM initialisés")


# Tâches périodiques pour les workflows
class WorkflowScheduler:
    """Planificateur de workflows"""
    
    @staticmethod
    def run_daily_workflows():
        """Exécute les workflows quotidiens"""
        try:
            workflow_engine.execute_workflow('daily_analytics')
            workflow_engine.execute_workflow('stock_replenishment')
        except Exception as e:
            logger.error(f"Erreur lors de l'exécution des workflows quotidiens: {e}")
    
    @staticmethod
    def run_invoice_workflow(facture):
        """Exécute le workflow de traitement de facture"""
        try:
            workflow_engine.execute_workflow('invoice_processing', {'facture': facture})
        except Exception as e:
            logger.error(f"Erreur lors du traitement de la facture {facture.numero}: {e}")


# Initialiser les workflows au démarrage
initialize_abm_workflows()
