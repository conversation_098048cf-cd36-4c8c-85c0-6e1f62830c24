import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useC<PERSON>back,
  useMemo,
  ReactNode,
} from "react";
import { toast } from "react-toastify";

export type UserRole = "NORMAL" | "COMPTABLE" | "ADMIN" | "SUPERADMIN";

interface User {
  id: string;
  email: string;
  nom: string;
  prenom: string;
  role: UserRole;
  permissions: string[];
  modules: string[];
  statut: "ACTIF" | "INACTIF" | "SUSPENDU";
  derniere_connexion: string;
  avatar?: string;
}

interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

interface RegisterCredentials {
  email: string;
  password: string;
  confirmPassword: string;
  prenom: string;
  nom: string;
  role?: UserRole;
  phone?: string;
  company?: string;
}

interface AuthResult {
  success: boolean;
  message: string;
  errors?: Record<string, string[]>;
  user?: User;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  sessionExpiry: Date | null;
  login: (credentials: LoginCredentials) => Promise<AuthResult>;
  register: (credentials: RegisterCredentials) => Promise<AuthResult>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: UserRole) => boolean;
  canAccessModule: (module: string) => boolean;
  hasModuleAccess: (module: string) => boolean;
  hasMinimumRole: (role: UserRole) => boolean;
  getRoleHierarchy: () => number;
  extendSession: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Configuration
const AUTH_CONFIG = {
  STORAGE_KEY: "abm_auth_token",
  USER_KEY: "abm_user_data",
  REMEMBER_KEY: "abm_remember_me",
  SESSION_DURATION: 8 * 60 * 60 * 1000, // 8 heures
};

// Hiérarchie des rôles
const ROLE_HIERARCHY: Record<UserRole, number> = {
  NORMAL: 1,
  COMPTABLE: 2,
  ADMIN: 3,
  SUPERADMIN: 4,
};

// Permissions par module
const MODULE_PERMISSIONS: Record<string, UserRole[]> = {
  dashboard: ["NORMAL", "COMPTABLE", "ADMIN", "SUPERADMIN"],
  clients: ["COMPTABLE", "ADMIN", "SUPERADMIN"],
  produits: ["COMPTABLE", "ADMIN", "SUPERADMIN"],
  factures: ["COMPTABLE", "ADMIN", "SUPERADMIN"],
  commandes: ["COMPTABLE", "ADMIN", "SUPERADMIN"],
  paiements: ["COMPTABLE", "ADMIN", "SUPERADMIN"],
  stock: ["COMPTABLE", "ADMIN", "SUPERADMIN"],
  rapports: ["COMPTABLE", "ADMIN", "SUPERADMIN"],
  settings: ["ADMIN", "SUPERADMIN"],
  users: ["SUPERADMIN"],
};

// Utilisateurs de démonstration
const demoUsers: Record<string, User> = {
  "<EMAIL>": {
    id: "1",
    email: "<EMAIL>",
    nom: "Admin",
    prenom: "Super",
    role: "SUPERADMIN",
    permissions: ["read", "write", "delete", "admin"],
    modules: Object.keys(MODULE_PERMISSIONS),
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "2",
    email: "<EMAIL>",
    nom: "Manager",
    prenom: "Principal",
    role: "ADMIN",
    permissions: ["read", "write", "delete"],
    modules: [
      "dashboard",
      "clients",
      "produits",
      "factures",
      "commandes",
      "paiements",
      "stock",
      "rapports",
      "settings",
    ],
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "3",
    email: "<EMAIL>",
    nom: "Comptable",
    prenom: "Expert",
    role: "COMPTABLE",
    permissions: ["read", "write"],
    modules: [
      "dashboard",
      "clients",
      "produits",
      "factures",
      "commandes",
      "paiements",
      "stock",
      "rapports",
    ],
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "4",
    email: "<EMAIL>",
    nom: "Utilisateur",
    prenom: "Normal",
    role: "NORMAL",
    permissions: ["read"],
    modules: ["dashboard"],
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);

  // Vérification de l'authentification au chargement
  useEffect(() => {
    const token = localStorage.getItem(AUTH_CONFIG.STORAGE_KEY);
    const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);

    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);

        // Vérifier l'expiration de la session
        if (parsedUser.tokenExpiry) {
          const expiry = new Date(parsedUser.tokenExpiry);
          if (expiry > new Date()) {
            setSessionExpiry(expiry);
          } else {
            // Session expirée
            logout();
          }
        }
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des données utilisateur:",
          error
        );
        logout();
      }
    }
  }, []);

  // Auto-déconnexion à l'expiration
  useEffect(() => {
    if (sessionExpiry) {
      const timeUntilExpiry = sessionExpiry.getTime() - Date.now();
      if (timeUntilExpiry > 0) {
        const timer = setTimeout(() => {
          toast.warning("Votre session a expiré. Veuillez vous reconnecter.");
          logout();
        }, timeUntilExpiry);

        return () => clearTimeout(timer);
      }
    }
  }, [user, sessionExpiry]);

  // Fonction de connexion
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const foundUser = demoUsers[credentials.email];

      if (!foundUser || credentials.password !== "123456") {
        return {
          success: false,
          message: "Email ou mot de passe incorrect",
          errors: { email: ["Identifiants invalides"] },
        };
      }

      if (foundUser.statut !== "ACTIF") {
        return {
          success: false,
          message: "Compte suspendu ou inactif",
          errors: { account: ["Compte non autorisé"] },
        };
      }

      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);
      const token = `abm_token_${foundUser.id}_${Date.now()}`;

      const userWithToken = {
        ...foundUser,
        tokenExpiry: expiry.toISOString(),
      };

      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);
      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));

      if (credentials.remember) {
        localStorage.setItem(AUTH_CONFIG.REMEMBER_KEY, "true");
      }

      setUser(foundUser);
      setSessionExpiry(expiry);

      toast.success(`Bienvenue ${foundUser.prenom} ${foundUser.nom} !`);

      return {
        success: true,
        message: "Connexion réussie",
      };
    } catch (error) {
      console.error("Erreur lors de la connexion:", error);
      return {
        success: false,
        message: "Erreur de connexion. Veuillez réessayer.",
        errors: { general: ["Erreur système"] },
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fonction d'inscription (simulation)
  const register = useCallback(async (credentials: RegisterCredentials) => {
    try {
      setIsLoading(true);

      // Validation côté client
      if (credentials.password !== credentials.confirmPassword) {
        return {
          success: false,
          message: "Les mots de passe ne correspondent pas",
          errors: {
            confirmPassword: ["Les mots de passe ne correspondent pas"],
          },
        };
      }

      if (credentials.password.length < 6) {
        return {
          success: false,
          message: "Le mot de passe doit contenir au moins 6 caractères",
          errors: {
            password: ["Le mot de passe doit contenir au moins 6 caractères"],
          },
        };
      }

      // Vérifier si l'email existe déjà
      const existingUser = Object.values(demoUsers).find(
        (user) => user.email === credentials.email
      );
      if (existingUser) {
        return {
          success: false,
          message: "Cet email est déjà utilisé",
          errors: { email: ["Cet email est déjà utilisé"] },
        };
      }

      // Simulation d'une API d'inscription
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Créer un nouvel utilisateur
      const newUser: User = {
        id: String(Date.now()),
        email: credentials.email,
        nom: credentials.nom,
        prenom: credentials.prenom,
        role: credentials.role || "NORMAL",
        permissions: ["read"],
        modules: ["dashboard"],
        statut: "ACTIF",
        derniere_connexion: new Date().toISOString(),
      };

      // Ajouter l'utilisateur aux utilisateurs de démo
      demoUsers[credentials.email] = newUser;

      // Créer automatiquement une session pour le nouvel utilisateur
      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);
      const token = `abm_token_${newUser.id}_${Date.now()}`;

      const userWithToken = {
        ...newUser,
        tokenExpiry: expiry.toISOString(),
      };

      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);
      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));

      setUser(newUser);
      setSessionExpiry(expiry);

      toast.success(
        `Bienvenue ${newUser.prenom} ${newUser.nom} ! Votre compte a été créé avec succès.`
      );

      return {
        success: true,
        message: "Inscription réussie ! Vous êtes maintenant connecté.",
        user: newUser,
      };
    } catch (error) {
      console.error("Erreur lors de l'inscription:", error);
      return {
        success: false,
        message: "Erreur lors de l'inscription. Veuillez réessayer.",
        errors: { general: ["Erreur système"] },
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fonction de déconnexion
  const logout = useCallback(() => {
    localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);
    localStorage.removeItem(AUTH_CONFIG.USER_KEY);
    localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);
    setUser(null);
    setSessionExpiry(null);
    toast.info("Vous avez été déconnecté.");
  }, []);

  // Vérification des permissions
  const hasPermission = useCallback(
    (permission: string) => {
      return user?.permissions.includes(permission) || false;
    },
    [user]
  );

  // Vérification du rôle
  const hasRole = useCallback(
    (role: UserRole) => {
      return user?.role === role;
    },
    [user]
  );

  // Vérification d'accès aux modules
  const canAccessModule = useCallback(
    (module: string) => {
      if (!user) return false;
      const allowedRoles = MODULE_PERMISSIONS[module] || [];
      return allowedRoles.includes(user.role);
    },
    [user]
  );

  // Obtenir la hiérarchie du rôle
  const getRoleHierarchy = useCallback(() => {
    return user ? ROLE_HIERARCHY[user.role] : 0;
  }, [user]);

  // Alias pour canAccessModule (pour compatibilité)
  const hasModuleAccess = useCallback(
    (module: string) => {
      return canAccessModule(module);
    },
    [canAccessModule]
  );

  // Vérifier si l'utilisateur a au minimum un certain rôle
  const hasMinimumRole = useCallback(
    (role: UserRole) => {
      if (!user) return false;
      const userHierarchy = ROLE_HIERARCHY[user.role];
      const requiredHierarchy = ROLE_HIERARCHY[role];
      return userHierarchy >= requiredHierarchy;
    },
    [user]
  );

  // Étendre la session utilisateur
  const extendSession = useCallback(() => {
    if (user) {
      const newExpiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);
      setSessionExpiry(newExpiry);

      const userWithToken = {
        ...user,
        tokenExpiry: newExpiry.toISOString(),
      };

      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));
      toast.info("Session étendue avec succès !");
    }
  }, [user]);

  const contextValue = useMemo(
    () => ({
      user,
      isAuthenticated: !!user,
      isLoading,
      sessionExpiry,
      login,
      register,
      logout,
      hasPermission,
      hasRole,
      canAccessModule,
      hasModuleAccess,
      hasMinimumRole,
      getRoleHierarchy,
      extendSession,
    }),
    [
      user,
      isLoading,
      sessionExpiry,
      login,
      register,
      logout,
      hasPermission,
      hasRole,
      canAccessModule,
      hasModuleAccess,
      hasMinimumRole,
      getRoleHierarchy,
      extendSession,
    ]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
