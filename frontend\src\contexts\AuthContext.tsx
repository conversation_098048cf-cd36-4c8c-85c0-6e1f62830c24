import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  ReactNode,
} from "react";
import { toast } from "react-toastify";
import { AuthService } from "../services/apiService";

// Types pour l'authentification
interface User {
  id: string;
  email: string;
  nom: string;
  prenom: string;
  role: UserRole;
  permissions: string[];
  modules: string[];
  avatar?: string;
  derniere_connexion?: string;
  statut: "ACTIF" | "INACTIF" | "SUSPENDU";
}

interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

interface RegisterCredentials {
  email: string;
  password: string;
  confirmPassword: string;
  prenom: string;
  nom: string;
  role?: UserRole;
  phone?: string;
  company?: string;
}

type UserRole = "NORMAL" | "COMPTABLE" | "ADMIN" | "SUPERADMIN";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  sessionExpiry: Date | null;
  login: (credentials: LoginCredentials) => Promise<{
    success: boolean;
    message?: string;
    errors?: Record<string, string[]>;
  }>;
  register: (credentials: RegisterCredentials) => Promise<{
    success: boolean;
    message?: string;
    errors?: Record<string, string[]>;
  }>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  hasRole: (role: UserRole) => boolean;
  hasMinimumRole: (minimumRole: UserRole) => boolean;
  hasModuleAccess: (moduleName: string) => boolean;
  hasPermission: (permission: string) => boolean;
  extendSession: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Configuration de sécurité
const AUTH_CONFIG = {
  SESSION_DURATION: 8 * 60 * 60 * 1000, // 8 heures
  STORAGE_KEY: "abm_auth_token",
  USER_KEY: "abm_user_data",
  REMEMBER_KEY: "abm_remember_me",
};

// Hiérarchie des rôles
const ROLE_HIERARCHY: Record<UserRole, number> = {
  UTILISATEUR: 1,
  VENDEUR: 2,
  COMPTABLE: 3,
  MANAGER: 4,
  ADMIN: 5,
};

// Permissions par module
const MODULE_PERMISSIONS: Record<string, UserRole[]> = {
  dashboard: ["UTILISATEUR", "VENDEUR", "COMPTABLE", "MANAGER", "ADMIN"],
  clients: ["VENDEUR", "COMPTABLE", "MANAGER", "ADMIN"],
  produits: ["VENDEUR", "COMPTABLE", "MANAGER", "ADMIN"],
  factures: ["VENDEUR", "COMPTABLE", "MANAGER", "ADMIN"],
  commandes: ["VENDEUR", "COMPTABLE", "MANAGER", "ADMIN"],
  paiements: ["COMPTABLE", "MANAGER", "ADMIN"],
  stock: ["VENDEUR", "MANAGER", "ADMIN"],
  rapports: ["COMPTABLE", "MANAGER", "ADMIN"],
  settings: ["MANAGER", "ADMIN"],
  users: ["ADMIN"],
};

// Utilisateurs de démonstration
const demoUsers: Record<string, User> = {
  "<EMAIL>": {
    id: "1",
    email: "<EMAIL>",
    nom: "Administrateur",
    prenom: "Système",
    role: "ADMIN",
    permissions: ["*"],
    modules: Object.keys(MODULE_PERMISSIONS),
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "2",
    email: "<EMAIL>",
    nom: "Manager",
    prenom: "Principal",
    role: "MANAGER",
    permissions: ["read", "write", "delete"],
    modules: [
      "dashboard",
      "clients",
      "produits",
      "factures",
      "commandes",
      "paiements",
      "stock",
      "rapports",
    ],
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "3",
    email: "<EMAIL>",
    nom: "Comptable",
    prenom: "Principal",
    role: "COMPTABLE",
    permissions: ["read", "write"],
    modules: ["dashboard", "clients", "factures", "paiements", "rapports"],
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
  "<EMAIL>": {
    id: "4",
    email: "<EMAIL>",
    nom: "Vendeur",
    prenom: "Commercial",
    role: "VENDEUR",
    permissions: ["read", "write"],
    modules: [
      "dashboard",
      "clients",
      "produits",
      "factures",
      "commandes",
      "stock",
    ],
    statut: "ACTIF",
    derniere_connexion: new Date().toISOString(),
  },
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);

  // Vérifier l'authentification au chargement
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem(AUTH_CONFIG.STORAGE_KEY);
        const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);

        if (token && userData) {
          const parsedUser = JSON.parse(userData);

          // Vérifier si le token n'est pas expiré
          const tokenExpiry = new Date(parsedUser.tokenExpiry || 0);
          if (tokenExpiry > new Date()) {
            setUser(parsedUser);
            setSessionExpiry(tokenExpiry);
          } else {
            // Token expiré, nettoyer le stockage
            localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);
            localStorage.removeItem(AUTH_CONFIG.USER_KEY);
            localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);
          }
        }
      } catch (error) {
        console.error(
          "Erreur lors de la vérification de l'authentification:",
          error
        );
        localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);
        localStorage.removeItem(AUTH_CONFIG.USER_KEY);
        localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Étendre la session
  const extendSession = useCallback(() => {
    if (user && sessionExpiry) {
      const newExpiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);
      setSessionExpiry(newExpiry);

      const updatedUser = { ...user, tokenExpiry: newExpiry.toISOString() };
      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(updatedUser));
    }
  }, [user, sessionExpiry]);

  // Fonction de connexion
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);

      // Appel API réel vers Django
      const response = await AuthService.login(
        credentials.email,
        credentials.password
      );

      if (response.success && response.tokens) {
        // Stocker les tokens JWT
        localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, response.tokens.access);
        localStorage.setItem("refresh_token", response.tokens.refresh);

        // Récupérer le profil utilisateur
        const profileResponse = await AuthService.getProfile();

        if (profileResponse.success) {
          const user = {
            id: profileResponse.user.id,
            email: profileResponse.user.email,
            nom: profileResponse.user.last_name,
            prenom: profileResponse.user.first_name,
            role: profileResponse.user.role as UserRole,
            permissions: profileResponse.user.permissions || [],
            modules: profileResponse.user.modules || [],
            statut: profileResponse.user.is_active ? "ACTIF" : "INACTIF",
            derniere_connexion: new Date().toISOString(),
          };

          const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);

          localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(user));

          if (credentials.remember) {
            localStorage.setItem(AUTH_CONFIG.REMEMBER_KEY, "true");
          }

          setUser(user);
          setSessionExpiry(expiry);

          toast.success(`Bienvenue ${user.prenom} ${user.nom} !`);

          return {
            success: true,
            message: "Connexion réussie",
          };
        }
      }

      return {
        success: false,
        message: response.message || "Email ou mot de passe incorrect",
        errors: response.errors || { email: ["Identifiants invalides"] },
      };
    } catch (error) {
      console.error("Erreur lors de la connexion:", error);
      return {
        success: false,
        message: "Erreur de connexion. Veuillez réessayer.",
        errors: { general: ["Erreur système"] },
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fonction d'inscription
  const register = useCallback(async (credentials: RegisterCredentials) => {
    try {
      setIsLoading(true);

      // Validation côté client
      if (credentials.password !== credentials.confirmPassword) {
        return {
          success: false,
          message: "Les mots de passe ne correspondent pas",
          errors: {
            confirmPassword: ["Les mots de passe ne correspondent pas"],
          },
        };
      }

      if (credentials.password.length < 6) {
        return {
          success: false,
          message: "Le mot de passe doit contenir au moins 6 caractères",
          errors: {
            password: ["Le mot de passe doit contenir au moins 6 caractères"],
          },
        };
      }

      // Préparer les données pour l'API Django
      const registrationData = {
        username: credentials.email, // Utiliser l'email comme username
        email: credentials.email,
        first_name: credentials.prenom,
        last_name: credentials.nom,
        password: credentials.password,
        password_confirm: credentials.confirmPassword,
        phone: credentials.phone || "",
        company: credentials.company || "",
      };

      // Appel API réel vers Django
      const response = await AuthService.register(registrationData);

      if (response.success) {
        // Inscription réussie - connecter automatiquement l'utilisateur
        const loginResult = await login({
          email: credentials.email,
          password: credentials.password,
        });

        return {
          success: true,
          message: "Inscription réussie ! Vous êtes maintenant connecté.",
          user: response.user,
        };
      } else {
        return {
          success: false,
          message: response.message || "Erreur lors de l'inscription",
          errors: response.errors || {},
        };
      }

      // Ajouter l'utilisateur aux utilisateurs de démo (en production, ceci serait sauvé en base)
      demoUsers[credentials.email] = newUser;

      // Créer automatiquement une session pour le nouvel utilisateur
      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);
      const token = `abm_token_${newUser.id}_${Date.now()}`;

      const userWithToken = {
        ...newUser,
        tokenExpiry: expiry.toISOString(),
      };

      // Stocker les données
      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);
      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));

      setUser(newUser);
      setSessionExpiry(expiry);

      toast.success(
        `Bienvenue ${newUser.prenom} ${newUser.nom} ! Votre compte a été créé avec succès.`
      );

      return {
        success: true,
        message: "Inscription réussie ! Vous êtes maintenant connecté.",
      };
    } catch (error) {
      console.error("Erreur lors de l'inscription:", error);
      return {
        success: false,
        message: "Erreur lors de l'inscription. Veuillez réessayer.",
        errors: { general: ["Erreur système"] },
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fonction de déconnexion
  const logout = useCallback(async () => {
    try {
      localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);
      localStorage.removeItem(AUTH_CONFIG.USER_KEY);
      localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);

      setUser(null);
      setSessionExpiry(null);

      toast.info("Vous avez été déconnecté");
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
    }
  }, []);

  // Rafraîchir le token
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      if (!user) return false;

      const newExpiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);
      const newToken = `abm_token_${user.id}_${Date.now()}`;

      const updatedUser = {
        ...user,
        tokenExpiry: newExpiry.toISOString(),
        derniere_connexion: new Date().toISOString(),
      };

      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, newToken);
      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(updatedUser));

      setSessionExpiry(newExpiry);

      return true;
    } catch (error) {
      console.error("Erreur lors du rafraîchissement du token:", error);
      await logout();
      return false;
    }
  }, [user, logout]);

  // Vérifier si l'utilisateur a un rôle spécifique
  const hasRole = useCallback(
    (role: UserRole): boolean => {
      return user?.role === role;
    },
    [user]
  );

  // Vérifier si l'utilisateur a au minimum un certain rôle
  const hasMinimumRole = useCallback(
    (minimumRole: UserRole): boolean => {
      if (!user) return false;
      return ROLE_HIERARCHY[user.role] >= ROLE_HIERARCHY[minimumRole];
    },
    [user]
  );

  // Vérifier l'accès à un module
  const hasModuleAccess = useCallback(
    (moduleName: string): boolean => {
      if (!user) return false;

      // Admin a accès à tout
      if (user.role === "ADMIN") return true;

      // Vérifier les permissions du module
      const moduleRoles = MODULE_PERMISSIONS[moduleName];
      if (!moduleRoles) return false;

      return moduleRoles.includes(user.role);
    },
    [user]
  );

  // Vérifier une permission spécifique
  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!user) return false;

      // Admin a toutes les permissions
      if (user.role === "ADMIN" || user.permissions.includes("*")) return true;

      return user.permissions.includes(permission);
    },
    [user]
  );

  // Valeur du contexte mémorisée
  const contextValue = useMemo(
    () => ({
      user,
      isAuthenticated: !!user,
      isLoading,
      sessionExpiry,
      login,
      register,
      logout,
      refreshToken,
      hasRole,
      hasMinimumRole,
      hasModuleAccess,
      hasPermission,
      extendSession,
    }),
    [
      user,
      isLoading,
      sessionExpiry,
      login,
      register,
      logout,
      refreshToken,
      hasRole,
      hasMinimumRole,
      hasModuleAccess,
      hasPermission,
      extendSession,
    ]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

// Hook personnalisé pour utiliser le contexte d'authentification
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth doit être utilisé dans un AuthProvider");
  }
  return context;
};

// Export des types pour utilisation externe
export type { User, LoginCredentials, RegisterCredentials, UserRole };
