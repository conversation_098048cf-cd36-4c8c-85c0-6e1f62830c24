"""
Système de recommandations intelligentes basé sur l'IA
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal
from django.utils import timezone
from django.db.models import Sum, Avg, Count, Q, F
from django.db.models.functions import <PERSON>runc<PERSON>onth, TruncWeek
from Core.models import AIInsight, SmartNotification
from Core.websocket_service import NotificationManager
import random
import math

logger = logging.getLogger(__name__)


class AIRecommendationEngine:
    """Moteur de recommandations IA pour l'application ABM"""
    
    def __init__(self):
        self.confidence_threshold = 0.7
        self.recommendation_types = {
            'PRICING': 'Optimisation des prix',
            'INVENTORY': 'Gestion des stocks',
            'SALES': 'Stratégie de vente',
            'CLIENT': 'Relation client',
            'PRODUCT': 'Gestion produits',
            'FINANCIAL': 'Optimisation financière'
        }
    
    def generate_pricing_recommendations(self) -> List[Dict[str, Any]]:
        """Génère des recommandations de prix intelligentes"""
        try:
            from Facturation.models import Produit, LigneFacture
            
            recommendations = []
            
            # Analyser les produits avec des ventes récentes
            recent_sales = LigneFacture.objects.filter(
                facture__date_emission__gte=timezone.now() - timedelta(days=90),
                facture__statut__in=['VALIDEE', 'PAYEE']
            ).values('produit').annotate(
                total_ventes=Sum('quantite'),
                ca_total=Sum(F('prix_unitaire') * F('quantite')),
                prix_moyen=Avg('prix_unitaire'),
                nb_factures=Count('facture', distinct=True)
            ).filter(total_ventes__gt=0)
            
            for sale_data in recent_sales:
                try:
                    produit = Produit.objects.get(id=sale_data['produit'])
                    
                    # Calculer les métriques
                    prix_actuel = produit.prix_vente
                    prix_moyen_vente = sale_data['prix_moyen']
                    total_ventes = sale_data['total_ventes']
                    ca_total = sale_data['ca_total']
                    
                    # Recommandations basées sur l'analyse
                    if prix_moyen_vente > prix_actuel * Decimal('1.1'):
                        # Le produit se vend plus cher que le prix catalogue
                        nouveau_prix = prix_moyen_vente * Decimal('0.95')
                        confidence = min(0.9, 0.6 + (total_ventes / 100) * 0.1)
                        
                        recommendations.append({
                            'type': 'PRICING',
                            'produit_id': str(produit.id),
                            'produit_nom': produit.nom,
                            'action': 'INCREASE_PRICE',
                            'prix_actuel': float(prix_actuel),
                            'prix_recommande': float(nouveau_prix),
                            'augmentation_pct': float((nouveau_prix - prix_actuel) / prix_actuel * 100),
                            'raison': f'Le produit se vend en moyenne {float(prix_moyen_vente):.2f} TND, '
                                     f'soit {float((prix_moyen_vente - prix_actuel) / prix_actuel * 100):.1f}% '
                                     f'au-dessus du prix catalogue',
                            'impact_estime': f'+{float((nouveau_prix - prix_actuel) * total_ventes):.2f} TND/mois',
                            'confidence': confidence,
                            'donnees_support': {
                                'ventes_3_mois': total_ventes,
                                'ca_3_mois': float(ca_total),
                                'prix_moyen_realise': float(prix_moyen_vente),
                                'nb_factures': sale_data['nb_factures']
                            }
                        })
                    
                    elif prix_moyen_vente < prix_actuel * Decimal('0.9') and total_ventes < 10:
                        # Le produit se vend moins cher et peu de ventes
                        nouveau_prix = prix_actuel * Decimal('0.9')
                        confidence = 0.7
                        
                        recommendations.append({
                            'type': 'PRICING',
                            'produit_id': str(produit.id),
                            'produit_nom': produit.nom,
                            'action': 'DECREASE_PRICE',
                            'prix_actuel': float(prix_actuel),
                            'prix_recommande': float(nouveau_prix),
                            'reduction_pct': float((prix_actuel - nouveau_prix) / prix_actuel * 100),
                            'raison': f'Faibles ventes ({total_ventes} unités) et prix moyen réalisé '
                                     f'({float(prix_moyen_vente):.2f} TND) inférieur au catalogue',
                            'impact_estime': f'Augmentation estimée des ventes: +30%',
                            'confidence': confidence,
                            'donnees_support': {
                                'ventes_3_mois': total_ventes,
                                'ca_3_mois': float(ca_total),
                                'prix_moyen_realise': float(prix_moyen_vente)
                            }
                        })
                
                except Produit.DoesNotExist:
                    continue
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération des recommandations de prix: {e}")
            return []
    
    def generate_inventory_recommendations(self) -> List[Dict[str, Any]]:
        """Génère des recommandations de gestion des stocks"""
        try:
            from Facturation.models import Produit, LigneFacture
            
            recommendations = []
            
            # Analyser les tendances de vente pour prédire les besoins
            produits = Produit.objects.filter(is_active=True).annotate(
                stock_total=Sum('stock__quantite')
            )
            
            for produit in produits:
                # Calculer la vélocité de vente (ventes des 30 derniers jours)
                ventes_30j = LigneFacture.objects.filter(
                    produit=produit,
                    facture__date_emission__gte=timezone.now() - timedelta(days=30),
                    facture__statut__in=['VALIDEE', 'PAYEE']
                ).aggregate(total=Sum('quantite'))['total'] or 0
                
                # Calculer la vélocité de vente (ventes des 90 derniers jours)
                ventes_90j = LigneFacture.objects.filter(
                    produit=produit,
                    facture__date_emission__gte=timezone.now() - timedelta(days=90),
                    facture__statut__in=['VALIDEE', 'PAYEE']
                ).aggregate(total=Sum('quantite'))['total'] or 0
                
                stock_actuel = produit.stock_total or 0
                stock_minimum = produit.stock_minimum or 0
                
                # Calculer la vélocité quotidienne
                velocite_quotidienne = ventes_30j / 30 if ventes_30j > 0 else 0
                
                # Prédire les jours de stock restants
                jours_stock_restant = stock_actuel / velocite_quotidienne if velocite_quotidienne > 0 else float('inf')
                
                # Recommandations basées sur l'analyse
                if stock_actuel <= stock_minimum and ventes_30j > 0:
                    # Stock critique avec demande
                    quantite_recommandee = max(
                        int(velocite_quotidienne * 60),  # 2 mois de stock
                        stock_minimum * 2
                    )
                    
                    recommendations.append({
                        'type': 'INVENTORY',
                        'produit_id': str(produit.id),
                        'produit_nom': produit.nom,
                        'action': 'URGENT_RESTOCK',
                        'stock_actuel': stock_actuel,
                        'stock_minimum': stock_minimum,
                        'quantite_recommandee': quantite_recommandee,
                        'priorite': 'CRITIQUE',
                        'raison': f'Stock critique ({stock_actuel} unités) avec demande active '
                                 f'({ventes_30j} ventes/30j)',
                        'jours_stock_restant': int(jours_stock_restant) if jours_stock_restant != float('inf') else 0,
                        'confidence': 0.95,
                        'donnees_support': {
                            'ventes_30j': ventes_30j,
                            'ventes_90j': ventes_90j,
                            'velocite_quotidienne': velocite_quotidienne
                        }
                    })
                
                elif jours_stock_restant < 14 and ventes_30j > 5:
                    # Stock faible avec bonne demande
                    quantite_recommandee = int(velocite_quotidienne * 45)  # 1.5 mois
                    
                    recommendations.append({
                        'type': 'INVENTORY',
                        'produit_id': str(produit.id),
                        'produit_nom': produit.nom,
                        'action': 'RESTOCK_SOON',
                        'stock_actuel': stock_actuel,
                        'quantite_recommandee': quantite_recommandee,
                        'priorite': 'HAUTE',
                        'raison': f'Stock pour {int(jours_stock_restant)} jours seulement '
                                 f'avec bonne demande ({ventes_30j} ventes/30j)',
                        'jours_stock_restant': int(jours_stock_restant),
                        'confidence': 0.8,
                        'donnees_support': {
                            'ventes_30j': ventes_30j,
                            'velocite_quotidienne': velocite_quotidienne
                        }
                    })
                
                elif stock_actuel > velocite_quotidienne * 120 and ventes_90j < 5:
                    # Surstock avec faible demande
                    recommendations.append({
                        'type': 'INVENTORY',
                        'produit_id': str(produit.id),
                        'produit_nom': produit.nom,
                        'action': 'REDUCE_STOCK',
                        'stock_actuel': stock_actuel,
                        'stock_optimal': int(velocite_quotidienne * 60) if velocite_quotidienne > 0 else stock_minimum,
                        'priorite': 'MOYENNE',
                        'raison': f'Surstock ({stock_actuel} unités pour {int(jours_stock_restant)} jours) '
                                 f'avec faible demande ({ventes_90j} ventes/90j)',
                        'jours_stock_restant': int(jours_stock_restant) if jours_stock_restant != float('inf') else 999,
                        'confidence': 0.7,
                        'donnees_support': {
                            'ventes_90j': ventes_90j,
                            'velocite_quotidienne': velocite_quotidienne
                        }
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération des recommandations de stock: {e}")
            return []
    
    def generate_sales_recommendations(self) -> List[Dict[str, Any]]:
        """Génère des recommandations de stratégie de vente"""
        try:
            from Facturation.models import Client, Facture, LigneFacture
            
            recommendations = []
            
            # Analyser les clients inactifs
            clients_inactifs = Client.objects.filter(
                is_active=True
            ).exclude(
                factures__date_emission__gte=timezone.now() - timedelta(days=60)
            ).annotate(
                derniere_facture=timezone.now() - timedelta(days=365)  # Valeur par défaut
            )
            
            for client in clients_inactifs[:10]:  # Top 10 clients inactifs
                # Calculer le CA historique
                ca_historique = Facture.objects.filter(
                    client=client,
                    statut__in=['VALIDEE', 'PAYEE']
                ).aggregate(total=Sum('montant_ttc'))['total'] or Decimal('0')
                
                if ca_historique > Decimal('1000'):  # Clients avec CA significatif
                    recommendations.append({
                        'type': 'SALES',
                        'client_id': str(client.id),
                        'client_nom': client.nom,
                        'action': 'REACTIVATE_CLIENT',
                        'priorite': 'HAUTE' if ca_historique > Decimal('5000') else 'MOYENNE',
                        'raison': f'Client inactif depuis plus de 60 jours avec CA historique '
                                 f'de {float(ca_historique):.2f} TND',
                        'ca_historique': float(ca_historique),
                        'actions_suggerees': [
                            'Appel téléphonique de relance',
                            'Offre promotionnelle personnalisée',
                            'Visite commerciale'
                        ],
                        'confidence': 0.8,
                        'donnees_support': {
                            'ca_total': float(ca_historique),
                            'jours_inactivite': 60
                        }
                    })
            
            # Analyser les opportunités de vente croisée
            top_clients = Client.objects.filter(
                is_active=True,
                factures__date_emission__gte=timezone.now() - timedelta(days=90)
            ).annotate(
                ca_recent=Sum('factures__montant_ttc'),
                nb_produits_achetes=Count('factures__lignes__produit', distinct=True)
            ).filter(ca_recent__gt=1000).order_by('-ca_recent')[:5]
            
            for client in top_clients:
                if client.nb_produits_achetes < 5:  # Clients avec peu de diversité
                    recommendations.append({
                        'type': 'SALES',
                        'client_id': str(client.id),
                        'client_nom': client.nom,
                        'action': 'CROSS_SELL',
                        'priorite': 'MOYENNE',
                        'raison': f'Client actif (CA: {float(client.ca_recent):.2f} TND) '
                                 f'mais achète peu de produits différents ({client.nb_produits_achetes})',
                        'ca_recent': float(client.ca_recent),
                        'actions_suggerees': [
                            'Proposer des produits complémentaires',
                            'Catalogue personnalisé',
                            'Remise sur lot de produits'
                        ],
                        'confidence': 0.7,
                        'donnees_support': {
                            'ca_90j': float(client.ca_recent),
                            'nb_produits': client.nb_produits_achetes
                        }
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération des recommandations de vente: {e}")
            return []
    
    def generate_client_recommendations(self) -> List[Dict[str, Any]]:
        """Génère des recommandations de relation client"""
        try:
            from Facturation.models import Client, Facture
            
            recommendations = []
            
            # Analyser les clients à risque (retards de paiement)
            clients_risque = Client.objects.filter(
                is_active=True,
                factures__date_echeance__lt=timezone.now(),
                factures__statut='VALIDEE'  # Factures non payées
            ).distinct()
            
            for client in clients_risque:
                factures_impayees = Facture.objects.filter(
                    client=client,
                    statut='VALIDEE',
                    date_echeance__lt=timezone.now()
                )
                
                montant_impaye = factures_impayees.aggregate(
                    total=Sum('montant_ttc')
                )['total'] or Decimal('0')
                
                nb_factures_impayees = factures_impayees.count()
                
                if montant_impaye > Decimal('500'):
                    recommendations.append({
                        'type': 'CLIENT',
                        'client_id': str(client.id),
                        'client_nom': client.nom,
                        'action': 'PAYMENT_FOLLOW_UP',
                        'priorite': 'CRITIQUE' if montant_impaye > Decimal('2000') else 'HAUTE',
                        'raison': f'Impayés de {float(montant_impaye):.2f} TND '
                                 f'sur {nb_factures_impayees} facture(s)',
                        'montant_impaye': float(montant_impaye),
                        'nb_factures': nb_factures_impayees,
                        'actions_suggerees': [
                            'Relance téléphonique immédiate',
                            'Mise en demeure',
                            'Suspension des livraisons',
                            'Plan de paiement échelonné'
                        ],
                        'confidence': 0.9,
                        'donnees_support': {
                            'montant_total': float(montant_impaye),
                            'nb_factures_impayees': nb_factures_impayees
                        }
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération des recommandations client: {e}")
            return []
    
    def generate_all_recommendations(self) -> Dict[str, List[Dict[str, Any]]]:
        """Génère toutes les recommandations"""
        logger.info("Génération des recommandations IA...")
        
        all_recommendations = {
            'pricing': self.generate_pricing_recommendations(),
            'inventory': self.generate_inventory_recommendations(),
            'sales': self.generate_sales_recommendations(),
            'client': self.generate_client_recommendations()
        }
        
        # Compter le total
        total_recommendations = sum(len(recs) for recs in all_recommendations.values())
        
        logger.info(f"Génération terminée: {total_recommendations} recommandations")
        
        return all_recommendations
    
    def save_recommendations_as_insights(self, recommendations: Dict[str, List[Dict[str, Any]]]):
        """Sauvegarde les recommandations comme insights IA"""
        try:
            for category, recs in recommendations.items():
                for rec in recs:
                    if rec['confidence'] >= self.confidence_threshold:
                        AIInsight.objects.create(
                            type=rec['type'],
                            title=f"Recommandation {self.recommendation_types.get(rec['type'], rec['type'])}",
                            description=rec['raison'],
                            data=rec,
                            confidence_score=rec['confidence'],
                            recommendations=[rec['raison']],
                            action_items=rec.get('actions_suggerees', [])
                        )
            
            logger.info("Recommandations sauvegardées comme insights IA")
            
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des recommandations: {e}")
    
    def create_recommendation_notifications(self, recommendations: Dict[str, List[Dict[str, Any]]]):
        """Crée des notifications pour les recommandations importantes"""
        try:
            high_priority_count = 0
            
            for category, recs in recommendations.items():
                for rec in recs:
                    if rec.get('priorite') in ['CRITIQUE', 'HAUTE'] and rec['confidence'] >= 0.8:
                        NotificationManager.create_smart_notification(
                            type_notification='INFO',
                            titre=f"Recommandation IA: {rec.get('action', 'Action')}",
                            message=rec['raison'],
                            priorite='HIGH' if rec.get('priorite') == 'CRITIQUE' else 'NORMAL',
                            action_url='/core/dashboard/'
                        )
                        high_priority_count += 1
            
            if high_priority_count > 0:
                logger.info(f"{high_priority_count} notifications de recommandations créées")
            
        except Exception as e:
            logger.error(f"Erreur lors de la création des notifications: {e}")


# Instance globale du moteur de recommandations
recommendation_engine = AIRecommendationEngine()


class RecommendationTasks:
    """Tâches automatisées pour les recommandations"""
    
    @staticmethod
    def run_daily_recommendations():
        """Exécute la génération quotidienne de recommandations"""
        try:
            recommendations = recommendation_engine.generate_all_recommendations()
            
            # Sauvegarder comme insights
            recommendation_engine.save_recommendations_as_insights(recommendations)
            
            # Créer des notifications pour les recommandations importantes
            recommendation_engine.create_recommendation_notifications(recommendations)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération quotidienne de recommandations: {e}")
            return None
    
    @staticmethod
    def get_recommendations_summary() -> Dict[str, Any]:
        """Récupère un résumé des recommandations"""
        try:
            recommendations = recommendation_engine.generate_all_recommendations()
            
            summary = {
                'total_recommendations': sum(len(recs) for recs in recommendations.values()),
                'by_category': {
                    category: len(recs) for category, recs in recommendations.items()
                },
                'high_priority': sum(
                    1 for recs in recommendations.values() 
                    for rec in recs 
                    if rec.get('priorite') in ['CRITIQUE', 'HAUTE']
                ),
                'high_confidence': sum(
                    1 for recs in recommendations.values() 
                    for rec in recs 
                    if rec.get('confidence', 0) >= 0.8
                ),
                'timestamp': timezone.now().isoformat()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération du résumé: {e}")
            return {}
