/**
 * Statistiques avancées des clients
 */

import React, { useState, useEffect } from "react";
import { ClientService } from "../../services/apiService";
import { useAuth } from "../../contexts/AuthContext";
import { useNotify } from "../Common/NotificationSystem";

interface ClientStats {
  total_clients: number;
  clients_actifs: number;
  clients_inactifs: number;
  prospects: number;
  nouveaux_ce_mois: number;
  chiffre_affaires_total: number;
  ca_moyen_par_client: number;
  clients_vip: number;
  clients_premium: number;
  clients_standard: number;
  entreprises: number;
  particuliers: number;
  top_clients: any[];
  evolution_mensuelle: any[];
}

interface ClientStatsProps {
  embedded?: boolean;
  period?: "month" | "quarter" | "year";
}

const ClientStats: React.FC<ClientStatsProps> = ({
  embedded = false,
  period = "month",
}) => {
  const { hasRole } = useAuth();
  const notify = useNotify();
  const [stats, setStats] = useState<ClientStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState(period);

  // Vérifier si l'utilisateur peut voir les chiffres d'affaires
  const canViewRevenue = hasRole("ADMIN");

  useEffect(() => {
    loadStats();
  }, [selectedPeriod]);

  // Rafraîchissement automatique des statistiques toutes les 60 secondes
  useEffect(() => {
    const interval = setInterval(() => {
      console.log("🔄 Rafraîchissement automatique des statistiques...");
      loadStats();
    }, 60000); // 60 secondes

    return () => clearInterval(interval);
  }, []);

  // Rafraîchissement au focus de la fenêtre
  useEffect(() => {
    const handleFocus = () => {
      console.log("👀 Fenêtre en focus - Rafraîchissement des statistiques");
      loadStats();
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);

      // Charger les vraies données depuis l'API
      console.log("📊 Chargement des statistiques clients...");
      const response = await ClientService.getClients({ page_size: 1000 });

      if (response.error) {
        throw new Error(response.error);
      }

      const clients = response.results || [];
      const totalClients = response.count || clients.length;

      console.log(`📊 ${totalClients} clients trouvés pour les statistiques`);

      // Calculer les statistiques réelles
      const clientsActifs = clients.filter(
        (c: any) => c.statut === "ACTIF"
      ).length;
      const clientsInactifs = clients.filter(
        (c: any) => c.statut === "INACTIF"
      ).length;
      const prospects = clients.filter(
        (c: any) => c.statut === "PROSPECT"
      ).length;
      const entreprises = clients.filter(
        (c: any) => c.type_client === "ENTREPRISE"
      ).length;
      const particuliers = clients.filter(
        (c: any) => c.type_client === "PARTICULIER"
      ).length;

      // Calculer le chiffre d'affaires total
      const chiffreAffairesTotal = clients.reduce(
        (total: number, client: any) => {
          return total + (client.chiffre_affaires_total || 0);
        },
        0
      );

      // Calculer la valeur moyenne par client
      const caMoyenParClient =
        totalClients > 0 ? chiffreAffairesTotal / totalClients : 0;

      // Calculer les nouveaux clients ce mois
      const maintenant = new Date();
      const debutMois = new Date(
        maintenant.getFullYear(),
        maintenant.getMonth(),
        1
      );
      const nouveauxCeMois = clients.filter((client: any) => {
        const dateCreation = new Date(
          client.created_at || client.date_creation
        );
        return dateCreation >= debutMois;
      }).length;

      // Classifier les clients par CA
      const clientsVip = clients.filter(
        (c: any) => (c.chiffre_affaires_total || 0) > 50000
      ).length;
      const clientsPremium = clients.filter((c: any) => {
        const ca = c.chiffre_affaires_total || 0;
        return ca > 10000 && ca <= 50000;
      }).length;
      const clientsStandard = totalClients - clientsVip - clientsPremium;

      // Top clients
      const topClients = clients
        .filter((c: any) => (c.chiffre_affaires_total || 0) > 0)
        .sort(
          (a: any, b: any) =>
            (b.chiffre_affaires_total || 0) - (a.chiffre_affaires_total || 0)
        )
        .slice(0, 3)
        .map((c: any) => ({
          nom: c.nom_complet || c.nom || "Client sans nom",
          ca: c.chiffre_affaires_total || 0,
        }));

      setStats({
        total_clients: totalClients,
        clients_actifs: clientsActifs,
        clients_inactifs: clientsInactifs,
        prospects: prospects,
        nouveaux_ce_mois: nouveauxCeMois,
        chiffre_affaires_total: chiffreAffairesTotal,
        ca_moyen_par_client: caMoyenParClient,
        clients_vip: clientsVip,
        clients_premium: clientsPremium,
        clients_standard: clientsStandard,
        entreprises: entreprises,
        particuliers: particuliers,
        top_clients: topClients,
        evolution_mensuelle: [],
      });

      console.log(
        `✅ Statistiques calculées: ${totalClients} total, ${clientsActifs} actifs, ${prospects} prospects`
      );
    } catch (error: any) {
      console.error("❌ Erreur lors du chargement des statistiques:", error);
      notify.error("Erreur lors du chargement des statistiques");

      // Fallback avec des données vides
      setStats({
        total_clients: 0,
        clients_actifs: 0,
        clients_inactifs: 0,
        prospects: 0,
        nouveaux_ce_mois: 0,
        chiffre_affaires_total: 0,
        ca_moyen_par_client: 0,
        clients_vip: 0,
        clients_premium: 0,
        clients_standard: 0,
        entreprises: 0,
        particuliers: 0,
        top_clients: [],
        evolution_mensuelle: [],
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const calculatePercentage = (value: number, total: number) => {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  if (loading) {
    return (
      <div className="client-stats-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des statistiques...</p>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className={`client-stats ${embedded ? "embedded" : ""}`}>
      {!embedded && (
        <div className="stats-header">
          <h3>📊 Statistiques Clients</h3>
          <div className="period-selector">
            <button
              className={`period-btn ${
                selectedPeriod === "month" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("month")}>
              Ce mois
            </button>
            <button
              className={`period-btn ${
                selectedPeriod === "quarter" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("quarter")}>
              Trimestre
            </button>
            <button
              className={`period-btn ${
                selectedPeriod === "year" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("year")}>
              Année
            </button>
          </div>
        </div>
      )}

      <div className="stats-grid">
        {/* Total clients */}
        <div className="stat-card primary">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h4>Total Clients</h4>
            <div className="stat-value">{stats.total_clients}</div>
            <div className="stat-subtitle">
              +{stats.nouveaux_ce_mois} ce mois
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar primary"
                style={{ width: "100%" }}></div>
            </div>
          </div>
        </div>

        {/* Clients actifs */}
        <div className="stat-card success">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h4>Clients Actifs</h4>
            <div className="stat-value">{stats.clients_actifs}</div>
            <div className="stat-subtitle">
              {calculatePercentage(stats.clients_actifs, stats.total_clients)}%
              du total
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar success"
                style={{
                  width: `${calculatePercentage(
                    stats.clients_actifs,
                    stats.total_clients
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>

        {/* Prospects */}
        <div className="stat-card warning">
          <div className="stat-icon">🎯</div>
          <div className="stat-content">
            <h4>Prospects</h4>
            <div className="stat-value">{stats.prospects}</div>
            <div className="stat-subtitle">À convertir</div>
            <div className="stat-progress">
              <div
                className="progress-bar warning"
                style={{
                  width: `${calculatePercentage(
                    stats.prospects,
                    stats.total_clients
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>

        {/* CA Total - Visible uniquement pour Admin/SuperAdmin */}
        {canViewRevenue && (
          <div className="stat-card info">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h4>CA Total</h4>
              <div className="stat-value">
                {formatCurrency(stats.chiffre_affaires_total)}
              </div>
              <div className="stat-subtitle">
                Moy: {formatCurrency(stats.ca_moyen_par_client)}
              </div>
              <div className="stat-progress">
                <div
                  className="progress-bar info"
                  style={{ width: "85%" }}></div>
              </div>
            </div>
          </div>
        )}

        {/* Clients VIP */}
        <div className="stat-card secondary">
          <div className="stat-icon">⭐</div>
          <div className="stat-content">
            <h4>Clients VIP</h4>
            <div className="stat-value">{stats.clients_vip}</div>
            <div className="stat-subtitle">
              {calculatePercentage(stats.clients_vip, stats.total_clients)}% du
              total
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar secondary"
                style={{
                  width: `${calculatePercentage(
                    stats.clients_vip,
                    stats.total_clients
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>

        {/* Entreprises vs Particuliers */}
        <div className="stat-card primary">
          <div className="stat-icon">🏢</div>
          <div className="stat-content">
            <h4>Entreprises</h4>
            <div className="stat-value">{stats.entreprises}</div>
            <div className="stat-subtitle">
              vs {stats.particuliers} particuliers
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar primary"
                style={{
                  width: `${calculatePercentage(
                    stats.entreprises,
                    stats.total_clients
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Résumé par segments */}
      <div className="stats-summary">
        <div className="summary-item">
          <div className="summary-label">⭐ VIP</div>
          <div className="summary-value success">{stats.clients_vip}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">💎 Premium</div>
          <div className="summary-value warning">{stats.clients_premium}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">👤 Standard</div>
          <div className="summary-value">{stats.clients_standard}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">🎯 Prospects</div>
          <div className="summary-value danger">{stats.prospects}</div>
        </div>
      </div>

      {/* Top clients */}
      {!embedded && stats.top_clients.length > 0 && (
        <div className="top-clients">
          <h4>🏆 Top Clients</h4>
          <div className="top-clients-list">
            {stats.top_clients.map((client, index) => (
              <div
                key={index}
                className="top-client-item">
                <div className="client-rank">#{index + 1}</div>
                <div className="client-info">
                  <span className="client-name">{client.nom}</span>
                  <span className="client-ca">{formatCurrency(client.ca)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientStats;
