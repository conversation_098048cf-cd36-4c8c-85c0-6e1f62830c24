<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture {{ facture.numero }}</title>
    <style>
        /* Styles basés sur l'image fournie */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        
        .logo-section {
            flex: 1;
        }
        
        .company-info {
            flex: 2;
            text-align: left;
        }
        
        .legal-info {
            flex: 1;
            text-align: right;
            font-size: 10px;
        }
        
        .company-name {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .title-section {
            text-align: center;
            margin: 30px 0;
        }
        
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        
        .invoice-date {
            text-align: right;
            font-size: 12px;
            margin-bottom: 20px;
        }
        
        .client-info {
            display: flex;
            margin-bottom: 30px;
        }
        
        .client-left, .client-right {
            flex: 1;
            border: 1px solid #000;
            padding: 10px;
        }
        
        .client-left {
            margin-right: 10px;
        }
        
        .articles-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .articles-table th {
            background-color: #2c3e50;
            color: white;
            padding: 8px 4px;
            text-align: center;
            font-size: 9px;
            border: 1px solid #000;
        }
        
        .articles-table td {
            padding: 6px 4px;
            text-align: center;
            font-size: 8px;
            border: 1px solid #000;
        }
        
        .articles-table td:nth-child(2) {
            text-align: left;
        }
        
        .totals-section {
            display: flex;
            margin-bottom: 30px;
        }
        
        .client-signature {
            flex: 1;
            border: 1px solid #000;
            padding: 10px;
            margin-right: 10px;
            min-height: 100px;
        }
        
        .totals-box {
            flex: 1;
            border: 1px solid #000;
            padding: 10px;
        }
        
        .totals-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .total-final {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
        }
        
        .footer {
            border-top: 1px solid #ccc;
            padding-top: 20px;
            font-size: 8px;
            text-align: center;
            color: #666;
        }
        
        .conditions {
            margin-bottom: 20px;
            font-size: 8px;
        }
        
        .payment-methods {
            margin: 10px 0;
        }
        
        .checkbox {
            margin-right: 10px;
        }
        
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <!-- En-tête avec logo et informations société -->
    <div class="header">
        <div class="logo-section">
            {% if config.logo %}
                <img src="{{ config.logo.url }}" alt="Logo" style="max-width: 80px; max-height: 60px;">
            {% endif %}
        </div>
        
        <div class="company-info">
            <div class="company-name">{{ config.nom_societe }}</div>
            <div>{{ config.adresse_ligne1 }}</div>
            {% if config.adresse_ligne2 %}
                <div>{{ config.adresse_ligne2 }}</div>
            {% endif %}
            <div>{{ config.code_postal }} {{ config.ville }}</div>
            {% if config.telephone %}
                <div>Tél: {{ config.telephone }}</div>
            {% endif %}
            {% if config.email %}
                <div>Email: {{ config.email }}</div>
            {% endif %}
        </div>
        
        <div class="legal-info">
            <div><strong>SIRET:</strong> {{ config.siret }}</div>
            <div><strong>TVA:</strong> {{ config.numero_tva }}</div>
            {% if config.code_ape %}
                <div><strong>APE:</strong> {{ config.code_ape }}</div>
            {% endif %}
        </div>
    </div>
    
    <!-- Titre et numéro de facture -->
    <div class="title-section">
        <div class="invoice-title">FACTURE N°: {{ facture.numero }}</div>
    </div>
    
    <div class="invoice-date">
        Date: {{ facture.date_facture|date:"d/m/Y" }}
    </div>
    
    <!-- Informations client -->
    <div class="client-info">
        <div class="client-left">
            <div><strong>N° Client:</strong> {{ facture.client.id }}</div>
            <div><strong>Nom:</strong> {{ facture.client.nom }}</div>
            <div><strong>Adresse:</strong> {{ facture.client.adresse|default:"N/A" }}</div>
        </div>
        
        <div class="client-right">
            <div><strong>Matricule Fiscal:</strong> MF 1234567890</div>
            <div><strong>Téléphone:</strong> {{ facture.client.telephone|default:"N/A" }}</div>
        </div>
    </div>
    
    <!-- Tableau des articles -->
    <table class="articles-table">
        <thead>
            <tr>
                <th>Code Article</th>
                <th>Désignation</th>
                <th>Qté</th>
                <th>P.U H.T</th>
                <th>Rem %</th>
                <th>P.U T.T.C</th>
                <th>Mt Ht H.T</th>
                <th>TVA %</th>
            </tr>
        </thead>
        <tbody>
            {% for ligne in facture.lignes.all %}
            <tr>
                <td>{{ ligne.code_article }}</td>
                <td>{{ ligne.designation }}</td>
                <td>{{ ligne.quantite|floatformat:0 }}</td>
                <td>{{ ligne.prix_unitaire_ht|floatformat:2 }}</td>
                <td>{% if ligne.remise_pourcentage %}{{ ligne.remise_pourcentage|floatformat:1 }}{% else %}0{% endif %}</td>
                <td>{{ ligne.prix_unitaire_ttc|floatformat:2 }}</td>
                <td>{{ ligne.montant_ht|floatformat:2 }}</td>
                <td>{{ ligne.taux_tva|floatformat:0 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <!-- Section totaux et signature -->
    <div class="totals-section">
        <div class="client-signature">
            <div><strong>Client</strong></div>
            <div style="height: 60px;"></div>
            <div><strong>Cachet et Signature</strong></div>
        </div>
        
        <div class="totals-box">
            <div class="totals-row">
                <span><strong>Montant Brut:</strong></span>
                <span>{{ facture.montant_ht|add:facture.remise_montant|floatformat:2 }}</span>
            </div>
            <div class="totals-row">
                <span><strong>Remise (%):</strong></span>
                <span>{{ facture.remise_montant|floatformat:2 }}</span>
            </div>
            <div class="totals-row">
                <span><strong>Montant HT:</strong></span>
                <span>{{ facture.montant_ht|floatformat:2 }}</span>
            </div>
            <div class="totals-row">
                <span><strong>TVA (20%):</strong></span>
                <span>{{ facture.montant_tva|floatformat:2 }}</span>
            </div>
            <div class="totals-row">
                <span><strong>Timbre Fiscal:</strong></span>
                <span>0.00</span>
            </div>
            <div class="totals-row total-final">
                <span><strong>TOTAL TTC:</strong></span>
                <span><strong>{{ facture.montant_ttc|floatformat:2 }}</strong></span>
            </div>
        </div>
    </div>
    
    <!-- Conditions et mentions légales -->
    <div class="conditions">
        <div><strong>Arrêtée la présente facture à la somme de:</strong></div>
        <div>{{ facture.montant_ttc|floatformat:2 }} euros</div>
        <br>
        <div><strong>Six cent quatre-vingt-dix euros et quarante-cinq centimes</strong></div>
        <br>
        <div class="payment-methods">
            Payé en: 
            <span class="checkbox">☐ Espèce</span>
            <span class="checkbox">☐ Chèque</span>
            <span class="checkbox">☐ Traite</span>
        </div>
        <br>
        <div>{{ config.conditions_paiement }}</div>
        <div>{{ config.mentions_legales }}</div>
    </div>
    
    <!-- Pied de page -->
    <div class="footer">
        <div><strong>Siège commercial:</strong> {{ config.adresse_ligne1 }}, {{ config.code_postal }} {{ config.ville }}</div>
        <div><strong>SIRET:</strong> {{ config.siret }} - <strong>APE:</strong> {{ config.code_ape|default:"N/A" }}</div>
        <div><strong>Email:</strong> {{ config.email }} - <strong>MF:</strong> {{ config.numero_tva }}</div>
    </div>
</body>
</html>
