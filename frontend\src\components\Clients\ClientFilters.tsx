/**
 * Filtres avancés pour la liste des clients
 */

import React, { useState } from 'react';

interface ClientFiltersProps {
  filters: {
    search: string;
    type_client: string;
    segment: string;
    statut: string;
    ville: string;
    date_debut: string;
    date_fin: string;
    ca_min: string;
    ca_max: string;
  };
  onFiltersChange: (filters: any) => void;
  onReset: () => void;
}

const ClientFilters: React.FC<ClientFiltersProps> = ({
  filters,
  onFiltersChange,
  onReset
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleFilterChange = (field: string, value: string) => {
    const newFilters = { ...filters, [field]: value };
    onFiltersChange(newFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== '').length;
  };

  const handleQuickFilter = (filterType: string) => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    let newFilters = { ...filters };

    switch (filterType) {
      case 'vip':
        newFilters.segment = 'VIP';
        break;
      case 'premium':
        newFilters.segment = 'PREMIUM';
        break;
      case 'entreprises':
        newFilters.type_client = 'ENTREPRISE';
        break;
      case 'particuliers':
        newFilters.type_client = 'PARTICULIER';
        break;
      case 'actifs':
        newFilters.statut = 'ACTIF';
        break;
      case 'prospects':
        newFilters.statut = 'PROSPECT';
        break;
      case 'nouveaux':
        newFilters.date_debut = thirtyDaysAgo.toISOString().split('T')[0];
        break;
      case 'gros_ca':
        newFilters.ca_min = '10000';
        break;
      default:
        break;
    }

    onFiltersChange(newFilters);
  };

  return (
    <div className="client-filters">
      <div className="filters-header">
        <div className="filters-title">
          <h3>🔍 Filtres</h3>
          {getActiveFiltersCount() > 0 && (
            <span className="active-filters-badge">
              {getActiveFiltersCount()}
            </span>
          )}
        </div>
        <div className="filters-actions">
          <button
            className="btn btn-outline btn-sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            {showAdvanced ? '📤 Masquer' : '📥 Avancés'}
          </button>
          <button
            className="btn btn-outline btn-sm"
            onClick={onReset}
          >
            🔄 Reset
          </button>
        </div>
      </div>

      {/* Barre de recherche */}
      <div className="search-bar">
        <div className="search-input-container">
          <span className="search-icon">🔍</span>
          <input
            type="text"
            placeholder="Rechercher par nom, email, téléphone..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="search-input"
          />
          {filters.search && (
            <button
              className="clear-search"
              onClick={() => handleFilterChange('search', '')}
            >
              ✕
            </button>
          )}
        </div>
      </div>

      {/* Filtres rapides */}
      <div className="quick-filters">
        <h4>Filtres rapides</h4>
        <div className="quick-filter-buttons">
          <button
            className={`quick-filter-btn ${filters.segment === 'VIP' ? 'active' : ''}`}
            onClick={() => handleQuickFilter('vip')}
          >
            ⭐ Clients VIP
          </button>
          <button
            className={`quick-filter-btn ${filters.segment === 'PREMIUM' ? 'active' : ''}`}
            onClick={() => handleQuickFilter('premium')}
          >
            💎 Premium
          </button>
          <button
            className={`quick-filter-btn ${filters.type_client === 'ENTREPRISE' ? 'active' : ''}`}
            onClick={() => handleQuickFilter('entreprises')}
          >
            🏢 Entreprises
          </button>
          <button
            className={`quick-filter-btn ${filters.type_client === 'PARTICULIER' ? 'active' : ''}`}
            onClick={() => handleQuickFilter('particuliers')}
          >
            👤 Particuliers
          </button>
          <button
            className={`quick-filter-btn ${filters.statut === 'ACTIF' ? 'active' : ''}`}
            onClick={() => handleQuickFilter('actifs')}
          >
            ✅ Actifs
          </button>
          <button
            className={`quick-filter-btn ${filters.statut === 'PROSPECT' ? 'active' : ''}`}
            onClick={() => handleQuickFilter('prospects')}
          >
            🎯 Prospects
          </button>
          <button
            className={`quick-filter-btn ${filters.ca_min === '10000' ? 'active' : ''}`}
            onClick={() => handleQuickFilter('gros_ca')}
          >
            💰 Gros CA
          </button>
        </div>
      </div>

      {/* Filtres avancés */}
      {showAdvanced && (
        <div className="advanced-filters">
          <div className="filters-grid">
            <div className="filter-group">
              <label>Type de client</label>
              <select
                value={filters.type_client}
                onChange={(e) => handleFilterChange('type_client', e.target.value)}
                className="filter-select"
              >
                <option value="">Tous les types</option>
                <option value="PARTICULIER">👤 Particulier</option>
                <option value="ENTREPRISE">🏢 Entreprise</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Segment</label>
              <select
                value={filters.segment}
                onChange={(e) => handleFilterChange('segment', e.target.value)}
                className="filter-select"
              >
                <option value="">Tous les segments</option>
                <option value="STANDARD">👤 Standard</option>
                <option value="PREMIUM">💎 Premium</option>
                <option value="VIP">⭐ VIP</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Statut</label>
              <select
                value={filters.statut}
                onChange={(e) => handleFilterChange('statut', e.target.value)}
                className="filter-select"
              >
                <option value="">Tous les statuts</option>
                <option value="ACTIF">✅ Actif</option>
                <option value="INACTIF">⏸️ Inactif</option>
                <option value="PROSPECT">🎯 Prospect</option>
              </select>
            </div>

            <div className="filter-group">
              <label>Ville</label>
              <input
                type="text"
                placeholder="Filtrer par ville"
                value={filters.ville}
                onChange={(e) => handleFilterChange('ville', e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>Date création (début)</label>
              <input
                type="date"
                value={filters.date_debut}
                onChange={(e) => handleFilterChange('date_debut', e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>Date création (fin)</label>
              <input
                type="date"
                value={filters.date_fin}
                onChange={(e) => handleFilterChange('date_fin', e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>CA minimum (€)</label>
              <input
                type="number"
                placeholder="0"
                value={filters.ca_min}
                onChange={(e) => handleFilterChange('ca_min', e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>CA maximum (€)</label>
              <input
                type="number"
                placeholder="Illimité"
                value={filters.ca_max}
                onChange={(e) => handleFilterChange('ca_max', e.target.value)}
                className="filter-input"
              />
            </div>
          </div>
        </div>
      )}

      {/* Résumé des filtres actifs */}
      {getActiveFiltersCount() > 0 && (
        <div className="active-filters-summary">
          <span className="summary-label">Filtres actifs:</span>
          <div className="active-filters-tags">
            {filters.search && (
              <div className="filter-tag">
                Recherche: "{filters.search}"
                <button onClick={() => handleFilterChange('search', '')}>✕</button>
              </div>
            )}
            {filters.type_client && (
              <div className="filter-tag">
                Type: {filters.type_client}
                <button onClick={() => handleFilterChange('type_client', '')}>✕</button>
              </div>
            )}
            {filters.segment && (
              <div className="filter-tag">
                Segment: {filters.segment}
                <button onClick={() => handleFilterChange('segment', '')}>✕</button>
              </div>
            )}
            {filters.statut && (
              <div className="filter-tag">
                Statut: {filters.statut}
                <button onClick={() => handleFilterChange('statut', '')}>✕</button>
              </div>
            )}
            {filters.ville && (
              <div className="filter-tag">
                Ville: {filters.ville}
                <button onClick={() => handleFilterChange('ville', '')}>✕</button>
              </div>
            )}
            {(filters.ca_min || filters.ca_max) && (
              <div className="filter-tag">
                CA: {filters.ca_min || '0'} - {filters.ca_max || '∞'}€
                <button onClick={() => {
                  handleFilterChange('ca_min', '');
                  handleFilterChange('ca_max', '');
                }}>✕</button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientFilters;
