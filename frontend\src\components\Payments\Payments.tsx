/**
 * Module Paiements - Gestion des encaissements et décaissements
 */

import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import PaymentsList from './PaymentsList';
import PaymentForm from './PaymentForm';
import PaymentDetail from './PaymentDetail';
import PaymentStats from './PaymentStats';
import PaymentReconciliation from './PaymentReconciliation';
import { useAuth } from '../../contexts/AuthContext';
import './Payments.css';

const Payments: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [activeView, setActiveView] = useState(() => {
    const path = location.pathname;
    if (path.includes('/new')) return 'new';
    if (path.includes('/stats')) return 'stats';
    if (path.includes('/reconciliation')) return 'reconciliation';
    if (path.match(/\/\d+$/)) return 'detail';
    return 'list';
  });

  // Vérifier les permissions
  const hasAccess = () => {
    const allowedRoles = ['COMPTABLE', 'ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || '');
  };

  if (!hasAccess()) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder au module paiements.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="payments-module">
      {/* Header */}
      <div className="payments-header">
        <div className="header-title">
          <h1>💳 Paiements</h1>
          <p>Gestion des encaissements et décaissements</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-outline"
            onClick={() => navigate('/dashboard')}
          >
            ← Dashboard
          </button>
          <button 
            className="btn btn-info"
            onClick={() => navigate('/payments/reconciliation')}
          >
            🔄 Rapprochement
          </button>
          <button 
            className="btn btn-success"
            onClick={() => navigate('/payments/stats')}
          >
            📊 Statistiques
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/payments/new')}
          >
            ➕ Nouveau paiement
          </button>
        </div>
      </div>

      {/* Navigation interne */}
      <div className="payments-nav">
        <button
          className={`nav-btn ${activeView === 'list' ? 'active' : ''}`}
          onClick={() => navigate('/payments')}
        >
          📋 Liste des paiements
        </button>
        <button
          className={`nav-btn ${activeView === 'stats' ? 'active' : ''}`}
          onClick={() => navigate('/payments/stats')}
        >
          📊 Statistiques
        </button>
        <button
          className={`nav-btn ${activeView === 'reconciliation' ? 'active' : ''}`}
          onClick={() => navigate('/payments/reconciliation')}
        >
          🔄 Rapprochement
        </button>
      </div>

      {/* Contenu */}
      <div className="payments-content">
        <Routes>
          <Route path="/" element={<PaymentsList />} />
          <Route path="/new" element={<PaymentForm />} />
          <Route path="/:id" element={<PaymentDetail />} />
          <Route path="/:id/edit" element={<PaymentForm />} />
          <Route path="/stats" element={<PaymentStats />} />
          <Route path="/reconciliation" element={<PaymentReconciliation />} />
        </Routes>
      </div>
    </div>
  );
};

export default Payments;
