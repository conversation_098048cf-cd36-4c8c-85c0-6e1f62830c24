from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import EmailValidator, RegexValidator, MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

User = get_user_model()

class Fournisseur(models.Model):
    """Modèle Fournisseur pour la gestion des achats"""

    TYPE_CHOICES = [
        ('PRODUITS', 'Fournisseur de produits'),
        ('SERVICES', 'Fournisseur de services'),
        ('MIXTE', 'Produits et services'),
    ]

    STATUT_CHOICES = [
        ('ACTIF', 'Actif'),
        ('INACTIF', 'Inactif'),
        ('SUSPENDU', 'Suspendu'),
        ('PROSPECT', 'Prospect'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code_fournisseur = models.CharField(max_length=50, unique=True, verbose_name="Code fournisseur")

    # Informations de base
    nom = models.CharField(max_length=200, verbose_name="Nom/Raison sociale")
    email = models.EmailField(validators=[EmailValidator()])
    telephone = models.CharField(
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^\+?[\d\s\-\(\)]+$', 'Format de téléphone invalide')]
    )
    site_web = models.URLField(blank=True)

    # Type et statut
    type_fournisseur = models.CharField(max_length=20, choices=TYPE_CHOICES, default='PRODUITS')
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='ACTIF')

    # Adresse
    adresse = models.TextField(verbose_name="Adresse")
    ville = models.CharField(max_length=100)
    code_postal = models.CharField(max_length=10)
    pays = models.CharField(max_length=100, default='France')

    # Informations légales
    siret = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="Numéro d'identification fiscale",
        help_text="Numéro d'identification fiscale de l'entreprise (optionnel)"
    )
    tva_intracommunautaire = models.CharField(max_length=20, blank=True)

    # Conditions commerciales
    conditions_paiement = models.CharField(max_length=200, default='30 jours net')
    delai_paiement = models.IntegerField(default=30, verbose_name="Délai de paiement (jours)")
    remise_accordee = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name="Remise accordée (%)"
    )

    # Contact principal
    contact_principal = models.CharField(max_length=200, blank=True)
    email_contact = models.EmailField(blank=True)
    telephone_contact = models.CharField(max_length=20, blank=True)

    # Statistiques
    total_achats = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name="Total des achats"
    )
    nb_commandes = models.IntegerField(default=0, verbose_name="Nombre de commandes")
    derniere_commande = models.DateTimeField(null=True, blank=True)

    # Évaluation
    note_qualite = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="Note qualité (1-5)"
    )
    note_delai = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="Note délai (1-5)"
    )
    note_service = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="Note service (1-5)"
    )

    # Métadonnées
    notes = models.TextField(blank=True, verbose_name="Notes internes")
    tags = models.CharField(max_length=500, blank=True, help_text="Tags séparés par des virgules")

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='fournisseurs_created')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='fournisseurs_updated')

    class Meta:
        verbose_name = "Fournisseur"
        verbose_name_plural = "Fournisseurs"
        ordering = ['nom']
        indexes = [
            models.Index(fields=['code_fournisseur']),
            models.Index(fields=['statut']),
            models.Index(fields=['type_fournisseur']),
        ]

    def __str__(self):
        return f"{self.code_fournisseur} - {self.nom}"

    @property
    def note_moyenne(self):
        """Calcule la note moyenne du fournisseur"""
        notes = [self.note_qualite, self.note_delai, self.note_service]
        notes_valides = [note for note in notes if note is not None]
        if notes_valides:
            return sum(notes_valides) / len(notes_valides)
        return None

    @property
    def adresse_complete(self):
        """Adresse complète formatée"""
        parts = [self.adresse, self.code_postal, self.ville, self.pays]
        return ", ".join([part for part in parts if part])


class CommandeFournisseur(models.Model):
    """Commandes passées aux fournisseurs"""

    STATUT_CHOICES = [
        ('BROUILLON', 'Brouillon'),
        ('ENVOYEE', 'Envoyée'),
        ('CONFIRMEE', 'Confirmée'),
        ('RECEPTION_PARTIELLE', 'Réception partielle'),
        ('RECUE', 'Reçue'),
        ('ANNULEE', 'Annulée'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    numero = models.CharField(max_length=50, unique=True, verbose_name="Numéro de commande")

    # Relations
    fournisseur = models.ForeignKey(Fournisseur, on_delete=models.PROTECT, related_name='commandes')

    # Informations
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='BROUILLON')
    date_commande = models.DateTimeField(auto_now_add=True)
    date_livraison_prevue = models.DateField(null=True, blank=True)
    date_reception = models.DateField(null=True, blank=True)

    # Montants
    sous_total = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    montant_tva = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    montant_total = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Informations complémentaires
    notes = models.TextField(blank=True)

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "Commande fournisseur"
        verbose_name_plural = "Commandes fournisseurs"
        ordering = ['-date_commande']

    def __str__(self):
        return f"Commande {self.numero} - {self.fournisseur.nom}"
