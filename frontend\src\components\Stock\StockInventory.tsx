/**
 * Composant StockInventory - Gestion des inventaires
 */

import React, { useState, useEffect } from 'react';
import { StockService } from '../../services/apiService';
import { useNotify } from '../Common/NotificationSystem';

interface InventoryItem {
  id: string;
  produit: {
    id: string;
    nom: string;
    reference: string;
  };
  quantite_theorique: number;
  quantite_reelle: number;
  ecart: number;
  valeur_ecart: number;
  date_inventaire: string;
}

const StockInventory: React.FC = () => {
  const notify = useNotify();
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewInventory, setShowNewInventory] = useState(false);

  useEffect(() => {
    loadInventoryData();
  }, []);

  const loadInventoryData = async () => {
    try {
      setLoading(true);
      const response = await StockService.getInventories();
      if (response.success) {
        setInventoryItems(response.data || []);
      } else {
        notify.error('Erreur lors du chargement des inventaires');
      }
    } catch (error) {
      console.error('Erreur:', error);
      notify.error('Erreur lors du chargement des inventaires');
    } finally {
      setLoading(false);
    }
  };

  const handleNewInventory = () => {
    setShowNewInventory(true);
  };

  const handleValidateInventory = async (itemId: string) => {
    try {
      const response = await StockService.validateInventoryItem(itemId);
      if (response.success) {
        notify.success('Inventaire validé avec succès');
        loadInventoryData();
      } else {
        notify.error('Erreur lors de la validation');
      }
    } catch (error) {
      console.error('Erreur:', error);
      notify.error('Erreur lors de la validation');
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Chargement des inventaires...</p>
      </div>
    );
  }

  return (
    <div className="stock-inventory">
      <div className="inventory-header">
        <h2>📋 Inventaires</h2>
        <button 
          className="btn btn-primary"
          onClick={handleNewInventory}
        >
          ➕ Nouvel Inventaire
        </button>
      </div>

      <div className="inventory-stats">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>{inventoryItems.length}</h3>
            <p>Articles inventoriés</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">⚠️</div>
          <div className="stat-content">
            <h3>{inventoryItems.filter(item => item.ecart !== 0).length}</h3>
            <p>Écarts détectés</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>{inventoryItems.reduce((sum, item) => sum + Math.abs(item.valeur_ecart), 0).toFixed(3)} DT</h3>
            <p>Valeur des écarts</p>
          </div>
        </div>
      </div>

      <div className="inventory-table">
        <table>
          <thead>
            <tr>
              <th>Produit</th>
              <th>Référence</th>
              <th>Qté Théorique</th>
              <th>Qté Réelle</th>
              <th>Écart</th>
              <th>Valeur Écart</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {inventoryItems.map(item => (
              <tr key={item.id} className={item.ecart !== 0 ? 'has-variance' : ''}>
                <td>{item.produit.nom}</td>
                <td>{item.produit.reference}</td>
                <td>{item.quantite_theorique}</td>
                <td>{item.quantite_reelle}</td>
                <td className={item.ecart > 0 ? 'positive' : item.ecart < 0 ? 'negative' : ''}>
                  {item.ecart > 0 ? '+' : ''}{item.ecart}
                </td>
                <td className={item.valeur_ecart > 0 ? 'positive' : item.valeur_ecart < 0 ? 'negative' : ''}>
                  {item.valeur_ecart > 0 ? '+' : ''}{item.valeur_ecart.toFixed(3)} DT
                </td>
                <td>{new Date(item.date_inventaire).toLocaleDateString('fr-FR')}</td>
                <td>
                  <button 
                    className="btn btn-sm btn-success"
                    onClick={() => handleValidateInventory(item.id)}
                  >
                    ✓ Valider
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {inventoryItems.length === 0 && (
          <div className="empty-state">
            <div className="empty-icon">📋</div>
            <h3>Aucun inventaire</h3>
            <p>Commencez par créer votre premier inventaire</p>
            <button 
              className="btn btn-primary"
              onClick={handleNewInventory}
            >
              ➕ Créer un inventaire
            </button>
          </div>
        )}
      </div>

      {showNewInventory && (
        <div className="modal-overlay" onClick={() => setShowNewInventory(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>📋 Nouvel Inventaire</h3>
              <button 
                className="modal-close"
                onClick={() => setShowNewInventory(false)}
              >
                ✕
              </button>
            </div>
            <div className="modal-body">
              <p>Fonctionnalité de création d'inventaire à implémenter</p>
            </div>
            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowNewInventory(false)}
              >
                Annuler
              </button>
              <button className="btn btn-primary">
                Créer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockInventory;
