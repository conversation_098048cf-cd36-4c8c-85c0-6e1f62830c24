from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import MouvementStockViewSet, InventaireViewSet, LigneInventaireViewSet

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'mouvements', MouvementStockViewSet, basename='mouvement-stock')
router.register(r'inventaires', InventaireViewSet, basename='inventaire')
router.register(r'lignes-inventaire', LigneInventaireViewSet, basename='ligne-inventaire')

urlpatterns = [
    path('', include(router.urls)),
]
