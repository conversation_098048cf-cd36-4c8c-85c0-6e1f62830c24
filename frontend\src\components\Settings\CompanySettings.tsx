/**
 * Paramètres de l'entreprise
 */

import React, { useState, useEffect } from "react";
import { useNotify } from "../Common/NotificationSystem";

interface CompanyData {
  nom: string;
  siret: string;
  tva_intracommunautaire: string;
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
  telephone: string;
  email: string;
  site_web: string;
  logo_url: string;
  capital_social: number;
  forme_juridique: string;
  code_ape: string;
  rcs: string;
  banque_nom: string;
  banque_iban: string;
  banque_bic: string;
}

const CompanySettings: React.FC = () => {
  const notify = useNotify();

  const [formData, setFormData] = useState<CompanyData>({
    nom: "Société Ben Chaabene de Commerce",
    siret: "1283008",
    tva_intracommunautaire: "W/A/M/000",
    adresse: "10 Rue de la Commission",
    ville: "Nabeul",
    code_postal: "8000",
    pays: "Tunisie",
    telephone: "+216 72 287 863",
    email: "<EMAIL>",
    site_web: "www.benachaabene.com",
    logo_url: "/logo_ben_chaabene_moderne.png",
    capital_social: 50000,
    forme_juridique: "SARL",
    code_ape: "4690Z",
    rcs: "Nabeul B ***********",
    banque_nom: "Banque de Tunisie",
    banque_iban: "TN59 1000 6035 0000 0000 0000 0000",
    banque_bic: "BTUBTNT1XXX",
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadCompanyData();
  }, []);

  const loadCompanyData = async () => {
    try {
      setLoading(true);
      // Simuler le chargement des données
      setFormData({
        nom: "Mon Entreprise SARL",
        siret: "12345678901234",
        tva_intracommunautaire: "FR12345678901",
        adresse: "123 Rue de la Paix",
        ville: "Paris",
        code_postal: "75001",
        pays: "France",
        telephone: "01 23 45 67 89",
        email: "<EMAIL>",
        site_web: "https://www.monentreprise.fr",
        logo_url: "",
        capital_social: 10000,
        forme_juridique: "SARL",
        code_ape: "6201Z",
        rcs: "Paris B ***********",
        banque_nom: "Banque Populaire",
        banque_iban: "FR76 1234 5678 9012 3456 7890 123",
        banque_bic: "CCBPFRPPXXX",
      });
    } catch (error: any) {
      notify.error("Erreur lors du chargement des paramètres");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.nom.trim()) {
      newErrors.nom = "Nom de l'entreprise requis";
    }

    // SIRET supprimé car non applicable en Tunisie

    if (!formData.email.trim()) {
      newErrors.email = "Email requis";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Format email invalide";
    }

    if (!formData.adresse.trim()) {
      newErrors.adresse = "Adresse requise";
    }

    if (!formData.ville.trim()) {
      newErrors.ville = "Ville requise";
    }

    if (!formData.code_postal.trim()) {
      newErrors.code_postal = "Code postal requis";
    }

    if (
      formData.banque_iban &&
      !/^[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}[A-Z0-9]{1,23}$/.test(
        formData.banque_iban.replace(/\s/g, "")
      )
    ) {
      newErrors.banque_iban = "Format IBAN invalide";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notify.error("Veuillez corriger les erreurs du formulaire");
      return;
    }

    try {
      setSaving(true);

      // Simuler la sauvegarde
      await new Promise((resolve) => setTimeout(resolve, 1000));

      notify.success("Paramètres de l'entreprise mis à jour avec succès");
    } catch (error: any) {
      notify.error("Erreur lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des paramètres...</p>
      </div>
    );
  }

  return (
    <div className="company-settings">
      <form onSubmit={handleSubmit}>
        {/* Informations générales */}
        <div className="settings-section">
          <h3>🏢 Informations Générales</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Nom de l'entreprise *</label>
              <input
                type="text"
                value={formData.nom}
                onChange={(e) => handleInputChange("nom", e.target.value)}
                className={`form-input ${errors.nom ? "error" : ""}`}
                placeholder="Mon Entreprise SARL"
              />
              {errors.nom && <span className="error-text">{errors.nom}</span>}
            </div>

            <div className="form-group">
              <label>Forme juridique</label>
              <select
                value={formData.forme_juridique}
                onChange={(e) =>
                  handleInputChange("forme_juridique", e.target.value)
                }
                className="form-select">
                <option value="">Sélectionner</option>
                <option value="SARL">SARL</option>
                <option value="SAS">SAS</option>
                <option value="SA">SA</option>
                <option value="EURL">EURL</option>
                <option value="SNC">SNC</option>
                <option value="Auto-entrepreneur">Auto-entrepreneur</option>
                <option value="Association">Association</option>
              </select>
            </div>

            <div className="form-group">
              <label>Capital social (€)</label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.capital_social}
                onChange={(e) =>
                  handleInputChange(
                    "capital_social",
                    parseFloat(e.target.value) || 0
                  )
                }
                className="form-input"
                placeholder="10000"
              />
            </div>

            <div className="form-group">
              <label>Numéro d'identification fiscale</label>
              <input
                type="text"
                value={formData.siret}
                onChange={(e) => handleInputChange("siret", e.target.value)}
                className="form-input"
                placeholder="Numéro d'identification fiscale (optionnel)"
                maxLength={20}
              />
              <small className="form-help">
                Numéro d'identification fiscale de l'entreprise (optionnel)
              </small>
            </div>

            <div className="form-group">
              <label>TVA Intracommunautaire</label>
              <input
                type="text"
                value={formData.tva_intracommunautaire}
                onChange={(e) =>
                  handleInputChange("tva_intracommunautaire", e.target.value)
                }
                className="form-input"
                placeholder="FR12345678901"
              />
            </div>

            <div className="form-group">
              <label>Code APE</label>
              <input
                type="text"
                value={formData.code_ape}
                onChange={(e) => handleInputChange("code_ape", e.target.value)}
                className="form-input"
                placeholder="6201Z"
              />
            </div>

            <div className="form-group">
              <label>RCS</label>
              <input
                type="text"
                value={formData.rcs}
                onChange={(e) => handleInputChange("rcs", e.target.value)}
                className="form-input"
                placeholder="Paris B ***********"
              />
            </div>
          </div>
        </div>

        {/* Adresse */}
        <div className="settings-section">
          <h3>📍 Adresse</h3>

          <div className="form-grid">
            <div className="form-group full-width">
              <label>Adresse *</label>
              <input
                type="text"
                value={formData.adresse}
                onChange={(e) => handleInputChange("adresse", e.target.value)}
                className={`form-input ${errors.adresse ? "error" : ""}`}
                placeholder="123 Rue de la Paix"
              />
              {errors.adresse && (
                <span className="error-text">{errors.adresse}</span>
              )}
            </div>

            <div className="form-group">
              <label>Code postal *</label>
              <input
                type="text"
                value={formData.code_postal}
                onChange={(e) =>
                  handleInputChange("code_postal", e.target.value)
                }
                className={`form-input ${errors.code_postal ? "error" : ""}`}
                placeholder="75001"
              />
              {errors.code_postal && (
                <span className="error-text">{errors.code_postal}</span>
              )}
            </div>

            <div className="form-group">
              <label>Ville *</label>
              <input
                type="text"
                value={formData.ville}
                onChange={(e) => handleInputChange("ville", e.target.value)}
                className={`form-input ${errors.ville ? "error" : ""}`}
                placeholder="Paris"
              />
              {errors.ville && (
                <span className="error-text">{errors.ville}</span>
              )}
            </div>

            <div className="form-group">
              <label>Pays</label>
              <select
                value={formData.pays}
                onChange={(e) => handleInputChange("pays", e.target.value)}
                className="form-select">
                <option value="France">France</option>
                <option value="Belgique">Belgique</option>
                <option value="Suisse">Suisse</option>
                <option value="Luxembourg">Luxembourg</option>
                <option value="Canada">Canada</option>
              </select>
            </div>
          </div>
        </div>

        {/* Contact */}
        <div className="settings-section">
          <h3>📞 Contact</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Téléphone</label>
              <input
                type="tel"
                value={formData.telephone}
                onChange={(e) => handleInputChange("telephone", e.target.value)}
                className="form-input"
                placeholder="01 23 45 67 89"
              />
            </div>

            <div className="form-group">
              <label>Email *</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`form-input ${errors.email ? "error" : ""}`}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <span className="error-text">{errors.email}</span>
              )}
            </div>

            <div className="form-group">
              <label>Site web</label>
              <input
                type="url"
                value={formData.site_web}
                onChange={(e) => handleInputChange("site_web", e.target.value)}
                className="form-input"
                placeholder="https://www.monentreprise.fr"
              />
            </div>

            <div className="form-group">
              <label>Logo (URL)</label>
              <input
                type="url"
                value={formData.logo_url}
                onChange={(e) => handleInputChange("logo_url", e.target.value)}
                className="form-input"
                placeholder="https://www.monentreprise.fr/logo.png"
              />
            </div>
          </div>
        </div>

        {/* Informations bancaires */}
        <div className="settings-section">
          <h3>🏦 Informations Bancaires</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Nom de la banque</label>
              <input
                type="text"
                value={formData.banque_nom}
                onChange={(e) =>
                  handleInputChange("banque_nom", e.target.value)
                }
                className="form-input"
                placeholder="Banque Populaire"
              />
            </div>

            <div className="form-group">
              <label>IBAN</label>
              <input
                type="text"
                value={formData.banque_iban}
                onChange={(e) =>
                  handleInputChange("banque_iban", e.target.value)
                }
                className={`form-input ${errors.banque_iban ? "error" : ""}`}
                placeholder="FR76 1234 5678 9012 3456 7890 123"
              />
              {errors.banque_iban && (
                <span className="error-text">{errors.banque_iban}</span>
              )}
            </div>

            <div className="form-group">
              <label>BIC/SWIFT</label>
              <input
                type="text"
                value={formData.banque_bic}
                onChange={(e) =>
                  handleInputChange("banque_bic", e.target.value)
                }
                className="form-input"
                placeholder="CCBPFRPPXXX"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="settings-actions">
          <button
            type="button"
            className="btn btn-outline"
            onClick={loadCompanyData}
            disabled={saving}>
            🔄 Annuler
          </button>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={saving}>
            {saving ? "⏳ Sauvegarde..." : "💾 Sauvegarder"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanySettings;
