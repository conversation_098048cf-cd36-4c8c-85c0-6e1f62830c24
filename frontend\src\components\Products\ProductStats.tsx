/**
 * Statistiques avancées des produits
 */

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ProductService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";

interface ProductStats {
  total_produits: number;
  produits_actifs: number;
  produits_inactifs: number;
  produits_rupture: number;
  stock_total_value: number;
  ca_total_produits: number;
  marge_moyenne: number;
  top_ventes: any[];
  categories: any[];
  alertes_stock: any[];
  evolution_ventes: any[];
}

interface ProductStatsProps {
  embedded?: boolean;
  period?: "month" | "quarter" | "year";
}

const ProductStats: React.FC<ProductStatsProps> = ({
  embedded = false,
  period = "month",
}) => {
  const navigate = useNavigate();
  const notify = useNotify();

  const [stats, setStats] = useState<ProductStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState(period);

  useEffect(() => {
    loadStats();
  }, [selectedPeriod]);

  const loadStats = async () => {
    try {
      setLoading(true);
      // Simulation de statistiques pour le mode démo
      setStats({
        total_produits: 245,
        produits_actifs: 220,
        produits_inactifs: 25,
        produits_rupture: 12,
        stock_total_value: 125000,
        ca_total_produits: 285000,
        marge_moyenne: 35.5,
        top_ventes: [],
        categories: [],
        alertes_stock: [],
        evolution_ventes: [],
      });
    } catch (error: any) {
      console.error("Erreur lors du chargement des statistiques:", error);
      notify.error("Erreur lors du chargement des statistiques");
      // Données de démonstration en cas d'erreur
      setStats({
        total_produits: 89,
        produits_actifs: 76,
        produits_inactifs: 8,
        produits_rupture: 5,
        stock_total_value: 98000,
        ca_total_produits: 185000,
        marge_moyenne: 32.5,
        top_ventes: [],
        categories: [],
        alertes_stock: [],
        evolution_ventes: [],
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const calculatePercentage = (value: number, total: number) => {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  if (loading) {
    return (
      <div className="product-stats-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des statistiques...</p>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className={`product-stats ${embedded ? "embedded" : ""}`}>
      {!embedded && (
        <div className="stats-header">
          <h3>📊 Statistiques Produits</h3>
          <div className="period-selector">
            <button
              className={`period-btn ${
                selectedPeriod === "month" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("month")}>
              Ce mois
            </button>
            <button
              className={`period-btn ${
                selectedPeriod === "quarter" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("quarter")}>
              Trimestre
            </button>
            <button
              className={`period-btn ${
                selectedPeriod === "year" ? "active" : ""
              }`}
              onClick={() => setSelectedPeriod("year")}>
              Année
            </button>
          </div>
        </div>
      )}

      <div className="stats-grid">
        {/* Total produits */}
        <div className="stat-card primary">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <h4>Total Produits</h4>
            <div className="stat-value">{stats.total_produits}</div>
            <div className="stat-subtitle">{stats.produits_actifs} actifs</div>
            <div className="stat-progress">
              <div
                className="progress-bar primary"
                style={{
                  width: `${calculatePercentage(
                    stats.produits_actifs,
                    stats.total_produits
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>

        {/* Valeur du stock */}
        <div className="stat-card success">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h4>Valeur Stock</h4>
            <div className="stat-value">
              {formatCurrency(stats.stock_total_value)}
            </div>
            <div className="stat-subtitle">Inventaire total</div>
            <div className="stat-progress">
              <div
                className="progress-bar success"
                style={{ width: "75%" }}></div>
            </div>
          </div>
        </div>

        {/* CA Produits */}
        <div className="stat-card info">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <h4>CA Produits</h4>
            <div className="stat-value">
              {formatCurrency(stats.ca_total_produits)}
            </div>
            <div className="stat-subtitle">
              Marge: {stats.marge_moyenne.toFixed(1)}%
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar info"
                style={{
                  width: `${Math.min(stats.marge_moyenne, 100)}%`,
                }}></div>
            </div>
          </div>
        </div>

        {/* Alertes stock */}
        <div className="stat-card danger">
          <div className="stat-icon">⚠️</div>
          <div className="stat-content">
            <h4>Alertes Stock</h4>
            <div className="stat-value">{stats.alertes_stock.length}</div>
            <div className="stat-subtitle">
              {stats.produits_rupture} en rupture
            </div>
            <div className="stat-progress">
              <div
                className="progress-bar danger"
                style={{
                  width: `${calculatePercentage(
                    stats.alertes_stock.length,
                    stats.total_produits
                  )}%`,
                }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Top ventes */}
      {!embedded && stats.top_ventes.length > 0 && (
        <div className="top-products">
          <h4>🏆 Top Ventes</h4>
          <div className="top-products-list">
            {stats.top_ventes.map((product, index) => (
              <div
                key={index}
                className="top-product-item">
                <div className="product-rank">#{index + 1}</div>
                <div className="product-info">
                  <div className="product-details">
                    <span className="product-name">{product.nom}</span>
                    <span className="product-sales">
                      {product.ventes} ventes
                    </span>
                  </div>
                  <span className="product-ca">
                    {formatCurrency(product.ca)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Catégories */}
      {!embedded && stats.categories.length > 0 && (
        <div className="categories-stats">
          <h4>📂 Répartition par Catégories</h4>
          <div className="categories-list">
            {stats.categories.map((category, index) => (
              <div
                key={index}
                className="category-item">
                <div className="category-info">
                  <span className="category-name">{category.nom}</span>
                  <span className="category-count">
                    {category.count} produits
                  </span>
                </div>
                <div className="category-ca">{formatCurrency(category.ca)}</div>
                <div className="category-progress">
                  <div
                    className="progress-bar primary"
                    style={{
                      width: `${calculatePercentage(
                        category.ca,
                        stats.ca_total_produits
                      )}%`,
                    }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Alertes stock */}
      {stats.alertes_stock.length > 0 && (
        <div className="stock-alerts">
          <h4>⚠️ Alertes Stock</h4>
          <div className="alerts-list">
            {stats.alertes_stock.map((alert, index) => (
              <div
                key={index}
                className="alert-item">
                <div className="alert-icon">
                  {alert.stock === 0 ? "❌" : "⚠️"}
                </div>
                <div className="alert-info">
                  <span className="alert-product">{alert.nom}</span>
                  <span className="alert-details">
                    Stock: {alert.stock} (min: {alert.minimum})
                  </span>
                </div>
                <button
                  className="btn btn-warning btn-sm"
                  onClick={() => navigate(`/products/${alert.id}/edit`)}>
                  🔧 Ajuster
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Résumé par statut */}
      <div className="stats-summary">
        <div className="summary-item">
          <div className="summary-label">✅ Actifs</div>
          <div className="summary-value success">{stats.produits_actifs}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">⏸️ Inactifs</div>
          <div className="summary-value warning">{stats.produits_inactifs}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">❌ Rupture</div>
          <div className="summary-value danger">{stats.produits_rupture}</div>
        </div>
        <div className="summary-item">
          <div className="summary-label">💎 Marge Moy.</div>
          <div className="summary-value">{stats.marge_moyenne.toFixed(1)}%</div>
        </div>
      </div>
    </div>
  );
};

export default ProductStats;
