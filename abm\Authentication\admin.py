"""
Administration pour l'authentification
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _

from .models import CustomUser, UserSession, LoginAttempt


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """Administration des utilisateurs personnalisés"""
    
    list_display = [
        'username', 'email', 'first_name', 'last_name',
        'role', 'is_active', 'date_joined'
    ]
    
    list_filter = [
        'role', 'is_active', 'is_staff', 'is_superuser', 'date_joined'
    ]
    
    search_fields = ['username', 'email', 'first_name', 'last_name', 'company']
    
    ordering = ['-date_joined']
    
    fieldsets = UserAdmin.fieldsets + (
        (_('Informations supplémentaires'), {
            'fields': ('role', 'phone', 'company')
        }),
    )
    
    add_fieldsets = UserAdmin.add_fieldsets + (
        (_('Informations supplémentaires'), {
            'fields': ('role', 'phone', 'company', 'email', 'first_name', 'last_name')
        }),
    )
    
    def get_queryset(self, request):
        """Filtrer les utilisateurs selon les permissions de l'admin"""
        qs = super().get_queryset(request)
        
        # Si l'admin n'est pas un superuser, il ne peut pas voir les superadmins
        if not request.user.is_superuser:
            qs = qs.exclude(role='SUPERADMIN')
        
        return qs


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """Administration des sessions utilisateur"""
    
    list_display = [
        'user', 'ip_address', 'created_at', 'last_activity', 'is_active'
    ]
    
    list_filter = ['is_active', 'created_at', 'last_activity']
    
    search_fields = ['user__username', 'ip_address', 'session_key']
    
    readonly_fields = ['session_key', 'created_at', 'last_activity']
    
    ordering = ['-last_activity']
    
    def has_add_permission(self, request):
        """Empêcher l'ajout manuel de sessions"""
        return False


@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    """Administration des tentatives de connexion"""
    
    list_display = [
        'username', 'ip_address', 'success', 'timestamp'
    ]
    
    list_filter = ['success', 'timestamp']
    
    search_fields = ['username', 'ip_address']
    
    readonly_fields = ['username', 'ip_address', 'success', 'timestamp', 'user_agent']
    
    ordering = ['-timestamp']
    
    def has_add_permission(self, request):
        """Empêcher l'ajout manuel de tentatives"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Empêcher la modification des tentatives"""
        return False
