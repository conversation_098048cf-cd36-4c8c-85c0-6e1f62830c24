import React, { useState, useEffect, useCallback } from "react";
import { ProductService } from "../../services/apiService";
import { toast } from "react-toastify";
import "./Products.css";

interface Product {
  id: string;
  reference: string;
  nom: string;
  description?: string;
  prix_vente: number;
  prix_achat: number;
  stock_actuel: number;
  stock_minimum: number;
  unite: string;
  categorie_nom?: string;
  statut: string;
  type_produit: string;
  taux_tva: number;
  created_at: string;
  updated_at: string;
}

const ProductsList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showProductDetails, setShowProductDetails] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [showStockUpdate, setShowStockUpdate] = useState(false);
  const [stockUpdateValue, setStockUpdateValue] = useState("");

  const loadProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {};
      if (searchTerm.trim()) {
        params.search = searchTerm;
      }

      // Données simulées pour les produits
      const mockProducts = [
        {
          id: "1",
          reference: "B1994471111",
          nom: "PRODUIT DESIGN FUTURISTE",
          description:
            "Produit innovant avec design futuriste de haute qualité",
          prix_vente: 57.015,
          prix_achat: 35.0,
          stock_actuel: 45,
          stock_minimum: 10,
          unite: "pièce",
          categorie_nom: "Design",
          statut: "ACTIF",
          type_produit: "PHYSIQUE",
          taux_tva: 20,
          created_at: "2024-01-10T08:00:00Z",
          updated_at: "2024-03-15T10:30:00Z",
        },
        {
          id: "2",
          reference: "B1991720001",
          nom: "ARTICLE PREMIER PLAN",
          description: "Article de première qualité pour usage professionnel",
          prix_vente: 25.0,
          prix_achat: 15.0,
          stock_actuel: 78,
          stock_minimum: 20,
          unite: "pièce",
          categorie_nom: "Professionnel",
          statut: "ACTIF",
          type_produit: "PHYSIQUE",
          taux_tva: 20,
          created_at: "2024-02-05T10:30:00Z",
          updated_at: "2024-03-10T14:15:00Z",
        },
        {
          id: "3",
          reference: "B1993500002",
          nom: "SERVICE FUTURISME ELEGANT",
          description: "Service élégant avec approche futuriste",
          prix_vente: 45.5,
          prix_achat: 25.0,
          stock_actuel: 0,
          stock_minimum: 5,
          unite: "service",
          categorie_nom: "Services",
          statut: "ACTIF",
          type_produit: "SERVICE",
          taux_tva: 20,
          created_at: "2024-03-01T16:20:00Z",
          updated_at: "2024-03-12T09:45:00Z",
        },
        {
          id: "4",
          reference: "PACK001",
          nom: "Pack Solution Complète",
          description: "Pack complet incluant produits et services",
          prix_vente: 125.75,
          prix_achat: 85.0,
          stock_actuel: 5,
          stock_minimum: 10,
          unite: "pack",
          categorie_nom: "Packs",
          statut: "ALERTE",
          type_produit: "PACK",
          taux_tva: 20,
          created_at: "2024-02-15T14:45:00Z",
          updated_at: "2024-03-08T11:20:00Z",
        },
      ];

      // Simuler un délai de chargement
      await new Promise((resolve) => setTimeout(resolve, 700));

      setProducts(mockProducts);
    } catch (err: any) {
      setError(err.message);
      toast.error(`Erreur lors du chargement des produits: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [searchTerm]);

  const showProductDetailsModal = (product: any) => {
    setSelectedProduct(product);
    setShowProductDetails(true);
  };

  const openStockUpdate = (product: any) => {
    setSelectedProduct(product);
    setStockUpdateValue(product.stock_actuel.toString());
    setShowStockUpdate(true);
  };

  const updateProductStock = () => {
    if (selectedProduct && stockUpdateValue) {
      const newStock = parseInt(stockUpdateValue);
      setProducts((prev) =>
        prev.map((product) =>
          product.id === selectedProduct.id
            ? {
                ...product,
                stock_actuel: newStock,
                statut: newStock <= 5 ? "ALERTE" : "ACTIF",
              }
            : product
        )
      );
      toast.success(
        `Stock mis à jour: ${selectedProduct.nom} → ${newStock} unités`
      );
      setShowStockUpdate(false);
      setStockUpdateValue("");
    }
  };

  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadProducts();
  };

  if (loading) {
    return (
      <div className="products-list">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Chargement des produits...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="products-list">
        <div className="error-container">
          <h2>Erreur</h2>
          <p>{error}</p>
          <button
            onClick={loadProducts}
            className="retry-button">
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="products-list">
      <div className="products-header">
        <h2>Gestion des Produits</h2>
        <div className="header-actions">
          <form
            onSubmit={handleSearch}
            className="search-form">
            <input
              type="text"
              placeholder="Rechercher un produit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <button
              type="submit"
              className="search-button">
              🔍
            </button>
          </form>
          <button
            onClick={() => toast.info("Fonctionnalité en développement")}
            className="btn-primary">
            + Nouveau Produit
          </button>
        </div>
      </div>

      <div className="products-grid">
        {products.map((product) => (
          <div
            key={product.id}
            className="product-card"
            onClick={() => toast.info(`Détail du produit: ${product.nom}`)}>
            <div className="product-header">
              <h3>{product.nom}</h3>
              <span className={`status-badge ${product.statut.toLowerCase()}`}>
                {product.statut}
              </span>
            </div>
            <p className="product-reference">Réf: {product.reference}</p>
            <p className="product-description">{product.description}</p>
            <div className="product-details">
              <span className="price">{product.prix_vente.toFixed(2)} TND</span>
              <span
                className={`stock ${
                  product.stock_actuel <= 10 ? "low-stock" : ""
                }`}>
                Stock: {product.stock_actuel}
              </span>
              <span className="category">
                {product.categorie_nom || "Sans catégorie"}
              </span>
            </div>
            <div className="product-actions">
              <button
                className="action-btn view-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  showProductDetailsModal(product);
                }}
                title="Voir détails">
                👁️
              </button>
              <button
                className="action-btn stock-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  openStockUpdate(product);
                }}
                title="Mettre à jour le stock">
                📦
              </button>
              <button
                className="action-btn edit-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  toast.info(`Modifier: ${product.nom}`);
                }}
                title="Modifier">
                ✏️
              </button>
              <button
                className="action-btn delete-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  if (window.confirm(`Supprimer le produit ${product.nom} ?`)) {
                    toast.info(`Produit ${product.nom} supprimé`);
                  }
                }}
                title="Supprimer">
                🗑️
              </button>
            </div>
          </div>
        ))}
      </div>

      {products.length === 0 && !loading && (
        <div className="no-products">
          <p>Aucun produit trouvé.</p>
          <button
            onClick={() => toast.info("Fonctionnalité en développement")}
            className="btn-primary">
            Créer le premier produit
          </button>
        </div>
      )}

      {/* Modal des détails de produit */}
      {showProductDetails && selectedProduct && (
        <div
          className="modal-overlay"
          onClick={() => setShowProductDetails(false)}>
          <div
            className="modal-content product-details-modal"
            onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>📦 Détails du Produit - {selectedProduct.nom}</h3>
              <div className="modal-actions">
                <button
                  className="btn btn-secondary"
                  onClick={() => openStockUpdate(selectedProduct)}>
                  📦 Gérer Stock
                </button>
                <button
                  className="btn btn-danger"
                  onClick={() => setShowProductDetails(false)}>
                  ✕ Fermer
                </button>
              </div>
            </div>
            <div className="modal-body">
              <div className="product-details-grid">
                <div className="detail-section">
                  <h4>📋 Informations Générales</h4>
                  <div className="detail-item">
                    <span className="detail-label">Nom:</span>
                    <span className="detail-value">{selectedProduct.nom}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Référence:</span>
                    <span className="detail-value">
                      {selectedProduct.reference}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Catégorie:</span>
                    <span className="detail-value">
                      {selectedProduct.categorie_nom}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Statut:</span>
                    <span
                      className={`status ${selectedProduct.statut.toLowerCase()}`}>
                      {selectedProduct.statut}
                    </span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>💰 Prix</h4>
                  <div className="detail-item">
                    <span className="detail-label">Prix d'achat:</span>
                    <span className="detail-value">
                      {selectedProduct.prix_achat.toFixed(3)} TND
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Prix de vente:</span>
                    <span className="detail-value">
                      {selectedProduct.prix_vente.toFixed(3)} TND
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Marge:</span>
                    <span className="detail-value profit">
                      {(
                        ((selectedProduct.prix_vente -
                          selectedProduct.prix_achat) /
                          selectedProduct.prix_achat) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>📦 Stock</h4>
                  <div className="detail-item">
                    <span className="detail-label">Stock actuel:</span>
                    <span
                      className={`detail-value ${
                        selectedProduct.stock_actuel <= 10 ? "low-stock" : ""
                      }`}>
                      {selectedProduct.stock_actuel} unités
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Stock minimum:</span>
                    <span className="detail-value">
                      {selectedProduct.stock_minimum} unités
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Unité:</span>
                    <span className="detail-value">
                      {selectedProduct.unite}
                    </span>
                  </div>
                </div>

                <div className="detail-section full-width">
                  <h4>📝 Description</h4>
                  <div className="description-content">
                    {selectedProduct.description}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de mise à jour du stock */}
      {showStockUpdate && selectedProduct && (
        <div
          className="modal-overlay"
          onClick={() => setShowStockUpdate(false)}>
          <div
            className="modal-content stock-update-modal"
            onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>📦 Mise à jour du Stock - {selectedProduct.nom}</h3>
              <button
                className="btn btn-danger"
                onClick={() => setShowStockUpdate(false)}>
                ✕ Fermer
              </button>
            </div>
            <div className="modal-body">
              <div className="stock-update-form">
                <div className="current-stock">
                  <h4>
                    Stock actuel:{" "}
                    <span className="stock-value">
                      {selectedProduct.stock_actuel} unités
                    </span>
                  </h4>
                </div>
                <div className="form-group">
                  <label htmlFor="new-stock">Nouveau stock:</label>
                  <input
                    id="new-stock"
                    type="number"
                    value={stockUpdateValue}
                    onChange={(e) => setStockUpdateValue(e.target.value)}
                    className="form-input"
                    min="0"
                    placeholder="Entrez le nouveau stock"
                  />
                </div>
                <div className="stock-actions">
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowStockUpdate(false)}>
                    Annuler
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={updateProductStock}
                    disabled={
                      !stockUpdateValue ||
                      stockUpdateValue ===
                        selectedProduct.stock_actuel.toString()
                    }>
                    ✅ Mettre à jour
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductsList;
