/**
 * Liste des produits e-commerce
 */

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ProductService } from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";
import "../Products/Products.css";

interface Product {
  id: number;
  nom: string;
  description: string;
  prix_unitaire: number;
  stock_actuel: number;
  taux_tva: number;
  is_active: boolean;
  code_produit: string;
}

const EcommerceProductsList: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const result = await ProductService.getProducts();
      if (result?.results) {
        // Filtrer seulement les produits actifs pour l'e-commerce
        setProducts(result.results.filter((p: Product) => p.is_active));
      }
    } catch (error) {
      notify.error("Erreur", "Erreur lors du chargement des produits");
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = products.filter(
    (product) =>
      product.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.code_produit.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">Chargement des produits...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Catalogue E-commerce</h1>
            <p className="page-subtitle">Produits disponibles à la vente</p>
          </div>
        </div>
      </div>

      <div className="page-content">
        {/* Barre de recherche */}
        <div style={{ marginBottom: "20px" }}>
          <input
            type="text"
            placeholder="Rechercher des produits..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: "100%",
              maxWidth: "400px",
              padding: "10px 15px",
              border: "1px solid #dee2e6",
              borderRadius: "8px",
              fontSize: "14px",
            }}
          />
        </div>

        {/* Grille des produits */}
        <div className="products-grid">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="product-card ecommerce-card"
              style={{
                border: "1px solid #e2e8f0",
                borderRadius: "12px",
                padding: "20px",
                backgroundColor: "white",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                transition: "transform 0.2s ease, box-shadow 0.2s ease",
                cursor: "pointer",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = "translateY(-4px)";
                e.currentTarget.style.boxShadow = "0 8px 25px rgba(0,0,0,0.15)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = "translateY(0)";
                e.currentTarget.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
              }}>
              <div className="product-header">
                <h3 style={{ margin: "0 0 8px 0", color: "#2c3e50" }}>
                  {product.nom}
                </h3>
                <span
                  className="product-code"
                  style={{
                    background: "#f8f9fa",
                    padding: "2px 8px",
                    borderRadius: "4px",
                    fontSize: "12px",
                    color: "#6c757d",
                  }}>
                  {product.code_produit}
                </span>
              </div>

              {product.description && (
                <p
                  style={{
                    color: "#64748b",
                    fontSize: "14px",
                    margin: "12px 0",
                    lineHeight: "1.4",
                  }}>
                  {product.description.length > 100
                    ? `${product.description.substring(0, 100)}...`
                    : product.description}
                </p>
              )}

              <div
                className="product-details"
                style={{ marginTop: "16px" }}>
                <div
                  className="price-section"
                  style={{ marginBottom: "12px" }}>
                  <span
                    className="price"
                    style={{
                      fontSize: "24px",
                      fontWeight: "bold",
                      color: "#3498db",
                    }}>
                    {product.prix_unitaire.toFixed(2)} €
                  </span>
                  <span
                    style={{
                      fontSize: "12px",
                      color: "#6c757d",
                      marginLeft: "8px",
                    }}>
                    TTC (TVA {product.taux_tva}%)
                  </span>
                </div>

                <div
                  className="stock-info"
                  style={{ marginBottom: "16px" }}>
                  <span
                    style={{
                      fontSize: "14px",
                      color: product.stock_actuel > 0 ? "#27ae60" : "#e74c3c",
                      fontWeight: "500",
                    }}>
                    {product.stock_actuel > 0
                      ? `${product.stock_actuel} en stock`
                      : "Rupture de stock"}
                  </span>
                </div>

                <button
                  style={{
                    width: "100%",
                    padding: "12px",
                    backgroundColor:
                      product.stock_actuel > 0 ? "#3498db" : "#95a5a6",
                    color: "white",
                    border: "none",
                    borderRadius: "8px",
                    fontSize: "14px",
                    fontWeight: "600",
                    cursor:
                      product.stock_actuel > 0 ? "pointer" : "not-allowed",
                    transition: "background-color 0.2s ease",
                  }}
                  disabled={product.stock_actuel === 0}
                  onClick={() => {
                    if (product.stock_actuel > 0) {
                      notify.success(
                        "Succès",
                        `${product.nom} ajouté au panier`
                      );
                    }
                  }}
                  onMouseEnter={(e) => {
                    if (product.stock_actuel > 0) {
                      e.currentTarget.style.backgroundColor = "#2980b9";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (product.stock_actuel > 0) {
                      e.currentTarget.style.backgroundColor = "#3498db";
                    }
                  }}>
                  {product.stock_actuel > 0
                    ? "Ajouter au panier"
                    : "Indisponible"}
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div
            style={{
              textAlign: "center",
              padding: "60px 20px",
              color: "#7f8c8d",
            }}>
            <div style={{ fontSize: "48px", marginBottom: "16px" }}>📦</div>
            <h3 style={{ marginBottom: "8px" }}>Aucun produit trouvé</h3>
            <p>Essayez de modifier votre recherche ou revenez plus tard.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EcommerceProductsList;
