/**
 * Index des composants Clients
 * Exporte tous les composants de gestion des clients
 */

// Composants principaux
export { default as SimpleClients } from "./SimpleClients";
export { default as ClientForm } from "./ClientForm";
export { default as ClientDetail } from "./ClientDetail";
export { default as ClientFilters } from "./ClientFilters";
export { default as ClientStats } from "./ClientStats";
export { default as NewClient } from "./NewClient";

// Alias pour compatibilité
export { default as ClientsList } from "./SimpleClients";
export { default as Clients } from "./SimpleClients";

// Types TypeScript - Cohérence avec le backend Django
export interface Client {
  id: string;
  nom: string;
  prenom?: string;
  email: string;
  telephone?: string;
  type_client: "PARTICULIER" | "ENTREPRISE" | "ASSOCIATION" | "ADMINISTRATION";
  statut: "ACTIF" | "INACTIF" | "SUSPENDU" | "PROSPECT";
  segment: "VIP" | "PREMIUM" | "STANDARD" | "NOUVEAU";
  adresse?: string;
  ville?: string;
  code_postal?: string;
  pays: string;
  siret?: string;
  tva_intracommunautaire?: string;
  remise_par_defaut: string;
  limite_credit: string;
  delai_paiement: number;
  total_achats: string;
  nb_commandes: number;
  derniere_commande?: string;
  notes?: string;
  tags?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  // Propriétés calculées
  nom_complet: string;
  adresse_complete: string;
  segment_color: string;
  ca_annuel: number;
  // Propriétés de compatibilité (mappées depuis les vraies propriétés)
  date_creation: string; // Mappé depuis created_at
  nombre_factures: number; // Mappé depuis nb_commandes ou calculé
  chiffre_affaires_total: number; // Mappé depuis ca_annuel ou total_achats
}

export interface ClientFilters {
  search: string;
  type_client: string;
  segment: string;
  statut: string;
  ville: string;
  date_debut: string;
  date_fin: string;
  ca_min: string;
  ca_max: string;
}

export interface ClientStatsData {
  total_clients: number;
  clients_actifs: number;
  clients_inactifs: number;
  prospects: number;
  nouveaux_ce_mois: number;
  chiffre_affaires_total: number;
  ca_moyen_par_client: number;
  clients_vip: number;
  clients_premium: number;
  clients_standard: number;
  entreprises: number;
  particuliers: number;
  top_clients: any[];
  evolution_mensuelle: any[];
}

// Constantes
export const CLIENT_TYPES = {
  INDIVIDUAL: "PARTICULIER",
  COMPANY: "ENTREPRISE",
} as const;

export const CLIENT_SEGMENTS = {
  STANDARD: "STANDARD",
  PREMIUM: "PREMIUM",
  VIP: "VIP",
} as const;

export const CLIENT_STATUS = {
  ACTIVE: "ACTIF",
  INACTIVE: "INACTIF",
  PROSPECT: "PROSPECT",
} as const;

export const CLIENT_TYPE_LABELS = {
  PARTICULIER: "Particulier",
  ENTREPRISE: "Entreprise",
} as const;

export const CLIENT_SEGMENT_LABELS = {
  STANDARD: "Standard",
  PREMIUM: "Premium",
  VIP: "VIP",
} as const;

export const CLIENT_STATUS_LABELS = {
  ACTIF: "Actif",
  INACTIF: "Inactif",
  PROSPECT: "Prospect",
} as const;

// Fonctions utilitaires
export const formatClientName = (client: Client) => {
  return client.nom || "Client sans nom";
};

export const formatClientAddress = (client: Client) => {
  const parts = [];
  if (client.adresse) parts.push(client.adresse);
  if (client.code_postal && client.ville)
    parts.push(`${client.code_postal} ${client.ville}`);
  if (client.pays) parts.push(client.pays);
  return parts.join(", ");
};

export const getClientTypeIcon = (type: string) => {
  return type === "ENTREPRISE" ? "🏢" : "👤";
};

export const getSegmentIcon = (segment: string) => {
  switch (segment) {
    case "VIP":
      return "⭐";
    case "PREMIUM":
      return "💎";
    case "STANDARD":
      return "👤";
    default:
      return "👤";
  }
};

export const getStatusIcon = (status: string) => {
  switch (status) {
    case "ACTIF":
      return "✅";
    case "INACTIF":
      return "⏸️";
    case "PROSPECT":
      return "🎯";
    default:
      return "❓";
  }
};

export const validateClientData = (data: Partial<Client>) => {
  const errors: Record<string, string> = {};

  if (!data.nom?.trim()) {
    errors.nom = "Nom requis";
  }

  if (!data.email?.trim()) {
    errors.email = "Email requis";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.email = "Format email invalide";
  }

  if (!data.type_client) {
    errors.type_client = "Type de client requis";
  }

  // SIRET supprimé car non applicable en Tunisie

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export const calculateClientValue = (client: Client) => {
  const recency = client.derniere_commande
    ? Math.max(
        0,
        365 -
          Math.floor(
            (Date.now() - new Date(client.derniere_commande).getTime()) /
              (1000 * 60 * 60 * 24)
          )
      )
    : 0;

  const frequency = client.nombre_factures;
  const monetary = client.chiffre_affaires_total;

  return {
    recency,
    frequency,
    monetary,
    score: recency * 0.3 + frequency * 0.3 + (monetary / 1000) * 0.4,
  };
};

// Routes des clients
export const CLIENT_ROUTES = {
  LIST: "/clients",
  NEW: "/clients/new",
  DETAIL: "/clients/:id",
  EDIT: "/clients/:id/edit",
  GROUPS: "/clients/groups",
  STATS: "/clients/stats",
} as const;

// Configuration par défaut
export const CLIENT_CONFIG = {
  ITEMS_PER_PAGE: 20,
  DEFAULT_SEGMENT: "STANDARD",
  DEFAULT_STATUS: "PROSPECT",
  DEFAULT_COUNTRY: "France",
  REQUIRED_FIELDS: ["nom", "email", "type_client"],
} as const;
