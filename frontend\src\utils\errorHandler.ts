/**
 * Gestionnaire d'erreurs centralisé pour l'application
 */

import { getErrorMessage } from '../config/api';

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  context?: string;
}

export class ErrorHandler {
  private static errors: AppError[] = [];
  private static maxErrors = 100;

  /**
   * Enregistre une erreur dans le système
   */
  static logError(error: any, context?: string): AppError {
    const appError: AppError = {
      code: error.code || error.status?.toString() || 'UNKNOWN',
      message: error.message || getErrorMessage(error.status) || 'Erreur inconnue',
      details: error.details || error.response?.data,
      timestamp: new Date().toISOString(),
      context
    };

    this.errors.unshift(appError);
    
    // Limiter le nombre d'erreurs stockées
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // Log dans la console en mode développement
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${context || 'App'}] Error:`, appError);
    }

    return appError;
  }

  /**
   * Récupère toutes les erreurs enregistrées
   */
  static getErrors(): AppError[] {
    return [...this.errors];
  }

  /**
   * Efface toutes les erreurs
   */
  static clearErrors(): void {
    this.errors = [];
  }

  /**
   * Récupère les erreurs récentes (dernières 24h)
   */
  static getRecentErrors(): AppError[] {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    return this.errors.filter(error => 
      new Date(error.timestamp) > yesterday
    );
  }

  /**
   * Vérifie si une erreur est critique
   */
  static isCriticalError(error: AppError): boolean {
    const criticalCodes = ['500', 'NETWORK_ERROR', 'AUTH_ERROR'];
    return criticalCodes.includes(error.code);
  }

  /**
   * Formate une erreur pour l'affichage utilisateur
   */
  static formatErrorForUser(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error.response?.data?.message) {
      return error.response.data.message;
    }

    if (error.response?.data?.error) {
      return error.response.data.error;
    }

    if (error.message) {
      return error.message;
    }

    return 'Une erreur inattendue s\'est produite';
  }

  /**
   * Gère les erreurs de réseau
   */
  static handleNetworkError(error: any): AppError {
    let message = 'Erreur de connexion au serveur';
    
    if (!navigator.onLine) {
      message = 'Aucune connexion Internet détectée';
    } else if (error.code === 'ECONNREFUSED') {
      message = 'Le serveur est inaccessible. Vérifiez qu\'il est démarré.';
    } else if (error.name === 'TimeoutError') {
      message = 'La requête a expiré. Le serveur met trop de temps à répondre.';
    }

    return this.logError({
      code: 'NETWORK_ERROR',
      message,
      details: error
    }, 'Network');
  }

  /**
   * Gère les erreurs d'authentification
   */
  static handleAuthError(error: any): AppError {
    // Nettoyer les tokens invalides
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');

    return this.logError({
      code: 'AUTH_ERROR',
      message: 'Session expirée. Veuillez vous reconnecter.',
      details: error
    }, 'Auth');
  }

  /**
   * Gère les erreurs de validation
   */
  static handleValidationError(error: any): AppError {
    let message = 'Erreur de validation des données';
    
    if (error.response?.data?.errors) {
      const errors = error.response.data.errors;
      message = Object.values(errors).flat().join(', ');
    }

    return this.logError({
      code: 'VALIDATION_ERROR',
      message,
      details: error.response?.data
    }, 'Validation');
  }

  /**
   * Intercepteur d'erreurs global
   */
  static handleGlobalError(error: any, context?: string): AppError {
    // Déterminer le type d'erreur
    if (error.response?.status === 401) {
      return this.handleAuthError(error);
    }

    if (error.response?.status === 422 || error.response?.status === 400) {
      return this.handleValidationError(error);
    }

    if (error.code === 'NETWORK_ERROR' || !error.response) {
      return this.handleNetworkError(error);
    }

    // Erreur générique
    return this.logError(error, context);
  }
}

/**
 * Hook pour utiliser le gestionnaire d'erreurs
 */
export const useErrorHandler = () => {
  const handleError = (error: any, context?: string) => {
    return ErrorHandler.handleGlobalError(error, context);
  };

  const getErrors = () => ErrorHandler.getErrors();
  const clearErrors = () => ErrorHandler.clearErrors();
  const getRecentErrors = () => ErrorHandler.getRecentErrors();

  return {
    handleError,
    getErrors,
    clearErrors,
    getRecentErrors,
    formatErrorForUser: ErrorHandler.formatErrorForUser
  };
};

/**
 * Intercepteur pour les requêtes fetch
 */
export const setupGlobalErrorHandling = () => {
  // Intercepter les erreurs non gérées
  window.addEventListener('unhandledrejection', (event) => {
    ErrorHandler.logError(event.reason, 'UnhandledPromise');
  });

  window.addEventListener('error', (event) => {
    ErrorHandler.logError({
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    }, 'GlobalError');
  });
};

export default ErrorHandler;
