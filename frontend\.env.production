# Configuration pour l'environnement de production

# URL de l'API Django (à modifier selon votre serveur de production)
REACT_APP_API_URL=https://votre-domaine.com/api

# Mode debug désactivé en production
REACT_APP_DEBUG=false

# Configuration CORS
REACT_APP_CORS_ENABLED=false

# Configuration WebSocket (si utilisé)
REACT_APP_WS_URL=wss://votre-domaine.com/ws

# Configuration de l'authentification
REACT_APP_AUTH_TOKEN_KEY=authToken
REACT_APP_REFRESH_TOKEN_KEY=refreshToken

# Configuration des uploads
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Configuration de la pagination
REACT_APP_DEFAULT_PAGE_SIZE=20
REACT_APP_MAX_PAGE_SIZE=100

# Configuration des notifications
REACT_APP_NOTIFICATION_TIMEOUT=5000

# Configuration du cache
REACT_APP_CACHE_ENABLED=true
REACT_APP_CACHE_DURATION=600000

# Configuration des rapports
REACT_APP_REPORTS_ENABLED=true

# Configuration E-commerce
REACT_APP_ECOMMERCE_ENABLED=true
REACT_APP_PAYMENT_GATEWAYS=stripe,paypal

# Configuration de monitoring (à configurer avec vos services)
REACT_APP_SENTRY_DSN=
REACT_APP_ANALYTICS_ID=

# Configuration de l'entreprise
REACT_APP_COMPANY_NAME=Ben Chaabene
REACT_APP_COMPANY_LOGO=/assets/logo.png
REACT_APP_COMPANY_ADDRESS=Tunis, Tunisie
REACT_APP_COMPANY_PHONE=+216 XX XXX XXX
REACT_APP_COMPANY_EMAIL=<EMAIL>
