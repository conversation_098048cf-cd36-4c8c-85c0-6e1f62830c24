import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import "./SimpleLoginForm.css";

const LoginForm: React.FC = () => {
  const { login, register } = useAuth();
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  });
  const [registerData, setRegisterData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    nom: "",
    prenom: "",
  });
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showRegister, setShowRegister] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const result = await login(credentials);
      
      if (!result.success) {
        setError(result.message || "Erreur de connexion");
      }
    } catch (err) {
      setError("Erreur de connexion");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const result = await register(registerData);
      
      if (!result.success) {
        setError(result.message || "Erreur d'inscription");
      } else {
        // Inscription réussie, revenir au login
        setShowRegister(false);
        setCredentials({ email: registerData.email, password: "" });
      }
    } catch (err) {
      setError("Erreur d'inscription");
    } finally {
      setIsLoading(false);
    }
  };

  const selectDemoAccount = (email: string) => {
    setCredentials({ email, password: "123456" });
  };

  if (showRegister) {
    return (
      <div className="simple-login-form">
        <div className="login-container">
          <div className="login-header">
            <div className="login-logo">👤</div>
            <h1>Inscription</h1>
            <p>Créer un nouveau compte ABM</p>
          </div>

          <form onSubmit={handleRegister} className="login-form">
            {error && <div className="error-message">❌ {error}</div>}

            <div className="form-group">
              <label htmlFor="prenom">👤 Prénom</label>
              <input
                type="text"
                id="prenom"
                value={registerData.prenom}
                onChange={(e) =>
                  setRegisterData({ ...registerData, prenom: e.target.value })
                }
                required
                className="form-input"
                placeholder="Votre prénom"
              />
            </div>

            <div className="form-group">
              <label htmlFor="nom">👤 Nom</label>
              <input
                type="text"
                id="nom"
                value={registerData.nom}
                onChange={(e) =>
                  setRegisterData({ ...registerData, nom: e.target.value })
                }
                required
                className="form-input"
                placeholder="Votre nom"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">📧 Email</label>
              <input
                type="email"
                id="email"
                value={registerData.email}
                onChange={(e) =>
                  setRegisterData({ ...registerData, email: e.target.value })
                }
                required
                className="form-input"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">🔒 Mot de passe</label>
              <input
                type="password"
                id="password"
                value={registerData.password}
                onChange={(e) =>
                  setRegisterData({ ...registerData, password: e.target.value })
                }
                required
                className="form-input"
                placeholder="••••••••"
              />
            </div>

            <div className="form-group">
              <label htmlFor="confirmPassword">🔒 Confirmer le mot de passe</label>
              <input
                type="password"
                id="confirmPassword"
                value={registerData.confirmPassword}
                onChange={(e) =>
                  setRegisterData({ ...registerData, confirmPassword: e.target.value })
                }
                required
                className="form-input"
                placeholder="••••••••"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="login-button">
              {isLoading ? (
                <>
                  <div className="loading-spinner"></div>
                  Inscription...
                </>
              ) : (
                <>
                  ✅ S'inscrire
                </>
              )}
            </button>
          </form>

          <div className="login-footer">
            <p>
              Déjà un compte ?{" "}
              <button
                type="button"
                onClick={() => setShowRegister(false)}
                className="register-link">
                Se connecter
              </button>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="simple-login-form">
      <div className="login-container">
        <div className="login-header">
          <div className="login-logo">🏢</div>
          <h1>ABM - Ben Chaabène</h1>
          <p>Système de Gestion d'Entreprise</p>
        </div>

        {/* Comptes de démonstration */}
        <div className="demo-accounts">
          <h3>🔑 Comptes de Démonstration</h3>
          <div className="demo-grid">
            <div className="demo-account" onClick={() => selectDemoAccount('<EMAIL>')}>
              <div className="demo-role">👑 Admin</div>
              <div className="demo-email"><EMAIL></div>
            </div>
            <div className="demo-account" onClick={() => selectDemoAccount('<EMAIL>')}>
              <div className="demo-role">👨‍💼 Manager</div>
              <div className="demo-email"><EMAIL></div>
            </div>
            <div className="demo-account" onClick={() => selectDemoAccount('<EMAIL>')}>
              <div className="demo-role">📊 Comptable</div>
              <div className="demo-email"><EMAIL></div>
            </div>
            <div className="demo-account" onClick={() => selectDemoAccount('<EMAIL>')}>
              <div className="demo-role">🛍️ Vendeur</div>
              <div className="demo-email"><EMAIL></div>
            </div>
          </div>
          <p className="demo-note">💡 Cliquez sur un compte pour le sélectionner</p>
        </div>

        <form onSubmit={handleLogin} className="login-form">
          {error && <div className="error-message">❌ {error}</div>}

          <div className="form-group">
            <label htmlFor="email">📧 Email</label>
            <input
              type="email"
              id="email"
              value={credentials.email}
              onChange={(e) =>
                setCredentials({ ...credentials, email: e.target.value })
              }
              required
              className="form-input"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">🔒 Mot de passe</label>
            <input
              type="password"
              id="password"
              value={credentials.password}
              onChange={(e) =>
                setCredentials({ ...credentials, password: e.target.value })
              }
              required
              className="form-input"
              placeholder="••••••••"
            />
            <small className="password-hint">💡 Mot de passe par défaut: 123456</small>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="login-button">
            {isLoading ? (
              <>
                <div className="loading-spinner"></div>
                Connexion...
              </>
            ) : (
              <>
                🚀 Se connecter
              </>
            )}
          </button>
        </form>

        <div className="login-footer">
          <p>
            Pas de compte ?{" "}
            <button
              type="button"
              onClick={() => setShowRegister(true)}
              className="register-link">
              S'inscrire
            </button>
          </p>
          <p className="demo-info">
            🎯 <strong>Mode Démonstration</strong> - Utilisez les comptes ci-dessus
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
