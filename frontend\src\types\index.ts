/**
 * Types TypeScript globaux - ABM
 * Correspondent exactement aux modèles Django backend
 */

// === TYPES UTILISATEURS ET AUTHENTIFICATION ===

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  is_active: boolean;
  permissions: string[];
  created_at: string;
  updated_at: string;
}

export interface UserRole {
  id: string;
  name: 'NORMAL' | 'COMPTABLE' | 'ADMIN' | 'SUPERADMIN';
  description: string;
  permissions: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  codename: string;
  module: string;
}

// === TYPES BUSINESS ===

export interface Client {
  id: string;
  nom: string;
  prenom?: string;
  email: string;
  telephone?: string;
  type_client: 'PARTICULIER' | 'ENTREPRISE' | 'ASSOCIATION' | 'ADMINISTRATION';
  statut: 'ACTIF' | 'INACTIF' | 'SUSPENDU' | 'PROSPECT';
  segment: 'VIP' | 'PREMIUM' | 'STANDARD' | 'NOUVEAU';
  adresse?: string;
  ville?: string;
  code_postal?: string;
  pays: string;
  total_achats: number;
  nb_commandes: number;
  derniere_commande?: string;
  created_at: string;
  updated_at: string;
}

export interface ContactClient {
  id: string;
  client: string;
  nom: string;
  prenom: string;
  email?: string;
  telephone?: string;
  fonction?: string;
  principal: boolean;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  reference: string;
  nom: string;
  description?: string;
  categorie?: string;
  type_produit: 'PHYSIQUE' | 'SERVICE' | 'NUMERIQUE' | 'ABONNEMENT';
  statut: 'ACTIF' | 'INACTIF' | 'RUPTURE' | 'DISCONTINUE';
  prix_achat: number;
  prix_vente: number;
  taux_tva: number;
  stock_actuel: number;
  stock_minimum: number;
  gestion_stock: boolean;
  visible_ecommerce: boolean;
  tags?: string;
  created_at: string;
  updated_at: string;
}

export interface Categorie {
  id: string;
  nom: string;
  description?: string;
  parent?: string;
  actif: boolean;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  numero: string;
  client: string;
  type_commande: 'VENTE' | 'DEVIS' | 'RETOUR';
  statut: 'BROUILLON' | 'CONFIRMEE' | 'PREPARATION' | 'EXPEDITION' | 'LIVREE' | 'ANNULEE';
  priorite: 'BASSE' | 'NORMALE' | 'HAUTE' | 'URGENTE';
  date_commande: string;
  date_livraison_prevue?: string;
  date_livraison_reelle?: string;
  sous_total: number;
  montant_tva: number;
  montant_total: number;
  notes?: string;
  lignes: OrderLine[];
  created_at: string;
  updated_at: string;
}

export interface OrderLine {
  id: string;
  commande: string;
  produit: string;
  quantite: number;
  prix_unitaire: number;
  remise: number;
  taux_tva: number;
  total_ht: number;
  montant_tva: number;
  total_ttc: number;
}

export interface Invoice {
  id: string;
  numero: string;
  client: string;
  commande?: string;
  type_facture: 'FACTURE' | 'DEVIS' | 'AVOIR' | 'ACOMPTE';
  statut: 'BROUILLON' | 'ENVOYEE' | 'PAYEE' | 'ANNULEE' | 'EN_RETARD';
  date_facture: string;
  date_echeance: string;
  date_paiement?: string;
  montant_ht: number;
  montant_tva: number;
  montant_total: number;
  remise_pourcentage: number;
  remise_montant: number;
  notes?: string;
  notes_internes?: string;
  lignes: InvoiceLine[];
  created_at: string;
  updated_at: string;
}

export interface InvoiceLine {
  id: string;
  facture: string;
  produit: string;
  quantite: number;
  prix_unitaire: number;
  remise: number;
  taux_tva: number;
  total_ht: number;
  montant_tva: number;
  total_ttc: number;
  description?: string;
}

export interface Payment {
  id: string;
  reference: string;
  type_paiement: 'ENCAISSEMENT' | 'DECAISSEMENT' | 'VIREMENT' | 'REMBOURSEMENT';
  mode_paiement: 'ESPECES' | 'CHEQUE' | 'VIREMENT' | 'CARTE' | 'PRELEVEMENT' | 'PAYPAL' | 'STRIPE';
  statut: 'EN_ATTENTE' | 'VALIDE' | 'REJETE' | 'REMBOURSE' | 'ANNULE';
  montant: number;
  devise: string;
  date_paiement: string;
  date_valeur?: string;
  client?: string;
  fournisseur?: string;
  facture?: string;
  description?: string;
  rapproche: boolean;
  created_at: string;
  updated_at: string;
}

export interface Supplier {
  id: string;
  code_fournisseur: string;
  nom: string;
  email: string;
  telephone?: string;
  type_fournisseur: 'PRODUITS' | 'SERVICES' | 'MIXTE';
  statut: 'ACTIF' | 'INACTIF' | 'SUSPENDU' | 'PROSPECT';
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
  total_achats: number;
  nb_commandes: number;
  note_qualite?: number;
  note_delai?: number;
  note_service?: number;
  created_at: string;
  updated_at: string;
}

export interface StockMovement {
  id: string;
  reference: string;
  produit: string;
  type_mouvement: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'INVENTAIRE' | 'TRANSFERT' | 'RETOUR';
  quantite: number;
  quantite_avant: number;
  quantite_apres: number;
  prix_unitaire?: number;
  valeur_totale?: number;
  motif: string;
  date_mouvement: string;
}

export interface Inventory {
  id: string;
  numero: string;
  nom: string;
  description?: string;
  statut: 'EN_COURS' | 'TERMINE' | 'VALIDE' | 'ANNULE';
  date_debut: string;
  date_fin?: string;
  nb_produits_comptes: number;
  nb_ecarts_detectes: number;
  valeur_ecarts: number;
  created_at: string;
  updated_at: string;
}

// === TYPES STATISTIQUES ===

export interface ClientStats {
  total_clients: number;
  clients_actifs: number;
  nouveaux_clients: number;
  taux_croissance: number;
  segments: any[];
  evolution_mensuelle: any[];
}

export interface ProductStats {
  total_produits: number;
  produits_actifs: number;
  produits_rupture: number;
  produits_alerte: number;
  valeur_stock_achat: number;
  valeur_stock_vente: number;
}

export interface OrderStats {
  total_commandes: number;
  commandes_mois: number;
  ca_mois: number;
  commandes_retard: number;
  statuts: any[];
}

export interface InvoiceStats {
  total_factures: number;
  ca_total: number;
  factures_retard: number;
  panier_moyen: number;
  evolution_mensuelle: any[];
}

export interface PaymentStats {
  encaissements: number;
  decaissements: number;
  solde_net: number;
  modes_paiement: any[];
  en_attente: any;
}

// === TYPES UTILITAIRES ===

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

export interface FilterOptions {
  page?: number;
  page_size?: number;
  search?: string;
  ordering?: string;
  [key: string]: any;
}

// === CONSTANTES ===

export const USER_ROLES = {
  NORMAL: 'NORMAL',
  COMPTABLE: 'COMPTABLE', 
  ADMIN: 'ADMIN',
  SUPERADMIN: 'SUPERADMIN'
} as const;

export const PERMISSIONS = {
  // Clients
  CLIENTS_READ: 'clients.read',
  CLIENTS_WRITE: 'clients.write',
  CLIENTS_DELETE: 'clients.delete',
  
  // Produits
  PRODUCTS_READ: 'products.read',
  PRODUCTS_WRITE: 'products.write',
  PRODUCTS_DELETE: 'products.delete',
  
  // Commandes
  ORDERS_READ: 'orders.read',
  ORDERS_WRITE: 'orders.write',
  ORDERS_DELETE: 'orders.delete',
  
  // Factures
  INVOICES_READ: 'invoices.read',
  INVOICES_WRITE: 'invoices.write',
  INVOICES_DELETE: 'invoices.delete',
  
  // Stock
  STOCK_READ: 'stock.read',
  STOCK_WRITE: 'stock.write',
  
  // Paiements
  PAYMENTS_READ: 'payments.read',
  PAYMENTS_WRITE: 'payments.write',
  
  // Fournisseurs
  SUPPLIERS_READ: 'suppliers.read',
  SUPPLIERS_WRITE: 'suppliers.write',
  SUPPLIERS_DELETE: 'suppliers.delete',
  
  // Comptabilité
  ACCOUNTING_READ: 'accounting.read',
  ACCOUNTING_WRITE: 'accounting.write',
  
  // Rapports
  REPORTS_READ: 'reports.read',
  REPORTS_WRITE: 'reports.write',
  
  // E-commerce
  ECOMMERCE_READ: 'ecommerce.read',
  ECOMMERCE_WRITE: 'ecommerce.write',
  
  // Administration
  ADMIN_USERS: 'admin.users',
  ADMIN_SETTINGS: 'admin.settings',
  ADMIN_SYSTEM: 'admin.system'
} as const;
