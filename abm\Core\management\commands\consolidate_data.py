"""
Commande de consolidation des données pour éliminer les duplications
"""
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from Core.consolidation import ConsolidationService
import json


class Command(BaseCommand):
    help = 'Consolide les données et élimine les duplications dans l\'application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--analyze-only',
            action='store_true',
            help='Analyse seulement sans effectuer de modifications',
        )
        parser.add_argument(
            '--sync-prices',
            action='store_true',
            help='Synchronise uniquement les prix entre modules',
        )
        parser.add_argument(
            '--report',
            action='store_true',
            help='Génère un rapport complet de consolidation',
        )
        parser.add_argument(
            '--format',
            choices=['text', 'json'],
            default='text',
            help='Format de sortie (text ou json)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Démarrage de la consolidation des données...')
        )
        
        start_time = timezone.now()
        
        try:
            if options['analyze_only']:
                self._handle_analyze_only(options)
            elif options['sync_prices']:
                self._handle_sync_prices(options)
            elif options['report']:
                self._handle_report(options)
            else:
                self._handle_full_consolidation(options)
                
        except Exception as e:
            raise CommandError(f'Erreur lors de la consolidation: {str(e)}')
        
        end_time = timezone.now()
        duration = (end_time - start_time).total_seconds()
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Consolidation terminée en {duration:.2f} secondes')
        )

    def _handle_analyze_only(self, options):
        """Gère l'analyse seule"""
        self.stdout.write('🔍 Analyse des duplications en cours...')
        
        analysis = ConsolidationService.analyze_duplications()
        
        if options['format'] == 'json':
            self.stdout.write(json.dumps(analysis, indent=2, default=str))
        else:
            self._display_analysis_text(analysis)

    def _handle_sync_prices(self, options):
        """Gère la synchronisation des prix"""
        self.stdout.write('💰 Synchronisation des prix en cours...')
        
        result = ConsolidationService.synchronize_prices()
        
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(f"✅ {result['message']}")
            )
        else:
            self.stdout.write(
                self.style.ERROR(f"❌ Erreur: {result['error']}")
            )

    def _handle_report(self, options):
        """Gère la génération de rapport"""
        self.stdout.write('📊 Génération du rapport de consolidation...')
        
        report = ConsolidationService.generate_consolidation_report()
        
        if options['format'] == 'json':
            self.stdout.write(json.dumps(report, indent=2, default=str))
        else:
            self._display_report_text(report)

    def _handle_full_consolidation(self, options):
        """Gère la consolidation complète"""
        self.stdout.write('🔧 Consolidation complète en cours...')
        
        # Étape 1: Analyse
        self.stdout.write('1️⃣ Analyse des duplications...')
        analysis = ConsolidationService.analyze_duplications()
        self._display_analysis_summary(analysis)
        
        # Étape 2: Consolidation des produits
        self.stdout.write('2️⃣ Consolidation des produits...')
        result = ConsolidationService.consolidate_products()
        
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(f"✅ {result['message']}")
            )
        else:
            self.stdout.write(
                self.style.ERROR(f"❌ Erreur: {result['error']}")
            )
            return
        
        # Étape 3: Synchronisation des prix
        self.stdout.write('3️⃣ Synchronisation des prix...')
        price_result = ConsolidationService.synchronize_prices()
        
        if price_result['success']:
            self.stdout.write(
                self.style.SUCCESS(f"✅ {price_result['message']}")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"⚠️ Avertissement prix: {price_result['error']}")
            )

    def _display_analysis_text(self, analysis):
        """Affiche l'analyse en format texte"""
        if 'error' in analysis:
            self.stdout.write(
                self.style.ERROR(f"❌ Erreur d'analyse: {analysis['error']}")
            )
            return
        
        self.stdout.write('\n📋 RÉSULTATS DE L\'ANALYSE')
        self.stdout.write('=' * 50)
        
        self.stdout.write(f"🔄 Duplications de noms: {analysis.get('duplications_nom', 0)}")
        self.stdout.write(f"👤 Produits orphelins: {analysis.get('produits_orphelins', 0)}")
        self.stdout.write(f"💰 Incohérences de prix: {analysis.get('incoherences_prix', 0)}")
        
        # Détails des duplications
        if analysis.get('details', {}).get('duplications'):
            self.stdout.write('\n🔍 DÉTAILS DES DUPLICATIONS:')
            for nom, produits in analysis['details']['duplications'].items():
                self.stdout.write(f"  • {nom}: {len(produits)} produits")
        
        # Détails des incohérences de prix
        if analysis.get('details', {}).get('incoherences_prix'):
            self.stdout.write('\n💸 INCOHÉRENCES DE PRIX:')
            for inc in analysis['details']['incoherences_prix'][:5]:  # Limiter à 5
                self.stdout.write(
                    f"  • {inc['produit']}: "
                    f"Facturation={inc['prix_facturation']}DT, "
                    f"E-commerce={inc['prix_ecommerce']}DT"
                )
            
            if len(analysis['details']['incoherences_prix']) > 5:
                remaining = len(analysis['details']['incoherences_prix']) - 5
                self.stdout.write(f"  ... et {remaining} autres incohérences")

    def _display_analysis_summary(self, analysis):
        """Affiche un résumé de l'analyse"""
        if 'error' in analysis:
            self.stdout.write(
                self.style.ERROR(f"❌ Erreur d'analyse: {analysis['error']}")
            )
            return
        
        total_issues = (
            analysis.get('duplications_nom', 0) + 
            analysis.get('produits_orphelins', 0) + 
            analysis.get('incoherences_prix', 0)
        )
        
        if total_issues == 0:
            self.stdout.write(
                self.style.SUCCESS('✅ Aucun problème détecté!')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'⚠️ {total_issues} problèmes détectés')
            )

    def _display_report_text(self, report):
        """Affiche le rapport en format texte"""
        self.stdout.write('\n📊 RAPPORT DE CONSOLIDATION')
        self.stdout.write('=' * 60)
        
        # Statistiques générales
        stats = report['statistiques_generales']
        self.stdout.write('\n📈 STATISTIQUES GÉNÉRALES:')
        self.stdout.write(f"  • Total produits Facturation: {stats['total_produits_facturation']}")
        self.stdout.write(f"  • Total produits E-commerce: {stats['total_produits_ecommerce']}")
        self.stdout.write(f"  • Produits actifs: {stats['produits_actifs']}")
        self.stdout.write(f"  • Taux d'activation: {stats['taux_activation']:.1f}%")
        
        # Problèmes détectés
        problems = report['problemes_detectes']
        self.stdout.write('\n🚨 PROBLÈMES DÉTECTÉS:')
        self.stdout.write(f"  • Duplications: {problems.get('duplications_nom', 0)}")
        self.stdout.write(f"  • Orphelins: {problems.get('produits_orphelins', 0)}")
        self.stdout.write(f"  • Prix incohérents: {problems.get('incoherences_prix', 0)}")
        
        # Recommandations
        recommendations = report['recommandations']
        if recommendations:
            self.stdout.write('\n💡 RECOMMANDATIONS:')
            for rec in recommendations:
                priority_icon = '🔴' if rec['priority'] == 'HIGH' else '🟡'
                self.stdout.write(f"  {priority_icon} {rec['message']}")
        else:
            self.stdout.write('\n✅ Aucune recommandation - Système optimisé!')
