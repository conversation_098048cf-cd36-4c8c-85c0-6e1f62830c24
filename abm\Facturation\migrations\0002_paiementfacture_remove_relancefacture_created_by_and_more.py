# Generated by Django 5.2.4 on 2025-08-16 00:05

import django.db.models.deletion
import django.utils.timezone
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Clients', '0002_alter_client_siret'),
        ('Facturation', '0001_initial'),
        ('Produits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaiementFacture',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('date_paiement', models.DateField(default=django.utils.timezone.now)),
                ('montant', models.DecimalField(decimal_places=3, max_digits=12)),
                ('mode_paiement', models.CharField(choices=[('ESPECES', 'Espèces'), ('CHEQUE', 'Chèque'), ('VIREMENT', 'Virement'), ('CARTE', 'Carte bancaire'), ('AUTRE', 'Autre')], default='VIREMENT', max_length=20)),
                ('reference', models.CharField(blank=True, max_length=100)),
                ('notes', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'Paiement',
                'verbose_name_plural': 'Paiements',
                'ordering': ['-date_paiement'],
            },
        ),
        migrations.RemoveField(
            model_name='relancefacture',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='relancefacture',
            name='facture',
        ),
        migrations.RemoveField(
            model_name='relancefacture',
            name='updated_by',
        ),
        migrations.AlterUniqueTogether(
            name='templatefacture',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='templatefacture',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='templatefacture',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='facture',
            name='template',
        ),
        migrations.AlterModelOptions(
            name='configurationfacturation',
            options={'verbose_name': 'Configuration de facturation', 'verbose_name_plural': 'Configurations de facturation'},
        ),
        migrations.AlterModelOptions(
            name='facture',
            options={'ordering': ['-date_emission', '-numero'], 'verbose_name': 'Facture', 'verbose_name_plural': 'Factures'},
        ),
        migrations.AlterModelOptions(
            name='lignefacture',
            options={'ordering': ['ordre', 'id'], 'verbose_name': 'Ligne de facture', 'verbose_name_plural': 'Lignes de facture'},
        ),
        migrations.RemoveIndex(
            model_name='facture',
            name='Facturation_client__c3f91b_idx',
        ),
        migrations.RemoveIndex(
            model_name='facture',
            name='Facturation_date_fa_adb952_idx',
        ),
        migrations.RemoveIndex(
            model_name='facture',
            name='Facturation_date_ec_87824b_idx',
        ),
        migrations.RemoveField(
            model_name='configurationfacturation',
            name='couleur_secondaire',
        ),
        migrations.RemoveField(
            model_name='facture',
            name='conditions_particulieres',
        ),
        migrations.RemoveField(
            model_name='facture',
            name='date_envoi_email',
        ),
        migrations.RemoveField(
            model_name='facture',
            name='date_facture',
        ),
        migrations.RemoveField(
            model_name='facture',
            name='notes_internes',
        ),
        migrations.RemoveField(
            model_name='lignefacture',
            name='code_article',
        ),
        migrations.RemoveField(
            model_name='lignefacture',
            name='prix_unitaire_ttc',
        ),
        migrations.RemoveField(
            model_name='lignefacture',
            name='remise_pourcentage',
        ),
        migrations.AddField(
            model_name='facture',
            name='conditions_paiement',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='facture',
            name='creee_par',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='factures_creees', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='facture',
            name='date_emission',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='facture',
            name='date_paiement',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='facture',
            name='notes',
            field=models.TextField(blank=True, verbose_name='Notes'),
        ),
        migrations.AddField(
            model_name='lignefacture',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='code_ape',
            field=models.CharField(blank=True, max_length=10, verbose_name='Code Activité'),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='conditions_paiement',
            field=models.TextField(default='Paiement à 30 jours fin de mois', verbose_name='Conditions de paiement'),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='couleur_principale',
            field=models.CharField(default='#2563eb', max_length=7, verbose_name='Couleur principale (hex)'),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='logo',
            field=models.ImageField(blank=True, null=True, upload_to='logos/', verbose_name="Logo de l'entreprise"),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='mentions_legales',
            field=models.TextField(blank=True, default='', verbose_name='Mentions légales'),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='numero_tva',
            field=models.CharField(blank=True, max_length=20, verbose_name='Numéro TVA'),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='pays',
            field=models.CharField(default='Tunisie', max_length=100),
        ),
        migrations.AlterField(
            model_name='configurationfacturation',
            name='siret',
            field=models.CharField(help_text="Numéro d'identification fiscale", max_length=20, verbose_name='Matricule Fiscal'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='factures', to='Clients.client'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='date_echeance',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='facture',
            name='fichier_pdf',
            field=models.FileField(blank=True, null=True, upload_to='factures/pdf/'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='montant_ht',
            field=models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=12, verbose_name='Montant HT'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='montant_ttc',
            field=models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=12, verbose_name='Montant TTC'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='montant_tva',
            field=models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=12, verbose_name='Montant TVA'),
        ),
        migrations.AlterField(
            model_name='facture',
            name='remise_montant',
            field=models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=12),
        ),
        migrations.AlterField(
            model_name='facture',
            name='statut',
            field=models.CharField(choices=[('BROUILLON', 'Brouillon'), ('ENVOYE', 'Envoyé'), ('ACCEPTE', 'Accepté'), ('REFUSE', 'Refusé'), ('PAYE', 'Payé'), ('ANNULE', 'Annulé')], default='BROUILLON', max_length=20),
        ),
        migrations.AlterField(
            model_name='facture',
            name='type_document',
            field=models.CharField(choices=[('DEVIS', 'Devis'), ('FACTURE', 'Facture'), ('AVOIR', 'Avoir')], default='FACTURE', max_length=20),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='designation',
            field=models.CharField(max_length=500),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='facture',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lignes', to='Facturation.facture'),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='montant_ht',
            field=models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=12, verbose_name='Montant HT'),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='montant_ttc',
            field=models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=12, verbose_name='Montant TTC'),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='montant_tva',
            field=models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=12, verbose_name='Montant TVA'),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='prix_unitaire_ht',
            field=models.DecimalField(decimal_places=3, max_digits=12, verbose_name='Prix unitaire HT'),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='produit',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Produits.produit'),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='quantite',
            field=models.DecimalField(decimal_places=3, default=Decimal('1.000'), max_digits=10),
        ),
        migrations.AlterField(
            model_name='lignefacture',
            name='taux_tva',
            field=models.DecimalField(decimal_places=2, default=Decimal('19.00'), max_digits=5, verbose_name='Taux TVA (%)'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['client', 'date_emission'], name='Facturation_client__ab4991_idx'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['type_document'], name='Facturation_type_do_9e4d90_idx'),
        ),
        migrations.AddField(
            model_name='paiementfacture',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='paiementfacture',
            name='facture',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='paiements', to='Facturation.facture'),
        ),
        migrations.AddField(
            model_name='paiementfacture',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='RelanceFacture',
        ),
        migrations.DeleteModel(
            name='TemplateFacture',
        ),
    ]
