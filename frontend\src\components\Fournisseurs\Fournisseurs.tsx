/**
 * Module Fournisseurs - Gestion des fournisseurs et achats
 */

import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import FournisseursList from './FournisseursList';
import FournisseurForm from './FournisseurForm';
import FournisseurDetail from './FournisseurDetail';
import FournisseurStats from './FournisseurStats';
import { useAuth } from '../../contexts/AuthContext';
import './Fournisseurs.css';

const Fournisseurs: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [activeView, setActiveView] = useState(() => {
    const path = location.pathname;
    if (path.includes('/new')) return 'new';
    if (path.includes('/stats')) return 'stats';
    if (path.includes('/edit')) return 'edit';
    if (path.match(/\/\d+$/)) return 'detail';
    return 'list';
  });

  // Vérifier les permissions
  const hasAccess = () => {
    const allowedRoles = ['COMPTABLE', 'ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || '');
  };

  if (!hasAccess()) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder au module fournisseurs.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fournisseurs-module">
      {/* Header */}
      <div className="fournisseurs-header">
        <div className="header-title">
          <h1>🏭 Fournisseurs</h1>
          <p>Gestion des fournisseurs et suivi des achats</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-outline"
            onClick={() => navigate('/dashboard')}
          >
            ← Dashboard
          </button>
          <button 
            className="btn btn-info"
            onClick={() => navigate('/fournisseurs/stats')}
          >
            📊 Statistiques
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/fournisseurs/new')}
          >
            ➕ Nouveau fournisseur
          </button>
        </div>
      </div>

      {/* Navigation interne */}
      <div className="fournisseurs-nav">
        <button
          className={`nav-btn ${activeView === 'list' ? 'active' : ''}`}
          onClick={() => navigate('/fournisseurs')}
        >
          📋 Liste des fournisseurs
        </button>
        <button
          className={`nav-btn ${activeView === 'stats' ? 'active' : ''}`}
          onClick={() => navigate('/fournisseurs/stats')}
        >
          📊 Statistiques
        </button>
      </div>

      {/* Contenu */}
      <div className="fournisseurs-content">
        <Routes>
          <Route path="/" element={<FournisseursList />} />
          <Route path="/new" element={<FournisseurForm />} />
          <Route path="/:id" element={<FournisseurDetail />} />
          <Route path="/:id/edit" element={<FournisseurForm />} />
          <Route path="/stats" element={<FournisseurStats />} />
        </Routes>
      </div>
    </div>
  );
};

export default Fournisseurs;
