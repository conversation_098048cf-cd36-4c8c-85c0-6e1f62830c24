"""
Système de monitoring intelligent pour l'application ABM
"""
import time
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.utils import timezone
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from Core.models import SystemHealth, SmartNotification
from Core.websocket_service import NotificationManager
from Core.cache_service import SmartCacheService
import json

logger = logging.getLogger(__name__)


class SystemMonitor:
    """Moniteur système intelligent"""
    
    def __init__(self):
        self.start_time = time.time()
        self.metrics_history = []
        self.alert_thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 90.0,
            'response_time': 2.0,
            'error_rate': 5.0,
            'cache_hit_rate': 70.0
        }
    
    def collect_system_metrics(self) -> Dict[str, Any]:
        """Collecte les métriques système"""
        try:
            # Métriques CPU et mémoire
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Métriques réseau
            network = psutil.net_io_counters()
            
            # Métriques de processus Python
            process = psutil.Process()
            process_memory = process.memory_info()
            
            metrics = {
                'timestamp': timezone.now().isoformat(),
                'system': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_available': memory.available,
                    'memory_used': memory.used,
                    'disk_percent': (disk.used / disk.total) * 100,
                    'disk_free': disk.free,
                    'disk_used': disk.used
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                    'open_files': len(process.open_files())
                }
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Erreur lors de la collecte des métriques système: {e}")
            return {}
    
    def collect_database_metrics(self) -> Dict[str, Any]:
        """Collecte les métriques de base de données"""
        try:
            with connection.cursor() as cursor:
                # Nombre de connexions actives
                cursor.execute("PRAGMA database_list")
                databases = cursor.fetchall()
                
                # Taille de la base de données
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                
                db_size = page_count * page_size
                
                # Statistiques des requêtes
                queries_stats = {
                    'total_queries': connection.queries_logged,
                    'slow_queries': len([q for q in connection.queries if float(q['time']) > 0.1])
                }
                
                return {
                    'database_size': db_size,
                    'page_count': page_count,
                    'page_size': page_size,
                    'databases_count': len(databases),
                    'queries': queries_stats
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de la collecte des métriques DB: {e}")
            return {}
    
    def collect_application_metrics(self) -> Dict[str, Any]:
        """Collecte les métriques de l'application"""
        try:
            from Facturation.models import Produit, Client, Facture
            from Ecommerce.models import ProduitEcommerce
            
            # Compteurs des modèles principaux
            model_counts = {
                'produits': Produit.objects.count(),
                'clients': Client.objects.count(),
                'factures': Facture.objects.count(),
                'produits_ecommerce': ProduitEcommerce.objects.count(),
                'notifications': SmartNotification.objects.count(),
                'notifications_non_lues': SmartNotification.objects.filter(is_read=False).count()
            }
            
            # Métriques de cache
            cache_stats = SmartCacheService.get_cache_stats()
            
            # Métriques de performance
            uptime = time.time() - self.start_time
            
            return {
                'models': model_counts,
                'cache': cache_stats,
                'uptime_seconds': uptime,
                'uptime_formatted': str(timedelta(seconds=int(uptime)))
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la collecte des métriques app: {e}")
            return {}
    
    def collect_all_metrics(self) -> Dict[str, Any]:
        """Collecte toutes les métriques"""
        return {
            'system': self.collect_system_metrics(),
            'database': self.collect_database_metrics(),
            'application': self.collect_application_metrics()
        }
    
    def check_health_status(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Vérifie l'état de santé du système"""
        health_status = {
            'overall_status': 'healthy',
            'issues': [],
            'warnings': [],
            'critical_alerts': []
        }
        
        system_metrics = metrics.get('system', {}).get('system', {})
        
        # Vérification CPU
        cpu_usage = system_metrics.get('cpu_percent', 0)
        if cpu_usage > self.alert_thresholds['cpu_usage']:
            issue = {
                'type': 'cpu_high',
                'severity': 'critical' if cpu_usage > 95 else 'warning',
                'message': f'Utilisation CPU élevée: {cpu_usage:.1f}%',
                'value': cpu_usage,
                'threshold': self.alert_thresholds['cpu_usage']
            }
            
            if issue['severity'] == 'critical':
                health_status['critical_alerts'].append(issue)
                health_status['overall_status'] = 'critical'
            else:
                health_status['warnings'].append(issue)
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'warning'
        
        # Vérification mémoire
        memory_usage = system_metrics.get('memory_percent', 0)
        if memory_usage > self.alert_thresholds['memory_usage']:
            issue = {
                'type': 'memory_high',
                'severity': 'critical' if memory_usage > 95 else 'warning',
                'message': f'Utilisation mémoire élevée: {memory_usage:.1f}%',
                'value': memory_usage,
                'threshold': self.alert_thresholds['memory_usage']
            }
            
            if issue['severity'] == 'critical':
                health_status['critical_alerts'].append(issue)
                health_status['overall_status'] = 'critical'
            else:
                health_status['warnings'].append(issue)
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'warning'
        
        # Vérification disque
        disk_usage = system_metrics.get('disk_percent', 0)
        if disk_usage > self.alert_thresholds['disk_usage']:
            issue = {
                'type': 'disk_high',
                'severity': 'critical' if disk_usage > 98 else 'warning',
                'message': f'Utilisation disque élevée: {disk_usage:.1f}%',
                'value': disk_usage,
                'threshold': self.alert_thresholds['disk_usage']
            }
            
            if issue['severity'] == 'critical':
                health_status['critical_alerts'].append(issue)
                health_status['overall_status'] = 'critical'
            else:
                health_status['warnings'].append(issue)
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'warning'
        
        # Vérification du cache
        cache_stats = metrics.get('application', {}).get('cache', {})
        hit_rate = cache_stats.get('hit_rate', 100)
        if hit_rate < self.alert_thresholds['cache_hit_rate']:
            issue = {
                'type': 'cache_low_hit_rate',
                'severity': 'warning',
                'message': f'Taux de succès du cache faible: {hit_rate:.1f}%',
                'value': hit_rate,
                'threshold': self.alert_thresholds['cache_hit_rate']
            }
            health_status['warnings'].append(issue)
            if health_status['overall_status'] == 'healthy':
                health_status['overall_status'] = 'warning'
        
        return health_status
    
    def save_health_record(self, metrics: Dict[str, Any], health_status: Dict[str, Any]):
        """Sauvegarde un enregistrement de santé système"""
        try:
            SystemHealth.objects.create(
                component='SYSTEM',
                status=health_status['overall_status'].upper(),
                metrics={
                    **metrics,
                    'issues': health_status.get('issues', []),
                    'warnings': health_status.get('warnings', []),
                    'critical_alerts': health_status.get('critical_alerts', [])
                }
            )
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde de l'état de santé: {e}")
    
    def send_alerts(self, health_status: Dict[str, Any]):
        """Envoie des alertes pour les problèmes critiques"""
        for alert in health_status.get('critical_alerts', []):
            NotificationManager.create_smart_notification(
                type_notification='ERROR',
                titre=f'Alerte Système Critique',
                message=alert['message'],
                priorite='URGENT',
                action_url='/core/monitoring/'
            )
        
        for warning in health_status.get('warnings', []):
            NotificationManager.create_smart_notification(
                type_notification='WARNING',
                titre=f'Avertissement Système',
                message=warning['message'],
                priorite='HIGH',
                action_url='/core/monitoring/'
            )
    
    def run_health_check(self) -> Dict[str, Any]:
        """Exécute une vérification complète de santé"""
        logger.info("Démarrage de la vérification de santé système")
        
        # Collecter les métriques
        metrics = self.collect_all_metrics()
        
        # Vérifier l'état de santé
        health_status = self.check_health_status(metrics)
        
        # Sauvegarder l'enregistrement
        self.save_health_record(metrics, health_status)
        
        # Envoyer des alertes si nécessaire
        if health_status['critical_alerts'] or health_status['warnings']:
            self.send_alerts(health_status)
        
        logger.info(f"Vérification de santé terminée - Statut: {health_status['overall_status']}")
        
        return {
            'metrics': metrics,
            'health_status': health_status,
            'timestamp': timezone.now().isoformat()
        }


class PerformanceProfiler:
    """Profileur de performance pour l'application"""
    
    def __init__(self):
        self.active_requests = {}
        self.performance_history = []
    
    def start_request_tracking(self, request_id: str, endpoint: str):
        """Démarre le suivi d'une requête"""
        self.active_requests[request_id] = {
            'endpoint': endpoint,
            'start_time': time.time(),
            'start_memory': psutil.Process().memory_info().rss
        }
    
    def end_request_tracking(self, request_id: str) -> Dict[str, Any]:
        """Termine le suivi d'une requête"""
        if request_id not in self.active_requests:
            return {}
        
        request_data = self.active_requests.pop(request_id)
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss
        
        performance_data = {
            'request_id': request_id,
            'endpoint': request_data['endpoint'],
            'duration': end_time - request_data['start_time'],
            'memory_delta': end_memory - request_data['start_memory'],
            'timestamp': timezone.now().isoformat()
        }
        
        self.performance_history.append(performance_data)
        
        # Garder seulement les 1000 dernières requêtes
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]
        
        return performance_data
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Récupère les statistiques de performance"""
        if not self.performance_history:
            return {}
        
        durations = [req['duration'] for req in self.performance_history]
        memory_deltas = [req['memory_delta'] for req in self.performance_history]
        
        return {
            'total_requests': len(self.performance_history),
            'avg_duration': sum(durations) / len(durations),
            'max_duration': max(durations),
            'min_duration': min(durations),
            'avg_memory_delta': sum(memory_deltas) / len(memory_deltas),
            'slow_requests': len([d for d in durations if d > 1.0]),
            'endpoints': list(set(req['endpoint'] for req in self.performance_history))
        }


# Instances globales
system_monitor = SystemMonitor()
performance_profiler = PerformanceProfiler()


class MonitoringTasks:
    """Tâches de monitoring automatisées"""
    
    @staticmethod
    def run_periodic_health_check():
        """Exécute une vérification de santé périodique"""
        try:
            result = system_monitor.run_health_check()
            logger.info("Vérification de santé périodique terminée")
            return result
        except Exception as e:
            logger.error(f"Erreur lors de la vérification de santé périodique: {e}")
            return None
    
    @staticmethod
    def cleanup_old_health_records():
        """Nettoie les anciens enregistrements de santé"""
        try:
            cutoff_date = timezone.now() - timedelta(days=30)
            deleted_count = SystemHealth.objects.filter(
                checked_at__lt=cutoff_date
            ).delete()[0]
            
            logger.info(f"Nettoyage terminé: {deleted_count} enregistrements supprimés")
            return deleted_count
        except Exception as e:
            logger.error(f"Erreur lors du nettoyage des enregistrements: {e}")
            return 0
    
    @staticmethod
    def generate_performance_report():
        """Génère un rapport de performance"""
        try:
            stats = performance_profiler.get_performance_stats()
            
            if stats:
                # Créer une notification avec le rapport
                NotificationManager.create_smart_notification(
                    type_notification='INFO',
                    titre='Rapport de Performance',
                    message=f'Requêtes traitées: {stats["total_requests"]}, '
                           f'Durée moyenne: {stats["avg_duration"]:.3f}s, '
                           f'Requêtes lentes: {stats["slow_requests"]}',
                    priorite='NORMAL',
                    action_url='/core/monitoring/'
                )
            
            return stats
        except Exception as e:
            logger.error(f"Erreur lors de la génération du rapport de performance: {e}")
            return None
