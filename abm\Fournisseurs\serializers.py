from rest_framework import serializers
from .models import Fournisseur, CommandeFournisseur

class FournisseurSerializer(serializers.ModelSerializer):
    """Serializer pour les fournisseurs"""
    
    note_moyenne = serializers.ReadOnlyField()
    adresse_complete = serializers.ReadOnlyField()
    
    class Meta:
        model = Fournisseur
        fields = '__all__'
        read_only_fields = (
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'total_achats', 'nb_commandes', 'derniere_commande'
        )

class CommandeFournisseurSerializer(serializers.ModelSerializer):
    """Serializer pour les commandes fournisseurs"""
    
    fournisseur_nom = serializers.CharField(source='fournisseur.nom', read_only=True)
    
    class Meta:
        model = CommandeFournisseur
        fields = '__all__'
        read_only_fields = (
            'created_at', 'updated_at', 'created_by',
            'sous_total', 'montant_tva', 'montant_total'
        )

class FournisseurStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques fournisseurs"""
    
    total_fournisseurs = serializers.IntegerField()
    fournisseurs_actifs = serializers.IntegerField()
    types = serializers.ListField()
    top_fournisseurs = FournisseurSerializer(many=True)
    notes_moyennes = serializers.DictField()
