/**
 * Module Comptabilité - Vue d'ensemble comptable
 */

import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import ComptabiliteGeneral from './ComptabiliteGeneral';
import JournalVentes from './JournalVentes';
import JournalAchats from './JournalAchats';
import GrandLivre from './GrandLivre';
import Balance from './Balance';
import BilanComptable from './BilanComptable';
import TVADeclaration from './TVADeclaration';
import ExportComptable from './ExportComptable';
import { useAuth } from '../../contexts/AuthContext';
import './Comptable.css';

const Comptable: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [activeTab, setActiveTab] = useState(() => {
    const path = location.pathname.split('/').pop();
    return path || 'general';
  });

  // Vérifier les permissions comptables
  const hasComptableAccess = () => {
    const allowedRoles = ['COMPTABLE', 'ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || '');
  };

  const tabs = [
    {
      id: 'general',
      label: '📊 Vue d\'ensemble',
      component: ComptabiliteGeneral,
      permission: hasComptableAccess()
    },
    {
      id: 'ventes',
      label: '💰 Journal des ventes',
      component: JournalVentes,
      permission: hasComptableAccess()
    },
    {
      id: 'achats',
      label: '🛒 Journal des achats',
      component: JournalAchats,
      permission: hasComptableAccess()
    },
    {
      id: 'grand-livre',
      label: '📚 Grand livre',
      component: GrandLivre,
      permission: hasComptableAccess()
    },
    {
      id: 'balance',
      label: '⚖️ Balance',
      component: Balance,
      permission: hasComptableAccess()
    },
    {
      id: 'bilan',
      label: '📋 Bilan',
      component: BilanComptable,
      permission: hasComptableAccess()
    },
    {
      id: 'tva',
      label: '🧾 TVA',
      component: TVADeclaration,
      permission: hasComptableAccess()
    },
    {
      id: 'export',
      label: '📤 Export',
      component: ExportComptable,
      permission: hasComptableAccess()
    }
  ].filter(tab => tab.permission);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    navigate(`/comptable/${tabId}`);
  };

  if (!hasComptableAccess()) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder au module comptabilité.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="comptable-module">
      {/* Header */}
      <div className="comptable-header">
        <div className="header-title">
          <h1>📊 Comptabilité</h1>
          <p>Gestion comptable et déclarations fiscales</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-outline"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/comptable/export')}
          >
            📤 Exporter données
          </button>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="comptable-nav">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => handleTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Contenu des onglets */}
      <div className="comptable-content">
        <Routes>
          <Route path="/" element={<ComptabiliteGeneral />} />
          <Route path="/general" element={<ComptabiliteGeneral />} />
          <Route path="/ventes" element={<JournalVentes />} />
          <Route path="/achats" element={<JournalAchats />} />
          <Route path="/grand-livre" element={<GrandLivre />} />
          <Route path="/balance" element={<Balance />} />
          <Route path="/bilan" element={<BilanComptable />} />
          <Route path="/tva" element={<TVADeclaration />} />
          <Route path="/export" element={<ExportComptable />} />
        </Routes>
      </div>
    </div>
  );
};

export default Comptable;
