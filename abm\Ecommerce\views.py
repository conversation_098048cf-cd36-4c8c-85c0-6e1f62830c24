"""
Vues pour l'API E-commerce
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, Sum, Count, Avg
from django.utils import timezone
from datetime import timedel<PERSON>

from .models import (
    CategorieEcommerce, ProduitEcommerce, ImageProduitEcommerce,
    Panier, ItemPanier, CommandeEcommerce, LigneCommandeEcommerce
)
from .serializers import (
    CategorieEcommerceSerializer, ProduitEcommerceSerializer, ProduitEcommerceCatalogueSerializer,
    PanierSerializer, ItemPanierSerializer, AjouterAuPanierSerializer,
    CommandeEcommerceSerializer, CommandeEcommerceCreateSerializer,
    StatistiquesEcommerceSerializer
)
from Clients.models import Client


class CategorieEcommerceViewSet(viewsets.ModelViewSet):
    """ViewSet pour les catégories e-commerce"""
    queryset = CategorieEcommerce.objects.filter(is_active=True)
    serializer_class = CategorieEcommerceSerializer
    lookup_field = 'slug'
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filtrer par parent si spécifié
        parent_id = self.request.query_params.get('parent')
        if parent_id:
            if parent_id == 'null':
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)
        
        return queryset.order_by('ordre', 'nom')


class ProduitEcommerceViewSet(viewsets.ModelViewSet):
    """ViewSet pour les produits e-commerce"""
    queryset = ProduitEcommerce.objects.filter(visible_en_ligne=True, produit__statut='ACTIF')
    lookup_field = 'slug'
    
    def get_serializer_class(self):
        if self.action == 'list':
            return ProduitEcommerceCatalogueSerializer
        return ProduitEcommerceSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filtres de recherche
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(produit__nom__icontains=search) |
                Q(produit__code_produit__icontains=search) |
                Q(description_courte__icontains=search) |
                Q(description_longue__icontains=search)
            )
        
        # Filtre par catégorie
        categorie = self.request.query_params.get('categorie')
        if categorie:
            queryset = queryset.filter(categories_ecommerce__slug=categorie)
        
        # Filtres spéciaux
        if self.request.query_params.get('en_vedette') == 'true':
            queryset = queryset.filter(en_vedette=True)
        
        if self.request.query_params.get('nouveau') == 'true':
            queryset = queryset.filter(nouveau=True)
        
        if self.request.query_params.get('en_promotion') == 'true':
            queryset = queryset.filter(en_promotion=True)
        
        # Filtre de prix
        prix_min = self.request.query_params.get('prix_min')
        prix_max = self.request.query_params.get('prix_max')
        if prix_min:
            queryset = queryset.filter(prix_public__gte=prix_min)
        if prix_max:
            queryset = queryset.filter(prix_public__lte=prix_max)
        
        # Tri
        ordering = self.request.query_params.get('ordering', '-en_vedette')
        if ordering == 'prix_asc':
            queryset = queryset.order_by('prix_public')
        elif ordering == 'prix_desc':
            queryset = queryset.order_by('-prix_public')
        elif ordering == 'nom':
            queryset = queryset.order_by('produit__nom')
        elif ordering == 'note':
            queryset = queryset.order_by('-note_moyenne')
        elif ordering == 'ventes':
            queryset = queryset.order_by('-nombre_ventes')
        else:
            queryset = queryset.order_by('-en_vedette', '-nouveau', 'produit__nom')
        
        return queryset
    
    def retrieve(self, request, *args, **kwargs):
        """Récupérer un produit et incrémenter le nombre de vues"""
        instance = self.get_object()
        
        # Incrémenter le nombre de vues
        ProduitEcommerce.objects.filter(id=instance.id).update(
            nombre_vues=instance.nombre_vues + 1
        )
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def suggestions(self, request):
        """Suggestions de produits basées sur les ventes et notes"""
        queryset = self.get_queryset().filter(
            nombre_ventes__gt=0
        ).order_by('-note_moyenne', '-nombre_ventes')[:6]
        
        serializer = ProduitEcommerceCatalogueSerializer(
            queryset, many=True, context={'request': request}
        )
        return Response(serializer.data)


class PanierViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion du panier"""
    serializer_class = PanierSerializer
    
    def get_queryset(self):
        # Pour l'instant, on gère seulement les paniers des clients connectés
        # TODO: Gérer les paniers par session pour les utilisateurs non connectés
        if hasattr(self.request, 'user') and self.request.user.is_authenticated:
            try:
                client = Client.objects.get(email=self.request.user.email)
                return Panier.objects.filter(client=client)
            except Client.DoesNotExist:
                return Panier.objects.none()
        return Panier.objects.none()
    
    def get_object(self):
        """Récupérer ou créer le panier du client"""
        if hasattr(self.request, 'user') and self.request.user.is_authenticated:
            try:
                client = Client.objects.get(email=self.request.user.email)
                panier, created = Panier.objects.get_or_create(client=client)
                return panier
            except Client.DoesNotExist:
                pass
        
        # TODO: Gérer les paniers par session
        return None
    
    @action(detail=False, methods=['get'])
    def mon_panier(self, request):
        """Récupérer le panier du client connecté"""
        panier = self.get_object()
        if panier:
            serializer = self.get_serializer(panier)
            return Response(serializer.data)
        return Response({'detail': 'Panier non trouvé'}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['post'])
    def ajouter(self, request):
        """Ajouter un produit au panier"""
        serializer = AjouterAuPanierSerializer(data=request.data)
        if serializer.is_valid():
            panier = self.get_object()
            if not panier:
                return Response(
                    {'detail': 'Impossible de créer le panier'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            produit_id = serializer.validated_data['produit_ecommerce_id']
            quantite = serializer.validated_data['quantite']
            
            try:
                produit_ecommerce = ProduitEcommerce.objects.get(id=produit_id)
                
                # Vérifier le stock disponible
                if produit_ecommerce.gestion_stock and not produit_ecommerce.autoriser_commande_sans_stock:
                    if produit_ecommerce.stock_disponible_reel < quantite:
                        return Response(
                            {'detail': f'Stock insuffisant. Disponible: {produit_ecommerce.stock_disponible_reel}'},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                
                # Ajouter ou mettre à jour l'article dans le panier
                item, created = ItemPanier.objects.get_or_create(
                    panier=panier,
                    produit_ecommerce=produit_ecommerce,
                    defaults={'quantite': quantite, 'prix_unitaire': produit_ecommerce.prix_actuel}
                )
                
                if not created:
                    # Mettre à jour la quantité
                    nouvelle_quantite = item.quantite + quantite
                    
                    # Vérifier le stock pour la nouvelle quantité
                    if produit_ecommerce.gestion_stock and not produit_ecommerce.autoriser_commande_sans_stock:
                        if produit_ecommerce.stock_disponible_reel < nouvelle_quantite:
                            return Response(
                                {'detail': f'Stock insuffisant pour cette quantité. Disponible: {produit_ecommerce.stock_disponible_reel}'},
                                status=status.HTTP_400_BAD_REQUEST
                            )
                    
                    item.quantite = nouvelle_quantite
                    item.prix_unitaire = produit_ecommerce.prix_actuel  # Mettre à jour le prix
                    item.save()
                
                # Retourner le panier mis à jour
                panier_serializer = PanierSerializer(panier, context={'request': request})
                return Response(panier_serializer.data)
                
            except ProduitEcommerce.DoesNotExist:
                return Response(
                    {'detail': 'Produit non trouvé'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['put'])
    def modifier_quantite(self, request):
        """Modifier la quantité d'un article dans le panier"""
        item_id = request.data.get('item_id')
        quantite = request.data.get('quantite', 0)
        
        if not item_id or quantite < 0:
            return Response(
                {'detail': 'item_id et quantite sont requis'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        panier = self.get_object()
        if not panier:
            return Response(
                {'detail': 'Panier non trouvé'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        try:
            item = ItemPanier.objects.get(id=item_id, panier=panier)
            
            if quantite == 0:
                # Supprimer l'article
                item.delete()
            else:
                # Vérifier le stock
                if item.produit_ecommerce.gestion_stock and not item.produit_ecommerce.autoriser_commande_sans_stock:
                    if item.produit_ecommerce.stock_disponible_reel < quantite:
                        return Response(
                            {'detail': f'Stock insuffisant. Disponible: {item.produit_ecommerce.stock_disponible_reel}'},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                
                # Mettre à jour la quantité
                item.quantite = quantite
                item.save()
            
            # Retourner le panier mis à jour
            panier_serializer = PanierSerializer(panier, context={'request': request})
            return Response(panier_serializer.data)
            
        except ItemPanier.DoesNotExist:
            return Response(
                {'detail': 'Article non trouvé dans le panier'}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['delete'])
    def vider(self, request):
        """Vider le panier"""
        panier = self.get_object()
        if panier:
            panier.items.all().delete()
            panier_serializer = PanierSerializer(panier, context={'request': request})
            return Response(panier_serializer.data)
        
        return Response(
            {'detail': 'Panier non trouvé'},
            status=status.HTTP_404_NOT_FOUND
        )


class CommandeEcommerceViewSet(viewsets.ModelViewSet):
    """ViewSet pour les commandes e-commerce"""
    queryset = CommandeEcommerce.objects.all()

    def get_serializer_class(self):
        if self.action == 'create':
            return CommandeEcommerceCreateSerializer
        return CommandeEcommerceSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filtrer par client si utilisateur connecté
        if hasattr(self.request, 'user') and self.request.user.is_authenticated:
            try:
                client = Client.objects.get(email=self.request.user.email)
                if not self.request.user.is_staff:
                    # Les clients ne voient que leurs commandes
                    queryset = queryset.filter(client=client)
            except Client.DoesNotExist:
                if not self.request.user.is_staff:
                    return CommandeEcommerce.objects.none()

        # Filtres
        statut = self.request.query_params.get('statut')
        if statut:
            queryset = queryset.filter(statut=statut)

        return queryset.order_by('-created_at')

    def create(self, request, *args, **kwargs):
        """Créer une commande à partir du panier"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            client = serializer.validated_data['client']

            # Récupérer le panier du client
            panier = Panier.objects.filter(client=client).first()
            if not panier or not panier.items.exists():
                return Response(
                    {'detail': 'Le panier est vide'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Vérifier la disponibilité des produits
            for item in panier.items.all():
                if item.produit_ecommerce.gestion_stock and not item.produit_ecommerce.autoriser_commande_sans_stock:
                    if item.produit_ecommerce.stock_disponible_reel < item.quantite:
                        return Response(
                            {'detail': f'Stock insuffisant pour {item.produit_ecommerce.produit.nom}'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

            # Créer la commande
            commande = serializer.save()

            # Créer les lignes de commande à partir du panier
            total_ht = 0
            total_tva = 0

            for item in panier.items.all():
                ligne = LigneCommandeEcommerce.objects.create(
                    commande=commande,
                    produit_ecommerce=item.produit_ecommerce,
                    quantite=item.quantite,
                    prix_unitaire=item.prix_unitaire,
                    taux_tva=item.produit_ecommerce.produit.taux_tva
                )

                total_ht += ligne.total_ht
                total_tva += ligne.total_tva

                # Réserver le stock
                if item.produit_ecommerce.gestion_stock:
                    ProduitEcommerce.objects.filter(id=item.produit_ecommerce.id).update(
                        stock_reserve=item.produit_ecommerce.stock_reserve + item.quantite
                    )

            # Mettre à jour les totaux de la commande
            commande.total_ht = total_ht
            commande.total_tva = total_tva
            commande.total_ttc = total_ht + total_tva + commande.frais_livraison
            commande.save()

            # Vider le panier
            panier.items.all().delete()

            # Retourner la commande créée
            response_serializer = CommandeEcommerceSerializer(commande, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def confirmer(self, request, pk=None):
        """Confirmer une commande"""
        commande = self.get_object()

        if commande.statut != 'EN_ATTENTE':
            return Response(
                {'detail': 'Seules les commandes en attente peuvent être confirmées'},
                status=status.HTTP_400_BAD_REQUEST
            )

        commande.statut = 'CONFIRMEE'
        commande.save()

        serializer = self.get_serializer(commande)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def annuler(self, request, pk=None):
        """Annuler une commande"""
        commande = self.get_object()

        if commande.statut in ['EXPEDIEE', 'LIVREE']:
            return Response(
                {'detail': 'Cette commande ne peut plus être annulée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Libérer le stock réservé
        for ligne in commande.lignes.all():
            if ligne.produit_ecommerce.gestion_stock:
                ProduitEcommerce.objects.filter(id=ligne.produit_ecommerce.id).update(
                    stock_reserve=ligne.produit_ecommerce.stock_reserve - ligne.quantite
                )

        commande.statut = 'ANNULEE'
        commande.save()

        serializer = self.get_serializer(commande)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def statistiques(self, request):
        """Statistiques e-commerce"""
        if not request.user.is_staff:
            return Response(
                {'detail': 'Permission refusée'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Période (30 derniers jours par défaut)
        now = timezone.now()
        periode = now - timedelta(days=30)

        # Statistiques générales
        total_commandes = CommandeEcommerce.objects.count()
        chiffre_affaires = CommandeEcommerce.objects.filter(
            statut__in=['CONFIRMEE', 'EN_PREPARATION', 'EXPEDIEE', 'LIVREE']
        ).aggregate(total=Sum('total_ttc'))['total'] or 0

        commandes_en_attente = CommandeEcommerce.objects.filter(statut='EN_ATTENTE').count()
        produits_en_ligne = ProduitEcommerce.objects.filter(visible_en_ligne=True).count()
        paniers_actifs = Panier.objects.filter(updated_at__gte=periode).count()

        # Top produits
        top_produits = list(ProduitEcommerce.objects.filter(
            nombre_ventes__gt=0
        ).order_by('-nombre_ventes')[:5].values(
            'produit__nom', 'nombre_ventes', 'prix_public'
        ))

        # Commandes récentes
        commandes_recentes = CommandeEcommerce.objects.order_by('-created_at')[:10]

        data = {
            'total_commandes': total_commandes,
            'chiffre_affaires': chiffre_affaires,
            'commandes_en_attente': commandes_en_attente,
            'produits_en_ligne': produits_en_ligne,
            'paniers_actifs': paniers_actifs,
            'top_produits': top_produits,
            'commandes_recentes': commandes_recentes
        }

        serializer = StatistiquesEcommerceSerializer(data)
        return Response(serializer.data)
