"""
Commande de gestion pour optimiser et maintenir le système ABM
"""
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db import connection
from Core.consolidation import ConsolidationService
from Core.cache_service import SmartCacheService, CacheCommands
from Core.analytics import AnalyticsService
from Core.workflow_engine import workflow_engine, WorkflowScheduler
from Core.websocket_service import AutoNotificationTasks
from Core.monitoring import MonitoringTasks
from Core.ai_recommendations import RecommendationTasks
from Core.intelligent_search import SearchTasks
from Core.smart_validation import ValidationTasks
import time
import json


class Command(BaseCommand):
    help = 'Optimise et maintient le système ABM intelligent'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=[
                'full-optimization',
                'consolidate-data',
                'warm-cache',
                'clear-cache',
                'generate-insights',
                'run-workflows',
                'system-health',
                'performance-report',
                'cleanup-old-data',
                'monitoring-check',
                'ai-recommendations',
                'search-cleanup',
                'validate-data',
                'intelligent-maintenance'
            ],
            default='full-optimization',
            help='Action à effectuer'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force l\'exécution même si des conditions ne sont pas remplies'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Affichage détaillé'
        )
        
        parser.add_argument(
            '--output-format',
            type=str,
            choices=['text', 'json'],
            default='text',
            help='Format de sortie'
        )

    def handle(self, *args, **options):
        start_time = time.time()
        
        self.verbosity = options['verbosity']
        self.force = options['force']
        self.output_format = options['output_format']
        
        try:
            if options['action'] == 'full-optimization':
                result = self.full_optimization()
            elif options['action'] == 'consolidate-data':
                result = self.consolidate_data()
            elif options['action'] == 'warm-cache':
                result = self.warm_cache()
            elif options['action'] == 'clear-cache':
                result = self.clear_cache()
            elif options['action'] == 'generate-insights':
                result = self.generate_insights()
            elif options['action'] == 'run-workflows':
                result = self.run_workflows()
            elif options['action'] == 'system-health':
                result = self.system_health_check()
            elif options['action'] == 'performance-report':
                result = self.performance_report()
            elif options['action'] == 'cleanup-old-data':
                result = self.cleanup_old_data()
            elif options['action'] == 'monitoring-check':
                result = self.run_monitoring_check()
            elif options['action'] == 'ai-recommendations':
                result = self.generate_ai_recommendations()
            elif options['action'] == 'search-cleanup':
                result = self.cleanup_search_data()
            elif options['action'] == 'validate-data':
                result = self.validate_existing_data()
            elif options['action'] == 'intelligent-maintenance':
                result = self.run_intelligent_maintenance()

            execution_time = time.time() - start_time
            result['execution_time'] = round(execution_time, 2)
            
            self.output_result(result)
            
        except Exception as e:
            self.stderr.write(
                self.style.ERROR(f'Erreur lors de l\'optimisation: {str(e)}')
            )
            raise CommandError(f'Optimisation échouée: {str(e)}')

    def full_optimization(self):
        """Optimisation complète du système"""
        self.stdout.write(self.style.SUCCESS('🚀 Démarrage de l\'optimisation complète...'))
        
        results = {
            'action': 'full-optimization',
            'timestamp': timezone.now().isoformat(),
            'steps': []
        }
        
        # Étape 1: Consolidation des données
        self.stdout.write('📊 Consolidation des données...')
        consolidation_result = ConsolidationService.consolidate_products()
        results['steps'].append({
            'name': 'consolidation',
            'status': 'success',
            'details': consolidation_result
        })
        
        # Étape 2: Nettoyage du cache
        self.stdout.write('🧹 Nettoyage du cache...')
        CacheCommands.clear_by_pattern('smart_cache:*')
        
        # Étape 3: Préchauffage du cache
        self.stdout.write('🔥 Préchauffage du cache...')
        SmartCacheService.warm_up_cache()
        results['steps'].append({
            'name': 'cache_warmup',
            'status': 'success'
        })
        
        # Étape 4: Génération des insights IA
        self.stdout.write('🧠 Génération des insights IA...')
        insights = AnalyticsService.generate_ai_insights()
        results['steps'].append({
            'name': 'ai_insights',
            'status': 'success',
            'insights_generated': len(insights)
        })
        
        # Étape 5: Vérification de la santé du système
        self.stdout.write('🏥 Vérification de la santé du système...')
        health_check = self.system_health_check()
        results['steps'].append({
            'name': 'health_check',
            'status': 'success',
            'details': health_check
        })
        
        # Étape 6: Exécution des workflows de maintenance
        self.stdout.write('⚙️ Exécution des workflows de maintenance...')
        try:
            WorkflowScheduler.run_daily_workflows()
            results['steps'].append({
                'name': 'workflows',
                'status': 'success'
            })
        except Exception as e:
            results['steps'].append({
                'name': 'workflows',
                'status': 'warning',
                'error': str(e)
            })
        
        results['status'] = 'completed'
        results['summary'] = f"{len([s for s in results['steps'] if s['status'] == 'success'])} étapes réussies"
        
        self.stdout.write(self.style.SUCCESS('✅ Optimisation complète terminée!'))
        return results

    def consolidate_data(self):
        """Consolidation des données"""
        self.stdout.write('📊 Consolidation des données en cours...')
        
        # Analyse des duplications
        analysis = ConsolidationService.analyze_duplications()
        
        # Consolidation si nécessaire
        if analysis['duplications'] > 0 or analysis['orphelins'] > 0:
            consolidation = ConsolidationService.consolidate_products()
        else:
            consolidation = {'message': 'Aucune consolidation nécessaire'}
        
        # Synchronisation des prix
        price_sync = ConsolidationService.synchronize_prices()
        
        return {
            'action': 'consolidate-data',
            'analysis': analysis,
            'consolidation': consolidation,
            'price_sync': price_sync,
            'status': 'completed'
        }

    def warm_cache(self):
        """Préchauffage du cache"""
        self.stdout.write('🔥 Préchauffage du cache...')
        
        SmartCacheService.warm_up_cache()
        cache_stats = CacheCommands.get_stats()
        
        return {
            'action': 'warm-cache',
            'cache_stats': cache_stats,
            'status': 'completed'
        }

    def clear_cache(self):
        """Nettoyage du cache"""
        self.stdout.write('🧹 Nettoyage du cache...')
        
        CacheCommands.clear_by_pattern('smart_cache:*')
        
        return {
            'action': 'clear-cache',
            'status': 'completed'
        }

    def generate_insights(self):
        """Génération des insights IA"""
        self.stdout.write('🧠 Génération des insights IA...')
        
        insights = AnalyticsService.generate_ai_insights()
        
        # Exécuter les tâches de notification automatique
        AutoNotificationTasks.check_stock_levels()
        AutoNotificationTasks.analyze_sales_trends()
        AutoNotificationTasks.generate_daily_insights()
        
        return {
            'action': 'generate-insights',
            'insights_generated': len(insights),
            'insights': insights,
            'status': 'completed'
        }

    def run_workflows(self):
        """Exécution des workflows"""
        self.stdout.write('⚙️ Exécution des workflows...')
        
        try:
            WorkflowScheduler.run_daily_workflows()
            
            running_workflows = workflow_engine.get_running_workflows()
            workflow_history = workflow_engine.get_workflow_history(10)
            
            return {
                'action': 'run-workflows',
                'running_workflows': running_workflows,
                'recent_history': workflow_history,
                'status': 'completed'
            }
            
        except Exception as e:
            return {
                'action': 'run-workflows',
                'status': 'error',
                'error': str(e)
            }

    def system_health_check(self):
        """Vérification de la santé du système"""
        self.stdout.write('🏥 Vérification de la santé du système...')
        
        health_data = {
            'action': 'system-health',
            'timestamp': timezone.now().isoformat(),
            'checks': []
        }
        
        # Vérification de la base de données
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM django_migrations")
                migrations_count = cursor.fetchone()[0]
            
            health_data['checks'].append({
                'name': 'database',
                'status': 'healthy',
                'details': f'{migrations_count} migrations appliquées'
            })
        except Exception as e:
            health_data['checks'].append({
                'name': 'database',
                'status': 'unhealthy',
                'error': str(e)
            })
        
        # Vérification du cache
        try:
            cache_stats = CacheCommands.get_stats()
            health_data['checks'].append({
                'name': 'cache',
                'status': 'healthy',
                'details': cache_stats
            })
        except Exception as e:
            health_data['checks'].append({
                'name': 'cache',
                'status': 'unhealthy',
                'error': str(e)
            })
        
        # Vérification des modèles
        try:
            from Facturation.models import Produit, Client, Facture
            from Ecommerce.models import ProduitEcommerce
            
            model_counts = {
                'produits': Produit.objects.count(),
                'clients': Client.objects.count(),
                'factures': Facture.objects.count(),
                'produits_ecommerce': ProduitEcommerce.objects.count()
            }
            
            health_data['checks'].append({
                'name': 'models',
                'status': 'healthy',
                'details': model_counts
            })
        except Exception as e:
            health_data['checks'].append({
                'name': 'models',
                'status': 'unhealthy',
                'error': str(e)
            })
        
        # Statut global
        unhealthy_checks = [c for c in health_data['checks'] if c['status'] == 'unhealthy']
        health_data['overall_status'] = 'unhealthy' if unhealthy_checks else 'healthy'
        health_data['issues_count'] = len(unhealthy_checks)
        
        return health_data

    def performance_report(self):
        """Rapport de performance"""
        self.stdout.write('📈 Génération du rapport de performance...')
        
        # Métriques du tableau de bord
        metrics = AnalyticsService.get_dashboard_metrics()
        
        # Performance des catégories
        category_performance = AnalyticsService.get_category_performance()
        
        # Top produits
        top_products = AnalyticsService.get_top_products()
        
        # Statistiques du cache
        cache_stats = CacheCommands.get_stats()
        
        # Rapport de consolidation
        consolidation_report = ConsolidationService.generate_consolidation_report()
        
        return {
            'action': 'performance-report',
            'timestamp': timezone.now().isoformat(),
            'dashboard_metrics': metrics,
            'category_performance': category_performance,
            'top_products': top_products,
            'cache_performance': cache_stats,
            'data_quality': consolidation_report,
            'status': 'completed'
        }

    def cleanup_old_data(self):
        """Nettoyage des anciennes données"""
        self.stdout.write('🧹 Nettoyage des anciennes données...')
        
        from Core.models import SmartNotification, AIInsight, AuditLog
        from datetime import timedelta
        
        cutoff_date = timezone.now() - timedelta(days=90)
        
        # Supprimer les anciennes notifications lues
        old_notifications = SmartNotification.objects.filter(
            lu=True,
            date_lecture__lt=cutoff_date
        )
        notifications_deleted = old_notifications.count()
        old_notifications.delete()
        
        # Supprimer les anciens insights traités
        old_insights = AIInsight.objects.filter(
            traite=True,
            created_at__lt=cutoff_date
        )
        insights_deleted = old_insights.count()
        old_insights.delete()
        
        # Supprimer les anciens logs d'audit
        old_logs = AuditLog.objects.filter(
            created_at__lt=cutoff_date
        )
        logs_deleted = old_logs.count()
        old_logs.delete()
        
        return {
            'action': 'cleanup-old-data',
            'cutoff_date': cutoff_date.isoformat(),
            'notifications_deleted': notifications_deleted,
            'insights_deleted': insights_deleted,
            'logs_deleted': logs_deleted,
            'total_deleted': notifications_deleted + insights_deleted + logs_deleted,
            'status': 'completed'
        }

    def output_result(self, result):
        """Affiche le résultat selon le format choisi"""
        if self.output_format == 'json':
            self.stdout.write(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            # Format texte
            self.stdout.write(self.style.SUCCESS(f"\n📋 RÉSULTAT: {result['action'].upper()}"))
            self.stdout.write("=" * 50)
            
            if 'execution_time' in result:
                self.stdout.write(f"⏱️  Temps d'exécution: {result['execution_time']}s")

            if 'status' in result:
                status_style = self.style.SUCCESS if result['status'] == 'completed' else self.style.WARNING
                self.stdout.write(status_style(f"📊 Statut: {result['status']}"))

    def run_monitoring_check(self):
        """Exécute une vérification de monitoring complète"""
        self.stdout.write(self.style.SUCCESS('🔍 Vérification du monitoring système...'))

        try:
            # Exécuter la vérification de santé
            health_result = MonitoringTasks.run_periodic_health_check()

            # Nettoyer les anciens enregistrements
            cleanup_count = MonitoringTasks.cleanup_old_health_records()

            return {
                'action': 'monitoring-check',
                'status': 'completed',
                'health_status': health_result.get('health_status', {}).get('overall_status', 'unknown') if health_result else 'error',
                'cleanup_count': cleanup_count,
                'timestamp': timezone.now().isoformat()
            }

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Erreur lors de la vérification de monitoring: {e}'))
            return {
                'action': 'monitoring-check',
                'status': 'error',
                'error': str(e)
            }

    def generate_ai_recommendations(self):
        """Génère les recommandations IA"""
        self.stdout.write(self.style.SUCCESS('🤖 Génération des recommandations IA...'))

        try:
            # Générer les recommandations quotidiennes
            recommendations = RecommendationTasks.run_daily_recommendations()
            summary = RecommendationTasks.get_recommendations_summary()

            return {
                'action': 'ai-recommendations',
                'status': 'completed',
                'total_recommendations': summary.get('total_recommendations', 0),
                'high_priority': summary.get('high_priority', 0),
                'high_confidence': summary.get('high_confidence', 0),
                'timestamp': timezone.now().isoformat()
            }

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Erreur lors de la génération des recommandations: {e}'))
            return {
                'action': 'ai-recommendations',
                'status': 'error',
                'error': str(e)
            }

    def cleanup_search_data(self):
        """Nettoie les données de recherche"""
        self.stdout.write(self.style.SUCCESS('🔍 Nettoyage des données de recherche...'))

        try:
            # Nettoyer l'historique de recherche
            SearchTasks.cleanup_search_history()

            # Générer des insights de recherche
            insights = SearchTasks.generate_search_insights()

            return {
                'action': 'search-cleanup',
                'status': 'completed',
                'insights_generated': insights is not None,
                'timestamp': timezone.now().isoformat()
            }

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Erreur lors du nettoyage de recherche: {e}'))
            return {
                'action': 'search-cleanup',
                'status': 'error',
                'error': str(e)
            }

    def validate_existing_data(self):
        """Valide les données existantes"""
        self.stdout.write(self.style.SUCCESS('✅ Validation des données existantes...'))

        try:
            # Valider les données existantes
            validation_report = ValidationTasks.validate_existing_data()

            if validation_report:
                total_errors = validation_report['clients']['errors'] + validation_report['produits']['errors']
                total_warnings = validation_report['clients']['warnings'] + validation_report['produits']['warnings']

                return {
                    'action': 'validate-data',
                    'status': 'completed',
                    'total_errors': total_errors,
                    'total_warnings': total_warnings,
                    'clients_validated': validation_report['clients']['total'],
                    'produits_validated': validation_report['produits']['total'],
                    'timestamp': timezone.now().isoformat()
                }
            else:
                return {
                    'action': 'validate-data',
                    'status': 'error',
                    'error': 'Aucun rapport de validation généré'
                }

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Erreur lors de la validation: {e}'))
            return {
                'action': 'validate-data',
                'status': 'error',
                'error': str(e)
            }

    def run_intelligent_maintenance(self):
        """Exécute une maintenance intelligente complète"""
        self.stdout.write(self.style.SUCCESS('🧠 Maintenance intelligente complète...'))

        try:
            results = {
                'action': 'intelligent-maintenance',
                'status': 'completed',
                'steps': [],
                'timestamp': timezone.now().isoformat()
            }

            # 1. Vérification de monitoring
            self.stdout.write('  🔍 Vérification du monitoring...')
            monitoring_result = self.run_monitoring_check()
            results['steps'].append(monitoring_result)

            # 2. Génération des recommandations IA
            self.stdout.write('  🤖 Génération des recommandations IA...')
            ai_result = self.generate_ai_recommendations()
            results['steps'].append(ai_result)

            # 3. Nettoyage des données de recherche
            self.stdout.write('  🔍 Nettoyage des recherches...')
            search_result = self.cleanup_search_data()
            results['steps'].append(search_result)

            # 4. Validation des données
            self.stdout.write('  ✅ Validation des données...')
            validation_result = self.validate_existing_data()
            results['steps'].append(validation_result)

            # 5. Optimisation du cache
            self.stdout.write('  🚀 Optimisation du cache...')
            cache_result = self.warm_cache()
            results['steps'].append(cache_result)

            # 6. Exécution des workflows
            self.stdout.write('  ⚙️ Exécution des workflows...')
            workflow_result = self.run_workflows()
            results['steps'].append(workflow_result)

            # Calculer le statut global
            failed_steps = [step for step in results['steps'] if step.get('status') == 'error']
            if failed_steps:
                results['status'] = 'partial'
                results['failed_steps'] = len(failed_steps)

            return results

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Erreur lors de la maintenance intelligente: {e}'))
            return {
                'action': 'intelligent-maintenance',
                'status': 'error',
                'error': str(e)
            }
            if 'summary' in result:
                self.stdout.write(f"📝 Résumé: {result['summary']}")
            
            self.stdout.write("✅ Opération terminée avec succès!")
