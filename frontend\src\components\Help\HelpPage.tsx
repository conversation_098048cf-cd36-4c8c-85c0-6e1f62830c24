import React, { useState } from 'react';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: string;
}

const HelpPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('general');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  const faqData: FAQItem[] = [
    {
      id: 1,
      question: "Comment créer une nouvelle facture ?",
      answer: "Pour créer une nouvelle facture, cliquez sur 'Nouvelle facture' dans le dashboard ou allez dans Facturation > Nouvelle facture. Remplissez les informations du client, ajoutez les produits/services et validez.",
      category: "facturation"
    },
    {
      id: 2,
      question: "Comment ajouter un nouveau client ?",
      answer: "Rendez-vous dans la section Clients > Nouveau client. Remplissez les informations obligatoires (nom, email) et optionnelles (adresse, téléphone). Le client sera automatiquement disponible lors de la création de factures.",
      category: "clients"
    },
    {
      id: 3,
      question: "Comment gérer les stocks ?",
      answer: "La gestion des stocks est accessible aux comptables et administrateurs. Allez dans Produits > Stock pour voir les niveaux actuels et configurer les alertes de stock minimum.",
      category: "stock"
    },
    {
      id: 4,
      question: "Quels sont les différents rôles utilisateur ?",
      answer: "Il existe 4 rôles : Utilisateur Normal (factures et clients), Comptable (+ produits et stocks), Admin (+ rapports et gestion), SuperAdmin (+ insights IA et administration système).",
      category: "general"
    },
    {
      id: 5,
      question: "Comment exporter mes données ?",
      answer: "Vous pouvez exporter vos factures en PDF depuis la liste des factures, ou exporter des rapports Excel depuis la section Rapports (accessible aux comptables et admins).",
      category: "rapports"
    },
    {
      id: 6,
      question: "Comment configurer les taux de TVA ?",
      answer: "Les taux de TVA sont configurables dans Paramètres > Fiscalité. Les taux standards tunisiens (0%, 7%, 13%, 19%) sont préconfigurés.",
      category: "parametres"
    },
    {
      id: 7,
      question: "Que faire si j'ai oublié mon mot de passe ?",
      answer: "Cliquez sur 'Mot de passe oublié' sur la page de connexion. Un email de réinitialisation sera envoyé à votre adresse email.",
      category: "general"
    },
    {
      id: 8,
      question: "Comment personnaliser mes factures ?",
      answer: "Allez dans Paramètres > Modèles de factures pour personnaliser le logo, les couleurs, et les mentions légales de vos factures.",
      category: "facturation"
    }
  ];

  const categories = [
    { id: 'general', name: 'Général', icon: '❓' },
    { id: 'facturation', name: 'Facturation', icon: '📄' },
    { id: 'clients', name: 'Clients', icon: '👥' },
    { id: 'stock', name: 'Stock', icon: '📦' },
    { id: 'rapports', name: 'Rapports', icon: '📊' },
    { id: 'parametres', name: 'Paramètres', icon: '⚙️' }
  ];

  const filteredFAQ = faqData.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleFAQ = (id: number) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Centre d'aide</h1>
            <p className="page-subtitle">Trouvez rapidement les réponses à vos questions</p>
          </div>
        </div>
      </div>

      <div className="content-grid two-columns">
        {/* Liens rapides */}
        <div className="content-card">
          <div className="card-header">
            <h3 className="card-title">🚀 Actions rapides</h3>
          </div>
          <div className="card-content">
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              <button 
                className="page-action"
                style={{ justifyContent: 'flex-start', width: '100%' }}
              >
                📄 Créer une facture
              </button>
              <button 
                className="page-action secondary"
                style={{ justifyContent: 'flex-start', width: '100%' }}
              >
                👥 Ajouter un client
              </button>
              <button 
                className="page-action secondary"
                style={{ justifyContent: 'flex-start', width: '100%' }}
              >
                📦 Gérer les produits
              </button>
              <button 
                className="page-action secondary"
                style={{ justifyContent: 'flex-start', width: '100%' }}
              >
                📊 Voir les rapports
              </button>
            </div>
          </div>
        </div>

        {/* Contact support */}
        <div className="content-card">
          <div className="card-header">
            <h3 className="card-title">💬 Besoin d'aide ?</h3>
          </div>
          <div className="card-content">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', marginBottom: '15px' }}>🎧</div>
              <h4 style={{ margin: '0 0 10px 0', color: '#2c3e50' }}>Support technique</h4>
              <p style={{ color: '#7f8c8d', marginBottom: '20px' }}>
                Notre équipe est là pour vous aider
              </p>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <span>📧</span>
                  <span><EMAIL></span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <span>📞</span>
                  <span>+216 71 123 456</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <span>🕒</span>
                  <span>Lun-Ven 8h-18h</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ */}
      <div className="content-card">
        <div className="card-header">
          <h3 className="card-title">❓ Questions fréquentes</h3>
        </div>
        <div className="card-content">
          {/* Recherche */}
          <div style={{ marginBottom: '25px' }}>
            <input
              type="text"
              placeholder="Rechercher dans la FAQ..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-control"
              style={{ margin: 0 }}
            />
          </div>

          {/* Catégories */}
          <div style={{ 
            display: 'flex', 
            gap: '10px', 
            marginBottom: '25px',
            flexWrap: 'wrap'
          }}>
            <button
              className={`filter-btn ${activeCategory === 'all' ? 'active' : ''}`}
              onClick={() => setActiveCategory('all')}
              style={{
                background: activeCategory === 'all' ? '#667eea' : '#f8f9fa',
                color: activeCategory === 'all' ? 'white' : '#2c3e50',
                border: '1px solid #dee2e6',
                padding: '8px 16px',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Toutes
            </button>
            {categories.map(category => (
              <button
                key={category.id}
                className={`filter-btn ${activeCategory === category.id ? 'active' : ''}`}
                onClick={() => setActiveCategory(category.id)}
                style={{
                  background: activeCategory === category.id ? '#667eea' : '#f8f9fa',
                  color: activeCategory === category.id ? 'white' : '#2c3e50',
                  border: '1px solid #dee2e6',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '5px'
                }}
              >
                {category.icon} {category.name}
              </button>
            ))}
          </div>

          {/* Liste FAQ */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
            {filteredFAQ.map(item => (
              <div
                key={item.id}
                style={{
                  border: '1px solid #dee2e6',
                  borderRadius: '8px',
                  overflow: 'hidden'
                }}
              >
                <button
                  onClick={() => toggleFAQ(item.id)}
                  style={{
                    width: '100%',
                    background: expandedFAQ === item.id ? '#f8f9fa' : 'white',
                    border: 'none',
                    padding: '15px 20px',
                    textAlign: 'left',
                    cursor: 'pointer',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontWeight: '600',
                    color: '#2c3e50'
                  }}
                >
                  <span>{item.question}</span>
                  <span style={{ 
                    transform: expandedFAQ === item.id ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.3s ease'
                  }}>
                    ▼
                  </span>
                </button>
                {expandedFAQ === item.id && (
                  <div
                    style={{
                      padding: '15px 20px',
                      background: '#f8f9fa',
                      borderTop: '1px solid #dee2e6',
                      color: '#7f8c8d',
                      lineHeight: '1.6'
                    }}
                  >
                    {item.answer}
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredFAQ.length === 0 && (
            <div style={{ textAlign: 'center', padding: '40px', color: '#7f8c8d' }}>
              <div style={{ fontSize: '3rem', marginBottom: '20px' }}>🔍</div>
              <h3>Aucune question trouvée</h3>
              <p>Essayez de modifier votre recherche ou contactez notre support.</p>
            </div>
          )}
        </div>
      </div>

      {/* Ressources utiles */}
      <div className="content-card">
        <div className="card-header">
          <h3 className="card-title">📚 Ressources utiles</h3>
        </div>
        <div className="card-content">
          <div className="content-grid three-columns">
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <div style={{ fontSize: '2.5rem', marginBottom: '15px' }}>📖</div>
              <h4 style={{ margin: '0 0 10px 0', color: '#2c3e50' }}>Guide utilisateur</h4>
              <p style={{ color: '#7f8c8d', marginBottom: '15px' }}>
                Documentation complète de l'application
              </p>
              <button className="page-action secondary">
                📥 Télécharger PDF
              </button>
            </div>
            
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <div style={{ fontSize: '2.5rem', marginBottom: '15px' }}>🎥</div>
              <h4 style={{ margin: '0 0 10px 0', color: '#2c3e50' }}>Tutoriels vidéo</h4>
              <p style={{ color: '#7f8c8d', marginBottom: '15px' }}>
                Apprenez avec nos vidéos explicatives
              </p>
              <button className="page-action secondary">
                ▶️ Voir les vidéos
              </button>
            </div>
            
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <div style={{ fontSize: '2.5rem', marginBottom: '15px' }}>💡</div>
              <h4 style={{ margin: '0 0 10px 0', color: '#2c3e50' }}>Conseils & astuces</h4>
              <p style={{ color: '#7f8c8d', marginBottom: '15px' }}>
                Optimisez votre utilisation d'ABM
              </p>
              <button className="page-action secondary">
                🚀 Découvrir
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpPage;
