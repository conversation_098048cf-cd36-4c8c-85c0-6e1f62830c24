# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200, verbose_name='Nom/Raison sociale')),
                ('prenom', models.CharField(blank=True, max_length=100, verbose_name='Prénom')),
                ('email', models.EmailField(max_length=254, unique=True, validators=[django.core.validators.EmailValidator()])),
                ('telephone', models.CharField(blank=True, max_length=20, validators=[django.core.validators.RegexValidator('^\\+?[\\d\\s\\-\\(\\)]+$', 'Format de téléphone invalide')])),
                ('type_client', models.CharField(choices=[('PARTICULIER', 'Particulier'), ('ENTREPRISE', 'Entreprise'), ('ASSOCIATION', 'Association'), ('ADMINISTRATION', 'Administration')], default='PARTICULIER', max_length=20)),
                ('statut', models.CharField(choices=[('ACTIF', 'Actif'), ('INACTIF', 'Inactif'), ('SUSPENDU', 'Suspendu'), ('PROSPECT', 'Prospect')], default='ACTIF', max_length=20)),
                ('segment', models.CharField(choices=[('VIP', 'VIP'), ('PREMIUM', 'Premium'), ('STANDARD', 'Standard'), ('NOUVEAU', 'Nouveau')], default='NOUVEAU', max_length=20)),
                ('adresse', models.TextField(blank=True, verbose_name='Adresse')),
                ('ville', models.CharField(blank=True, max_length=100)),
                ('code_postal', models.CharField(blank=True, max_length=10)),
                ('pays', models.CharField(default='France', max_length=100)),
                ('siret', models.CharField(blank=True, max_length=14, validators=[django.core.validators.RegexValidator('^\\d{14}$', 'SIRET doit contenir 14 chiffres')])),
                ('tva_intracommunautaire', models.CharField(blank=True, max_length=20)),
                ('remise_par_defaut', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5, verbose_name='Remise par défaut (%)')),
                ('limite_credit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='Limite de crédit')),
                ('delai_paiement', models.IntegerField(default=30, verbose_name='Délai de paiement (jours)')),
                ('total_achats', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Total des achats')),
                ('nb_commandes', models.IntegerField(default=0, verbose_name='Nombre de commandes')),
                ('derniere_commande', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, verbose_name='Notes internes')),
                ('tags', models.CharField(blank=True, help_text='Tags séparés par des virgules', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clients_created', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clients_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Client',
                'verbose_name_plural': 'Clients',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ContactClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100)),
                ('prenom', models.CharField(max_length=100)),
                ('fonction', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('telephone', models.CharField(blank=True, max_length=20)),
                ('principal', models.BooleanField(default=False, verbose_name='Contact principal')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to='Clients.client')),
            ],
            options={
                'verbose_name': 'Contact client',
                'verbose_name_plural': 'Contacts clients',
                'ordering': ['-principal', 'nom'],
            },
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['email'], name='Clients_cli_email_563905_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['statut'], name='Clients_cli_statut_a10743_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['segment'], name='Clients_cli_segment_88d0c1_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['type_client'], name='Clients_cli_type_cl_d4f6f5_idx'),
        ),
    ]
