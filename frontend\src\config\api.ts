/**
 * Configuration API pour connecter le frontend React au backend Django
 */

// Configuration de base
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || "http://localhost:8000/api",
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Endpoints API correspondant exactement au backend Django
export const API_ENDPOINTS = {
  // Authentification
  AUTH: {
    LOGIN: "/auth/token/",
    REFRESH: "/auth/token/refresh/",
    REGISTER: "/auth/register/",
    LOGOUT: "/auth/logout/",
    PROFILE: "/auth/profile/",
    CHANGE_PASSWORD: "/auth/change-password/",
    FORGOT_PASSWORD: "/auth/forgot-password/",
    RESET_PASSWORD: "/auth/reset-password/",
  },

  // Clients (CRM)
  CLIENTS: {
    LIST: "/clients/",
    DETAIL: (id: string) => `/clients/${id}/`,
    STATS: "/clients/stats/",
    HISTORIQUE: (id: string) => `/clients/${id}/historique/`,
  },

  // Produits (Catalogue)
  PRODUCTS: {
    LIST: "/products/",
    DETAIL: (id: string) => `/products/${id}/`,
    STATS: "/products/stats/",
    ALERTES_STOCK: "/products/alertes-stock/",
    CATEGORIES: "/products/categories/",
  },

  // Commandes (Ventes)
  ORDERS: {
    LIST: "/orders/",
    DETAIL: (id: string) => `/orders/${id}/`,
    STATS: "/orders/stats/",
    CHANGER_STATUT: (id: string) => `/orders/${id}/changer-statut/`,
  },

  // Factures (Facturation)
  INVOICES: {
    LIST: "/factures/",
    DETAIL: (id: string) => `/factures/${id}/`,
    STATS: "/factures/stats/",
    PDF: (id: string) => `/factures/${id}/pdf/`,
    EMAIL: (id: string) => `/factures/${id}/envoyer-email/`,
  },

  // Stock (Inventaires)
  STOCK: {
    MOUVEMENTS: "/stock/mouvements/",
    INVENTAIRES: "/stock/inventaires/",
    STATS: "/stock/stats/",
  },

  // Paiements (Trésorerie)
  PAYMENTS: {
    LIST: "/payments/",
    DETAIL: (id: string) => `/payments/${id}/`,
    STATS: "/payments/stats/",
    RAPPROCHEMENT: "/payments/rapprochement/",
  },

  // Fournisseurs (Achats)
  FOURNISSEURS: {
    LIST: "/fournisseurs/",
    DETAIL: (id: string) => `/fournisseurs/${id}/`,
    STATS: "/fournisseurs/stats/",
    COMMANDES: "/fournisseurs/commandes/",
  },

  // Comptabilité
  COMPTABILITE: {
    STATS: "/comptabilite/stats/",
    JOURNAL_VENTES: "/comptabilite/journal-ventes/",
    JOURNAL_ACHATS: "/comptabilite/journal-achats/",
    TVA: (periode: string) => `/comptabilite/tva/${periode}/`,
  },

  // Rapports
  RAPPORTS: {
    TEMPLATES: "/rapports/templates/",
    VENTES: "/rapports/ventes/",
    CLIENTS: "/rapports/clients/",
    PRODUITS: "/rapports/produits/",
    FINANCIER: "/rapports/financier/",
  },

  // E-commerce
  ECOMMERCE: {
    CATALOG: "/ecommerce/catalog/",
    CART: "/ecommerce/cart/",
    STATS: "/ecommerce/stats/",
    CATEGORIES: "/ecommerce/categories/",
  },

  // Core (Dashboard et fonctionnalités intelligentes)
  CORE: {
    FUTURISTIC_DASHBOARD: "/core/futuristic-dashboard/",
    INSIGHTS: "/core/insights/",
    KPIS: "/core/kpis/",
    NOTIFICATIONS: "/core/notifications/",
    PRICE_SUGGESTION: "/core/price-suggestion/",
    GENERATE_INVOICE_NUMBER: "/core/generate-invoice-number/",
  },
};

// Types de réponse API
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

// Configuration des headers par défaut
export const getDefaultHeaders = (): Record<string, string> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  const token = localStorage.getItem("authToken");
  if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  }

  return headers;
};

// Utilitaires pour les paramètres de requête
export const buildQueryParams = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== "") {
      searchParams.append(key, value.toString());
    }
  });

  return searchParams.toString();
};

// Configuration des filtres par défaut
export const DEFAULT_FILTERS = {
  page: 1,
  page_size: 20,
  ordering: "-created_at",
};

// Messages d'erreur par défaut
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "Erreur de connexion au serveur",
  UNAUTHORIZED: "Vous devez vous connecter pour accéder à cette ressource",
  FORBIDDEN: "Vous n'avez pas les permissions nécessaires",
  NOT_FOUND: "Ressource non trouvée",
  SERVER_ERROR: "Erreur interne du serveur",
  VALIDATION_ERROR: "Erreur de validation des données",
  UNKNOWN_ERROR: "Une erreur inattendue s'est produite",
};

// Mapping des codes d'erreur HTTP
export const getErrorMessage = (status: number): string => {
  switch (status) {
    case 401:
      return ERROR_MESSAGES.UNAUTHORIZED;
    case 403:
      return ERROR_MESSAGES.FORBIDDEN;
    case 404:
      return ERROR_MESSAGES.NOT_FOUND;
    case 422:
      return ERROR_MESSAGES.VALIDATION_ERROR;
    case 500:
      return ERROR_MESSAGES.SERVER_ERROR;
    default:
      return ERROR_MESSAGES.UNKNOWN_ERROR;
  }
};

// Validation des données
export const validateRequired = (
  data: Record<string, any>,
  requiredFields: string[]
): Record<string, string> => {
  const errors: Record<string, string> = {};

  requiredFields.forEach((field) => {
    if (
      !data[field] ||
      (typeof data[field] === "string" && !data[field].trim())
    ) {
      errors[field] = `Le champ ${field} est requis`;
    }
  });

  return errors;
};

// Formatage des données
export const formatCurrency = (
  amount: number,
  currency: string = "TND"
): string => {
  return new Intl.NumberFormat("fr-TN", {
    style: "currency",
    currency: currency,
  }).format(amount);
};

export const formatDate = (
  date: string | Date,
  format: "short" | "long" = "short"
): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (format === "long") {
    return dateObj.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  }

  return dateObj.toLocaleDateString("fr-FR");
};

export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleString("fr-FR");
};

// Utilitaires de validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
  return phoneRegex.test(phone);
};

// SIRET supprimé car non applicable en Tunisie

// Constantes métier
export const BUSINESS_CONSTANTS = {
  DEFAULT_TVA_RATE: 20,
  DEFAULT_PAYMENT_DELAY: 30,
  DEFAULT_CURRENCY: "TND",
  DEFAULT_COUNTRY: "Tunisie",

  CLIENT_TYPES: {
    PARTICULIER: "Particulier",
    ENTREPRISE: "Entreprise",
    ASSOCIATION: "Association",
    ADMINISTRATION: "Administration",
  },

  CLIENT_SEGMENTS: {
    NOUVEAU: "Nouveau",
    STANDARD: "Standard",
    PREMIUM: "Premium",
    VIP: "VIP",
  },

  ORDER_STATUS: {
    BROUILLON: "Brouillon",
    CONFIRMEE: "Confirmée",
    PREPARATION: "En préparation",
    EXPEDITION: "Expédiée",
    LIVREE: "Livrée",
    ANNULEE: "Annulée",
  },

  INVOICE_STATUS: {
    BROUILLON: "Brouillon",
    ENVOYEE: "Envoyée",
    PAYEE: "Payée",
    PARTIELLEMENT_PAYEE: "Partiellement payée",
    EN_RETARD: "En retard",
    ANNULEE: "Annulée",
  },

  PAYMENT_MODES: {
    ESPECES: "Espèces",
    CHEQUE: "Chèque",
    VIREMENT: "Virement",
    CARTE: "Carte bancaire",
    PRELEVEMENT: "Prélèvement",
    PAYPAL: "PayPal",
    STRIPE: "Stripe",
  },
};
