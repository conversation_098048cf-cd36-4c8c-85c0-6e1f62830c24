# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Clients', '0001_initial'),
        ('Produits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Commande',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('numero', models.CharField(max_length=50, unique=True, verbose_name='Numéro de commande')),
                ('type_commande', models.CharField(choices=[('VENTE', 'Vente'), ('DEVIS', 'Devis'), ('RETOUR', 'Retour')], default='VENTE', max_length=20)),
                ('statut', models.CharField(choices=[('BROUILLON', 'Brouillon'), ('CONFIRMEE', 'Confirmée'), ('PREPARATION', 'En préparation'), ('EXPEDITION', 'Expédiée'), ('LIVREE', 'Livrée'), ('ANNULEE', 'Annulée')], default='BROUILLON', max_length=20)),
                ('priorite', models.CharField(choices=[('BASSE', 'Basse'), ('NORMALE', 'Normale'), ('HAUTE', 'Haute'), ('URGENTE', 'Urgente')], default='NORMALE', max_length=20)),
                ('date_commande', models.DateTimeField(auto_now_add=True)),
                ('date_livraison_prevue', models.DateField(blank=True, null=True)),
                ('date_livraison_reelle', models.DateField(blank=True, null=True)),
                ('adresse_livraison', models.TextField(blank=True)),
                ('ville_livraison', models.CharField(blank=True, max_length=100)),
                ('code_postal_livraison', models.CharField(blank=True, max_length=10)),
                ('pays_livraison', models.CharField(default='France', max_length=100)),
                ('sous_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('remise_globale', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('montant_tva', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('montant_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('notes_internes', models.TextField(blank=True, verbose_name='Notes internes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='commandes', to='Clients.client')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='commandes_created', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='commandes_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Commande',
                'verbose_name_plural': 'Commandes',
                'ordering': ['-date_commande'],
            },
        ),
        migrations.CreateModel(
            name='LigneCommande',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantite', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10)),
                ('remise', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('taux_tva', models.DecimalField(decimal_places=2, default=Decimal('20.00'), max_digits=5)),
                ('total_ht', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('montant_tva', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_ttc', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('commande', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lignes', to='Commandes.commande')),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='Produits.produit')),
            ],
            options={
                'verbose_name': 'Ligne de commande',
                'verbose_name_plural': 'Lignes de commande',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='StatutCommande',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ancien_statut', models.CharField(blank=True, max_length=20)),
                ('nouveau_statut', models.CharField(max_length=20)),
                ('commentaire', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('commande', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='historique_statuts', to='Commandes.commande')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Historique statut',
                'verbose_name_plural': 'Historique des statuts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='commande',
            index=models.Index(fields=['numero'], name='Commandes_c_numero_20cf41_idx'),
        ),
        migrations.AddIndex(
            model_name='commande',
            index=models.Index(fields=['statut'], name='Commandes_c_statut_187cc1_idx'),
        ),
        migrations.AddIndex(
            model_name='commande',
            index=models.Index(fields=['client'], name='Commandes_c_client__acd9c7_idx'),
        ),
        migrations.AddIndex(
            model_name='commande',
            index=models.Index(fields=['date_commande'], name='Commandes_c_date_co_33336d_idx'),
        ),
    ]
