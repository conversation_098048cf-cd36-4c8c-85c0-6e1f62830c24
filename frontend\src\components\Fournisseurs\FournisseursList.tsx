/**
 * Liste des fournisseurs avec gestion complète
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNotify } from '../Common/NotificationSystem';

interface Fournisseur {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse?: string;
  ville?: string;
  code_postal?: string;
  pays: string;
  siret?: string;
  tva_intracommunautaire?: string;
  statut: 'ACTIF' | 'INACTIF' | 'SUSPENDU';
  type: 'PRODUITS' | 'SERVICES' | 'MIXTE';
  conditions_paiement: string;
  delai_paiement: number;
  total_achats: number;
  derniere_commande?: string;
  created_at: string;
}

const FournisseursList: React.FC = () => {
  const navigate = useNavigate();
  const notify = useNotify();

  const [fournisseurs, setFournisseurs] = useState<Fournisseur[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    statut: '',
    type: '',
    pays: ''
  });

  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [sortField, setSortField] = useState<string>('nom');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    loadFournisseurs();
  }, [searchTerm, filters, sortField, sortDirection]);

  const loadFournisseurs = async () => {
    try {
      setLoading(true);
      
      // Simuler le chargement des fournisseurs
      const mockFournisseurs: Fournisseur[] = [
        {
          id: '1',
          nom: 'TechSupply SARL',
          email: '<EMAIL>',
          telephone: '01 23 45 67 89',
          adresse: '123 Rue de la Tech',
          ville: 'Paris',
          code_postal: '75001',
          pays: 'France',
          siret: '12345678901234',
          tva_intracommunautaire: 'FR12345678901',
          statut: 'ACTIF',
          type: 'PRODUITS',
          conditions_paiement: '30 jours fin de mois',
          delai_paiement: 30,
          total_achats: 25680.50,
          derniere_commande: '2024-08-05',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          nom: 'Services Pro',
          email: '<EMAIL>',
          telephone: '04 56 78 90 12',
          adresse: '456 Avenue des Services',
          ville: 'Lyon',
          code_postal: '69000',
          pays: 'France',
          statut: 'ACTIF',
          type: 'SERVICES',
          conditions_paiement: '15 jours',
          delai_paiement: 15,
          total_achats: 12450.00,
          derniere_commande: '2024-08-08',
          created_at: '2024-02-20T14:30:00Z'
        },
        {
          id: '3',
          nom: 'Global Supplies Ltd',
          email: '<EMAIL>',
          telephone: '+44 20 1234 5678',
          adresse: '789 Business Street',
          ville: 'London',
          code_postal: 'SW1A 1AA',
          pays: 'Royaume-Uni',
          statut: 'INACTIF',
          type: 'MIXTE',
          conditions_paiement: '45 jours',
          delai_paiement: 45,
          total_achats: 8750.25,
          derniere_commande: '2024-06-15',
          created_at: '2024-03-10T09:15:00Z'
        }
      ];

      // Appliquer les filtres et tri
      let filtered = mockFournisseurs.filter(fournisseur => {
        const matchSearch = fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           fournisseur.email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchStatut = !filters.statut || fournisseur.statut === filters.statut;
        const matchType = !filters.type || fournisseur.type === filters.type;
        const matchPays = !filters.pays || fournisseur.pays === filters.pays;
        
        return matchSearch && matchStatut && matchType && matchPays;
      });

      // Tri
      filtered.sort((a, b) => {
        const aValue = a[sortField as keyof Fournisseur];
        const bValue = b[sortField as keyof Fournisseur];
        
        if (sortDirection === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      setFournisseurs(filtered);
    } catch (error: any) {
      notify.error('Erreur lors du chargement des fournisseurs');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedIds(checked ? fournisseurs.map(f => f.id) : []);
  };

  const handleSelectOne = (id: string, checked: boolean) => {
    setSelectedIds(prev => 
      checked 
        ? [...prev, id]
        : prev.filter(selectedId => selectedId !== id)
    );
  };

  const handleBulkAction = async (action: string) => {
    if (selectedIds.length === 0) {
      notify.warning('Aucun fournisseur sélectionné');
      return;
    }

    try {
      switch (action) {
        case 'activate':
          notify.success(`${selectedIds.length} fournisseur(s) activé(s)`);
          break;
        case 'deactivate':
          notify.success(`${selectedIds.length} fournisseur(s) désactivé(s)`);
          break;
        case 'export':
          notify.success('Export en cours...');
          break;
        case 'delete':
          if (window.confirm(`Supprimer ${selectedIds.length} fournisseur(s) ?`)) {
            notify.success(`${selectedIds.length} fournisseur(s) supprimé(s)`);
          }
          break;
      }
      setSelectedIds([]);
      loadFournisseurs();
    } catch (error: any) {
      notify.error('Erreur lors de l\'action groupée');
    }
  };

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'ACTIF': return 'status-active';
      case 'INACTIF': return 'status-inactive';
      case 'SUSPENDU': return 'status-suspended';
      default: return 'status-default';
    }
  };

  const getStatutLabel = (statut: string) => {
    switch (statut) {
      case 'ACTIF': return '✅ Actif';
      case 'INACTIF': return '⭕ Inactif';
      case 'SUSPENDU': return '⚠️ Suspendu';
      default: return statut;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'PRODUITS': return '📦 Produits';
      case 'SERVICES': return '🔧 Services';
      case 'MIXTE': return '🔄 Mixte';
      default: return type;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  return (
    <div className="fournisseurs-list">
      {/* Filtres et recherche */}
      <div className="list-filters">
        <div className="search-section">
          <div className="search-input-container">
            <input
              type="text"
              placeholder="🔍 Rechercher un fournisseur..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        <div className="filters-section">
          <select
            value={filters.statut}
            onChange={(e) => setFilters(prev => ({ ...prev, statut: e.target.value }))}
            className="filter-select"
          >
            <option value="">Tous les statuts</option>
            <option value="ACTIF">✅ Actifs</option>
            <option value="INACTIF">⭕ Inactifs</option>
            <option value="SUSPENDU">⚠️ Suspendus</option>
          </select>

          <select
            value={filters.type}
            onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
            className="filter-select"
          >
            <option value="">Tous les types</option>
            <option value="PRODUITS">📦 Produits</option>
            <option value="SERVICES">🔧 Services</option>
            <option value="MIXTE">🔄 Mixte</option>
          </select>

          <select
            value={filters.pays}
            onChange={(e) => setFilters(prev => ({ ...prev, pays: e.target.value }))}
            className="filter-select"
          >
            <option value="">Tous les pays</option>
            <option value="France">🇫🇷 France</option>
            <option value="Belgique">🇧🇪 Belgique</option>
            <option value="Suisse">🇨🇭 Suisse</option>
            <option value="Royaume-Uni">🇬🇧 Royaume-Uni</option>
          </select>
        </div>
      </div>

      {/* Actions groupées */}
      {selectedIds.length > 0 && (
        <div className="bulk-actions">
          <div className="bulk-info">
            <span>{selectedIds.length} fournisseur(s) sélectionné(s)</span>
          </div>
          <div className="bulk-buttons">
            <button
              className="btn btn-success btn-sm"
              onClick={() => handleBulkAction('activate')}
            >
              ✅ Activer
            </button>
            <button
              className="btn btn-warning btn-sm"
              onClick={() => handleBulkAction('deactivate')}
            >
              ⭕ Désactiver
            </button>
            <button
              className="btn btn-info btn-sm"
              onClick={() => handleBulkAction('export')}
            >
              📤 Exporter
            </button>
            <button
              className="btn btn-danger btn-sm"
              onClick={() => handleBulkAction('delete')}
            >
              🗑️ Supprimer
            </button>
          </div>
        </div>
      )}

      {/* Tableau des fournisseurs */}
      <div className="fournisseurs-table-container">
        <table className="fournisseurs-table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedIds.length === fournisseurs.length && fournisseurs.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="table-checkbox"
                />
              </th>
              <th 
                className="sortable"
                onClick={() => handleSort('nom')}
              >
                Nom {sortField === 'nom' && (sortDirection === 'asc' ? '↑' : '↓')}
              </th>
              <th>Contact</th>
              <th>Localisation</th>
              <th 
                className="sortable"
                onClick={() => handleSort('type')}
              >
                Type {sortField === 'type' && (sortDirection === 'asc' ? '↑' : '↓')}
              </th>
              <th 
                className="sortable"
                onClick={() => handleSort('statut')}
              >
                Statut {sortField === 'statut' && (sortDirection === 'asc' ? '↑' : '↓')}
              </th>
              <th 
                className="sortable"
                onClick={() => handleSort('total_achats')}
              >
                Total achats {sortField === 'total_achats' && (sortDirection === 'asc' ? '↑' : '↓')}
              </th>
              <th>Dernière commande</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {fournisseurs.map(fournisseur => (
              <tr key={fournisseur.id} className="fournisseur-row">
                <td>
                  <input
                    type="checkbox"
                    checked={selectedIds.includes(fournisseur.id)}
                    onChange={(e) => handleSelectOne(fournisseur.id, e.target.checked)}
                    className="table-checkbox"
                  />
                </td>
                <td className="fournisseur-name">
                  <div className="name-cell">
                    <strong>{fournisseur.nom}</strong>
                    {fournisseur.siret && (
                      <small>SIRET: {fournisseur.siret}</small>
                    )}
                  </div>
                </td>
                <td className="contact-cell">
                  <div>📧 {fournisseur.email}</div>
                  {fournisseur.telephone && (
                    <div>📞 {fournisseur.telephone}</div>
                  )}
                </td>
                <td className="location-cell">
                  <div>
                    {fournisseur.ville && `${fournisseur.ville}, `}
                    {fournisseur.pays}
                  </div>
                  {fournisseur.code_postal && (
                    <small>{fournisseur.code_postal}</small>
                  )}
                </td>
                <td>
                  <span className="type-badge">
                    {getTypeLabel(fournisseur.type)}
                  </span>
                </td>
                <td>
                  <span className={`status-badge ${getStatutColor(fournisseur.statut)}`}>
                    {getStatutLabel(fournisseur.statut)}
                  </span>
                </td>
                <td className="amount-cell">
                  <strong>{formatCurrency(fournisseur.total_achats)}</strong>
                </td>
                <td className="date-cell">
                  {fournisseur.derniere_commande 
                    ? formatDate(fournisseur.derniere_commande)
                    : '-'
                  }
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={() => navigate(`/fournisseurs/${fournisseur.id}`)}
                      title="Voir détail"
                    >
                      👁️
                    </button>
                    <button
                      className="btn btn-sm btn-info"
                      onClick={() => navigate(`/fournisseurs/${fournisseur.id}/edit`)}
                      title="Modifier"
                    >
                      ✏️
                    </button>
                    <button
                      className="btn btn-sm btn-success"
                      onClick={() => navigate(`/orders/new?fournisseur=${fournisseur.id}`)}
                      title="Nouvelle commande"
                    >
                      🛒
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* États de chargement et vide */}
      {loading && (
        <div className="list-loading">
          <div className="loading-spinner"></div>
          <p>Chargement des fournisseurs...</p>
        </div>
      )}

      {!loading && fournisseurs.length === 0 && (
        <div className="list-empty">
          <div className="empty-icon">🏭</div>
          <h3>Aucun fournisseur trouvé</h3>
          <p>
            {searchTerm || Object.values(filters).some(f => f)
              ? 'Aucun fournisseur ne correspond aux critères de recherche'
              : 'Commencez par ajouter votre premier fournisseur'
            }
          </p>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/fournisseurs/new')}
          >
            ➕ Ajouter un fournisseur
          </button>
        </div>
      )}

      {/* Statistiques rapides */}
      {!loading && fournisseurs.length > 0 && (
        <div className="list-stats">
          <div className="stat-item">
            <span className="stat-label">Total fournisseurs:</span>
            <span className="stat-value">{fournisseurs.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Actifs:</span>
            <span className="stat-value">
              {fournisseurs.filter(f => f.statut === 'ACTIF').length}
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Total achats:</span>
            <span className="stat-value">
              {formatCurrency(fournisseurs.reduce((sum, f) => sum + f.total_achats, 0))}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default FournisseursList;
