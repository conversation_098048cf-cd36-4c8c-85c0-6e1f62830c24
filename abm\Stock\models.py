from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid

User = get_user_model()

class MouvementStock(models.Model):
    """Mouvements de stock (entrées, sorties, ajustements)"""

    TYPE_CHOICES = [
        ('ENTREE', 'Entrée'),
        ('SORTIE', 'Sortie'),
        ('AJUSTEMENT', 'Ajustement'),
        ('INVENTAIRE', 'Inventaire'),
        ('TRANSFERT', 'Transfert'),
        ('RETOUR', 'Retour'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reference = models.CharField(max_length=50, unique=True, verbose_name="Référence")

    # Relations
    produit = models.ForeignKey('Produits.Produit', on_delete=models.PROTECT, related_name='mouvements_stock')

    # Type et quantités
    type_mouvement = models.CharField(max_length=20, choices=TYPE_CHOICES)
    quantite = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    quantite_avant = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    quantite_apres = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Prix et valeur
    prix_unitaire = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="Prix unitaire"
    )
    valeur_totale = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="Valeur totale"
    )

    # Informations
    motif = models.CharField(max_length=200, verbose_name="Motif du mouvement")
    reference_externe = models.CharField(max_length=100, blank=True, verbose_name="Référence externe")

    # Relations optionnelles
    commande = models.ForeignKey(
        'Commandes.Commande',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='mouvements_stock'
    )
    facture = models.ForeignKey(
        'Facturation.Facture',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='mouvements_stock'
    )

    # Audit
    date_mouvement = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='mouvements_created')

    class Meta:
        verbose_name = "Mouvement de stock"
        verbose_name_plural = "Mouvements de stock"
        ordering = ['-date_mouvement']
        indexes = [
            models.Index(fields=['reference']),
            models.Index(fields=['produit']),
            models.Index(fields=['type_mouvement']),
            models.Index(fields=['date_mouvement']),
        ]

    def __str__(self):
        return f"{self.reference} - {self.produit.nom} ({self.get_type_mouvement_display()})"

    def save(self, *args, **kwargs):
        """Calcule automatiquement la valeur totale"""
        if self.prix_unitaire:
            self.valeur_totale = self.quantite * self.prix_unitaire

        super().save(*args, **kwargs)


class Inventaire(models.Model):
    """Inventaires physiques"""

    STATUT_CHOICES = [
        ('EN_COURS', 'En cours'),
        ('TERMINE', 'Terminé'),
        ('VALIDE', 'Validé'),
        ('ANNULE', 'Annulé'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    numero = models.CharField(max_length=50, unique=True, verbose_name="Numéro d'inventaire")

    # Informations
    nom = models.CharField(max_length=200, verbose_name="Nom de l'inventaire")
    description = models.TextField(blank=True)
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='EN_COURS')

    # Dates
    date_debut = models.DateTimeField(verbose_name="Date de début")
    date_fin = models.DateTimeField(null=True, blank=True, verbose_name="Date de fin")
    date_validation = models.DateTimeField(null=True, blank=True, verbose_name="Date de validation")

    # Filtres (pour inventaires partiels)
    categories = models.ManyToManyField('Produits.Categorie', blank=True)
    produits_specifiques = models.ManyToManyField('Produits.Produit', blank=True)

    # Résultats
    nb_produits_comptes = models.IntegerField(default=0)
    nb_ecarts_detectes = models.IntegerField(default=0)
    valeur_ecarts = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='inventaires_created')
    validated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='inventaires_validated')

    class Meta:
        verbose_name = "Inventaire"
        verbose_name_plural = "Inventaires"
        ordering = ['-date_debut']

    def __str__(self):
        return f"{self.numero} - {self.nom}"


class LigneInventaire(models.Model):
    """Lignes d'inventaire pour chaque produit compté"""

    inventaire = models.ForeignKey(Inventaire, on_delete=models.CASCADE, related_name='lignes')
    produit = models.ForeignKey('Produits.Produit', on_delete=models.PROTECT)

    # Quantités
    stock_theorique = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Stock théorique")
    stock_physique = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Stock physique compté")
    ecart = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Valeurs
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2)
    valeur_ecart = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Informations
    notes = models.TextField(blank=True, verbose_name="Notes de comptage")

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    counted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='comptages')

    class Meta:
        verbose_name = "Ligne d'inventaire"
        verbose_name_plural = "Lignes d'inventaire"
        unique_together = ['inventaire', 'produit']

    def __str__(self):
        return f"{self.inventaire.numero} - {self.produit.nom}"

    def save(self, *args, **kwargs):
        """Calcule automatiquement l'écart et sa valeur"""
        self.ecart = self.stock_physique - self.stock_theorique
        self.valeur_ecart = self.ecart * self.prix_unitaire

        super().save(*args, **kwargs)
