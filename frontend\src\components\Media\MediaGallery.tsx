import React, { useState, useEffect } from "react";
import { useNotify } from "../Common/NotificationSystem";
import FileUpload from "./FileUpload";
import "../Invoices/Invoice.css";
import { MediaFile } from "../../services/mediaService";

// Service Media
const MediaService = {
  async getFiles(params?: any) {
    // Simulation d'appel API - à remplacer par le vrai service
    return { success: true, data: { results: [] } };
  },

  async deleteFile(fileId: number) {
    // Simulation d'appel API - à remplacer par le vrai service
    return { success: true, message: "Fichier supprimé" };
  },

  isImage(type: string) {
    return type.startsWith("image/");
  },

  getFileIcon(type: string) {
    if (type.startsWith("image/")) return "🖼️";
    if (type.startsWith("video/")) return "🎥";
    if (type.startsWith("audio/")) return "🎵";
    if (type.includes("pdf")) return "📄";
    if (type.includes("word")) return "📝";
    if (type.includes("excel")) return "📊";
    return "📁";
  },

  formatFileSize(bytes: number) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },

  formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("fr-FR");
  },
};

interface MediaGalleryProps {
  onSelectFile?: (file: MediaFile) => void;
  selectionMode?: boolean;
  allowedTypes?: string[];
  showUpload?: boolean;
}

const MediaGallery: React.FC<MediaGalleryProps> = ({
  onSelectFile,
  selectionMode = false,
  allowedTypes,
  showUpload = true,
}) => {
  const notify = useNotify();
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [filters, setFilters] = useState({
    search: "",
    type_fichier: "all",
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadFiles();
  }, [filters, currentPage]);

  const loadFiles = async () => {
    setLoading(true);
    const filterParams: any = {
      page: currentPage,
      page_size: 12,
    };

    if (filters.search) filterParams.search = filters.search;
    if (filters.type_fichier !== "all")
      filterParams.type_fichier = filters.type_fichier;
    if (allowedTypes) filterParams.type_fichier = allowedTypes.join(",");

    const res = await MediaService.getFiles(filterParams);
    if (res.success && res.data) {
      const data = res.data as any;
      setFiles(data.results || []);
      setTotalPages(Math.ceil(data.count / 12));
    }
    setLoading(false);
  };

  const handleFileSelect = (file: MediaFile) => {
    if (selectionMode) {
      onSelectFile?.(file);
    } else {
      setSelectedFile(file);
    }
  };

  const handleDeleteFile = async (fileId: number) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce fichier ?"))
      return;

    const res = await MediaService.deleteFile(fileId);
    if (res.success) {
      notify.success("Succès", "Fichier supprimé");
      loadFiles();
      if (selectedFile?.id === fileId) {
        setSelectedFile(null);
      }
    } else {
      notify.error(
        "Erreur",
        res.message || "Impossible de supprimer le fichier"
      );
    }
  };

  const handleUploadComplete = (uploadedFiles: MediaFile[]) => {
    setShowUploadModal(false);
    loadFiles();
    notify.success("Succès", `${uploadedFiles.length} fichier(s) uploadé(s)`);
  };

  const resetFilters = () => {
    setFilters({
      search: "",
      type_fichier: "all",
    });
    setCurrentPage(1);
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">
              {selectionMode ? "Sélectionner un fichier" : "Galerie Média"}
            </h1>
            <p className="page-subtitle">Gestion des fichiers et documents</p>
          </div>
          <div className="page-actions">
            {showUpload && (
              <button
                className="page-action"
                onClick={() => setShowUploadModal(true)}>
                📤 Upload
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="page-content">
        {/* Filtres */}
        <div
          style={{
            marginBottom: "20px",
            background: "#f8f9fa",
            padding: "20px",
            borderRadius: "8px",
          }}>
          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: "wrap",
              alignItems: "center",
            }}>
            <div>
              <input
                type="text"
                placeholder="Rechercher des fichiers..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
                style={{
                  width: "300px",
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}
              />
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Type:
              </label>
              <select
                value={filters.type_fichier}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    type_fichier: e.target.value,
                  }))
                }
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="all">Tous</option>
                <option value="image/">Images</option>
                <option value="video/">Vidéos</option>
                <option value="audio/">Audio</option>
                <option value="application/pdf">PDF</option>
                <option value="application/">Documents</option>
              </select>
            </div>

            <button
              onClick={resetFilters}
              style={{
                padding: "8px 12px",
                background: "#e74c3c",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
              }}>
              🗑️ Effacer
            </button>
          </div>
        </div>

        {/* Grille de fichiers */}
        {loading ? (
          <div style={{ textAlign: "center", padding: "40px" }}>
            <div className="loading-spinner"></div>
            <div className="loading-text">Chargement des fichiers...</div>
          </div>
        ) : (
          <>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))",
                gap: "20px",
                marginBottom: "30px",
              }}>
              {files.map((file) => (
                <div
                  key={file.id}
                  style={{
                    border:
                      selectedFile?.id === file.id
                        ? "2px solid #3498db"
                        : "1px solid #dee2e6",
                    borderRadius: "8px",
                    overflow: "hidden",
                    background: "white",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    cursor: "pointer",
                    transition: "transform 0.2s",
                  }}
                  onClick={() => handleFileSelect(file)}
                  onMouseEnter={(e) =>
                    (e.currentTarget.style.transform = "translateY(-2px)")
                  }
                  onMouseLeave={(e) =>
                    (e.currentTarget.style.transform = "translateY(0)")
                  }>
                  {/* Aperçu du fichier */}
                  <div
                    style={{
                      height: "150px",
                      background: "#f8f9fa",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      position: "relative",
                    }}>
                    {MediaService.isImage(file.type_fichier) ? (
                      <img
                        src={file.miniature || file.url}
                        alt={file.nom}
                        style={{
                          maxWidth: "100%",
                          maxHeight: "100%",
                          objectFit: "cover",
                        }}
                      />
                    ) : (
                      <div style={{ fontSize: "48px" }}>
                        {MediaService.getFileIcon(file.type_fichier)}
                      </div>
                    )}

                    {/* Badge du type de fichier */}
                    <div
                      style={{
                        position: "absolute",
                        top: "8px",
                        right: "8px",
                        background: "rgba(0,0,0,0.7)",
                        color: "white",
                        padding: "2px 6px",
                        borderRadius: "4px",
                        fontSize: "10px",
                      }}>
                      {file.type_fichier.split("/")[1]?.toUpperCase() || "FILE"}
                    </div>
                  </div>

                  {/* Informations du fichier */}
                  <div style={{ padding: "12px" }}>
                    <h4
                      style={{
                        margin: "0 0 4px 0",
                        fontSize: "14px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}>
                      {file.nom}
                    </h4>
                    <p
                      style={{
                        margin: "0 0 8px 0",
                        fontSize: "12px",
                        color: "#666",
                      }}>
                      {MediaService.formatFileSize(file.taille)}
                    </p>
                    <p
                      style={{
                        margin: "0",
                        fontSize: "11px",
                        color: "#999",
                      }}>
                      {MediaService.formatDate(file.date_upload)}
                    </p>
                  </div>

                  {/* Actions */}
                  {!selectionMode && (
                    <div
                      style={{
                        padding: "8px 12px",
                        borderTop: "1px solid #f0f0f0",
                        display: "flex",
                        gap: "8px",
                      }}>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(file.url, "_blank");
                        }}
                        style={{
                          flex: 1,
                          padding: "4px 8px",
                          background: "#3498db",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer",
                          fontSize: "12px",
                        }}>
                        👁️ Voir
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteFile(file.id);
                        }}
                        style={{
                          padding: "4px 8px",
                          background: "#e74c3c",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer",
                          fontSize: "12px",
                        }}>
                        🗑️
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: "10px",
                  alignItems: "center",
                }}>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  style={{
                    padding: "8px 12px",
                    background: currentPage === 1 ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: currentPage === 1 ? "not-allowed" : "pointer",
                  }}>
                  ← Précédent
                </button>

                <span style={{ margin: "0 15px" }}>
                  Page {currentPage} sur {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  style={{
                    padding: "8px 12px",
                    background:
                      currentPage === totalPages ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor:
                      currentPage === totalPages ? "not-allowed" : "pointer",
                  }}>
                  Suivant →
                </button>
              </div>
            )}
          </>
        )}

        {files.length === 0 && !loading && (
          <div
            style={{ textAlign: "center", padding: "60px", color: "#7f8c8d" }}>
            <div style={{ fontSize: "48px", marginBottom: "20px" }}>📁</div>
            <h3>Aucun fichier</h3>
            <p>Commencez par uploader vos premiers fichiers</p>
            {showUpload && (
              <button
                onClick={() => setShowUploadModal(true)}
                style={{
                  padding: "12px 24px",
                  background: "#3498db",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  cursor: "pointer",
                  fontSize: "16px",
                  marginTop: "20px",
                }}>
                📤 Upload des fichiers
              </button>
            )}
          </div>
        )}
      </div>

      {/* Modal d'upload */}
      {showUploadModal && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(0,0,0,0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}>
          <div
            style={{
              background: "white",
              borderRadius: "12px",
              padding: "24px",
              width: "90%",
              maxWidth: "600px",
              maxHeight: "90vh",
              overflow: "auto",
            }}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "20px",
              }}>
              <h3 style={{ margin: 0 }}>Upload de fichiers</h3>
              <button
                onClick={() => setShowUploadModal(false)}
                style={{
                  background: "none",
                  border: "none",
                  fontSize: "24px",
                  cursor: "pointer",
                  color: "#666",
                }}>
                ×
              </button>
            </div>

            <FileUpload
              multiple={true}
              maxFiles={10}
              onUploadComplete={handleUploadComplete}
              onUploadError={(error) => notify.error("Erreur", error)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaGallery;
