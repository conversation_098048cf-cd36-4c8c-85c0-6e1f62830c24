/**
 * Composant de configuration de l'application
 */

import React, { useState, useEffect } from 'react';
import { API_CONFIG, BUSINESS_CONSTANTS } from '../../config/api';
import { useNotify } from '../Common/NotificationSystem';
import ApiConnectionTest from './ApiConnectionTest';

interface AppConfig {
  apiUrl: string;
  timeout: number;
  defaultCurrency: string;
  defaultTvaRate: number;
  defaultPaymentDelay: number;
  enableDebugMode: boolean;
  enableNotifications: boolean;
  autoSave: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: 'fr' | 'ar' | 'en';
}

const AppSettings: React.FC = () => {
  const notify = useNotify();
  const [config, setConfig] = useState<AppConfig>({
    apiUrl: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    defaultCurrency: BUSINESS_CONSTANTS.DEFAULT_CURRENCY,
    defaultTvaRate: BUSINESS_CONSTANTS.DEFAULT_TVA_RATE,
    defaultPaymentDelay: BUSINESS_CONSTANTS.DEFAULT_PAYMENT_DELAY,
    enableDebugMode: false,
    enableNotifications: true,
    autoSave: true,
    theme: 'light',
    language: 'fr'
  });

  const [showApiTest, setShowApiTest] = useState(false);

  useEffect(() => {
    // Charger la configuration depuis localStorage
    const savedConfig = localStorage.getItem('appConfig');
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig);
        setConfig(prev => ({ ...prev, ...parsedConfig }));
      } catch (error) {
        console.error('Erreur lors du chargement de la configuration:', error);
      }
    }
  }, []);

  const saveConfig = () => {
    try {
      localStorage.setItem('appConfig', JSON.stringify(config));
      notify.success('Configuration sauvegardée');
    } catch (error) {
      notify.error('Erreur lors de la sauvegarde');
    }
  };

  const resetConfig = () => {
    if (window.confirm('Êtes-vous sûr de vouloir réinitialiser la configuration ?')) {
      localStorage.removeItem('appConfig');
      setConfig({
        apiUrl: API_CONFIG.BASE_URL,
        timeout: API_CONFIG.TIMEOUT,
        defaultCurrency: BUSINESS_CONSTANTS.DEFAULT_CURRENCY,
        defaultTvaRate: BUSINESS_CONSTANTS.DEFAULT_TVA_RATE,
        defaultPaymentDelay: BUSINESS_CONSTANTS.DEFAULT_PAYMENT_DELAY,
        enableDebugMode: false,
        enableNotifications: true,
        autoSave: true,
        theme: 'light',
        language: 'fr'
      });
      notify.success('Configuration réinitialisée');
    }
  };

  const handleConfigChange = (key: keyof AppConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    
    if (config.autoSave) {
      setTimeout(saveConfig, 500); // Auto-save avec délai
    }
  };

  return (
    <div className="app-settings">
      <div className="settings-header">
        <h2>Paramètres de l'Application</h2>
        <p>Configurez les paramètres généraux de l'application ABM</p>
      </div>

      <div className="settings-sections">
        {/* Section API */}
        <div className="settings-section">
          <h3>🔗 Configuration API</h3>
          
          <div className="form-group">
            <label>URL de l'API Backend</label>
            <input
              type="url"
              value={config.apiUrl}
              onChange={(e) => handleConfigChange('apiUrl', e.target.value)}
              placeholder="http://localhost:8000/api"
            />
          </div>

          <div className="form-group">
            <label>Timeout (ms)</label>
            <input
              type="number"
              value={config.timeout}
              onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
              min="5000"
              max="60000"
              step="1000"
            />
          </div>

          <div className="form-actions">
            <button
              onClick={() => setShowApiTest(!showApiTest)}
              className="btn-secondary">
              {showApiTest ? 'Masquer' : 'Tester'} la connexion
            </button>
          </div>

          {showApiTest && <ApiConnectionTest />}
        </div>

        {/* Section Business */}
        <div className="settings-section">
          <h3>💼 Paramètres Métier</h3>
          
          <div className="form-group">
            <label>Devise par défaut</label>
            <select
              value={config.defaultCurrency}
              onChange={(e) => handleConfigChange('defaultCurrency', e.target.value)}>
              <option value="TND">Dinar Tunisien (TND)</option>
              <option value="EUR">Euro (EUR)</option>
              <option value="USD">Dollar US (USD)</option>
            </select>
          </div>

          <div className="form-group">
            <label>Taux TVA par défaut (%)</label>
            <input
              type="number"
              value={config.defaultTvaRate}
              onChange={(e) => handleConfigChange('defaultTvaRate', parseFloat(e.target.value))}
              min="0"
              max="100"
              step="0.1"
            />
          </div>

          <div className="form-group">
            <label>Délai de paiement par défaut (jours)</label>
            <input
              type="number"
              value={config.defaultPaymentDelay}
              onChange={(e) => handleConfigChange('defaultPaymentDelay', parseInt(e.target.value))}
              min="0"
              max="365"
            />
          </div>
        </div>

        {/* Section Interface */}
        <div className="settings-section">
          <h3>🎨 Interface Utilisateur</h3>
          
          <div className="form-group">
            <label>Thème</label>
            <select
              value={config.theme}
              onChange={(e) => handleConfigChange('theme', e.target.value)}>
              <option value="light">Clair</option>
              <option value="dark">Sombre</option>
              <option value="auto">Automatique</option>
            </select>
          </div>

          <div className="form-group">
            <label>Langue</label>
            <select
              value={config.language}
              onChange={(e) => handleConfigChange('language', e.target.value)}>
              <option value="fr">Français</option>
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>

          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={config.enableNotifications}
                onChange={(e) => handleConfigChange('enableNotifications', e.target.checked)}
              />
              Activer les notifications
            </label>
          </div>

          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={config.autoSave}
                onChange={(e) => handleConfigChange('autoSave', e.target.checked)}
              />
              Sauvegarde automatique
            </label>
          </div>
        </div>

        {/* Section Développement */}
        <div className="settings-section">
          <h3>🔧 Développement</h3>
          
          <div className="form-group checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={config.enableDebugMode}
                onChange={(e) => handleConfigChange('enableDebugMode', e.target.checked)}
              />
              Mode debug (affiche les logs détaillés)
            </label>
          </div>

          <div className="debug-info">
            <h4>Informations de debug</h4>
            <pre className="debug-output">
              {JSON.stringify({
                apiUrl: config.apiUrl,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                localStorage: {
                  authToken: !!localStorage.getItem('authToken'),
                  appConfig: !!localStorage.getItem('appConfig')
                }
              }, null, 2)}
            </pre>
          </div>
        </div>
      </div>

      <div className="settings-actions">
        <button onClick={saveConfig} className="btn-primary">
          💾 Sauvegarder
        </button>
        <button onClick={resetConfig} className="btn-secondary">
          🔄 Réinitialiser
        </button>
      </div>
    </div>
  );
};

export default AppSettings;
