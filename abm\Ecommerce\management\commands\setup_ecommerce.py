"""
Commande pour initialiser les données e-commerce de test
"""

from django.core.management.base import BaseCommand
from django.utils.text import slugify
from decimal import Decimal
from Facturation.models import Produit, Client
from Ecommerce.models import (
    CategorieEcommerce, ProduitEcommerce, ImageProduitEcommerce,
    <PERSON><PERSON>, ItemPanier
)


class Command(BaseCommand):
    help = 'Initialise les données e-commerce de test'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Supprime toutes les données e-commerce existantes',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('🗑️  Suppression des données e-commerce existantes...')
            ProduitEcommerce.objects.all().delete()
            CategorieEcommerce.objects.all().delete()
            Panier.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('✅ Données supprimées'))

        self.stdout.write('🛒 Initialisation des données e-commerce...')
        
        # Créer les catégories
        self.create_categories()
        
        # Créer les produits e-commerce
        self.create_produits_ecommerce()
        
        # Créer un panier de test
        self.create_panier_test()
        
        self.stdout.write(self.style.SUCCESS('🎉 Données e-commerce initialisées avec succès!'))

    def create_categories(self):
        """Créer les catégories e-commerce"""
        self.stdout.write('📂 Création des catégories...')
        
        categories_data = [
            {
                'nom': 'Électronique',
                'description': 'Produits électroniques et high-tech',
                'enfants': [
                    {'nom': 'Smartphones', 'description': 'Téléphones mobiles et accessoires'},
                    {'nom': 'Ordinateurs', 'description': 'PC, laptops et composants'},
                    {'nom': 'Audio', 'description': 'Casques, enceintes et équipements audio'},
                ]
            },
            {
                'nom': 'Mode',
                'description': 'Vêtements et accessoires de mode',
                'enfants': [
                    {'nom': 'Homme', 'description': 'Mode masculine'},
                    {'nom': 'Femme', 'description': 'Mode féminine'},
                    {'nom': 'Accessoires', 'description': 'Sacs, bijoux et accessoires'},
                ]
            },
            {
                'nom': 'Maison & Jardin',
                'description': 'Produits pour la maison et le jardin',
                'enfants': [
                    {'nom': 'Décoration', 'description': 'Objets décoratifs'},
                    {'nom': 'Jardinage', 'description': 'Outils et plantes'},
                    {'nom': 'Électroménager', 'description': 'Appareils électroménagers'},
                ]
            }
        ]
        
        for cat_data in categories_data:
            # Créer la catégorie parent
            parent, created = CategorieEcommerce.objects.get_or_create(
                nom=cat_data['nom'],
                defaults={
                    'slug': slugify(cat_data['nom']),
                    'description': cat_data['description'],
                    'actif': True,
                    'ordre': 0
                }
            )
            
            if created:
                self.stdout.write(f'  ✅ Catégorie créée: {parent.nom}')
            
            # Créer les sous-catégories
            for i, enfant_data in enumerate(cat_data.get('enfants', [])):
                enfant, created = CategorieEcommerce.objects.get_or_create(
                    nom=enfant_data['nom'],
                    parent=parent,
                    defaults={
                        'slug': slugify(enfant_data['nom']),
                        'description': enfant_data['description'],
                        'actif': True,
                        'ordre': i
                    }
                )
                
                if created:
                    self.stdout.write(f'    ✅ Sous-catégorie créée: {enfant.nom}')

    def create_produits_ecommerce(self):
        """Créer les produits e-commerce à partir des produits existants"""
        self.stdout.write('🛍️  Création des produits e-commerce...')
        
        # Récupérer les catégories
        cat_electronique = CategorieEcommerce.objects.filter(nom='Smartphones').first()
        cat_mode = CategorieEcommerce.objects.filter(nom='Homme').first()
        cat_maison = CategorieEcommerce.objects.filter(nom='Décoration').first()
        
        # Récupérer les produits existants
        produits = Produit.objects.filter(actif=True)[:10]  # Limiter à 10 produits
        
        for i, produit in enumerate(produits):
            # Créer le produit e-commerce s'il n'existe pas
            produit_ecommerce, created = ProduitEcommerce.objects.get_or_create(
                produit=produit,
                defaults={
                    'slug': slugify(f"{produit.nom}-{produit.id}"),
                    'visible_en_ligne': True,
                    'en_vedette': i < 3,  # Les 3 premiers en vedette
                    'nouveau': i < 2,     # Les 2 premiers nouveaux
                    'en_promotion': i % 3 == 0,  # Un sur trois en promotion
                    'prix_public': produit.prix_unitaire * Decimal('1.2'),  # Marge de 20%
                    'prix_promo': produit.prix_unitaire * Decimal('1.1') if i % 3 == 0 else None,
                    'stock_disponible': 50 + (i * 10),
                    'stock_reserve': 0,
                    'gestion_stock': True,
                    'autoriser_commande_sans_stock': False,
                    'description_courte': f"Description courte pour {produit.nom}",
                    'description_longue': f"Description détaillée pour {produit.nom}. Ce produit offre d'excellentes performances et une qualité supérieure.",
                    'caracteristiques': {
                        'couleur': ['Noir', 'Blanc', 'Gris'][i % 3],
                        'garantie': '2 ans',
                        'origine': 'France'
                    },
                    'poids': Decimal('0.5') + (Decimal('0.1') * i),
                    'meta_title': f"{produit.nom} - Achat en ligne",
                    'meta_description': f"Achetez {produit.nom} au meilleur prix. Livraison rapide et garantie.",
                    'nombre_vues': i * 25,
                    'nombre_ventes': i * 5,
                    'note_moyenne': Decimal('4.0') + (Decimal('0.1') * (i % 10)),
                    'nombre_avis': i * 2
                }
            )
            
            if created:
                # Assigner des catégories
                if i % 3 == 0 and cat_electronique:
                    produit_ecommerce.categories_ecommerce.add(cat_electronique)
                elif i % 3 == 1 and cat_mode:
                    produit_ecommerce.categories_ecommerce.add(cat_mode)
                elif cat_maison:
                    produit_ecommerce.categories_ecommerce.add(cat_maison)
                
                self.stdout.write(f'  ✅ Produit e-commerce créé: {produit_ecommerce.produit.nom}')

    def create_panier_test(self):
        """Créer un panier de test"""
        self.stdout.write('🛒 Création d\'un panier de test...')
        
        # Récupérer un client de test
        client = Client.objects.first()
        if not client:
            self.stdout.write(self.style.WARNING('⚠️  Aucun client trouvé, création d\'un client de test...'))
            client = Client.objects.create(
                nom='Test',
                prenom='Client',
                email='<EMAIL>',
                telephone='01 23 45 67 89',
                adresse_ligne1='123 Rue de Test',
                ville='Paris',
                code_postal='75001',
                type_client='PARTICULIER'
            )
        
        # Créer ou récupérer le panier
        panier, created = Panier.objects.get_or_create(client=client)
        
        if created or not panier.items.exists():
            # Ajouter quelques produits au panier
            produits_ecommerce = ProduitEcommerce.objects.filter(visible_en_ligne=True)[:3]
            
            for i, produit_ecommerce in enumerate(produits_ecommerce):
                ItemPanier.objects.get_or_create(
                    panier=panier,
                    produit_ecommerce=produit_ecommerce,
                    defaults={
                        'quantite': i + 1,
                        'prix_unitaire': produit_ecommerce.prix_actuel
                    }
                )
            
            self.stdout.write(f'  ✅ Panier créé avec {panier.total_articles} articles')
        else:
            self.stdout.write(f'  ℹ️  Panier existant avec {panier.total_articles} articles')

    def create_sample_data(self):
        """Créer des données d'exemple complètes"""
        self.stdout.write('📊 Création de données d\'exemple...')
        
        # Cette méthode peut être étendue pour créer plus de données de test
        # comme des commandes d'exemple, des avis clients, etc.
        pass
