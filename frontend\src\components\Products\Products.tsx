/**
 * Composant principal de gestion des produits
 * Version moderne avec routing vers les sous-composants
 */

import React from "react";
import { Routes, Route, useNavigate } from "react-router-dom";
import ProductsList from "./ProductsList";
import ProductForm from "./ProductForm";
import ProductDetail from "./ProductDetail";
import ProductStats from "./ProductStats";
import { useAuth } from "../../contexts/AuthContext";
import "./Products.css";

const Products: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Vérifier les permissions
  const hasProductAccess = () => {
    const allowedRoles = ['NORMAL', 'COMPTABLE', 'ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || 'NORMAL');
  };

  if (!hasProductAccess()) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder à la gestion des produits.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="products-module">
      <Routes>
        {/* Liste des produits */}
        <Route 
          path="/" 
          element={<ProductsList />} 
        />
        
        {/* Nouveau produit */}
        <Route 
          path="/new" 
          element={<ProductForm />} 
        />
        
        {/* Détail d'un produit */}
        <Route 
          path="/:id" 
          element={<ProductDetail />} 
        />
        
        {/* Édition d'un produit */}
        <Route 
          path="/:id/edit" 
          element={<ProductForm />} 
        />
        
        {/* Catégories */}
        <Route 
          path="/categories" 
          element={<ProductsList />} 
        />
        
        {/* Statistiques produits */}
        <Route 
          path="/stats" 
          element={<ProductStats />} 
        />
      </Routes>
    </div>
  );
};

export default Products;
