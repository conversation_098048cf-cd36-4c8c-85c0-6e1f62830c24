"""
Configuration des modules ABM
Définit l'architecture et les relations entre modules
"""

# === CONFIGURATION DES MODULES ===

MODULES_CONFIG = {
    'Authentication': {
        'description': 'Authentification et gestion des utilisateurs',
        'models': ['User', 'UserRole', 'Permission'],
        'permissions': ['admin.users', 'admin.roles'],
        'dependencies': [],
        'api_prefix': 'auth'
    },
    
    'Core': {
        'description': 'Fonctionnalités centrales et utilitaires',
        'models': ['BaseModel', 'Configuration', 'Log'],
        'permissions': ['admin.system'],
        'dependencies': ['Authentication'],
        'api_prefix': 'core'
    },
    
    'Clients': {
        'description': 'Gestion des clients et CRM',
        'models': ['Client', 'ContactClient'],
        'permissions': ['clients.read', 'clients.write', 'clients.delete'],
        'dependencies': ['Authentication', 'Core'],
        'api_prefix': 'clients'
    },
    
    'Produits': {
        'description': 'Gestion du catalogue produits',
        'models': ['Produit', 'Categorie', 'ImageProduit'],
        'permissions': ['products.read', 'products.write', 'products.delete'],
        'dependencies': ['Authentication', 'Core'],
        'api_prefix': 'products'
    },
    
    'Stock': {
        'description': 'Gestion des stocks et inventaires',
        'models': ['MouvementStock', 'Inventaire', 'LigneInventaire'],
        'permissions': ['stock.read', 'stock.write'],
        'dependencies': ['Authentication', 'Core', 'Produits'],
        'api_prefix': 'stock'
    },
    
    'Commandes': {
        'description': 'Gestion des commandes clients',
        'models': ['Commande', 'LigneCommande', 'StatutCommande'],
        'permissions': ['orders.read', 'orders.write', 'orders.delete'],
        'dependencies': ['Authentication', 'Core', 'Clients', 'Produits', 'Stock'],
        'api_prefix': 'orders'
    },
    
    'Facturation': {
        'description': 'Gestion des factures et devis',
        'models': ['Facture', 'LigneFacture', 'TemplateFacture'],
        'permissions': ['invoices.read', 'invoices.write', 'invoices.delete'],
        'dependencies': ['Authentication', 'Core', 'Clients', 'Produits', 'Commandes'],
        'api_prefix': 'factures'
    },
    
    'Fournisseurs': {
        'description': 'Gestion des fournisseurs et achats',
        'models': ['Fournisseur', 'CommandeFournisseur'],
        'permissions': ['suppliers.read', 'suppliers.write', 'suppliers.delete'],
        'dependencies': ['Authentication', 'Core', 'Produits'],
        'api_prefix': 'fournisseurs'
    },
    
    'Paiements': {
        'description': 'Gestion des paiements et trésorerie',
        'models': ['Paiement', 'CompteBancaire'],
        'permissions': ['payments.read', 'payments.write'],
        'dependencies': ['Authentication', 'Core', 'Clients', 'Fournisseurs', 'Facturation'],
        'api_prefix': 'payments'
    },
    
    'Comptabilite': {
        'description': 'Comptabilité et déclarations',
        'models': [],  # Utilise les données des autres modules
        'permissions': ['accounting.read', 'accounting.write'],
        'dependencies': ['Authentication', 'Core', 'Facturation', 'Paiements', 'Fournisseurs'],
        'api_prefix': 'comptabilite'
    },
    
    'Rapports': {
        'description': 'Rapports et analytics',
        'models': ['RapportTemplate', 'RapportGenere', 'TableauBord'],
        'permissions': ['reports.read', 'reports.write'],
        'dependencies': ['Authentication', 'Core'],  # Peut accéder à tous les modules
        'api_prefix': 'rapports'
    },
    
    'Ecommerce': {
        'description': 'Boutique en ligne',
        'models': [],  # Utilise Produits et Commandes
        'permissions': ['ecommerce.read', 'ecommerce.write'],
        'dependencies': ['Authentication', 'Core', 'Produits', 'Commandes', 'Clients'],
        'api_prefix': 'ecommerce'
    }
}

# === PERMISSIONS PAR RÔLE ===

ROLE_PERMISSIONS = {
    'NORMAL': [
        'ecommerce.read',
        'products.read',
        'orders.read'
    ],
    
    'COMPTABLE': [
        'clients.read', 'clients.write',
        'products.read', 'products.write',
        'orders.read', 'orders.write',
        'invoices.read', 'invoices.write',
        'stock.read', 'stock.write',
        'payments.read', 'payments.write',
        'suppliers.read', 'suppliers.write',
        'accounting.read', 'accounting.write',
        'reports.read', 'reports.write',
        'ecommerce.read', 'ecommerce.write'
    ],
    
    'ADMIN': [
        # Toutes les permissions COMPTABLE plus :
        'clients.delete',
        'products.delete',
        'orders.delete',
        'invoices.delete',
        'suppliers.delete',
        'admin.users',
        'admin.settings'
    ],
    
    'SUPERADMIN': [
        # Toutes les permissions plus :
        'admin.system',
        'admin.modules'
    ]
}

# === ROUTES API ===

API_ROUTES = {
    # Authentification
    'auth/login/': 'Authentication.views.LoginView',
    'auth/logout/': 'Authentication.views.LogoutView',
    'auth/register/': 'Authentication.views.RegisterView',
    'auth/profile/': 'Authentication.views.ProfileView',
    
    # Clients
    'clients/': 'Clients.views.ClientViewSet',
    'clients/stats/': 'Clients.views.ClientViewSet.stats',
    'clients/{id}/historique/': 'Clients.views.ClientViewSet.historique',
    
    # Produits
    'products/': 'Produits.views.ProduitViewSet',
    'products/stats/': 'Produits.views.ProduitViewSet.stats',
    'products/alertes-stock/': 'Produits.views.ProduitViewSet.alertes_stock',
    'products/categories/': 'Produits.views.CategorieViewSet',
    
    # Commandes
    'orders/': 'Commandes.views.CommandeViewSet',
    'orders/stats/': 'Commandes.views.CommandeViewSet.stats',
    'orders/{id}/changer-statut/': 'Commandes.views.CommandeViewSet.changer_statut',
    
    # Factures
    'factures/': 'Facturation.views.FactureViewSet',
    'factures/stats/': 'Facturation.views.FactureViewSet.stats',
    'factures/{id}/pdf/': 'Facturation.views.FactureViewSet.generate_pdf',
    
    # Stock
    'stock/mouvements/': 'Stock.views.MouvementStockViewSet',
    'stock/inventaires/': 'Stock.views.InventaireViewSet',
    'stock/stats/': 'Stock.views.MouvementStockViewSet.stats',
    
    # Paiements
    'payments/': 'Paiements.views.PaiementViewSet',
    'payments/stats/': 'Paiements.views.PaiementViewSet.stats',
    'payments/rapprochement/': 'Paiements.views.PaiementViewSet.rapprochement',
    
    # Fournisseurs
    'fournisseurs/': 'Fournisseurs.views.FournisseurViewSet',
    'fournisseurs/stats/': 'Fournisseurs.views.FournisseurViewSet.stats',
    'fournisseurs/commandes/': 'Fournisseurs.views.CommandeFournisseurViewSet',
    
    # Comptabilité
    'comptabilite/stats/': 'Comptabilite.views.ComptabiliteStatsView',
    'comptabilite/journal-ventes/': 'Comptabilite.views.JournalVentesView',
    'comptabilite/journal-achats/': 'Comptabilite.views.JournalAchatsView',
    'comptabilite/tva/{periode}/': 'Comptabilite.views.TVADeclarationView',
    
    # Rapports
    'rapports/templates/': 'Rapports.views.RapportTemplateViewSet',
    'rapports/ventes/': 'Rapports.views.RapportVentesView',
    'rapports/clients/': 'Rapports.views.RapportClientsView',
    'rapports/produits/': 'Rapports.views.RapportProduitsView',
    'rapports/financier/': 'Rapports.views.RapportFinancierView',
    
    # E-commerce
    'ecommerce/catalog/': 'Ecommerce.views.CatalogView',
    'ecommerce/cart/': 'Ecommerce.views.CartView',
    'ecommerce/stats/': 'Ecommerce.views.EcommerceStatsView',
}

# === CONFIGURATION DE LA BASE DE DONNÉES ===

DATABASE_RELATIONS = {
    'Facturation.Facture': {
        'client': 'Clients.Client',
        'commande': 'Commandes.Commande'
    },
    
    'Facturation.LigneFacture': {
        'produit': 'Produits.Produit'
    },
    
    'Commandes.Commande': {
        'client': 'Clients.Client'
    },
    
    'Commandes.LigneCommande': {
        'produit': 'Produits.Produit'
    },
    
    'Stock.MouvementStock': {
        'produit': 'Produits.Produit',
        'commande': 'Commandes.Commande',
        'facture': 'Facturation.Facture'
    },
    
    'Paiements.Paiement': {
        'client': 'Clients.Client',
        'fournisseur': 'Fournisseurs.Fournisseur',
        'facture': 'Facturation.Facture'
    }
}

# === FONCTIONS UTILITAIRES ===

def get_module_dependencies(module_name):
    """Retourne les dépendances d'un module"""
    return MODULES_CONFIG.get(module_name, {}).get('dependencies', [])

def get_module_permissions(module_name):
    """Retourne les permissions d'un module"""
    return MODULES_CONFIG.get(module_name, {}).get('permissions', [])

def get_user_accessible_modules(user_role):
    """Retourne les modules accessibles pour un rôle"""
    user_permissions = ROLE_PERMISSIONS.get(user_role, [])
    accessible_modules = []
    
    for module, config in MODULES_CONFIG.items():
        module_permissions = config.get('permissions', [])
        
        # Vérifier si l'utilisateur a au moins une permission du module
        if any(perm in user_permissions for perm in module_permissions):
            accessible_modules.append(module)
        
        # Modules publics (comme ecommerce pour NORMAL)
        if 'ecommerce.read' in user_permissions and module == 'Ecommerce':
            accessible_modules.append(module)
    
    return accessible_modules

def validate_module_structure():
    """Valide la cohérence de la structure des modules"""
    errors = []
    
    for module, config in MODULES_CONFIG.items():
        # Vérifier les dépendances
        for dependency in config.get('dependencies', []):
            if dependency not in MODULES_CONFIG:
                errors.append(f"Module {module} dépend de {dependency} qui n'existe pas")
    
    return errors
