import React, { useState } from "react";
import { toast } from "react-toastify";
import "./SimpleReports.css";

const SimpleReports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("month");
  const [selectedReport, setSelectedReport] = useState("sales");

  const reportTypes = [
    { id: "sales", name: "Vente<PERSON>", icon: "📊", description: "Rapport des ventes et chiffre d'affaires" },
    { id: "clients", name: "Clients", icon: "👥", description: "Analyse de la clientèle" },
    { id: "products", name: "Produits", icon: "📦", description: "Performance des produits" },
    { id: "payments", name: "Paiements", icon: "💳", description: "Suivi des paiements" },
    { id: "stock", name: "Stock", icon: "📋", description: "État des stocks" },
    { id: "financial", name: "Financier", icon: "💰", description: "Bilan financier" },
  ];

  const periods = [
    { id: "week", name: "<PERSON><PERSON> semaine" },
    { id: "month", name: "<PERSON> mois" },
    { id: "quarter", name: "Ce trimestre" },
    { id: "year", name: "Cette année" },
    { id: "custom", name: "Période personnalisée" },
  ];

  const mockData = {
    sales: {
      total: 45280.50,
      growth: 12.5,
      transactions: 156,
      avgOrder: 290.26,
    },
    clients: {
      total: 4,
      new: 1,
      active: 3,
      retention: 75,
    },
    products: {
      total: 4,
      topSeller: "Pack Solution Complète",
      lowStock: 1,
      revenue: 15420.50,
    },
  };

  const generateReport = () => {
    const reportName = reportTypes.find(r => r.id === selectedReport)?.name;
    const periodName = periods.find(p => p.id === selectedPeriod)?.name;
    
    toast.success(`Rapport ${reportName} généré pour ${periodName}`);
    
    // Simuler la génération d'un PDF
    setTimeout(() => {
      const link = document.createElement('a');
      link.href = '#';
      link.download = `rapport-${selectedReport}-${selectedPeriod}.pdf`;
      link.click();
      toast.info("Le téléchargement du rapport a commencé");
    }, 1000);
  };

  const exportData = (format: string) => {
    toast.success(`Export en format ${format.toUpperCase()} lancé`);
  };

  return (
    <div className="reports-container">
      <div className="reports-header">
        <h1>📊 Rapports & Analyses</h1>
        <p>Générez et consultez vos rapports d'activité</p>
      </div>

      <div className="reports-content">
        {/* Configuration du rapport */}
        <div className="report-config">
          <div className="config-section">
            <h3>📋 Type de Rapport</h3>
            <div className="report-types">
              {reportTypes.map(type => (
                <div
                  key={type.id}
                  className={`report-type ${selectedReport === type.id ? 'selected' : ''}`}
                  onClick={() => setSelectedReport(type.id)}>
                  <div className="type-icon">{type.icon}</div>
                  <div className="type-info">
                    <h4>{type.name}</h4>
                    <p>{type.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="config-section">
            <h3>📅 Période</h3>
            <div className="period-selector">
              {periods.map(period => (
                <button
                  key={period.id}
                  className={`period-btn ${selectedPeriod === period.id ? 'active' : ''}`}
                  onClick={() => setSelectedPeriod(period.id)}>
                  {period.name}
                </button>
              ))}
            </div>
          </div>

          <div className="config-section">
            <h3>⚙️ Actions</h3>
            <div className="report-actions">
              <button className="btn btn-primary" onClick={generateReport}>
                📄 Générer le rapport
              </button>
              <div className="export-options">
                <button className="btn btn-secondary" onClick={() => exportData('pdf')}>
                  📄 PDF
                </button>
                <button className="btn btn-secondary" onClick={() => exportData('excel')}>
                  📊 Excel
                </button>
                <button className="btn btn-secondary" onClick={() => exportData('csv')}>
                  📋 CSV
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Aperçu des données */}
        <div className="report-preview">
          <h3>👁️ Aperçu des Données</h3>
          
          {selectedReport === 'sales' && (
            <div className="preview-content">
              <div className="preview-cards">
                <div className="preview-card">
                  <div className="card-icon">💰</div>
                  <div className="card-content">
                    <h4>{mockData.sales.total.toLocaleString()} TND</h4>
                    <p>Chiffre d'affaires</p>
                    <span className="growth positive">+{mockData.sales.growth}%</span>
                  </div>
                </div>
                <div className="preview-card">
                  <div className="card-icon">🛒</div>
                  <div className="card-content">
                    <h4>{mockData.sales.transactions}</h4>
                    <p>Transactions</p>
                  </div>
                </div>
                <div className="preview-card">
                  <div className="card-icon">📊</div>
                  <div className="card-content">
                    <h4>{mockData.sales.avgOrder.toFixed(2)} TND</h4>
                    <p>Panier moyen</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedReport === 'clients' && (
            <div className="preview-content">
              <div className="preview-cards">
                <div className="preview-card">
                  <div className="card-icon">👥</div>
                  <div className="card-content">
                    <h4>{mockData.clients.total}</h4>
                    <p>Total clients</p>
                  </div>
                </div>
                <div className="preview-card">
                  <div className="card-icon">✨</div>
                  <div className="card-content">
                    <h4>{mockData.clients.new}</h4>
                    <p>Nouveaux clients</p>
                  </div>
                </div>
                <div className="preview-card">
                  <div className="card-icon">🎯</div>
                  <div className="card-content">
                    <h4>{mockData.clients.retention}%</h4>
                    <p>Taux de rétention</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedReport === 'products' && (
            <div className="preview-content">
              <div className="preview-cards">
                <div className="preview-card">
                  <div className="card-icon">📦</div>
                  <div className="card-content">
                    <h4>{mockData.products.total}</h4>
                    <p>Produits actifs</p>
                  </div>
                </div>
                <div className="preview-card">
                  <div className="card-icon">🏆</div>
                  <div className="card-content">
                    <h4>{mockData.products.topSeller}</h4>
                    <p>Meilleur vendeur</p>
                  </div>
                </div>
                <div className="preview-card">
                  <div className="card-icon">⚠️</div>
                  <div className="card-content">
                    <h4>{mockData.products.lowStock}</h4>
                    <p>Stock faible</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {!['sales', 'clients', 'products'].includes(selectedReport) && (
            <div className="preview-placeholder">
              <div className="placeholder-icon">📊</div>
              <h4>Rapport en cours de développement</h4>
              <p>Ce type de rapport sera bientôt disponible</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleReports;
