# Generated by Django 5.2.4 on 2025-08-17 22:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Facturation', '0002_paiementfacture_remove_relancefacture_created_by_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='configurationfacturation',
            name='compteur_bon_livraison',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='configurationfacturation',
            name='prefixe_bon_livraison',
            field=models.CharField(default='BL', max_length=10),
        ),
        migrations.AddField(
            model_name='facture',
            name='facture_origine',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='bons_livraison', to='Facturation.facture', verbose_name="Facture d'origine"),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='facture',
            name='type_document',
            field=models.Char<PERSON>ield(choices=[('DEVIS', 'Devis'), ('FACTURE', 'Facture'), ('AVOIR', 'Avoir'), ('BON_LIVRAISON', 'Bon de livraison')], default='FACTURE', max_length=20),
        ),
    ]
