from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import FournisseurViewSet, CommandeFournisseurViewSet

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'fournisseurs', FournisseurViewSet, basename='fournisseur')
router.register(r'commandes', CommandeFournisseurViewSet, basename='commande-fournisseur')

urlpatterns = [
    path('', include(router.urls)),
]
