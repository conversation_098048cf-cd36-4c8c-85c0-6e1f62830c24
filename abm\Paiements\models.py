from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

User = get_user_model()

class Paiement(models.Model):
    """Modèle Paiement pour la gestion des encaissements/décaissements"""

    TYPE_CHOICES = [
        ('ENCAISSEMENT', 'Encaissement'),
        ('DECAISSEMENT', 'Décaissement'),
        ('VIREMENT', 'Virement'),
        ('REMBOURSEMENT', 'Remboursement'),
    ]

    MODE_CHOICES = [
        ('ESPECES', 'Espèces'),
        ('CHEQUE', 'Chèque'),
        ('VIREMENT', 'Virement bancaire'),
        ('CARTE', 'Carte bancaire'),
        ('PRELEVEMENT', 'Prélèvement'),
        ('PAYPAL', 'PayPal'),
        ('STRIPE', 'Stripe'),
    ]

    STATUT_CHOICES = [
        ('EN_ATTENTE', 'En attente'),
        ('VALIDE', 'Validé'),
        ('REJETE', 'Rejeté'),
        ('REMBOURSE', 'Remboursé'),
        ('ANNULE', 'Annulé'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reference = models.CharField(max_length=50, unique=True, verbose_name="Référence")

    # Type et mode
    type_paiement = models.CharField(max_length=20, choices=TYPE_CHOICES)
    mode_paiement = models.CharField(max_length=20, choices=MODE_CHOICES)
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='EN_ATTENTE')

    # Montant
    montant = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    devise = models.CharField(max_length=3, default='EUR')

    # Dates
    date_paiement = models.DateTimeField(verbose_name="Date du paiement")
    date_valeur = models.DateField(null=True, blank=True, verbose_name="Date de valeur")
    date_echeance = models.DateField(null=True, blank=True, verbose_name="Date d'échéance")

    # Relations
    facture = models.ForeignKey(
        'Facturation.Facture',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='paiements_generaux'
    )
    client = models.ForeignKey(
        'Clients.Client',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='paiements'
    )
    fournisseur = models.ForeignKey(
        'Fournisseurs.Fournisseur',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='paiements'
    )

    # Informations bancaires
    numero_cheque = models.CharField(max_length=50, blank=True)
    banque_emettrice = models.CharField(max_length=200, blank=True)
    numero_transaction = models.CharField(max_length=100, blank=True)

    # Description
    description = models.TextField(blank=True)
    notes_internes = models.TextField(blank=True)

    # Rapprochement bancaire
    rapproche = models.BooleanField(default=False, verbose_name="Rapproché")
    date_rapprochement = models.DateTimeField(null=True, blank=True)

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='paiements_created')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='paiements_updated')

    class Meta:
        verbose_name = "Paiement"
        verbose_name_plural = "Paiements"
        ordering = ['-date_paiement']
        indexes = [
            models.Index(fields=['reference']),
            models.Index(fields=['statut']),
            models.Index(fields=['type_paiement']),
            models.Index(fields=['date_paiement']),
            models.Index(fields=['rapproche']),
        ]

    def __str__(self):
        return f"{self.reference} - {self.montant}€ ({self.get_type_paiement_display()})"

    @property
    def est_encaissement(self):
        return self.type_paiement == 'ENCAISSEMENT'

    @property
    def est_decaissement(self):
        return self.type_paiement == 'DECAISSEMENT'

    @property
    def peut_modifier(self):
        """Vérifie si le paiement peut être modifié"""
        return self.statut in ['EN_ATTENTE', 'REJETE']

    @property
    def peut_valider(self):
        """Vérifie si le paiement peut être validé"""
        return self.statut == 'EN_ATTENTE'


class CompteBancaire(models.Model):
    """Comptes bancaires de l'entreprise"""

    TYPE_CHOICES = [
        ('COURANT', 'Compte courant'),
        ('EPARGNE', 'Compte épargne'),
        ('PROFESSIONNEL', 'Compte professionnel'),
    ]

    nom = models.CharField(max_length=200, verbose_name="Nom du compte")
    banque = models.CharField(max_length=200, verbose_name="Nom de la banque")
    iban = models.CharField(max_length=34, unique=True)
    bic = models.CharField(max_length=11, blank=True)
    type_compte = models.CharField(max_length=20, choices=TYPE_CHOICES, default='COURANT')

    solde_initial = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    solde_actuel = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    actif = models.BooleanField(default=True)
    principal = models.BooleanField(default=False, verbose_name="Compte principal")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Compte bancaire"
        verbose_name_plural = "Comptes bancaires"
        ordering = ['-principal', 'nom']

    def __str__(self):
        return f"{self.nom} - {self.banque}"

    def calculer_solde(self):
        """Calcule le solde actuel basé sur les paiements"""
        encaissements = self.paiements.filter(
            type_paiement='ENCAISSEMENT',
            statut='VALIDE'
        ).aggregate(total=models.Sum('montant'))['total'] or Decimal('0.00')

        decaissements = self.paiements.filter(
            type_paiement='DECAISSEMENT',
            statut='VALIDE'
        ).aggregate(total=models.Sum('montant'))['total'] or Decimal('0.00')

        self.solde_actuel = self.solde_initial + encaissements - decaissements
        self.save()
        return self.solde_actuel
