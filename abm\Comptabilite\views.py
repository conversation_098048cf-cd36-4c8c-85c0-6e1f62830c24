from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from django.db.models import Sum, Count, Q, F
from django.utils import timezone
from datetime import datetime, timedelta

from Authentication.permissions import HasModulePermission

class ComptabiliteStatsView(APIView):
    """Vue d'ensemble comptable"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'comptabilite'

    def get(self, request):
        from Facturation.models import Facture
        from Paiements.models import Paiement

        # Période par défaut : mois en cours
        now = timezone.now()
        debut_mois = now.replace(day=1).date()
        fin_mois = now.date()

        # CA facturé du mois
        ca_facture_mois = Facture.objects.filter(
            date_facture__gte=debut_mois,
            date_facture__lte=fin_mois,
            statut__in=['ENVOYEE', 'PAYEE']
        ).aggregate(total=Sum('montant_total'))['total'] or 0

        # CA encaissé du mois
        ca_encaisse_mois = Paiement.objects.filter(
            date_paiement__gte=debut_mois,
            date_paiement__lte=fin_mois,
            type_paiement='ENCAISSEMENT',
            statut='VALIDE'
        ).aggregate(total=Sum('montant'))['total'] or 0

        # Factures en attente de paiement
        factures_impayees = Facture.objects.filter(
            statut='ENVOYEE'
        ).aggregate(
            count=Count('id'),
            montant=Sum('montant_total')
        )

        # TVA collectée du mois
        tva_collectee = Facture.objects.filter(
            date_facture__gte=debut_mois,
            date_facture__lte=fin_mois,
            statut__in=['ENVOYEE', 'PAYEE']
        ).aggregate(total=Sum('montant_tva'))['total'] or 0

        # Évolution mensuelle du CA
        evolution_ca = []
        for i in range(12):
            date_debut = now.replace(day=1) - timedelta(days=30*i)
            date_fin = date_debut.replace(day=28) + timedelta(days=4)

            ca = Facture.objects.filter(
                date_facture__gte=date_debut,
                date_facture__lt=date_fin,
                statut__in=['ENVOYEE', 'PAYEE']
            ).aggregate(total=Sum('montant_total'))['total'] or 0

            evolution_ca.append({
                'mois': date_debut.strftime('%Y-%m'),
                'ca': float(ca)
            })

        data = {
            'periode_actuelle': {
                'debut': debut_mois.isoformat(),
                'fin': fin_mois.isoformat()
            },
            'ca_facture_mois': float(ca_facture_mois),
            'ca_encaisse_mois': float(ca_encaisse_mois),
            'tva_collectee_mois': float(tva_collectee),
            'factures_impayees': {
                'count': factures_impayees['count'] or 0,
                'montant': float(factures_impayees['montant'] or 0)
            },
            'taux_recouvrement': (ca_encaisse_mois / ca_facture_mois * 100) if ca_facture_mois > 0 else 0,
            'evolution_ca_mensuelle': list(reversed(evolution_ca))
        }

        return Response(data)


class JournalVentesView(APIView):
    """Journal des ventes"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'comptabilite'

    def get(self, request):
        from Facturation.models import Facture, LigneFacture

        # Paramètres
        date_debut = request.query_params.get('date_debut')
        date_fin = request.query_params.get('date_fin')

        if not date_debut or not date_fin:
            now = timezone.now()
            date_debut = now.replace(day=1).date()
            date_fin = now.date()
        else:
            date_debut = datetime.strptime(date_debut, '%Y-%m-%d').date()
            date_fin = datetime.strptime(date_fin, '%Y-%m-%d').date()

        # Factures de la période
        factures = Facture.objects.filter(
            date_facture__gte=date_debut,
            date_facture__lte=date_fin,
            statut__in=['ENVOYEE', 'PAYEE']
        ).order_by('date_facture')

        # Détail des ventes
        journal = []
        for facture in factures:
            journal.append({
                'date': facture.date_facture,
                'numero': facture.numero,
                'client': facture.client.nom_complet,
                'montant_ht': float(facture.montant_ht),
                'montant_tva': float(facture.montant_tva),
                'montant_ttc': float(facture.montant_total),
                'statut': facture.statut
            })

        # Totaux
        totaux = factures.aggregate(
            total_ht=Sum('montant_ht'),
            total_tva=Sum('montant_tva'),
            total_ttc=Sum('montant_total')
        )

        data = {
            'periode': {
                'debut': date_debut.isoformat(),
                'fin': date_fin.isoformat()
            },
            'journal': journal,
            'totaux': {
                'total_ht': float(totaux['total_ht'] or 0),
                'total_tva': float(totaux['total_tva'] or 0),
                'total_ttc': float(totaux['total_ttc'] or 0),
                'nb_factures': len(journal)
            }
        }

        return Response(data)


class JournalAchatsView(APIView):
    """Journal des achats"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'comptabilite'

    def get(self, request):
        from Fournisseurs.models import CommandeFournisseur

        # Paramètres
        date_debut = request.query_params.get('date_debut')
        date_fin = request.query_params.get('date_fin')

        if not date_debut or not date_fin:
            now = timezone.now()
            date_debut = now.replace(day=1).date()
            date_fin = now.date()
        else:
            date_debut = datetime.strptime(date_debut, '%Y-%m-%d').date()
            date_fin = datetime.strptime(date_fin, '%Y-%m-%d').date()

        # Commandes fournisseurs de la période
        commandes = CommandeFournisseur.objects.filter(
            date_commande__gte=date_debut,
            date_commande__lte=date_fin,
            statut__in=['CONFIRMEE', 'RECUE']
        ).order_by('date_commande')

        # Détail des achats
        journal = []
        for commande in commandes:
            journal.append({
                'date': commande.date_commande,
                'numero': commande.numero,
                'fournisseur': commande.fournisseur.nom,
                'montant_ht': float(commande.sous_total),
                'montant_tva': float(commande.montant_tva),
                'montant_ttc': float(commande.montant_total),
                'statut': commande.statut
            })

        # Totaux
        totaux = commandes.aggregate(
            total_ht=Sum('sous_total'),
            total_tva=Sum('montant_tva'),
            total_ttc=Sum('montant_total')
        )

        data = {
            'periode': {
                'debut': date_debut.isoformat(),
                'fin': date_fin.isoformat()
            },
            'journal': journal,
            'totaux': {
                'total_ht': float(totaux['total_ht'] or 0),
                'total_tva': float(totaux['total_tva'] or 0),
                'total_ttc': float(totaux['total_ttc'] or 0),
                'nb_commandes': len(journal)
            }
        }

        return Response(data)


class TVADeclarationView(APIView):
    """Déclaration de TVA"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'comptabilite'

    def get(self, request):
        from Facturation.models import Facture
        from Fournisseurs.models import CommandeFournisseur

        # Paramètres (trimestre ou mois)
        periode = request.query_params.get('periode')  # Format: 2024-Q1 ou 2024-01

        if not periode:
            return Response(
                {'error': 'Période requise (format: 2024-Q1 ou 2024-01)'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Calculer les dates selon la période
        if 'Q' in periode:
            # Période trimestrielle
            year, quarter = periode.split('-Q')
            year = int(year)
            quarter = int(quarter)

            if quarter == 1:
                date_debut = datetime(year, 1, 1).date()
                date_fin = datetime(year, 3, 31).date()
            elif quarter == 2:
                date_debut = datetime(year, 4, 1).date()
                date_fin = datetime(year, 6, 30).date()
            elif quarter == 3:
                date_debut = datetime(year, 7, 1).date()
                date_fin = datetime(year, 9, 30).date()
            else:
                date_debut = datetime(year, 10, 1).date()
                date_fin = datetime(year, 12, 31).date()
        else:
            # Période mensuelle
            year, month = periode.split('-')
            year, month = int(year), int(month)
            date_debut = datetime(year, month, 1).date()

            if month == 12:
                date_fin = datetime(year + 1, 1, 1).date() - timedelta(days=1)
            else:
                date_fin = datetime(year, month + 1, 1).date() - timedelta(days=1)

        # TVA collectée (sur les ventes)
        tva_collectee = Facture.objects.filter(
            date_facture__gte=date_debut,
            date_facture__lte=date_fin,
            statut__in=['ENVOYEE', 'PAYEE']
        ).aggregate(total=Sum('montant_tva'))['total'] or 0

        # TVA déductible (sur les achats)
        tva_deductible = CommandeFournisseur.objects.filter(
            date_commande__gte=date_debut,
            date_commande__lte=date_fin,
            statut__in=['CONFIRMEE', 'RECUE']
        ).aggregate(total=Sum('montant_tva'))['total'] or 0

        # TVA à payer
        tva_a_payer = tva_collectee - tva_deductible

        data = {
            'periode': periode,
            'date_debut': date_debut.isoformat(),
            'date_fin': date_fin.isoformat(),
            'tva_collectee': float(tva_collectee),
            'tva_deductible': float(tva_deductible),
            'tva_a_payer': float(tva_a_payer),
            'date_generation': timezone.now().isoformat()
        }

        return Response(data)
