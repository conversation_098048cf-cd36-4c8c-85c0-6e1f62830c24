import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useNotify } from "../Common/NotificationSystem";

interface FournisseurFormData {
  nom: string;
  email: string;
  telephone: string;
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
  matricule_fiscal: string;
  conditions_paiement: string;
  delai_paiement: number;
  remise_habituelle: number;
  contact_principal: string;
  secteur_activite: string;
  notes: string;
}

const FournisseurForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const notify = useNotify();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const [formData, setFormData] = useState<FournisseurFormData>({
    nom: "",
    email: "",
    telephone: "",
    adresse: "",
    ville: "",
    code_postal: "",
    pays: "Tunisie",
    matricule_fiscal: "",
    conditions_paiement: "Paiement à 30 jours",
    delai_paiement: 30,
    remise_habituelle: 0,
    contact_principal: "",
    secteur_activite: "",
    notes: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isEdit && id) {
      loadFournisseur(id);
    }
  }, [id, isEdit]);

  const loadFournisseur = async (fournisseurId: string) => {
    try {
      setLoading(true);
      // Simulation de chargement d'un fournisseur existant
      const mockFournisseur = {
        nom: "FOURNISSEUR EXEMPLE SARL",
        email: "<EMAIL>",
        telephone: "+216 71 456 789",
        adresse: "123 Rue du Commerce",
        ville: "Tunis",
        code_postal: "1000",
        pays: "Tunisie",
        matricule_fiscal: "MF123456789",
        conditions_paiement: "Paiement à 45 jours",
        delai_paiement: 45,
        remise_habituelle: 5,
        contact_principal: "Ahmed Ben Ali",
        secteur_activite: "Commerce de gros",
        notes: "Fournisseur fiable avec de bons délais de livraison",
      };

      setFormData(mockFournisseur);
    } catch (error: any) {
      notify.error("Erreur lors du chargement du fournisseur");
      navigate("/fournisseurs");
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.nom.trim()) {
      newErrors.nom = "Le nom du fournisseur est requis";
    }

    if (!formData.email.trim()) {
      newErrors.email = "L'email est requis";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Format d'email invalide";
    }

    if (!formData.telephone.trim()) {
      newErrors.telephone = "Le téléphone est requis";
    }

    if (!formData.adresse.trim()) {
      newErrors.adresse = "L'adresse est requise";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notify.error("Veuillez corriger les erreurs du formulaire");
      return;
    }

    try {
      setSaving(true);

      // Simulation de sauvegarde
      console.log("Données fournisseur à sauvegarder:", formData);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      notify.success(
        isEdit
          ? "Fournisseur modifié avec succès"
          : "Fournisseur créé avec succès"
      );
      navigate("/fournisseurs");
    } catch (error: any) {
      notify.error("Erreur lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (
    field: keyof FournisseurFormData,
    value: string | number
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Effacer l'erreur si elle existe
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  if (loading) {
    return (
      <div className="fournisseur-form-loading">
        <div className="loading-spinner"></div>
        <p>Chargement...</p>
      </div>
    );
  }

  return (
    <div className="fournisseur-form">
      <div className="form-header">
        <div className="header-content">
          <div className="header-info">
            <h1>
              {isEdit ? "✏️ Modifier le fournisseur" : "➕ Nouveau fournisseur"}
            </h1>
            <p>
              {isEdit
                ? "Modifiez les informations du fournisseur"
                : "Ajoutez un nouveau fournisseur à votre base de données"}
            </p>
          </div>
          <div className="header-actions">
            <button
              type="button"
              className="btn-secondary"
              onClick={() => navigate("/fournisseurs")}>
              ← Retour
            </button>
          </div>
        </div>
      </div>

      <form
        onSubmit={handleSubmit}
        className="fournisseur-form-content">
        {/* Informations générales */}
        <div className="form-section">
          <h3>📋 Informations générales</h3>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="nom">Nom du fournisseur *</label>
              <input
                type="text"
                id="nom"
                value={formData.nom}
                onChange={(e) => handleInputChange("nom", e.target.value)}
                className={errors.nom ? "error" : ""}
                placeholder="Nom de l'entreprise fournisseur"
              />
              {errors.nom && (
                <span className="error-message">{errors.nom}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="secteur_activite">Secteur d'activité</label>
              <select
                id="secteur_activite"
                value={formData.secteur_activite}
                onChange={(e) =>
                  handleInputChange("secteur_activite", e.target.value)
                }>
                <option value="">Sélectionner un secteur</option>
                <option value="Commerce de gros">Commerce de gros</option>
                <option value="Commerce de détail">Commerce de détail</option>
                <option value="Services">Services</option>
                <option value="Industrie">Industrie</option>
                <option value="Technologie">Technologie</option>
                <option value="Alimentation">Alimentation</option>
                <option value="Textile">Textile</option>
                <option value="Construction">Construction</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="matricule_fiscal">Matricule fiscal</label>
              <input
                type="text"
                id="matricule_fiscal"
                value={formData.matricule_fiscal}
                onChange={(e) =>
                  handleInputChange("matricule_fiscal", e.target.value)
                }
                placeholder="MF123456789"
              />
            </div>

            <div className="form-group">
              <label htmlFor="contact_principal">Contact principal</label>
              <input
                type="text"
                id="contact_principal"
                value={formData.contact_principal}
                onChange={(e) =>
                  handleInputChange("contact_principal", e.target.value)
                }
                placeholder="Nom du contact principal"
              />
            </div>
          </div>
        </div>

        {/* Coordonnées */}
        <div className="form-section">
          <h3>📞 Coordonnées</h3>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={errors.email ? "error" : ""}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <span className="error-message">{errors.email}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="telephone">Téléphone *</label>
              <input
                type="tel"
                id="telephone"
                value={formData.telephone}
                onChange={(e) => handleInputChange("telephone", e.target.value)}
                className={errors.telephone ? "error" : ""}
                placeholder="+216 71 123 456"
              />
              {errors.telephone && (
                <span className="error-message">{errors.telephone}</span>
              )}
            </div>

            <div className="form-group full-width">
              <label htmlFor="adresse">Adresse *</label>
              <input
                type="text"
                id="adresse"
                value={formData.adresse}
                onChange={(e) => handleInputChange("adresse", e.target.value)}
                className={errors.adresse ? "error" : ""}
                placeholder="Adresse complète"
              />
              {errors.adresse && (
                <span className="error-message">{errors.adresse}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="ville">Ville</label>
              <input
                type="text"
                id="ville"
                value={formData.ville}
                onChange={(e) => handleInputChange("ville", e.target.value)}
                placeholder="Ville"
              />
            </div>

            <div className="form-group">
              <label htmlFor="code_postal">Code postal</label>
              <input
                type="text"
                id="code_postal"
                value={formData.code_postal}
                onChange={(e) =>
                  handleInputChange("code_postal", e.target.value)
                }
                placeholder="1000"
              />
            </div>

            <div className="form-group">
              <label htmlFor="pays">Pays</label>
              <select
                id="pays"
                value={formData.pays}
                onChange={(e) => handleInputChange("pays", e.target.value)}>
                <option value="Tunisie">Tunisie</option>
                <option value="Algérie">Algérie</option>
                <option value="Maroc">Maroc</option>
                <option value="France">France</option>
                <option value="Autre">Autre</option>
              </select>
            </div>
          </div>
        </div>

        {/* Conditions commerciales */}
        <div className="form-section">
          <h3>💼 Conditions commerciales</h3>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="conditions_paiement">
                Conditions de paiement
              </label>
              <select
                id="conditions_paiement"
                value={formData.conditions_paiement}
                onChange={(e) =>
                  handleInputChange("conditions_paiement", e.target.value)
                }>
                <option value="Paiement comptant">Paiement comptant</option>
                <option value="Paiement à 15 jours">Paiement à 15 jours</option>
                <option value="Paiement à 30 jours">Paiement à 30 jours</option>
                <option value="Paiement à 45 jours">Paiement à 45 jours</option>
                <option value="Paiement à 60 jours">Paiement à 60 jours</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="delai_paiement">Délai de paiement (jours)</label>
              <input
                type="number"
                id="delai_paiement"
                value={formData.delai_paiement}
                onChange={(e) =>
                  handleInputChange(
                    "delai_paiement",
                    parseInt(e.target.value) || 0
                  )
                }
                min="0"
                max="365"
              />
            </div>

            <div className="form-group">
              <label htmlFor="remise_habituelle">Remise habituelle (%)</label>
              <input
                type="number"
                id="remise_habituelle"
                value={formData.remise_habituelle}
                onChange={(e) =>
                  handleInputChange(
                    "remise_habituelle",
                    parseFloat(e.target.value) || 0
                  )
                }
                min="0"
                max="100"
                step="0.1"
              />
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="form-section">
          <h3>📝 Notes et commentaires</h3>
          <div className="form-group">
            <label htmlFor="notes">Notes internes</label>
            <textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={4}
              placeholder="Notes internes sur ce fournisseur..."
            />
          </div>
        </div>

        {/* Actions */}
        <div className="form-actions">
          <button
            type="button"
            className="btn-secondary"
            onClick={() => navigate("/fournisseurs")}
            disabled={saving}>
            Annuler
          </button>
          <button
            type="submit"
            className="btn-primary"
            disabled={saving}>
            {saving ? "⏳ Sauvegarde..." : isEdit ? "💾 Modifier" : "➕ Créer"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default FournisseurForm;
