/* Système de notifications */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  pointer-events: none;
}

.notification {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transform: translateX(100%);
  animation: slideInRight 0.3s ease forwards;
  pointer-events: auto;
  position: relative;
  border-left: 4px solid;
}

.notification-success {
  border-left-color: #2ed573;
}

.notification-error {
  border-left-color: #ff4757;
}

.notification-warning {
  border-left-color: #ffa502;
}

.notification-info {
  border-left-color: #3742fa;
}

.notification-content {
  padding: 16px 20px;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

.notification-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-title {
  flex: 1;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: #7f8c8d;
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background: #f1f2f6;
  color: #2c3e50;
}

.notification-message {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-left: 32px;
  margin-bottom: 12px;
}

.notification-actions {
  margin-left: 32px;
  margin-top: 12px;
}

.notification-action-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-action-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background: #667eea;
  transform-origin: left;
  animation: progressBar linear forwards;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes progressBar {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* Variantes de couleur pour les barres de progression */
.notification-success .notification-progress {
  background: #2ed573;
}

.notification-error .notification-progress {
  background: #ff4757;
}

.notification-warning .notification-progress {
  background: #ffa502;
}

.notification-info .notification-progress {
  background: #3742fa;
}

/* Animation de sortie */
.notification.removing {
  animation: slideOutRight 0.3s ease forwards;
}

/* Responsive */
@media (max-width: 768px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification {
    transform: translateY(-100%);
    animation: slideInDown 0.3s ease forwards;
  }
  
  .notification.removing {
    animation: slideOutUp 0.3s ease forwards;
  }
  
  .notification-content {
    padding: 14px 16px;
  }
  
  .notification-header {
    gap: 10px;
  }
  
  .notification-message {
    margin-left: 28px;
  }
  
  .notification-actions {
    margin-left: 28px;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

/* États de hover pour toute la notification */
.notification:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.notification:hover .notification-progress {
  animation-play-state: paused;
}

/* Styles pour les notifications empilées */
.notification:nth-child(n+4) {
  opacity: 0.8;
  transform: scale(0.95) translateX(0);
}

.notification:nth-child(n+5) {
  opacity: 0.6;
  transform: scale(0.9) translateX(0);
}

.notification:nth-child(n+6) {
  display: none;
}

/* Indicateur de nombre de notifications */
.notification-container::before {
  content: attr(data-count) " notifications";
  position: absolute;
  top: -30px;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-container[data-count]:hover::before {
  opacity: 1;
}

/* Styles pour les notifications avec icônes personnalisées */
.notification-icon img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

/* Animation de pulsation pour les notifications importantes */
.notification.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
  50% {
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  }
  100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
}

/* Styles pour les notifications persistantes */
.notification.persistent .notification-progress {
  display: none;
}

.notification.persistent .notification-close {
  background: #f1f2f6;
}

/* Amélioration de l'accessibilité */
.notification {
  role: alert;
  aria-live: polite;
}

.notification.notification-error {
  aria-live: assertive;
}

.notification-close {
  aria-label: "Fermer la notification";
}

.notification-action-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Thème sombre */
@media (prefers-color-scheme: dark) {
  .notification {
    background: #2c3e50;
    color: white;
  }
  
  .notification-title {
    color: white;
  }
  
  .notification-message {
    color: #bdc3c7;
  }
  
  .notification-close {
    color: #bdc3c7;
  }
  
  .notification-close:hover {
    background: #34495e;
    color: white;
  }
}

/* Animation d'entrée en lot */
.notification:nth-child(1) {
  animation-delay: 0ms;
}

.notification:nth-child(2) {
  animation-delay: 100ms;
}

.notification:nth-child(3) {
  animation-delay: 200ms;
}

/* Styles pour les notifications avec contenu riche */
.notification-rich {
  max-width: 450px;
}

.notification-rich .notification-content {
  padding: 20px;
}

.notification-rich .notification-message {
  margin-left: 0;
  margin-top: 12px;
}

.notification-rich .notification-actions {
  margin-left: 0;
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.notification-rich .notification-action-btn.secondary {
  background: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.notification-rich .notification-action-btn.secondary:hover {
  background: #667eea;
  color: white;
}
