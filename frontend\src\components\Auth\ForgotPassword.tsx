import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { toast } from "react-toastify";
import { AuthService } from "../../services/apiService";
import "./Auth.css";

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [code, setCode] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [step, setStep] = useState<"email" | "code" | "password">("email");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSendCode = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      toast.error("Veuillez saisir votre adresse email");
      return;
    }

    setIsLoading(true);

    try {
      const response = await AuthService.forgotPassword(email);

      if (response.data && !response.error) {
        setIsCodeSent(true);
        setStep("code");
        toast.success("Un code de vérification a été envoyé à votre email");
      } else {
        toast.error(response.error || "Erreur lors de l'envoi du code");
      }
    } catch (error: any) {
      toast.error(error.message || "Erreur lors de l'envoi du code");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!code || code.length !== 6) {
      toast.error(
        "Veuillez saisir un code de vérification valide (6 chiffres)"
      );
      return;
    }

    setIsLoading(true);

    try {
      const response = await AuthService.verifyResetCode(email, code);

      if (response.data && !response.error) {
        setStep("password");
        toast.success("Code vérifié avec succès");
      } else {
        toast.error(response.error || "Code de vérification invalide");
      }
    } catch (error: any) {
      toast.error(error.message || "Code de vérification invalide");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newPassword || !confirmPassword) {
      toast.error("Veuillez remplir tous les champs");
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error("Les mots de passe ne correspondent pas");
      return;
    }

    if (newPassword.length < 8) {
      toast.error("Le mot de passe doit contenir au moins 8 caractères");
      return;
    }

    setIsLoading(true);

    try {
      const response = await AuthService.resetPassword({
        email,
        code,
        new_password: newPassword,
        confirm_password: confirmPassword,
      });

      if (response.data && !response.error) {
        toast.success("Mot de passe réinitialisé avec succès");
        // Rediriger vers la page de connexion après un délai
        setTimeout(() => {
          window.location.href = "/login";
        }, 2000);
      } else {
        toast.error(response.error || "Erreur lors de la réinitialisation");
      }
    } catch (error: any) {
      toast.error(error.message || "Erreur lors de la réinitialisation");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsLoading(true);

    try {
      const response = await AuthService.forgotPassword(email);

      if (response.data && !response.error) {
        toast.success("Nouveau code envoyé à votre email");
      } else {
        toast.error(response.error || "Erreur lors de l'envoi du nouveau code");
      }
    } catch (error: any) {
      toast.error("Erreur lors de l'envoi du nouveau code");
    } finally {
      setIsLoading(false);
    }
  };

  const renderEmailStep = () => (
    <form
      onSubmit={handleSendCode}
      className="auth-form">
      <div className="auth-header">
        <h2>Mot de passe oublié</h2>
        <p>
          Saisissez votre adresse email pour recevoir un code de récupération
        </p>
      </div>

      <div className="form-group">
        <label htmlFor="email">Adresse email</label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
          required
          disabled={isLoading}
        />
      </div>

      <button
        type="submit"
        className="auth-button"
        disabled={isLoading}>
        {isLoading ? "Envoi en cours..." : "Envoyer le code"}
      </button>

      <div className="auth-links">
        <Link to="/login">Retour à la connexion</Link>
      </div>
    </form>
  );

  const renderCodeStep = () => (
    <form
      onSubmit={handleVerifyCode}
      className="auth-form">
      <div className="auth-header">
        <h2>Code de vérification</h2>
        <p>
          Saisissez le code à 6 chiffres envoyé à <strong>{email}</strong>
        </p>
      </div>

      <div className="form-group">
        <label htmlFor="code">Code de vérification</label>
        <input
          type="text"
          id="code"
          value={code}
          onChange={(e) =>
            setCode(e.target.value.replace(/\D/g, "").slice(0, 6))
          }
          placeholder="123456"
          maxLength={6}
          required
          disabled={isLoading}
          className="code-input"
        />
        <small>Le code expire dans 15 minutes</small>
      </div>

      <button
        type="submit"
        className="auth-button"
        disabled={isLoading || code.length !== 6}>
        {isLoading ? "Vérification..." : "Vérifier le code"}
      </button>

      <div className="auth-links">
        <button
          type="button"
          onClick={handleResendCode}
          className="link-button"
          disabled={isLoading}>
          Renvoyer le code
        </button>
        <Link to="/login">Retour à la connexion</Link>
      </div>
    </form>
  );

  const renderPasswordStep = () => (
    <form
      onSubmit={handleResetPassword}
      className="auth-form">
      <div className="auth-header">
        <h2>Nouveau mot de passe</h2>
        <p>Choisissez un nouveau mot de passe sécurisé</p>
      </div>

      <div className="form-group">
        <label htmlFor="newPassword">Nouveau mot de passe</label>
        <div className="password-input-container">
          <input
            type={showPassword ? "text" : "password"}
            id="newPassword"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Nouveau mot de passe"
            required
            disabled={isLoading}
            minLength={8}
          />
          <button
            type="button"
            className="password-toggle"
            onClick={() => setShowPassword(!showPassword)}>
            {showPassword ? "👁️" : "👁️‍🗨️"}
          </button>
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="confirmPassword">Confirmer le mot de passe</label>
        <div className="password-input-container">
          <input
            type={showConfirmPassword ? "text" : "password"}
            id="confirmPassword"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirmer le mot de passe"
            required
            disabled={isLoading}
            minLength={8}
          />
          <button
            type="button"
            className="password-toggle"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}>
            {showConfirmPassword ? "👁️" : "👁️‍🗨️"}
          </button>
        </div>
      </div>

      <button
        type="submit"
        className="auth-button"
        disabled={isLoading}>
        {isLoading ? "Réinitialisation..." : "Réinitialiser le mot de passe"}
      </button>

      <div className="auth-links">
        <Link to="/login">Retour à la connexion</Link>
      </div>
    </form>
  );

  return (
    <div className="auth-container">
      <div className="auth-card">
        {step === "email" && renderEmailStep()}
        {step === "code" && renderCodeStep()}
        {step === "password" && renderPasswordStep()}
      </div>
    </div>
  );
};

export default ForgotPassword;
