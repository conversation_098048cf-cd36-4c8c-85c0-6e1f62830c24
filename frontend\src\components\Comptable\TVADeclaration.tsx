/**
 * Déclaration TVA
 */

import React, { useState, useEffect } from 'react';
import { useNotify } from '../Common/NotificationSystem';

const TVADeclaration: React.FC = () => {
  const notify = useNotify();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setTimeout(() => setLoading(false), 500);
  }, []);

  if (loading) {
    return (
      <div className="journal-loading">
        <div className="loading-spinner"></div>
        <p>Chargement de la déclaration TVA...</p>
      </div>
    );
  }

  return (
    <div className="tva-declaration">
      <div className="coming-soon">
        <div className="coming-soon-icon">🧾</div>
        <h3>Déclaration TVA</h3>
        <p>Module en cours de développement</p>
        <p>Fonctionnalités prévues :</p>
        <ul>
          <li>📊 Calcul automatique TVA</li>
          <li>📋 Formulaire CA3</li>
          <li>📤 Export DGFiP</li>
          <li>📅 Calendrier déclarations</li>
        </ul>
      </div>
    </div>
  );
};

export default TVADeclaration;
