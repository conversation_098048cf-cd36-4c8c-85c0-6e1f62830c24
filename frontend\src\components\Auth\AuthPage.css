.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.auth-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}

/* === EFFETS DE PARTICULES === */

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat linear infinite;
}

.particle:nth-child(odd) {
  background: rgba(102, 126, 234, 0.4);
}

.particle:nth-child(3n) {
  background: rgba(118, 75, 162, 0.4);
  width: 6px;
  height: 6px;
}

.particle:nth-child(5n) {
  background: rgba(240, 147, 251, 0.4);
  width: 3px;
  height: 3px;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.auth-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.25), 0 16px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  padding: 0;
  width: 100%;
  max-width: 520px;
  overflow: hidden;
  position: relative;
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.auth-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(
    90deg,
    #667eea,
    #764ba2,
    #f093fb,
    #f5576c,
    #4facfe,
    #00f2fe
  );
  background-size: 300% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.auth-header {
  padding: 50px 40px 30px 40px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.auth-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 25px;
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.logo-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.logo h1 {
  margin: 0;
  font-size: 2.2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1e3c72, #2a5298, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-subtitle {
  margin: 15px 0 25px 0;
  color: #64748b;
  font-size: 1.05rem;
  font-weight: 500;
  text-align: center;
  opacity: 0.8;
  font-style: italic;
  animation: fadeIn 0.8s ease-out 0.5s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 0.8;
    transform: translateY(0);
  }
}

.auth-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 6px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.tab-button {
  flex: 1;
  padding: 16px 24px;
  border: none;
  background: transparent;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  color: #64748b;
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.6s;
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 12px rgba(118, 75, 162, 0.3);
}

.tab-button.active::before {
  display: none;
}

.tab-button:hover:not(.active) {
  background: rgba(102, 126, 234, 0.1);
  color: #4c51bf;
  transform: translateY(-1px);
}

.auth-content {
  padding: 40px 45px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
}

.auth-footer {
  padding: 25px 40px 35px 40px;
  text-align: center;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.auth-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #667eea, transparent);
}

.auth-footer p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
}

.link-button {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 700;
  cursor: pointer;
  margin-left: 10px;
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.link-button::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.link-button:hover {
  color: #5a67d8;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.link-button:hover::after {
  width: 100%;
}

.demo-info {
  position: absolute;
  top: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  max-width: 320px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideInRight 0.8s ease-out 0.3s both;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.demo-info h3 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-accounts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.demo-account {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.demo-account::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.demo-account:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.demo-account:hover::before {
  width: 100%;
  opacity: 0.1;
}

.demo-account strong {
  color: #1e293b;
  font-size: 0.95rem;
  font-weight: 700;
}

.demo-account span {
  color: #667eea;
  font-size: 0.85rem;
  font-family: "Courier New", monospace;
  font-weight: 600;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.auth-loading {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.auth-loading p {
  font-size: 1.1rem;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* === RESPONSIVE DESIGN === */

@media (max-width: 1024px) {
  .demo-info {
    position: static;
    margin-top: 30px;
    max-width: 100%;
    animation: slideInUp 0.8s ease-out 0.3s both;
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@media (max-width: 768px) {
  .auth-page {
    padding: 15px;
  }

  .auth-container {
    max-width: 100%;
    border-radius: 20px;
  }

  .auth-header {
    padding: 40px 30px 25px 30px;
  }

  .auth-content {
    padding: 30px 35px;
  }

  .auth-footer {
    padding: 20px 30px 30px 30px;
  }

  .logo h1 {
    font-size: 1.9rem;
  }

  .logo-icon {
    font-size: 2.7rem;
  }

  .tab-button {
    padding: 14px 20px;
    font-size: 0.95rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .auth-page {
    padding: 10px;
  }

  .auth-header {
    padding: 35px 25px 20px 25px;
  }

  .auth-content {
    padding: 25px 30px;
  }

  .auth-footer {
    padding: 18px 25px 25px 25px;
  }

  .logo {
    flex-direction: column;
    gap: 10px;
  }

  .logo h1 {
    font-size: 1.6rem;
  }

  .logo-icon {
    font-size: 2.5rem;
  }

  .tab-button {
    padding: 12px 16px;
    font-size: 0.9rem;
  }

  .auth-subtitle {
    font-size: 1rem;
  }

  .form-group input {
    padding: 14px 18px;
    font-size: 0.95rem;
  }

  .auth-button {
    padding: 16px 20px;
    font-size: 1rem;
  }

  .demo-accounts {
    gap: 10px;
  }

  .demo-account {
    padding: 14px;
  }

  .particles {
    display: none; /* Masquer les particules sur mobile pour les performances */
  }
}
