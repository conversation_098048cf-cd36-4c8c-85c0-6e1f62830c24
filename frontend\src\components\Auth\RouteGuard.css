/* Styles pour RouteGuard */
.route-guard-loading,
.route-guard-unauthorized,
.route-guard-forbidden {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
}

.loading-container,
.unauthorized-container,
.forbidden-container {
  text-align: center;
  background: white;
  padding: 60px 40px;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container h3 {
  margin: 0 0 15px 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
}

.loading-container p {
  margin: 0;
  color: #718096;
  font-size: 1.1rem;
}

.unauthorized-icon,
.forbidden-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.unauthorized-container h2,
.forbidden-container h2 {
  margin: 0 0 15px 0;
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: 700;
}

.unauthorized-container p,
.forbidden-container p {
  margin: 0 0 15px 0;
  color: #718096;
  font-size: 1.1rem;
  line-height: 1.6;
}

.forbidden-container p:last-of-type {
  margin-bottom: 30px;
}

.btn-login,
.btn-back,
.btn-dashboard {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-login {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  margin-top: 20px;
}

.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.forbidden-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-back {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-back:hover {
  background: #cbd5e0;
  transform: translateY(-1px);
}

.btn-dashboard {
  background: #667eea;
  color: white;
}

.btn-dashboard:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

/* Styles pour UserInfo */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 0.9rem;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* Styles pour SessionStatus */
.session-warning {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #fed7d7;
  color: #742a2a;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid #feb2b2;
}

.warning-icon {
  font-size: 1.1rem;
}

.extend-btn {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.extend-btn:hover {
  background: #c53030;
  transform: scale(1.05);
}

/* Responsive */
@media (max-width: 768px) {
  .loading-container,
  .unauthorized-container,
  .forbidden-container {
    padding: 40px 30px;
    margin: 20px;
  }

  .unauthorized-icon,
  .forbidden-icon {
    font-size: 3rem;
  }

  .unauthorized-container h2,
  .forbidden-container h2 {
    font-size: 1.5rem;
  }

  .forbidden-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-login,
  .btn-back,
  .btn-dashboard {
    width: 100%;
    justify-content: center;
  }

  .user-info {
    gap: 8px;
    padding: 6px 10px;
  }

  .user-avatar {
    width: 35px;
    height: 35px;
  }

  .user-name {
    font-size: 0.85rem;
  }

  .user-role {
    font-size: 0.75rem;
  }

  .logout-btn {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .loading-container,
  .unauthorized-container,
  .forbidden-container {
    padding: 30px 20px;
  }

  .session-warning {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .extend-btn {
    width: 100%;
  }
}
