/**
 * Module de paramètres et configuration
 */

import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import CompanySettings from './CompanySettings';
import UserSettings from './UserSettings';
import InvoiceSettings from './InvoiceSettings';
import SystemSettings from './SystemSettings';
import { useAuth } from '../../contexts/AuthContext';
import './Settings.css';

const Settings: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [activeTab, setActiveTab] = useState(() => {
    const path = location.pathname.split('/').pop();
    return path || 'company';
  });

  // Vérifier les permissions
  const hasSettingsAccess = () => {
    const allowedRoles = ['ADMIN', 'SUPERADMIN'];
    return allowedRoles.includes(user?.role || '');
  };

  const hasUserSettingsAccess = () => {
    return true; // Tous les utilisateurs peuvent modifier leurs paramètres
  };

  const tabs = [
    {
      id: 'company',
      label: '🏢 Entreprise',
      component: CompanySettings,
      permission: hasSettingsAccess()
    },
    {
      id: 'user',
      label: '👤 Utilisateur',
      component: UserSettings,
      permission: hasUserSettingsAccess()
    },
    {
      id: 'invoices',
      label: '📄 Factures',
      component: InvoiceSettings,
      permission: hasSettingsAccess()
    },
    {
      id: 'system',
      label: '⚙️ Système',
      component: SystemSettings,
      permission: hasSettingsAccess()
    }
  ].filter(tab => tab.permission);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    navigate(`/settings/${tabId}`);
  };

  if (tabs.length === 0) {
    return (
      <div className="access-denied">
        <div className="access-denied-content">
          <div className="access-icon">🚫</div>
          <h2>Accès refusé</h2>
          <p>Vous n'avez pas les permissions nécessaires pour accéder aux paramètres.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="settings-module">
      {/* Header */}
      <div className="settings-header">
        <div className="header-title">
          <h1>⚙️ Paramètres</h1>
          <p>Configurez votre application selon vos besoins</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-outline"
            onClick={() => navigate('/dashboard')}
          >
            ← Retour au dashboard
          </button>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="settings-nav">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => handleTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Contenu des onglets */}
      <div className="settings-content">
        <Routes>
          <Route path="/" element={<CompanySettings />} />
          <Route path="/company" element={<CompanySettings />} />
          <Route path="/user" element={<UserSettings />} />
          <Route path="/invoices" element={<InvoiceSettings />} />
          <Route path="/system" element={<SystemSettings />} />
        </Routes>
      </div>
    </div>
  );
};

export default Settings;
