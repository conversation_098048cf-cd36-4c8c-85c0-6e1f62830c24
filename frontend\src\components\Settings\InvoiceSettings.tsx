/**
 * Paramètres de facturation
 */

import React, { useState, useEffect } from "react";
import { useNotify } from "../Common/NotificationSystem";

interface InvoiceSettingsData {
  numerotation_auto: boolean;
  prefixe_facture: string;
  compteur_facture: number;
  template_defaut: string;
  mentions_legales: string;
  conditions_paiement: string;
  delai_paiement_defaut: number;
  taux_penalite: number;
  escompte_defaut: number;
  devise_defaut: string;
  langue_defaut: string;
  envoi_auto_email: boolean;
  relance_auto: boolean;
  delai_relance: number;
}

const InvoiceSettings: React.FC = () => {
  const notify = useNotify();

  const [formData, setFormData] = useState<InvoiceSettingsData>({
    numerotation_auto: true,
    prefixe_facture: "FAC",
    compteur_facture: 5005,
    template_defaut: "ben_chaabene",
    mentions_legales:
      "Société Ben Chaabene de Commerce - 10 Rue de la Commission, 8000 Nabeul - M.F: 1283008 W/A/M/000",
    conditions_paiement: "Paiement à 30 jours fin de mois",
    delai_paiement_defaut: 30,
    taux_penalite: 3,
    escompte_defaut: 2,
    devise_defaut: "TND",
    langue_defaut: "fr",
    envoi_auto_email: true,
    relance_auto: true,
    delai_relance: 7,
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadInvoiceSettings();
  }, []);

  const loadInvoiceSettings = async () => {
    try {
      setLoading(true);
      // Simuler le chargement des paramètres
      setFormData({
        numerotation_auto: true,
        prefixe_facture: "FAC",
        compteur_facture: 1,
        template_defaut: "modern",
        mentions_legales:
          "Conditions de vente disponibles sur notre site web. TVA non applicable, art. 293 B du CGI.",
        conditions_paiement: "Paiement à 30 jours fin de mois",
        delai_paiement_defaut: 30,
        taux_penalite: 3,
        escompte_defaut: 2,
        devise_defaut: "EUR",
        langue_defaut: "fr",
        envoi_auto_email: true,
        relance_auto: true,
        delai_relance: 7,
      });
    } catch (error: any) {
      notify.error("Erreur lors du chargement des paramètres");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.prefixe_facture.trim()) {
      newErrors.prefixe_facture = "Préfixe requis";
    }

    if (formData.compteur_facture < 1) {
      newErrors.compteur_facture = "Compteur doit être positif";
    }

    if (formData.delai_paiement_defaut < 0) {
      newErrors.delai_paiement_defaut = "Délai ne peut pas être négatif";
    }

    if (formData.taux_penalite < 0 || formData.taux_penalite > 100) {
      newErrors.taux_penalite = "Taux entre 0 et 100%";
    }

    if (formData.escompte_defaut < 0 || formData.escompte_defaut > 100) {
      newErrors.escompte_defaut = "Escompte entre 0 et 100%";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      notify.error("Veuillez corriger les erreurs du formulaire");
      return;
    }

    try {
      setSaving(true);

      // Simuler la sauvegarde
      await new Promise((resolve) => setTimeout(resolve, 1000));

      notify.success("Paramètres de facturation mis à jour avec succès");
    } catch (error: any) {
      notify.error("Erreur lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des paramètres...</p>
      </div>
    );
  }

  return (
    <div className="invoice-settings">
      <form onSubmit={handleSubmit}>
        {/* Numérotation */}
        <div className="settings-section">
          <h3>🔢 Numérotation</h3>

          <div className="form-grid">
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.numerotation_auto}
                  onChange={(e) =>
                    handleInputChange("numerotation_auto", e.target.checked)
                  }
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  🔄 Numérotation automatique
                </span>
              </label>
              <small className="form-help">
                Générer automatiquement les numéros de facture
              </small>
            </div>

            <div className="form-group">
              <label>Préfixe des factures *</label>
              <input
                type="text"
                value={formData.prefixe_facture}
                onChange={(e) =>
                  handleInputChange("prefixe_facture", e.target.value)
                }
                className={`form-input ${
                  errors.prefixe_facture ? "error" : ""
                }`}
                placeholder="FAC"
                maxLength={10}
              />
              {errors.prefixe_facture && (
                <span className="error-text">{errors.prefixe_facture}</span>
              )}
              <small className="form-help">Exemple: FAC-2024-001</small>
            </div>

            <div className="form-group">
              <label>Compteur actuel *</label>
              <input
                type="number"
                min="1"
                value={formData.compteur_facture}
                onChange={(e) =>
                  handleInputChange(
                    "compteur_facture",
                    parseInt(e.target.value) || 1
                  )
                }
                className={`form-input ${
                  errors.compteur_facture ? "error" : ""
                }`}
                disabled={!formData.numerotation_auto}
              />
              {errors.compteur_facture && (
                <span className="error-text">{errors.compteur_facture}</span>
              )}
              <small className="form-help">
                Prochaine facture: {formData.prefixe_facture}-
                {new Date().getFullYear()}-
                {String(formData.compteur_facture).padStart(3, "0")}
              </small>
            </div>
          </div>
        </div>

        {/* Templates */}
        <div className="settings-section">
          <h3>🎨 Templates</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Template par défaut</label>
              <select
                value={formData.template_defaut}
                onChange={(e) =>
                  handleInputChange("template_defaut", e.target.value)
                }
                className="form-select">
                <option value="modern">🎨 Moderne</option>
                <option value="classic">📄 Classique</option>
                <option value="minimal">⚪ Minimal</option>
              </select>
            </div>

            <div className="form-group">
              <label>Devise par défaut</label>
              <select
                value={formData.devise_defaut}
                onChange={(e) =>
                  handleInputChange("devise_defaut", e.target.value)
                }
                className="form-select">
                <option value="EUR">€ Euro</option>
                <option value="USD">$ Dollar US</option>
                <option value="GBP">£ Livre Sterling</option>
                <option value="CHF">CHF Franc Suisse</option>
              </select>
            </div>

            <div className="form-group">
              <label>Langue par défaut</label>
              <select
                value={formData.langue_defaut}
                onChange={(e) =>
                  handleInputChange("langue_defaut", e.target.value)
                }
                className="form-select">
                <option value="fr">🇫🇷 Français</option>
                <option value="en">🇬🇧 English</option>
                <option value="es">🇪🇸 Español</option>
              </select>
            </div>
          </div>
        </div>

        {/* Conditions de paiement */}
        <div className="settings-section">
          <h3>💳 Conditions de Paiement</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Délai de paiement par défaut (jours) *</label>
              <input
                type="number"
                min="0"
                max="365"
                value={formData.delai_paiement_defaut}
                onChange={(e) =>
                  handleInputChange(
                    "delai_paiement_defaut",
                    parseInt(e.target.value) || 0
                  )
                }
                className={`form-input ${
                  errors.delai_paiement_defaut ? "error" : ""
                }`}
              />
              {errors.delai_paiement_defaut && (
                <span className="error-text">
                  {errors.delai_paiement_defaut}
                </span>
              )}
            </div>

            <div className="form-group">
              <label>Taux de pénalité (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.taux_penalite}
                onChange={(e) =>
                  handleInputChange(
                    "taux_penalite",
                    parseFloat(e.target.value) || 0
                  )
                }
                className={`form-input ${errors.taux_penalite ? "error" : ""}`}
              />
              {errors.taux_penalite && (
                <span className="error-text">{errors.taux_penalite}</span>
              )}
              <small className="form-help">
                Pénalité en cas de retard de paiement
              </small>
            </div>

            <div className="form-group">
              <label>Escompte par défaut (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.escompte_defaut}
                onChange={(e) =>
                  handleInputChange(
                    "escompte_defaut",
                    parseFloat(e.target.value) || 0
                  )
                }
                className={`form-input ${
                  errors.escompte_defaut ? "error" : ""
                }`}
              />
              {errors.escompte_defaut && (
                <span className="error-text">{errors.escompte_defaut}</span>
              )}
              <small className="form-help">Remise pour paiement anticipé</small>
            </div>

            <div className="form-group full-width">
              <label>Conditions de paiement</label>
              <textarea
                value={formData.conditions_paiement}
                onChange={(e) =>
                  handleInputChange("conditions_paiement", e.target.value)
                }
                className="form-textarea"
                rows={3}
                placeholder="Paiement à 30 jours fin de mois..."
              />
            </div>
          </div>
        </div>

        {/* Mentions légales */}
        <div className="settings-section">
          <h3>⚖️ Mentions Légales</h3>

          <div className="form-grid">
            <div className="form-group full-width">
              <label>Mentions légales</label>
              <textarea
                value={formData.mentions_legales}
                onChange={(e) =>
                  handleInputChange("mentions_legales", e.target.value)
                }
                className="form-textarea"
                rows={4}
                placeholder="Conditions de vente disponibles sur notre site web..."
              />
              <small className="form-help">
                Texte affiché en bas des factures
              </small>
            </div>
          </div>
        </div>

        {/* Automatisation */}
        <div className="settings-section">
          <h3>🤖 Automatisation</h3>

          <div className="form-grid">
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.envoi_auto_email}
                  onChange={(e) =>
                    handleInputChange("envoi_auto_email", e.target.checked)
                  }
                  className="form-checkbox"
                />
                <span className="checkbox-text">
                  📧 Envoi automatique par email
                </span>
              </label>
              <small className="form-help">
                Envoyer automatiquement les factures par email
              </small>
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.relance_auto}
                  onChange={(e) =>
                    handleInputChange("relance_auto", e.target.checked)
                  }
                  className="form-checkbox"
                />
                <span className="checkbox-text">🔔 Relances automatiques</span>
              </label>
              <small className="form-help">
                Envoyer des relances automatiques pour les factures impayées
              </small>
            </div>

            <div className="form-group">
              <label>Délai de relance (jours)</label>
              <input
                type="number"
                min="1"
                max="365"
                value={formData.delai_relance}
                onChange={(e) =>
                  handleInputChange(
                    "delai_relance",
                    parseInt(e.target.value) || 7
                  )
                }
                className="form-input"
                disabled={!formData.relance_auto}
              />
              <small className="form-help">
                Délai avant envoi de la première relance
              </small>
            </div>
          </div>
        </div>

        {/* Aperçu */}
        <div className="settings-section">
          <h3>👁️ Aperçu</h3>

          <div className="invoice-preview">
            <div className="preview-header">
              <h4>Aperçu de facture</h4>
              <span className="preview-template">
                Template: {formData.template_defaut}
              </span>
            </div>

            <div className="preview-content">
              <div className="preview-number">
                {formData.prefixe_facture}-{new Date().getFullYear()}-
                {String(formData.compteur_facture).padStart(3, "0")}
              </div>
              <div className="preview-conditions">
                {formData.conditions_paiement}
              </div>
              <div className="preview-mentions">
                {formData.mentions_legales || "Aucune mention légale définie"}
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="settings-actions">
          <button
            type="button"
            className="btn btn-outline"
            onClick={loadInvoiceSettings}
            disabled={saving}>
            🔄 Annuler
          </button>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={saving}>
            {saving ? "⏳ Sauvegarde..." : "💾 Sauvegarder"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default InvoiceSettings;
