/* Spinner de base */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.spinner-medium {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

.spinner-large {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

.loading-text {
  color: #7f8c8d;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Page loader */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.page-loader-content {
  text-align: center;
  color: white;
}

.page-loader-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 30px auto;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top: 4px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.375s;
  width: 90%;
  height: 90%;
  top: 5%;
  left: 5%;
}

.spinner-ring:nth-child(3) {
  animation-delay: -0.75s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

.spinner-ring:nth-child(4) {
  animation-delay: -1.125s;
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
}

.page-loader-text {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  opacity: 0.9;
}

.page-loader-subtitle {
  font-size: 1rem;
  opacity: 0.7;
  font-weight: 400;
}

/* Button loader */
.btn-with-loader {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-with-loader.loading {
  pointer-events: none;
  opacity: 0.8;
}

.btn-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.btn-spinner-ring {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.btn-text-hidden {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.btn-text-visible {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
}

/* Skeleton loading */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Card skeleton */
.card-skeleton {
  padding: 20px;
  border: 1px solid #ecf0f1;
  border-radius: 12px;
  background: white;
}

.card-skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.card-skeleton-footer {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* List skeleton */
.list-skeleton {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.list-skeleton-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  background: white;
}

.list-skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Animations avancées */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.skeleton.pulse {
  animation: pulse 2s infinite;
}

/* Dots loader */
.dots-loader {
  display: flex;
  gap: 4px;
  align-items: center;
}

.dots-loader .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: dotPulse 1.4s infinite ease-in-out both;
}

.dots-loader .dot:nth-child(1) {
  animation-delay: -0.32s;
}
.dots-loader .dot:nth-child(2) {
  animation-delay: -0.16s;
}
.dots-loader .dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes dotPulse {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Wave loader */
.wave-loader {
  display: flex;
  gap: 2px;
  align-items: center;
}

.wave-loader .bar {
  width: 4px;
  height: 20px;
  background: currentColor;
  animation: wave 1.2s infinite ease-in-out;
}

.wave-loader .bar:nth-child(1) {
  animation-delay: -1.1s;
}
.wave-loader .bar:nth-child(2) {
  animation-delay: -1s;
}
.wave-loader .bar:nth-child(3) {
  animation-delay: -0.9s;
}
.wave-loader .bar:nth-child(4) {
  animation-delay: -0.8s;
}
.wave-loader .bar:nth-child(5) {
  animation-delay: -0.7s;
}

@keyframes wave {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .page-loader-spinner {
    width: 60px;
    height: 60px;
  }

  .page-loader-text {
    font-size: 1.2rem;
  }

  .page-loader-subtitle {
    font-size: 0.9rem;
  }

  .card-skeleton {
    padding: 15px;
  }

  .list-skeleton-item {
    padding: 12px;
  }
}

/* Thème sombre */
@media (prefers-color-scheme: dark) {
  .loading-overlay {
    background: rgba(44, 62, 80, 0.9);
  }

  .loading-text {
    color: #bdc3c7;
  }

  .skeleton {
    background: linear-gradient(90deg, #34495e 25%, #2c3e50 50%, #34495e 75%);
    background-size: 200% 100%;
  }

  .card-skeleton,
  .list-skeleton-item {
    background: #2c3e50;
    border-color: #34495e;
  }
}

/* États de chargement pour les tables */
.table-loading {
  position: relative;
}

.table-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Loader inline pour les textes */
.inline-loader {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.inline-loader .spinner-small {
  width: 14px;
  height: 14px;
  border-width: 2px;
}

/* Loader pour les images */
.image-loader {
  position: relative;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.image-loader::before {
  content: "";
  width: 30px;
  height: 30px;
  border: 3px solid #ecf0f1;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Loader pour les widgets du dashboard */
.widget-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #ecf0f1;
}

.widget-skeleton {
  padding: 20px;
  width: 100%;
}

.widget-skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.widget-skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.widget-skeleton-row {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* Loader pour les métriques */
.metrics-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-skeleton {
  padding: 20px;
  border: 1px solid #ecf0f1;
  border-radius: 12px;
  background: white;
}

.metric-skeleton-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.metric-skeleton-value {
  width: 80px;
  height: 32px;
  margin-bottom: 8px;
}

.metric-skeleton-label {
  width: 120px;
  height: 16px;
}
