import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import LoginForm from "./LoginForm";
import "./AuthPage.css";

const AuthPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const [isLoginMode, setIsLoginMode] = useState(true);

  // Rediriger si déjà connecté
  useEffect(() => {
    if (isAuthenticated) {
      window.location.href = "/dashboard";
    }
  }, [isAuthenticated]);

  if (isLoading) {
    return (
      <div className="auth-loading">
        <div className="loading-spinner"></div>
        <p>Vérification de l'authentification...</p>
      </div>
    );
  }

  const toggleMode = () => {
    setIsLoginMode(!isLoginMode);
  };

  return (
    <div className="auth-page">
      {/* Effets de particules en arrière-plan */}
      <div className="particles">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 20}s`,
              animationDuration: `${15 + Math.random() * 10}s`,
            }}
          />
        ))}
      </div>

      <div className="auth-container">
        <div className="auth-header">
          <div className="logo">
            <span className="logo-icon">📊</span>
            <h1>ABM System</h1>
          </div>
          <p className="auth-subtitle">
            {isLoginMode
              ? "Connectez-vous à votre espace professionnel"
              : "Créez votre compte pour commencer"}
          </p>
          <div className="auth-tabs">
            <button
              className={`tab-button ${isLoginMode ? "active" : ""}`}
              onClick={() => setIsLoginMode(true)}>
              🔑 Connexion
            </button>
            <button
              className={`tab-button ${!isLoginMode ? "active" : ""}`}
              onClick={() => setIsLoginMode(false)}>
              👤 Inscription
            </button>
          </div>
        </div>

        <div className="auth-content">
          <LoginForm />
        </div>

        <div className="auth-footer">
          <p>
            {isLoginMode ? "Pas encore de compte ?" : "Déjà un compte ?"}
            <button
              className="link-button"
              onClick={toggleMode}>
              {isLoginMode ? "S'inscrire" : "Se connecter"}
            </button>
          </p>
        </div>
      </div>

      {/* Informations sur les comptes de démonstration */}
      {isLoginMode && (
        <div className="demo-info">
          <h3>🎯 Comptes de démonstration</h3>
          <div className="demo-accounts">
            <div className="demo-account">
              <strong>Administrateur</strong>
              <span><EMAIL> / 123456</span>
            </div>
            <div className="demo-account">
              <strong>Manager</strong>
              <span><EMAIL> / 123456</span>
            </div>
            <div className="demo-account">
              <strong>Comptable</strong>
              <span><EMAIL> / 123456</span>
            </div>
            <div className="demo-account">
              <strong>Vendeur</strong>
              <span><EMAIL> / 123456</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthPage;
