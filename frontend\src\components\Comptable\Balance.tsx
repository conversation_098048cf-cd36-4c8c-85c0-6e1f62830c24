/**
 * Balance comptable
 */

import React, { useState, useEffect } from "react";
import { useNotify } from "../Common/NotificationSystem";

interface CompteBalance {
  numero: string;
  nom: string;
  debit: number;
  credit: number;
  solde: number;
  type: "ACTIF" | "PASSIF" | "CHARGE" | "PRODUIT";
}

interface BalanceData {
  comptes: CompteBalance[];
  total_debit: number;
  total_credit: number;
  total_actif: number;
  total_passif: number;
  total_charges: number;
  total_produits: number;
  resultat: number;
  periode: string;
}

const Balance: React.FC = () => {
  const notify = useNotify();
  const [loading, setLoading] = useState(true);
  const [balance, setBalance] = useState<BalanceData>({
    comptes: [],
    total_debit: 0,
    total_credit: 0,
    total_actif: 0,
    total_passif: 0,
    total_charges: 0,
    total_produits: 0,
    resultat: 0,
    periode: "",
  });
  const [period, setPeriod] = useState("2025");
  const [filterType, setFilterType] = useState("ALL");

  useEffect(() => {
    loadBalance();
  }, [period]);

  const loadBalance = async () => {
    try {
      setLoading(true);

      // Simulation de données de balance comptable
      const mockBalance: BalanceData = {
        comptes: [
          // ACTIF
          {
            numero: "101",
            nom: "Capital social",
            debit: 0,
            credit: 50000,
            solde: -50000,
            type: "PASSIF",
          },
          {
            numero: "106",
            nom: "Réserves",
            debit: 0,
            credit: 15000,
            solde: -15000,
            type: "PASSIF",
          },
          {
            numero: "120",
            nom: "Résultat de l'exercice",
            debit: 0,
            credit: 25000,
            solde: -25000,
            type: "PASSIF",
          },
          {
            numero: "213",
            nom: "Installations techniques",
            debit: 35000,
            credit: 0,
            solde: 35000,
            type: "ACTIF",
          },
          {
            numero: "218",
            nom: "Matériel de bureau",
            debit: 12000,
            credit: 0,
            solde: 12000,
            type: "ACTIF",
          },
          {
            numero: "281",
            nom: "Amortissements installations",
            debit: 0,
            credit: 8000,
            solde: -8000,
            type: "ACTIF",
          },
          {
            numero: "310",
            nom: "Marchandises",
            debit: 125680,
            credit: 0,
            solde: 125680,
            type: "ACTIF",
          },
          {
            numero: "411",
            nom: "Clients",
            debit: 45000,
            credit: 0,
            solde: 45000,
            type: "ACTIF",
          },
          {
            numero: "401",
            nom: "Fournisseurs",
            debit: 0,
            credit: 28000,
            solde: -28000,
            type: "PASSIF",
          },
          {
            numero: "437",
            nom: "TVA collectée",
            debit: 0,
            credit: 18500,
            solde: -18500,
            type: "PASSIF",
          },
          {
            numero: "445",
            nom: "TVA déductible",
            debit: 12000,
            credit: 0,
            solde: 12000,
            type: "ACTIF",
          },
          {
            numero: "512",
            nom: "Banque",
            debit: 85000,
            credit: 0,
            solde: 85000,
            type: "ACTIF",
          },
          {
            numero: "530",
            nom: "Caisse",
            debit: 5500,
            credit: 0,
            solde: 5500,
            type: "ACTIF",
          },
          // CHARGES
          {
            numero: "607",
            nom: "Achats de marchandises",
            debit: 180000,
            credit: 0,
            solde: 180000,
            type: "CHARGE",
          },
          {
            numero: "613",
            nom: "Locations",
            debit: 24000,
            credit: 0,
            solde: 24000,
            type: "CHARGE",
          },
          {
            numero: "621",
            nom: "Personnel",
            debit: 45000,
            credit: 0,
            solde: 45000,
            type: "CHARGE",
          },
          {
            numero: "626",
            nom: "Services extérieurs",
            debit: 8500,
            credit: 0,
            solde: 8500,
            type: "CHARGE",
          },
          {
            numero: "681",
            nom: "Dotations amortissements",
            debit: 4000,
            credit: 0,
            solde: 4000,
            type: "CHARGE",
          },
          // PRODUITS
          {
            numero: "707",
            nom: "Ventes de marchandises",
            debit: 0,
            credit: 285000,
            solde: -285000,
            type: "PRODUIT",
          },
          {
            numero: "758",
            nom: "Produits divers",
            debit: 0,
            credit: 2500,
            solde: -2500,
            type: "PRODUIT",
          },
        ],
        total_debit: 582180,
        total_credit: 582180,
        total_actif: 337180,
        total_passif: 164500,
        total_charges: 261500,
        total_produits: 287500,
        resultat: 26000,
        periode: "2025",
      };

      setBalance(mockBalance);
    } catch (error: any) {
      notify.error("Erreur lors du chargement de la balance");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "ACTIF":
        return "type-actif";
      case "PASSIF":
        return "type-passif";
      case "CHARGE":
        return "type-charge";
      case "PRODUIT":
        return "type-produit";
      default:
        return "";
    }
  };

  const filteredComptes = balance.comptes.filter((compte) => {
    if (filterType === "ALL") return true;
    return compte.type === filterType;
  });

  if (loading) {
    return (
      <div className="balance-loading">
        <div className="loading-spinner"></div>
        <p>Chargement de la balance...</p>
      </div>
    );
  }

  return (
    <div className="balance">
      <div className="balance-header">
        <div className="header-content">
          <h1>⚖️ Balance Comptable</h1>
          <p>Balance générale pour l'exercice {balance.periode}</p>
        </div>

        <div className="balance-controls">
          <div className="period-selector">
            <label>Exercice:</label>
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}>
              <option value="2025">2025</option>
              <option value="2024">2024</option>
              <option value="2023">2023</option>
            </select>
          </div>

          <div className="filter-selector">
            <label>Type:</label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}>
              <option value="ALL">Tous les comptes</option>
              <option value="ACTIF">Actif</option>
              <option value="PASSIF">Passif</option>
              <option value="CHARGE">Charges</option>
              <option value="PRODUIT">Produits</option>
            </select>
          </div>
        </div>
      </div>

      {/* Résumé de la balance */}
      <div className="balance-summary">
        <div className="summary-card">
          <h4>📊 Totaux généraux</h4>
          <div className="summary-grid">
            <div className="summary-item">
              <label>Total Débit:</label>
              <span className="amount">
                {formatCurrency(balance.total_debit)}
              </span>
            </div>
            <div className="summary-item">
              <label>Total Crédit:</label>
              <span className="amount">
                {formatCurrency(balance.total_credit)}
              </span>
            </div>
            <div className="summary-item">
              <label>Équilibre:</label>
              <span
                className={`amount ${
                  balance.total_debit === balance.total_credit
                    ? "success"
                    : "danger"
                }`}>
                {balance.total_debit === balance.total_credit
                  ? "✅ Équilibrée"
                  : "❌ Déséquilibrée"}
              </span>
            </div>
          </div>
        </div>

        <div className="summary-card">
          <h4>💼 Bilan</h4>
          <div className="summary-grid">
            <div className="summary-item">
              <label>Total Actif:</label>
              <span className="amount">
                {formatCurrency(balance.total_actif)}
              </span>
            </div>
            <div className="summary-item">
              <label>Total Passif:</label>
              <span className="amount">
                {formatCurrency(balance.total_passif)}
              </span>
            </div>
          </div>
        </div>

        <div className="summary-card">
          <h4>📈 Résultat</h4>
          <div className="summary-grid">
            <div className="summary-item">
              <label>Charges:</label>
              <span className="amount">
                {formatCurrency(balance.total_charges)}
              </span>
            </div>
            <div className="summary-item">
              <label>Produits:</label>
              <span className="amount">
                {formatCurrency(balance.total_produits)}
              </span>
            </div>
            <div className="summary-item">
              <label>Résultat:</label>
              <span
                className={`amount ${
                  balance.resultat >= 0 ? "success" : "danger"
                }`}>
                {formatCurrency(balance.resultat)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Tableau de la balance */}
      <div className="balance-table-container">
        <table className="balance-table">
          <thead>
            <tr>
              <th>N° Compte</th>
              <th>Nom du compte</th>
              <th>Type</th>
              <th>Débit</th>
              <th>Crédit</th>
              <th>Solde</th>
            </tr>
          </thead>
          <tbody>
            {filteredComptes.map((compte, index) => (
              <tr
                key={index}
                className={getTypeColor(compte.type)}>
                <td className="compte-numero">{compte.numero}</td>
                <td className="compte-nom">{compte.nom}</td>
                <td className="compte-type">
                  <span className={`type-badge ${compte.type.toLowerCase()}`}>
                    {compte.type}
                  </span>
                </td>
                <td className="montant debit">
                  {compte.debit > 0 ? formatCurrency(compte.debit) : "-"}
                </td>
                <td className="montant credit">
                  {compte.credit > 0 ? formatCurrency(compte.credit) : "-"}
                </td>
                <td
                  className={`montant solde ${
                    compte.solde >= 0 ? "positive" : "negative"
                  }`}>
                  {formatCurrency(Math.abs(compte.solde))}
                  {compte.solde < 0 && " (C)"}
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="totals-row">
              <td colSpan={3}>
                <strong>TOTAUX</strong>
              </td>
              <td className="montant total">
                <strong>{formatCurrency(balance.total_debit)}</strong>
              </td>
              <td className="montant total">
                <strong>{formatCurrency(balance.total_credit)}</strong>
              </td>
              <td className="montant total">
                <strong>
                  {balance.total_debit === balance.total_credit
                    ? "✅ Équilibrée"
                    : "❌ Écart"}
                </strong>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      {/* Actions */}
      <div className="balance-actions">
        <button
          className="btn-secondary"
          onClick={() => window.print()}>
          🖨️ Imprimer
        </button>
        <button
          className="btn-primary"
          onClick={() => notify.info("Export en cours...")}>
          📤 Exporter Excel
        </button>
        <button
          className="btn-success"
          onClick={() => notify.info("Export en cours...")}>
          📄 Exporter PDF
        </button>
      </div>
    </div>
  );
};

export default Balance;
