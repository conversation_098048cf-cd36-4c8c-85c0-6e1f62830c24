"""
Vues API professionnelles pour le système de facturation moderne
Avec génération PDF, templates et fonctionnalités avancées
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from django.db.models import Sum, Count, Q, Max
from django.db import models
from decimal import Decimal
import logging

from .models import (
    ConfigurationFacturation, Facture,
    LigneFacture, PaiementFacture
)
from .serializers import (
    ConfigurationFacturationSerializer,
    FactureSerializer, FactureCreateSerializer, FactureUpdateSerializer,
    FactureListSerializer, LigneFactureSerializer, PaiementFactureSerializer,
    FactureStatsSerializer
)
from .pdf_generator_tunisian import TunisianFacturePDFGenerator
from Core.permissions import IsAuthenticated, HasModulePermission

logger = logging.getLogger(__name__)


class ConfigurationFacturationViewSet(viewsets.ModelViewSet):
    """API pour la configuration de facturation"""

    queryset = ConfigurationFacturation.objects.all()
    serializer_class = ConfigurationFacturationSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]

    def get_permissions(self):
        """Seuls les admins peuvent modifier la configuration"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAdminUser()]
        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Récupère la configuration active"""
        config = ConfigurationFacturation.get_config()
        serializer = self.get_serializer(config)
        return Response(serializer.data)


class FactureViewSet(viewsets.ModelViewSet):
    """API pour les factures avec fonctionnalités avancées"""

    queryset = Facture.objects.all().select_related('client', 'creee_par').prefetch_related('lignes', 'paiements')
    permission_classes = []  # Temporaire pour test

    def get_serializer_class(self):
        """Sélection du serializer selon l'action"""
        if self.action == 'create':
            return FactureCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return FactureUpdateSerializer
        elif self.action == 'list':
            return FactureListSerializer
        return FactureSerializer

    def get_queryset(self):
        """Filtrage et optimisation des requêtes"""
        queryset = super().get_queryset()

        # Filtres de recherche
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(numero__icontains=search) |
                Q(client__nom__icontains=search) |
                Q(client__email__icontains=search)
            )

        # Filtres spécifiques
        client_id = self.request.query_params.get('client')
        if client_id:
            queryset = queryset.filter(client_id=client_id)

        statut = self.request.query_params.get('statut')
        if statut:
            queryset = queryset.filter(statut=statut)

        type_document = self.request.query_params.get('type')
        if type_document:
            queryset = queryset.filter(type_document=type_document)

        # Filtres de date
        date_debut = self.request.query_params.get('date_debut')
        if date_debut:
            queryset = queryset.filter(date_emission__gte=date_debut)

        date_fin = self.request.query_params.get('date_fin')
        if date_fin:
            queryset = queryset.filter(date_emission__lte=date_fin)

        # Optimisation pour la liste
        if self.action == 'list':
            queryset = queryset.annotate(
                montant_paye=Sum('paiements__montant')
            )

        return queryset.order_by('-date_emission', '-numero')

    @action(detail=False, methods=['get'])
    def statistiques(self, request):
        """Statistiques de facturation"""
        now = timezone.now()
        debut_mois = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        debut_annee = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

        stats = {
            'total_factures': Facture.objects.filter(type_document='FACTURE').count(),
            'total_devis': Facture.objects.filter(type_document='DEVIS').count(),
            'chiffre_affaires_mois': Facture.objects.filter(
                type_document='FACTURE',
                statut='PAYE',
                date_emission__gte=debut_mois
            ).aggregate(total=Sum('montant_ttc'))['total'] or Decimal('0'),
            'chiffre_affaires_annee': Facture.objects.filter(
                type_document='FACTURE',
                statut='PAYE',
                date_emission__gte=debut_annee
            ).aggregate(total=Sum('montant_ttc'))['total'] or Decimal('0'),
            'factures_en_attente': Facture.objects.filter(
                type_document='FACTURE',
                statut__in=['ENVOYE', 'ACCEPTE']
            ).count(),
            'factures_en_retard': Facture.objects.filter(
                type_document='FACTURE',
                statut__in=['ENVOYE', 'ACCEPTE'],
                date_echeance__lt=now.date()
            ).count(),
            'montant_impaye': Facture.objects.filter(
                type_document='FACTURE',
                statut__in=['ENVOYE', 'ACCEPTE']
            ).aggregate(total=Sum('montant_ttc'))['total'] or Decimal('0')
        }

        serializer = FactureStatsSerializer(stats)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def pdf(self, request, pk=None):
        """Génère le PDF de la facture"""
        facture = self.get_object()

        try:
            # Préparer les données pour le générateur PDF
            facture_data = {
                'numero': facture.numero,
                'type_document': facture.type_document,
                'date': facture.date_emission.strftime('%d/%m/%Y'),
                'client': {
                    'nom': facture.client.nom,
                    'adresse': facture.client.adresse or '',
                    'ville': facture.client.ville or '',
                    'code_postal': facture.client.code_postal or '',
                    'telephone': facture.client.telephone or '',
                    'email': facture.client.email or '',
                },
                'produits': [
                    {
                        'designation': ligne.designation,
                        'quantite': float(ligne.quantite),
                        'prix_unitaire': float(ligne.prix_unitaire_ht),
                        'taux_tva': float(ligne.taux_tva),
                        'montant_ttc': float(ligne.montant_ttc),
                    }
                    for ligne in facture.lignes.all()
                ],
                'montant_ht': float(facture.montant_ht),
                'montant_tva': float(facture.montant_tva),
                'montant_ttc': float(facture.montant_ttc),
                'notes': facture.notes or '',
                'conditions_paiement': facture.conditions_paiement or '',
            }

            # Générer le PDF
            generator = TunisianFacturePDFGenerator()
            pdf_content = generator.generate_facture_pdf(facture_data)

            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="facture_{facture.numero}.pdf"'

            # Sauvegarder le fichier PDF
            if not facture.fichier_pdf:
                from django.core.files.base import ContentFile
                facture.fichier_pdf.save(
                    f'facture_{facture.numero}.pdf',
                    ContentFile(pdf_content),
                    save=True
                )

            return response

        except Exception as e:
            logger.error(f"Erreur génération PDF facture {facture.numero}: {str(e)}")
            return Response(
                {'error': f'Erreur lors de la génération du PDF: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def dupliquer(self, request, pk=None):
        """Duplique une facture"""
        facture_originale = self.get_object()

        try:
            with transaction.atomic():
                # Créer la nouvelle facture
                nouvelle_facture = Facture.objects.create(
                    type_document=facture_originale.type_document,
                    client=facture_originale.client,
                    date_emission=timezone.now().date(),
                    remise_pourcentage=facture_originale.remise_pourcentage,
                    notes=facture_originale.notes,
                    conditions_paiement=facture_originale.conditions_paiement,
                    creee_par=request.user
                )

                # Dupliquer les lignes
                for ligne in facture_originale.lignes.all():
                    LigneFacture.objects.create(
                        facture=nouvelle_facture,
                        produit=ligne.produit,
                        designation=ligne.designation,
                        description=ligne.description,
                        quantite=ligne.quantite,
                        prix_unitaire_ht=ligne.prix_unitaire_ht,
                        taux_tva=ligne.taux_tva,
                        ordre=ligne.ordre
                    )

                serializer = self.get_serializer(nouvelle_facture)
                return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Erreur duplication facture {facture_originale.numero}: {str(e)}")
            return Response(
                {'error': f'Erreur lors de la duplication: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def marquer_payee(self, request, pk=None):
        """Marque une facture comme payée"""
        facture = self.get_object()

        if facture.statut == 'PAYE':
            return Response(
                {'error': 'La facture est déjà marquée comme payée'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Créer un paiement automatique
        montant = request.data.get('montant', facture.montant_ttc)
        mode_paiement = request.data.get('mode_paiement', 'VIREMENT')

        PaiementFacture.objects.create(
            facture=facture,
            montant=montant,
            mode_paiement=mode_paiement,
            date_paiement=timezone.now().date()
        )

        return Response({'message': 'Facture marquée comme payée'})


class LigneFactureViewSet(viewsets.ModelViewSet):
    """API pour les lignes de facture"""

    queryset = LigneFacture.objects.all().select_related('facture', 'produit')
    serializer_class = LigneFactureSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]

    def get_queryset(self):
        """Filtrage des lignes"""
        queryset = super().get_queryset()

        facture_id = self.request.query_params.get('facture')
        if facture_id:
            queryset = queryset.filter(facture_id=facture_id)

        return queryset.order_by('ordre', 'id')

    def perform_create(self, serializer):
        """Création d'une ligne avec ordre automatique"""
        facture = serializer.validated_data['facture']

        # Définir l'ordre automatiquement
        if not serializer.validated_data.get('ordre'):
            max_ordre = facture.lignes.aggregate(
                max_ordre=models.Max('ordre')
            )['max_ordre'] or 0
            serializer.save(ordre=max_ordre + 1)
        else:
            serializer.save()


class PaiementFactureViewSet(viewsets.ModelViewSet):
    """API pour les paiements de factures"""

    queryset = PaiementFacture.objects.all().select_related('facture')
    serializer_class = PaiementFactureSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]

    def get_queryset(self):
        """Filtrage des paiements"""
        queryset = super().get_queryset()

        facture_id = self.request.query_params.get('facture')
        if facture_id:
            queryset = queryset.filter(facture_id=facture_id)

        return queryset.order_by('-date_paiement')

    def perform_create(self, serializer):
        """Création d'un paiement avec validation"""
        paiement = serializer.save()

        # Vérifier si la facture est entièrement payée
        facture = paiement.facture
        total_paiements = facture.paiements.aggregate(
            total=Sum('montant')
        )['total'] or Decimal('0')

        if total_paiements >= facture.montant_ttc:
            facture.statut = 'PAYE'
            facture.date_paiement = paiement.date_paiement
            facture.save()
