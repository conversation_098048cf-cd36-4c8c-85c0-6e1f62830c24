import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import "./SimpleNavigation.css";

interface NavigationProps {
  currentPage: string;
  onPageChange: (page: string) => void;
  isCollapsed?: boolean;
  onToggle?: () => void;
}

const SimpleNavigation: React.FC<NavigationProps> = ({
  currentPage,
  onPageChange,
  isCollapsed = false,
  onToggle,
}) => {
  const { user, logout } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = async () => {
    await logout();
    onPageChange("login");
  };

  const navigateToPage = (pageId: string) => {
    const urlMap: { [key: string]: string } = {
      dashboard: "/",
      clients: "/clients",
      produits: "/produits",
      factures: "/factures",
      orders: "/commandes",
      payments: "/paiements",
      stock: "/stock",
      comptable: "/comptable",
      rapports: "/rapports",
      ecommerce: "/ecommerce",
      settings: "/settings",
      profile: "/profile",
    };

    const url = urlMap[pageId] || "/";
    window.history.pushState({}, "", url);
    onPageChange(pageId);
  };

  const menuItems = [
    {
      id: "dashboard",
      icon: "📊",
      label: "Dashboard",
      description: "Vue d'ensemble",
    },
    {
      id: "clients",
      icon: "👥",
      label: "Clients",
      description: "Gestion clients",
    },
    {
      id: "products",
      icon: "📦",
      label: "Produits",
      description: "Catalogue",
    },
    {
      id: "factures",
      icon: "📄",
      label: "Factures",
      description: "Facturation",
    },
    {
      id: "orders",
      icon: "📋",
      label: "Commandes",
      description: "Gestion commandes",
    },
    {
      id: "payments",
      icon: "💰",
      label: "Paiements",
      description: "Encaissements",
    },
    {
      id: "stock",
      icon: "📊",
      label: "Stock",
      description: "Inventaire",
    },
    {
      id: "comptable",
      icon: "📈",
      label: "Comptabilité",
      description: "Gestion comptable",
    },
    {
      id: "rapports",
      icon: "📊",
      label: "Rapports",
      description: "Analyses",
    },
    {
      id: "ecommerce",
      icon: "🛒",
      label: "E-commerce",
      description: "Boutique en ligne",
    },
    {
      id: "settings",
      icon: "⚙️",
      label: "Paramètres",
      description: "Configuration",
    },
  ];

  return (
    <nav className={`simple-navigation ${isCollapsed ? "collapsed" : ""}`}>
      {/* Header */}
      <div className="nav-header">
        <div className="nav-brand">
          <img
            src="/logo_ben_chaabene.png"
            alt="Logo Ben Chaabene"
            className="brand-logo"
            style={{ width: "32px", height: "auto" }}
          />
          {!isCollapsed && (
            <div className="brand-text">
              <span style={{ fontWeight: "bold", color: "#2c5aa0" }}>
                Ben Chaabene
              </span>
            </div>
          )}
        </div>
        {onToggle && (
          <button
            className="nav-toggle"
            onClick={onToggle}>
            {isCollapsed ? "→" : "←"}
          </button>
        )}
      </div>

      {/* Menu Items */}
      <div className="nav-menu">
        {menuItems.map((item) => (
          <button
            key={item.id}
            onClick={() => navigateToPage(item.id)}
            className={`nav-item ${currentPage === item.id ? "active" : ""}`}
            title={isCollapsed ? item.label : ""}>
            <span className="nav-icon">{item.icon}</span>
            {!isCollapsed && (
              <div className="nav-content">
                <span className="nav-label">{item.label}</span>
                <span className="nav-description">{item.description}</span>
              </div>
            )}
          </button>
        ))}
      </div>

      {/* User Menu */}
      <div className="nav-footer">
        <div className="nav-user">
          <button
            className="user-button"
            onClick={() => setShowUserMenu(!showUserMenu)}>
            <div className="user-avatar">{user?.prenom?.[0] || "U"}</div>
            {!isCollapsed && (
              <div className="user-info">
                <span className="user-name">
                  {user?.prenom} {user?.nom}
                </span>
                <span className="user-role">{user?.role || "Utilisateur"}</span>
              </div>
            )}
          </button>

          {showUserMenu && (
            <div className="user-menu">
              <button
                onClick={() => navigateToPage("profile")}
                className="user-menu-item">
                <span>👤</span>
                Profil
              </button>
              <button
                onClick={() => navigateToPage("settings")}
                className="user-menu-item">
                <span>⚙️</span>
                Paramètres
              </button>
              <button
                onClick={() => navigateToPage("debug")}
                className="user-menu-item">
                <span>🧪</span>
                Test Services
              </button>
              <button
                onClick={handleLogout}
                className="user-menu-item logout">
                <span>🚪</span>
                Déconnexion
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default SimpleNavigation;
