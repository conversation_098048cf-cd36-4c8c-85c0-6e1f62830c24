import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import "./SimpleDashboard.css";

interface DashboardStats {
  clients: { total: number; actifs: number };
  produits: { total: number; actifs: number; rupture: number };
  factures: { total: number; ca_total: number; retard: number };
  commandes: { total: number; en_cours: number };
  paiements: { encaissements: number; taux_recouvrement: number };
}

const SimpleDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Simuler un délai de chargement
      await new Promise((resolve) => setTimeout(resolve, 800));

      // Utiliser des données simulées au lieu des services API
      setStats({
        clients: {
          total: 4,
          actifs: 3,
        },
        produits: {
          total: 4,
          actifs: 3,
          rupture: 1,
        },
        factures: {
          total: 3,
          ca_total: 3518.0,
          retard: 0,
        },
        commandes: {
          total: 4,
          en_cours: 2,
        },
        paiements: {
          encaissements: 3669.0,
          taux_recouvrement: 95.2,
        },
      });
    } catch (err: any) {
      console.error("Erreur lors du chargement des données:", err);
      setError(
        "Impossible de charger les données du dashboard. Veuillez réessayer."
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const navigateToPage = (url: string) => {
    window.history.pushState({}, "", url);
    window.location.href = url;
  };

  const dashboardCards = stats
    ? [
        {
          title: "Clients",
          value: stats.clients.total.toString(),
          icon: "👥",
          color: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          description: `${stats.clients.actifs} clients actifs`,
          action: () => navigateToPage("/clients"),
        },
        {
          title: "Produits",
          value: stats.produits.total.toString(),
          icon: "📦",
          color: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
          description: `${stats.produits.rupture} en rupture`,
          action: () => navigateToPage("/produits"),
        },
        {
          title: "Factures",
          value: stats.factures.total.toString(),
          icon: "📄",
          color: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
          description: `${stats.factures.retard} en retard`,
          action: () => navigateToPage("/factures"),
        },
        {
          title: "CA Total",
          value: `${stats.factures.ca_total.toLocaleString()} TND`,
          icon: "💰",
          color: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
          description: "Chiffre d'affaires",
          action: () => navigateToPage("/rapports"),
        },
        {
          title: "Commandes",
          value: stats.commandes.en_cours.toString(),
          icon: "📋",
          color: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
          description: "En cours",
          action: () => navigateToPage("/commandes"),
        },
        {
          title: "Paiements",
          value: `${stats.paiements.taux_recouvrement}%`,
          icon: "✅",
          color: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
          description: "Taux de recouvrement",
          action: () => navigateToPage("/paiements"),
        },
      ]
    : [];

  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    loadRecentActivities();
  }, []);

  const loadRecentActivities = async () => {
    try {
      // Simuler un délai de chargement
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Activités récentes simulées
      const activities = [
        {
          id: "activity-1",
          type: "facture",
          description: "Facture FAC-2024-5004 créée pour CLIENT FOND FUTURISTE",
          time: "15/03/2024",
          data: { created_at: "2024-03-15T10:30:00Z" },
        },
        {
          id: "activity-2",
          type: "commande",
          description: "Commande CMD-2024-002 - EN_PREPARATION",
          time: "14/03/2024",
          data: { created_at: "2024-03-14T14:15:00Z" },
        },
        {
          id: "activity-3",
          type: "client",
          description: "Nouveau client: Société Innovation Tech ajouté",
          time: "14/03/2024",
          data: { created_at: "2024-03-14T14:15:00Z" },
        },
        {
          id: "activity-4",
          type: "paiement",
          description: "Paiement PAY-2024-001 validé (998.0 TND)",
          time: "13/03/2024",
          data: { created_at: "2024-03-13T16:20:00Z" },
        },
        {
          id: "activity-5",
          type: "commande",
          description: "Commande CMD-2024-004 expédiée",
          time: "12/03/2024",
          data: { created_at: "2024-03-12T16:20:00Z" },
        },
      ];

      setRecentActivities(activities);

      // Notifications simulées
      const notifications = [
        {
          id: "1",
          message: "Stock faible pour Pack Solution Complète",
          type: "warning",
          created_at: "2024-03-15T10:30:00Z",
        },
        {
          id: "2",
          message: "Nouvelle commande CMD-2024-002 reçue",
          type: "success",
          created_at: "2024-03-14T14:15:00Z",
        },
      ];
      setNotifications(notifications);
    } catch (error) {
      console.error("Erreur lors du chargement des activités récentes:", error);
    }
  };

  const quickActions = [
    {
      title: "Nouvelle Facture",
      icon: "📄",
      action: () => {
        window.history.pushState({}, "", "/nouvelle-facture");
        window.location.href = "/nouvelle-facture";
      },
    },
    {
      title: "Ajouter Client",
      icon: "👥",
      action: () => {
        window.history.pushState({}, "", "/nouveau-client");
        window.location.href = "/nouveau-client";
      },
    },
    {
      title: "Gérer Stock",
      icon: "📦",
      action: () => {
        window.history.pushState({}, "", "/stock");
        window.location.href = "/stock";
      },
    },
    {
      title: "Voir Rapports",
      icon: "📊",
      action: () => {
        window.history.pushState({}, "", "/rapports");
        window.location.href = "/rapports";
      },
    },
  ];

  if (loading) {
    return (
      <div className="simple-dashboard">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Chargement du tableau de bord...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="simple-dashboard">
        <div className="error-container">
          <h2>Erreur</h2>
          <p>{error}</p>
          <button
            onClick={loadDashboardData}
            className="retry-button">
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="simple-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="welcome-section">
          <div className="welcome-content">
            <img
              src="/logo_ben_chaabene.png"
              alt="Ben Chaabène"
              className="dashboard-logo"
            />
            <div className="welcome-text">
              <h1>Bonjour, {user?.prenom || "Utilisateur"} ! 👋</h1>
              <p>
                Société Ben Chaabène de Commerce - Voici un aperçu de votre
                activité aujourd'hui
              </p>
            </div>
          </div>
        </div>
        <div className="date-section">
          <span>
            {new Date().toLocaleDateString("fr-FR", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        {dashboardCards.map((stat, index) => (
          <div
            key={index}
            className="stat-card clickable"
            style={{ background: stat.color }}
            onClick={stat.action}
            title={`Cliquer pour voir ${stat.title.toLowerCase()}`}>
            <div className="stat-icon">{stat.icon}</div>
            <div className="stat-content">
              <h3>{stat.value}</h3>
              <p className="stat-title">{stat.title}</p>
              <span className="stat-description">{stat.description}</span>
            </div>
            <div className="stat-arrow">→</div>
          </div>
        ))}
      </div>

      {/* Main Content */}
      <div className="dashboard-content">
        {/* Quick Actions */}
        <div className="dashboard-section">
          <h2>Actions Rapides</h2>
          <div className="quick-actions">
            {quickActions.map((action, index) => (
              <button
                key={index}
                className="action-button"
                onClick={action.action}>
                <span className="action-icon">{action.icon}</span>
                <span className="action-title">{action.title}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="dashboard-section">
          <h2>Activité Récente</h2>
          <div className="activity-list">
            {recentActivities.map((activity) => (
              <div
                key={activity.id}
                className="activity-item clickable"
                onClick={() => {
                  if (activity.type === "facture") {
                    navigateToPage("/factures");
                  } else if (activity.type === "commande") {
                    navigateToPage("/commandes");
                  } else if (activity.type === "client") {
                    navigateToPage("/clients");
                  } else if (activity.type === "paiement") {
                    navigateToPage("/paiements");
                  }
                }}
                title="Cliquer pour voir les détails">
                <div className="activity-icon">
                  {activity.type === "facture" && "📄"}
                  {activity.type === "commande" && "📦"}
                  {activity.type === "client" && "👥"}
                  {activity.type === "paiement" && "💳"}
                </div>
                <div className="activity-content">
                  <p>{activity.description}</p>
                  <span className="activity-time">{activity.time}</span>
                </div>
                <div className="activity-arrow">→</div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Overview */}
        <div className="dashboard-section">
          <h2>Aperçu des Performances</h2>
          <div className="performance-grid">
            <div className="performance-item">
              <h4>Objectif Mensuel</h4>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: "75%" }}></div>
              </div>
              <span>75% atteint (€45,000 / €60,000)</span>
            </div>
            <div className="performance-item">
              <h4>Satisfaction Client</h4>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: "92%" }}></div>
              </div>
              <span>92% de satisfaction</span>
            </div>
            <div className="performance-item">
              <h4>Délai de Livraison</h4>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: "88%" }}></div>
              </div>
              <span>88% dans les délais</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleDashboard;
