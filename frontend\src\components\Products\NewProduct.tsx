import React, { useState } from "react";
import { toast } from "react-toastify";
import { ProductService } from "../../services/apiService";
import "./NewProduct.css";

interface NewProductData {
  code: string;
  designation: string;
  description: string;
  prix_vente: number;
  prix_achat: number;
  stock_actuel: number;
  stock_minimum: number;
  unite: string;
  categorie: string;
  tva_taux: number;
  actif: boolean;
  notes: string;
}

const NewProduct: React.FC = () => {
  const navigateBack = () => {
    window.history.pushState({}, "", "/produits");
    window.location.href = "/produits";
  };
  const [productData, setProductData] = useState<NewProductData>({
    code: `PROD-${Date.now().toString().slice(-6)}`,
    designation: "",
    description: "",
    prix_vente: 0,
    prix_achat: 0,
    stock_actuel: 0,
    stock_minimum: 5,
    unite: "pièce",
    categorie: "",
    tva_taux: 20,
    actif: true,
    notes: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const categories = [
    "Électronique",
    "Informatique",
    "Mobilier",
    "Vêtements",
    "Alimentation",
    "Santé & Beauté",
    "Sport & Loisirs",
    "Automobile",
    "Maison & Jardin",
    "Services",
    "Autre",
  ];

  const unites = [
    "pièce",
    "kg",
    "g",
    "litre",
    "ml",
    "mètre",
    "cm",
    "m²",
    "m³",
    "heure",
    "jour",
    "mois",
    "année",
  ];

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!productData.code.trim()) {
      newErrors.code = "Le code produit est obligatoire";
    }

    if (!productData.designation.trim()) {
      newErrors.designation = "La désignation est obligatoire";
    }

    if (productData.prix_vente <= 0) {
      newErrors.prix_vente = "Le prix de vente doit être supérieur à 0";
    }

    if (productData.prix_achat < 0) {
      newErrors.prix_achat = "Le prix d'achat ne peut pas être négatif";
    }

    if (productData.stock_actuel < 0) {
      newErrors.stock_actuel = "Le stock ne peut pas être négatif";
    }

    if (productData.stock_minimum < 0) {
      newErrors.stock_minimum = "Le stock minimum ne peut pas être négatif";
    }

    if (!productData.categorie.trim()) {
      newErrors.categorie = "La catégorie est obligatoire";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof NewProductData, value: any) => {
    setProductData((prev) => ({ ...prev, [field]: value }));
    // Supprimer l'erreur si le champ est maintenant valide
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const calculateMargin = () => {
    if (productData.prix_achat > 0 && productData.prix_vente > 0) {
      const margin =
        ((productData.prix_vente - productData.prix_achat) /
          productData.prix_vente) *
        100;
      return margin.toFixed(2);
    }
    return "0.00";
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast.error("Veuillez corriger les erreurs dans le formulaire");
      return;
    }

    setIsLoading(true);

    try {
      const response = await ProductService.createProduct({
        ...productData,
        prix_unitaire: productData.prix_vente,
        statut: "ACTIF",
      });

      if (response.data) {
        toast.success(`Produit ${productData.designation} créé avec succès !`);
        navigateBack();
      } else if (response.error) {
        toast.error(response.error);
      }
    } catch (error) {
      console.error("Erreur lors de la création du produit:", error);
      toast.error("Erreur lors de la création du produit");
    } finally {
      setIsLoading(false);
    }
  };

  const goBack = () => {
    navigateBack();
  };

  return (
    <div className="new-product-container">
      <div className="new-product-header">
        <div className="header-left">
          <button
            className="btn-back"
            onClick={goBack}>
            ← Retour
          </button>
          <h1>📦 Nouveau Produit</h1>
        </div>
        <div className="header-actions">
          <button
            className="btn btn-secondary"
            onClick={goBack}>
            Annuler
          </button>
          <button
            className="btn btn-primary"
            onClick={handleSave}>
            💾 Enregistrer
          </button>
        </div>
      </div>

      <div className="new-product-content">
        <div className="product-form">
          {/* Informations générales */}
          <div className="form-section">
            <h3>📋 Informations Générales</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>Code produit *</label>
                <input
                  type="text"
                  value={productData.code}
                  onChange={(e) => handleInputChange("code", e.target.value)}
                  className={`form-input ${errors.code ? "error" : ""}`}
                  placeholder="Code unique du produit"
                />
                {errors.code && (
                  <span className="error-message">{errors.code}</span>
                )}
              </div>

              <div className="form-group">
                <label>Désignation *</label>
                <input
                  type="text"
                  value={productData.designation}
                  onChange={(e) =>
                    handleInputChange("designation", e.target.value)
                  }
                  className={`form-input ${errors.designation ? "error" : ""}`}
                  placeholder="Nom du produit"
                />
                {errors.designation && (
                  <span className="error-message">{errors.designation}</span>
                )}
              </div>

              <div className="form-group">
                <label>Catégorie *</label>
                <select
                  value={productData.categorie}
                  onChange={(e) =>
                    handleInputChange("categorie", e.target.value)
                  }
                  className={`form-input ${errors.categorie ? "error" : ""}`}>
                  <option value="">-- Sélectionner une catégorie --</option>
                  {categories.map((cat) => (
                    <option
                      key={cat}
                      value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
                {errors.categorie && (
                  <span className="error-message">{errors.categorie}</span>
                )}
              </div>

              <div className="form-group">
                <label>Unité</label>
                <select
                  value={productData.unite}
                  onChange={(e) => handleInputChange("unite", e.target.value)}
                  className="form-input">
                  {unites.map((unite) => (
                    <option
                      key={unite}
                      value={unite}>
                      {unite}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group full-width">
                <label>Description</label>
                <textarea
                  value={productData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  className="form-textarea"
                  rows={3}
                  placeholder="Description détaillée du produit"
                />
              </div>
            </div>
          </div>

          {/* Prix et marges */}
          <div className="form-section">
            <h3>💰 Prix et Marges</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>Prix d'achat (TND)</label>
                <input
                  type="number"
                  value={productData.prix_achat}
                  onChange={(e) =>
                    handleInputChange(
                      "prix_achat",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className={`form-input ${errors.prix_achat ? "error" : ""}`}
                  min="0"
                  step="0.001"
                />
                {errors.prix_achat && (
                  <span className="error-message">{errors.prix_achat}</span>
                )}
              </div>

              <div className="form-group">
                <label>Prix de vente (TND) *</label>
                <input
                  type="number"
                  value={productData.prix_vente}
                  onChange={(e) =>
                    handleInputChange(
                      "prix_vente",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className={`form-input ${errors.prix_vente ? "error" : ""}`}
                  min="0"
                  step="0.001"
                />
                {errors.prix_vente && (
                  <span className="error-message">{errors.prix_vente}</span>
                )}
              </div>

              <div className="form-group">
                <label>Taux TVA (%)</label>
                <select
                  value={productData.tva_taux}
                  onChange={(e) =>
                    handleInputChange("tva_taux", parseFloat(e.target.value))
                  }
                  className="form-input">
                  <option value={0}>0%</option>
                  <option value={7}>7%</option>
                  <option value={13}>13%</option>
                  <option value={19}>19%</option>
                  <option value={20}>20%</option>
                </select>
              </div>

              <div className="form-group">
                <label>Marge calculée</label>
                <div className="margin-display">
                  <span className="margin-value">{calculateMargin()}%</span>
                  <span className="margin-label">
                    {parseFloat(calculateMargin()) > 30
                      ? "🟢 Bonne marge"
                      : parseFloat(calculateMargin()) > 15
                      ? "🟡 Marge correcte"
                      : "🔴 Marge faible"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Stock */}
          <div className="form-section">
            <h3>📦 Gestion du Stock</h3>
            <div className="form-grid">
              <div className="form-group">
                <label>Stock actuel</label>
                <input
                  type="number"
                  value={productData.stock_actuel}
                  onChange={(e) =>
                    handleInputChange(
                      "stock_actuel",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className={`form-input ${errors.stock_actuel ? "error" : ""}`}
                  min="0"
                  step="1"
                />
                {errors.stock_actuel && (
                  <span className="error-message">{errors.stock_actuel}</span>
                )}
              </div>

              <div className="form-group">
                <label>Stock minimum</label>
                <input
                  type="number"
                  value={productData.stock_minimum}
                  onChange={(e) =>
                    handleInputChange(
                      "stock_minimum",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className={`form-input ${
                    errors.stock_minimum ? "error" : ""
                  }`}
                  min="0"
                  step="1"
                />
                {errors.stock_minimum && (
                  <span className="error-message">{errors.stock_minimum}</span>
                )}
              </div>

              <div className="form-group">
                <label>Statut</label>
                <div className="status-toggle">
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={productData.actif}
                      onChange={(e) =>
                        handleInputChange("actif", e.target.checked)
                      }
                    />
                    <span className="toggle-slider"></span>
                  </label>
                  <span
                    className={`status-label ${
                      productData.actif ? "active" : "inactive"
                    }`}>
                    {productData.actif ? "🟢 Actif" : "🔴 Inactif"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="form-section">
            <h3>📝 Notes</h3>
            <textarea
              value={productData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              className="form-textarea"
              rows={4}
              placeholder="Notes additionnelles sur le produit..."
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewProduct;
