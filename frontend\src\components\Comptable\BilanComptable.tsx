/**
 * Bilan comptable
 */

import React, { useState, useEffect } from "react";
import { useNotify } from "../Common/NotificationSystem";

interface PosteBilan {
  code: string;
  libelle: string;
  montant_n: number;
  montant_n1: number;
  variation: number;
  pourcentage_variation: number;
}

interface BilanData {
  actif: {
    immobilisations: PosteBilan[];
    actif_circulant: PosteBilan[];
    total_actif: number;
  };
  passif: {
    capitaux_propres: PosteBilan[];
    dettes: PosteBilan[];
    total_passif: number;
  };
  compte_resultat: {
    charges: PosteBilan[];
    produits: PosteBilan[];
    resultat_net: number;
  };
  exercice: string;
  date_cloture: string;
}

const BilanComptable: React.FC = () => {
  const notify = useNotify();
  const [loading, setLoading] = useState(true);
  const [bilan, setBilan] = useState<BilanData>({
    actif: { immobilisations: [], actif_circulant: [], total_actif: 0 },
    passif: { capitaux_propres: [], dettes: [], total_passif: 0 },
    compte_resultat: { charges: [], produits: [], resultat_net: 0 },
    exercice: "",
    date_cloture: "",
  });
  const [activeTab, setActiveTab] = useState("bilan");
  const [exercice, setExercice] = useState("2025");

  useEffect(() => {
    loadBilan();
  }, [exercice]);

  const loadBilan = async () => {
    try {
      setLoading(true);

      // Simulation de données de bilan comptable
      const mockBilan: BilanData = {
        actif: {
          immobilisations: [
            {
              code: "213",
              libelle: "Installations techniques",
              montant_n: 35000,
              montant_n1: 30000,
              variation: 5000,
              pourcentage_variation: 16.7,
            },
            {
              code: "218",
              libelle: "Matériel de bureau",
              montant_n: 12000,
              montant_n1: 15000,
              variation: -3000,
              pourcentage_variation: -20.0,
            },
            {
              code: "281",
              libelle: "Amortissements",
              montant_n: -8000,
              montant_n1: -6000,
              variation: -2000,
              pourcentage_variation: 33.3,
            },
          ],
          actif_circulant: [
            {
              code: "310",
              libelle: "Stock de marchandises",
              montant_n: 125680,
              montant_n1: 98000,
              variation: 27680,
              pourcentage_variation: 28.2,
            },
            {
              code: "411",
              libelle: "Clients",
              montant_n: 45000,
              montant_n1: 38000,
              variation: 7000,
              pourcentage_variation: 18.4,
            },
            {
              code: "445",
              libelle: "TVA déductible",
              montant_n: 12000,
              montant_n1: 9500,
              variation: 2500,
              pourcentage_variation: 26.3,
            },
            {
              code: "512",
              libelle: "Banque",
              montant_n: 85000,
              montant_n1: 65000,
              variation: 20000,
              pourcentage_variation: 30.8,
            },
            {
              code: "530",
              libelle: "Caisse",
              montant_n: 5500,
              montant_n1: 4200,
              variation: 1300,
              pourcentage_variation: 31.0,
            },
          ],
          total_actif: 312180,
        },
        passif: {
          capitaux_propres: [
            {
              code: "101",
              libelle: "Capital social",
              montant_n: 50000,
              montant_n1: 50000,
              variation: 0,
              pourcentage_variation: 0,
            },
            {
              code: "106",
              libelle: "Réserves",
              montant_n: 15000,
              montant_n1: 10000,
              variation: 5000,
              pourcentage_variation: 50.0,
            },
            {
              code: "120",
              libelle: "Résultat de l'exercice",
              montant_n: 26000,
              montant_n1: 22000,
              variation: 4000,
              pourcentage_variation: 18.2,
            },
          ],
          dettes: [
            {
              code: "401",
              libelle: "Fournisseurs",
              montant_n: 28000,
              montant_n1: 35000,
              variation: -7000,
              pourcentage_variation: -20.0,
            },
            {
              code: "437",
              libelle: "TVA collectée",
              montant_n: 18500,
              montant_n1: 15200,
              variation: 3300,
              pourcentage_variation: 21.7,
            },
            {
              code: "421",
              libelle: "Personnel",
              montant_n: 8500,
              montant_n1: 7800,
              variation: 700,
              pourcentage_variation: 9.0,
            },
            {
              code: "164",
              libelle: "Emprunts bancaires",
              montant_n: 166180,
              montant_n1: 180000,
              variation: -13820,
              pourcentage_variation: -7.7,
            },
          ],
          total_passif: 312180,
        },
        compte_resultat: {
          charges: [
            {
              code: "607",
              libelle: "Achats de marchandises",
              montant_n: 180000,
              montant_n1: 165000,
              variation: 15000,
              pourcentage_variation: 9.1,
            },
            {
              code: "613",
              libelle: "Locations",
              montant_n: 24000,
              montant_n1: 22000,
              variation: 2000,
              pourcentage_variation: 9.1,
            },
            {
              code: "621",
              libelle: "Personnel",
              montant_n: 45000,
              montant_n1: 42000,
              variation: 3000,
              pourcentage_variation: 7.1,
            },
            {
              code: "626",
              libelle: "Services extérieurs",
              montant_n: 8500,
              montant_n1: 7200,
              variation: 1300,
              pourcentage_variation: 18.1,
            },
            {
              code: "681",
              libelle: "Dotations amortissements",
              montant_n: 4000,
              montant_n1: 3500,
              variation: 500,
              pourcentage_variation: 14.3,
            },
          ],
          produits: [
            {
              code: "707",
              libelle: "Ventes de marchandises",
              montant_n: 285000,
              montant_n1: 245000,
              variation: 40000,
              pourcentage_variation: 16.3,
            },
            {
              code: "758",
              libelle: "Produits divers",
              montant_n: 2500,
              montant_n1: 1800,
              variation: 700,
              pourcentage_variation: 38.9,
            },
          ],
          resultat_net: 26000,
        },
        exercice: "2025",
        date_cloture: "31/12/2025",
      };

      setBilan(mockBilan);
    } catch (error: any) {
      notify.error("Erreur lors du chargement du bilan");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  const getVariationColor = (variation: number) => {
    if (variation > 0) return "positive";
    if (variation < 0) return "negative";
    return "neutral";
  };

  if (loading) {
    return (
      <div className="bilan-loading">
        <div className="loading-spinner"></div>
        <p>Chargement du bilan...</p>
      </div>
    );
  }

  return (
    <div className="bilan-comptable">
      <div className="bilan-header">
        <div className="header-content">
          <div className="company-info">
            <img
              src="/logo_ben_chaabene_moderne.png"
              alt="Logo"
              className="company-logo"
            />
            <div>
              <h1>📋 Bilan Comptable</h1>
              <h2>Société Ben Chaabene de Commerce</h2>
              <p>Exercice clos le {bilan.date_cloture}</p>
            </div>
          </div>
        </div>

        <div className="bilan-controls">
          <div className="exercice-selector">
            <label>Exercice:</label>
            <select
              value={exercice}
              onChange={(e) => setExercice(e.target.value)}>
              <option value="2025">2025</option>
              <option value="2024">2024</option>
              <option value="2023">2023</option>
            </select>
          </div>
        </div>
      </div>

      {/* Onglets */}
      <div className="bilan-tabs">
        <button
          className={`tab-button ${activeTab === "bilan" ? "active" : ""}`}
          onClick={() => setActiveTab("bilan")}>
          📊 Bilan
        </button>
        <button
          className={`tab-button ${activeTab === "resultat" ? "active" : ""}`}
          onClick={() => setActiveTab("resultat")}>
          📈 Compte de résultat
        </button>
      </div>

      {activeTab === "bilan" && (
        <div className="bilan-content">
          <div className="bilan-grid">
            {/* ACTIF */}
            <div className="bilan-section actif">
              <h3>💼 ACTIF</h3>

              <div className="bilan-subsection">
                <h4>Immobilisations</h4>
                <table className="bilan-table">
                  <thead>
                    <tr>
                      <th>Poste</th>
                      <th>N</th>
                      <th>N-1</th>
                      <th>Variation</th>
                    </tr>
                  </thead>
                  <tbody>
                    {bilan.actif.immobilisations.map((poste, index) => (
                      <tr key={index}>
                        <td>{poste.libelle}</td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n)}
                        </td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n1)}
                        </td>
                        <td
                          className={`montant variation ${getVariationColor(
                            poste.variation
                          )}`}>
                          {formatCurrency(poste.variation)} (
                          {poste.pourcentage_variation.toFixed(1)}%)
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="bilan-subsection">
                <h4>Actif circulant</h4>
                <table className="bilan-table">
                  <thead>
                    <tr>
                      <th>Poste</th>
                      <th>N</th>
                      <th>N-1</th>
                      <th>Variation</th>
                    </tr>
                  </thead>
                  <tbody>
                    {bilan.actif.actif_circulant.map((poste, index) => (
                      <tr key={index}>
                        <td>{poste.libelle}</td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n)}
                        </td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n1)}
                        </td>
                        <td
                          className={`montant variation ${getVariationColor(
                            poste.variation
                          )}`}>
                          {formatCurrency(poste.variation)} (
                          {poste.pourcentage_variation.toFixed(1)}%)
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="total-section">
                <div className="total-item">
                  <strong>
                    TOTAL ACTIF: {formatCurrency(bilan.actif.total_actif)}
                  </strong>
                </div>
              </div>
            </div>

            {/* PASSIF */}
            <div className="bilan-section passif">
              <h3>🏛️ PASSIF</h3>

              <div className="bilan-subsection">
                <h4>Capitaux propres</h4>
                <table className="bilan-table">
                  <thead>
                    <tr>
                      <th>Poste</th>
                      <th>N</th>
                      <th>N-1</th>
                      <th>Variation</th>
                    </tr>
                  </thead>
                  <tbody>
                    {bilan.passif.capitaux_propres.map((poste, index) => (
                      <tr key={index}>
                        <td>{poste.libelle}</td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n)}
                        </td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n1)}
                        </td>
                        <td
                          className={`montant variation ${getVariationColor(
                            poste.variation
                          )}`}>
                          {formatCurrency(poste.variation)} (
                          {poste.pourcentage_variation.toFixed(1)}%)
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="bilan-subsection">
                <h4>Dettes</h4>
                <table className="bilan-table">
                  <thead>
                    <tr>
                      <th>Poste</th>
                      <th>N</th>
                      <th>N-1</th>
                      <th>Variation</th>
                    </tr>
                  </thead>
                  <tbody>
                    {bilan.passif.dettes.map((poste, index) => (
                      <tr key={index}>
                        <td>{poste.libelle}</td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n)}
                        </td>
                        <td className="montant">
                          {formatCurrency(poste.montant_n1)}
                        </td>
                        <td
                          className={`montant variation ${getVariationColor(
                            poste.variation
                          )}`}>
                          {formatCurrency(poste.variation)} (
                          {poste.pourcentage_variation.toFixed(1)}%)
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="total-section">
                <div className="total-item">
                  <strong>
                    TOTAL PASSIF: {formatCurrency(bilan.passif.total_passif)}
                  </strong>
                </div>
              </div>
            </div>
          </div>

          {/* Vérification équilibre */}
          <div className="equilibre-check">
            <div
              className={`equilibre-status ${
                bilan.actif.total_actif === bilan.passif.total_passif
                  ? "equilibre"
                  : "desequilibre"
              }`}>
              {bilan.actif.total_actif === bilan.passif.total_passif ? (
                <span>✅ Bilan équilibré</span>
              ) : (
                <span>
                  ❌ Bilan déséquilibré - Écart:{" "}
                  {formatCurrency(
                    Math.abs(
                      bilan.actif.total_actif - bilan.passif.total_passif
                    )
                  )}
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === "resultat" && (
        <div className="resultat-content">
          <div className="resultat-grid">
            {/* CHARGES */}
            <div className="resultat-section charges">
              <h3>📉 CHARGES</h3>
              <table className="resultat-table">
                <thead>
                  <tr>
                    <th>Poste</th>
                    <th>N</th>
                    <th>N-1</th>
                    <th>Variation</th>
                  </tr>
                </thead>
                <tbody>
                  {bilan.compte_resultat.charges.map((poste, index) => (
                    <tr key={index}>
                      <td>{poste.libelle}</td>
                      <td className="montant">
                        {formatCurrency(poste.montant_n)}
                      </td>
                      <td className="montant">
                        {formatCurrency(poste.montant_n1)}
                      </td>
                      <td
                        className={`montant variation ${getVariationColor(
                          poste.variation
                        )}`}>
                        {formatCurrency(poste.variation)} (
                        {poste.pourcentage_variation.toFixed(1)}%)
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* PRODUITS */}
            <div className="resultat-section produits">
              <h3>📈 PRODUITS</h3>
              <table className="resultat-table">
                <thead>
                  <tr>
                    <th>Poste</th>
                    <th>N</th>
                    <th>N-1</th>
                    <th>Variation</th>
                  </tr>
                </thead>
                <tbody>
                  {bilan.compte_resultat.produits.map((poste, index) => (
                    <tr key={index}>
                      <td>{poste.libelle}</td>
                      <td className="montant">
                        {formatCurrency(poste.montant_n)}
                      </td>
                      <td className="montant">
                        {formatCurrency(poste.montant_n1)}
                      </td>
                      <td
                        className={`montant variation ${getVariationColor(
                          poste.variation
                        )}`}>
                        {formatCurrency(poste.variation)} (
                        {poste.pourcentage_variation.toFixed(1)}%)
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Résultat net */}
          <div className="resultat-net">
            <div
              className={`resultat-final ${
                bilan.compte_resultat.resultat_net >= 0 ? "benefice" : "perte"
              }`}>
              <h3>
                {bilan.compte_resultat.resultat_net >= 0
                  ? "💰 Bénéfice"
                  : "📉 Perte"}
                :{formatCurrency(Math.abs(bilan.compte_resultat.resultat_net))}
              </h3>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="bilan-actions">
        <button
          className="btn-secondary"
          onClick={() => window.print()}>
          🖨️ Imprimer
        </button>
        <button
          className="btn-primary"
          onClick={() => notify.info("Export en cours...")}>
          📤 Exporter Excel
        </button>
        <button
          className="btn-success"
          onClick={() => notify.info("Export en cours...")}>
          📄 Exporter PDF
        </button>
        <button
          className="btn-info"
          onClick={() => notify.info("Génération en cours...")}>
          📋 Liasse fiscale
        </button>
      </div>
    </div>
  );
};

export default BilanComptable;
