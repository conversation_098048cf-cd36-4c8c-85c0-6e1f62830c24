/**
 * Composant de test d'intégration complète Frontend-Backend
 */

import React, { useState } from "react";
import {
  AuthService,
  ClientService,
  ProductService,
  InvoiceService,
  OrderService,
  PaymentService,
  DashboardService,
  EcommerceService,
} from "../../services/apiService";
import { toast } from "react-toastify";

interface TestScenario {
  name: string;
  description: string;
  steps: TestStep[];
}

interface TestStep {
  name: string;
  action: () => Promise<any>;
  expectedResult?: string;
}

interface TestResult {
  scenario: string;
  step: string;
  status: "pending" | "success" | "error";
  message: string;
  duration?: number;
}

const IntegrationTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [currentScenario, setCurrentScenario] = useState<string | null>(null);

  const testScenarios: TestScenario[] = [
    {
      name: "Authentification",
      description: "Test du processus de connexion/déconnexion",
      steps: [
        {
          name: "Récupération du profil utilisateur",
          action: () => AuthService.getProfile(),
          expectedResult: "Profil utilisateur récupéré",
        },
      ],
    },
    {
      name: "Gestion des Clients",
      description: "Test CRUD complet des clients",
      steps: [
        {
          name: "Liste des clients",
          action: () => ClientService.getClients({ page_size: 5 }),
          expectedResult: "Liste des clients récupérée",
        },
        {
          name: "Statistiques clients",
          action: () => ClientService.getClientStats(),
          expectedResult: "Statistiques clients récupérées",
        },
      ],
    },
    {
      name: "Catalogue Produits",
      description: "Test de gestion des produits",
      steps: [
        {
          name: "Liste des produits",
          action: () => ProductService.getProducts({ page_size: 5 }),
          expectedResult: "Liste des produits récupérée",
        },
        {
          name: "Catégories de produits",
          action: () => ProductService.getCategories(),
          expectedResult: "Catégories récupérées",
        },
        {
          name: "Alertes de stock",
          action: () => ProductService.getStockAlerts(),
          expectedResult: "Alertes de stock récupérées",
        },
      ],
    },
    {
      name: "Facturation",
      description: "Test du système de facturation",
      steps: [
        {
          name: "Liste des factures",
          action: () => InvoiceService.getInvoices({ page_size: 5 }),
          expectedResult: "Liste des factures récupérée",
        },
        {
          name: "Statistiques factures",
          action: () => InvoiceService.getInvoiceStats(),
          expectedResult: "Statistiques factures récupérées",
        },
      ],
    },
    {
      name: "Commandes",
      description: "Test de gestion des commandes",
      steps: [
        {
          name: "Liste des commandes",
          action: () => OrderService.getOrders({ page_size: 5 }),
          expectedResult: "Liste des commandes récupérée",
        },
        {
          name: "Statistiques commandes",
          action: () => OrderService.getOrderStats(),
          expectedResult: "Statistiques commandes récupérées",
        },
      ],
    },
    {
      name: "Paiements",
      description: "Test de gestion des paiements",
      steps: [
        {
          name: "Liste des paiements",
          action: () => PaymentService.getPayments({ page_size: 5 }),
          expectedResult: "Liste des paiements récupérée",
        },
        {
          name: "Statistiques paiements",
          action: () => PaymentService.getPaymentStats(),
          expectedResult: "Statistiques paiements récupérées",
        },
      ],
    },
    {
      name: "E-commerce",
      description: "Test du module e-commerce",
      steps: [
        {
          name: "Catalogue e-commerce",
          action: () => EcommerceService.getCatalog({ page_size: 5 }),
          expectedResult: "Catalogue e-commerce récupéré",
        },
        {
          name: "Panier utilisateur",
          action: () => EcommerceService.getCart(),
          expectedResult: "Panier récupéré",
        },
        {
          name: "Catégories e-commerce",
          action: () => EcommerceService.getCategories(),
          expectedResult: "Catégories e-commerce récupérées",
        },
      ],
    },
    {
      name: "Dashboard",
      description: "Test des données du tableau de bord",
      steps: [
        {
          name: "KPIs généraux",
          action: () => DashboardService.getStats(),
          expectedResult: "KPIs récupérés",
        },
        {
          name: "Notifications",
          action: () => DashboardService.getNotifications(),
          expectedResult: "Notifications récupérées",
        },
        {
          name: "Insights IA",
          action: () => DashboardService.getInsights(),
          expectedResult: "Insights IA récupérés",
        },
      ],
    },
  ];

  const runAllTests = async () => {
    setTesting(true);
    setResults([]);

    for (const scenario of testScenarios) {
      setCurrentScenario(scenario.name);

      for (const step of scenario.steps) {
        const startTime = Date.now();

        try {
          const result: TestResult = {
            scenario: scenario.name,
            step: step.name,
            status: "pending",
            message: "Test en cours...",
          };

          setResults((prev) => [...prev, result]);

          const response = await step.action();
          const duration = Date.now() - startTime;

          const updatedResult: TestResult = {
            ...result,
            status: response.error ? "error" : "success",
            message: response.error || step.expectedResult || "Test réussi",
            duration,
          };

          setResults((prev) =>
            prev.map((r) =>
              r.scenario === scenario.name && r.step === step.name
                ? updatedResult
                : r
            )
          );
        } catch (error: any) {
          const duration = Date.now() - startTime;
          const errorResult: TestResult = {
            scenario: scenario.name,
            step: step.name,
            status: "error",
            message: error.message || "Erreur inconnue",
            duration,
          };

          setResults((prev) =>
            prev.map((r) =>
              r.scenario === scenario.name && r.step === step.name
                ? errorResult
                : r
            )
          );
        }
      }
    }

    setCurrentScenario(null);
    setTesting(false);

    // Afficher un résumé
    const successCount = results.filter((r) => r.status === "success").length;
    const totalCount = results.length;

    if (successCount === totalCount) {
      toast.success(
        `Intégration complète réussie! (${successCount}/${totalCount})`
      );
    } else {
      toast.warning(
        `Intégration partielle: ${successCount}/${totalCount} tests réussis`
      );
    }
  };

  const runScenario = async (scenario: TestScenario) => {
    setTesting(true);
    setCurrentScenario(scenario.name);

    for (const step of scenario.steps) {
      const startTime = Date.now();

      try {
        const response = await step.action();
        const duration = Date.now() - startTime;

        const result: TestResult = {
          scenario: scenario.name,
          step: step.name,
          status: response.error ? "error" : "success",
          message: response.error || step.expectedResult || "Test réussi",
          duration,
        };

        setResults((prev) => [...prev, result]);
      } catch (error: any) {
        const duration = Date.now() - startTime;
        const result: TestResult = {
          scenario: scenario.name,
          step: step.name,
          status: "error",
          message: error.message || "Erreur inconnue",
          duration,
        };

        setResults((prev) => [...prev, result]);
      }
    }

    setCurrentScenario(null);
    setTesting(false);
  };

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "pending":
        return "⏳";
      case "success":
        return "✅";
      case "error":
        return "❌";
      default:
        return "❓";
    }
  };

  const getStatusColor = (status: TestResult["status"]) => {
    switch (status) {
      case "pending":
        return "#ffa500";
      case "success":
        return "#28a745";
      case "error":
        return "#dc3545";
      default:
        return "#6c757d";
    }
  };

  return (
    <div className="integration-test">
      <div className="test-header">
        <h2>🔗 Test d'Intégration Frontend-Backend</h2>
        <p>
          Vérifiez que tous les modules communiquent correctement avec le
          backend Django
        </p>
      </div>

      <div className="test-actions">
        <button
          onClick={runAllTests}
          disabled={testing}
          className="btn-primary">
          {testing ? "Tests en cours..." : "🚀 Lancer tous les tests"}
        </button>

        {currentScenario && (
          <div className="current-test">
            Test en cours: <strong>{currentScenario}</strong>
          </div>
        )}
      </div>

      <div className="scenarios-grid">
        {testScenarios.map((scenario) => (
          <div
            key={scenario.name}
            className="scenario-card">
            <div className="scenario-header">
              <h3>{scenario.name}</h3>
              <p>{scenario.description}</p>
            </div>

            <div className="scenario-steps">
              {scenario.steps.map((step, index) => {
                const result = results.find(
                  (r) => r.scenario === scenario.name && r.step === step.name
                );

                return (
                  <div
                    key={index}
                    className="step-item">
                    <span className="step-icon">
                      {result ? getStatusIcon(result.status) : "⚪"}
                    </span>
                    <span className="step-name">{step.name}</span>
                    {result?.duration && (
                      <span className="step-duration">{result.duration}ms</span>
                    )}
                  </div>
                );
              })}
            </div>

            <button
              onClick={() => runScenario(scenario)}
              disabled={testing}
              className="btn-secondary">
              Tester ce module
            </button>
          </div>
        ))}
      </div>

      {results.length > 0 && (
        <div className="test-results">
          <h3>Résultats détaillés</h3>

          <div className="results-list">
            {results.map((result, index) => (
              <div
                key={index}
                className="result-item"
                style={{
                  borderLeft: `4px solid ${getStatusColor(result.status)}`,
                }}>
                <div className="result-header">
                  <span className="result-icon">
                    {getStatusIcon(result.status)}
                  </span>
                  <strong>{result.scenario}</strong>
                  <span className="result-step">{result.step}</span>
                  {result.duration && (
                    <span className="result-duration">{result.duration}ms</span>
                  )}
                </div>

                <div className="result-message">{result.message}</div>
              </div>
            ))}
          </div>

          <div className="test-summary">
            <h4>Résumé</h4>
            <div className="summary-stats">
              <span className="stat success">
                ✅ Réussis:{" "}
                {results.filter((r) => r.status === "success").length}
              </span>
              <span className="stat error">
                ❌ Échecs: {results.filter((r) => r.status === "error").length}
              </span>
              <span className="stat pending">
                ⏳ En cours:{" "}
                {results.filter((r) => r.status === "pending").length}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntegrationTest;
