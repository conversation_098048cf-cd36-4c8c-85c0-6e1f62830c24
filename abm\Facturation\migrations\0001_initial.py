# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Clients', '0001_initial'),
        ('Produits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConfigurationFacturation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                ('nom_societe', models.CharField(max_length=200, verbose_name='Nom de la société')),
                ('adresse_ligne1', models.CharField(max_length=200, verbose_name='Adresse ligne 1')),
                ('adresse_ligne2', models.CharField(blank=True, max_length=200, verbose_name='Adresse ligne 2')),
                ('code_postal', models.CharField(max_length=10, verbose_name='Code postal')),
                ('ville', models.CharField(max_length=100, verbose_name='Ville')),
                ('pays', models.CharField(default='France', max_length=100)),
                ('siret', models.CharField(max_length=20, verbose_name='SIRET')),
                ('numero_tva', models.CharField(max_length=20, verbose_name='Numéro TVA')),
                ('code_ape', models.CharField(max_length=10, verbose_name='Code APE')),
                ('capital_social', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('telephone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('site_web', models.URLField(blank=True)),
                ('numerotation_auto', models.BooleanField(default=True)),
                ('prefixe_facture', models.CharField(default='F', max_length=10)),
                ('prefixe_devis', models.CharField(default='D', max_length=10)),
                ('compteur_facture', models.IntegerField(default=1)),
                ('compteur_devis', models.IntegerField(default=1)),
                ('conditions_paiement', models.TextField(default='Paiement à 30 jours net. Pénalités de retard : 3 fois le taux légal.')),
                ('mentions_legales', models.TextField(default='TVA non applicable, art. 293 B du CGI')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='facturation/logos/')),
                ('couleur_principale', models.CharField(default='#3498db', max_length=7)),
                ('couleur_secondaire', models.CharField(default='#2c3e50', max_length=7)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Configuration Facturation',
                'verbose_name_plural': 'Configurations Facturation',
            },
        ),
        migrations.CreateModel(
            name='Facture',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('numero', models.CharField(max_length=50, unique=True, verbose_name='Numéro')),
                ('type_document', models.CharField(choices=[('FACTURE', 'Facture'), ('DEVIS', 'Devis'), ('AVOIR', 'Avoir'), ('PROFORMA', 'Pro forma')], default='FACTURE', max_length=20)),
                ('date_facture', models.DateField(default=django.utils.timezone.now, verbose_name='Date de facturation')),
                ('date_echeance', models.DateField(verbose_name="Date d'échéance")),
                ('date_envoi_email', models.DateTimeField(blank=True, null=True)),
                ('montant_ht', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Montant HT')),
                ('montant_tva', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Montant TVA')),
                ('montant_ttc', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Montant TTC')),
                ('remise_pourcentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00')), django.core.validators.MaxValueValidator(Decimal('100.00'))])),
                ('remise_montant', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('statut', models.CharField(choices=[('BROUILLON', 'Brouillon'), ('ENVOYEE', 'Envoyée'), ('PAYEE', 'Payée'), ('PARTIELLEMENT_PAYEE', 'Partiellement payée'), ('EN_RETARD', 'En retard'), ('ANNULEE', 'Annulée')], default='BROUILLON', max_length=30)),
                ('notes_internes', models.TextField(blank=True, verbose_name='Notes internes')),
                ('conditions_particulieres', models.TextField(blank=True, verbose_name='Conditions particulières')),
                ('fichier_pdf', models.FileField(blank=True, null=True, upload_to='facturation/pdf/')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='factures', to='Clients.client', verbose_name='Client')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Facture',
                'verbose_name_plural': 'Factures',
                'ordering': ['-date_facture', '-numero'],
            },
        ),
        migrations.CreateModel(
            name='LigneFacture',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('code_article', models.CharField(max_length=50, verbose_name='Code Article')),
                ('designation', models.CharField(max_length=500, verbose_name='Désignation')),
                ('quantite', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Qté')),
                ('prix_unitaire_ht', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='P.U H.T')),
                ('remise_pourcentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00')), django.core.validators.MaxValueValidator(Decimal('100.00'))], verbose_name='Rem %')),
                ('prix_unitaire_ttc', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='P.U T.T.C')),
                ('montant_ht', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Mt Ht H.T')),
                ('taux_tva', models.DecimalField(decimal_places=2, default=Decimal('20.00'), max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0.00')), django.core.validators.MaxValueValidator(Decimal('100.00'))], verbose_name='TVA %')),
                ('montant_tva', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Montant TVA')),
                ('montant_ttc', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Montant TTC')),
                ('ordre', models.PositiveIntegerField(default=0)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('facture', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lignes', to='Facturation.facture', verbose_name='Facture')),
                ('produit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Produits.produit', verbose_name='Produit')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Ligne de Facture',
                'verbose_name_plural': 'Lignes de Factures',
                'ordering': ['ordre', 'id'],
            },
        ),
        migrations.CreateModel(
            name='RelanceFacture',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('type_relance', models.CharField(choices=[('RELANCE_1', 'Première relance'), ('RELANCE_2', 'Deuxième relance'), ('RELANCE_3', 'Troisième relance'), ('MISE_EN_DEMEURE', 'Mise en demeure')], max_length=20)),
                ('date_relance', models.DateTimeField(auto_now_add=True)),
                ('date_prevue', models.DateField()),
                ('objet', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('envoyee', models.BooleanField(default=False)),
                ('date_envoi', models.DateTimeField(blank=True, null=True)),
                ('email_envoye', models.EmailField(blank=True, max_length=254)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('facture', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='relances', to='Facturation.facture')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Relance de Facture',
                'verbose_name_plural': 'Relances de Factures',
                'ordering': ['-date_relance'],
            },
        ),
        migrations.CreateModel(
            name='TemplateFacture',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('nom', models.CharField(max_length=100, verbose_name='Nom du template')),
                ('type_document', models.CharField(choices=[('FACTURE', 'Facture'), ('DEVIS', 'Devis'), ('AVOIR', 'Avoir'), ('PROFORMA', 'Pro forma')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('template_html', models.TextField(verbose_name='Template HTML')),
                ('template_css', models.TextField(blank=True, verbose_name='CSS personnalisé')),
                ('actif', models.BooleanField(default=True)),
                ('par_defaut', models.BooleanField(default=False)),
                ('afficher_logo', models.BooleanField(default=True)),
                ('afficher_conditions', models.BooleanField(default=True)),
                ('afficher_mentions', models.BooleanField(default=True)),
                ('afficher_signature', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Template de Facture',
                'verbose_name_plural': 'Templates de Factures',
                'unique_together': {('type_document', 'par_defaut')},
            },
        ),
        migrations.AddField(
            model_name='facture',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Facturation.templatefacture', verbose_name='Template utilisé'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['numero'], name='Facturation_numero_e91ac1_idx'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['statut'], name='Facturation_statut_090301_idx'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['client'], name='Facturation_client__c3f91b_idx'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['date_facture'], name='Facturation_date_fa_adb952_idx'),
        ),
        migrations.AddIndex(
            model_name='facture',
            index=models.Index(fields=['date_echeance'], name='Facturation_date_ec_87824b_idx'),
        ),
    ]
