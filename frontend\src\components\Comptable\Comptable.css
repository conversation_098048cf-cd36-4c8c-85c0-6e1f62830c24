/* Styles pour le module Comptabilité */

/* === LAYOUT GÉNÉRAL === */
.comptable-module {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* === HEADER === */
.comptable-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-title h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.header-title p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* === NAVIGATION === */
.comptable-nav {
  display: flex;
  gap: 4px;
  background: white;
  padding: 8px;
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow-x: auto;
}

.nav-tab {
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.nav-tab:hover {
  background: #f8fafc;
  color: #475569;
}

.nav-tab.active {
  background: #3498db;
  color: white;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* === CONTENU === */
.comptable-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.comptabilite-general,
.journal-ventes,
.journal-achats,
.grand-livre,
.balance,
.bilan-comptable,
.tva-declaration,
.export-comptable {
  padding: 30px;
}

/* === KPI GRID === */
.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.kpi-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #3498db;
  transition: transform 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.kpi-card.revenue {
  border-left-color: #27ae60;
}

.kpi-card.tva {
  border-left-color: #f39c12;
}

.kpi-card.receivables {
  border-left-color: #e74c3c;
}

.kpi-card.treasury {
  border-left-color: #3498db;
}

.kpi-card.profit {
  border-left-color: #9b59b6;
}

.kpi-card.actions {
  border-left-color: #34495e;
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.kpi-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.kpi-period {
  font-size: 0.8rem;
  color: #64748b;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 6px;
}

.kpi-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.kpi-trend.positive {
  color: #27ae60;
}

.kpi-trend.negative {
  color: #e74c3c;
}

.trend-icon {
  font-size: 1rem;
}

.kpi-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  font-size: 0.9rem;
  color: #64748b;
}

.kpi-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e74c3c;
  font-size: 0.9rem;
  font-weight: 500;
}

.alert-icon {
  font-size: 1rem;
}

/* === ACTIONS RAPIDES === */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  padding: 12px 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-2px);
}

/* === FILTRES PÉRIODE === */
.periode-filters {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.periode-filters h3 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.filter-buttons {
  display: flex;
  gap: 8px;
}

.filter-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.filter-btn.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

/* === GRAPHIQUES === */
.comptable-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 40px;
}

.chart-section {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.chart-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
}

.chart-placeholder {
  height: 200px;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.chart-info span {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.chart-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #64748b;
}

/* === ALERTES === */
.comptable-alerts {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 40px;
}

.comptable-alerts h3 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.alert-item.warning {
  background: #fef3c7;
  border-color: #fbbf24;
}

.alert-item.info {
  background: #dbeafe;
  border-color: #60a5fa;
}

.alert-item.success {
  background: #dcfce7;
  border-color: #4ade80;
}

.alert-icon {
  font-size: 1.5rem;
}

.alert-content {
  flex: 1;
}

.alert-content h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.alert-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #64748b;
}

.alert-action {
  padding: 8px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
  transition: all 0.3s ease;
}

.alert-action:hover {
  background: #f8fafc;
  transform: translateY(-1px);
}

/* === RACCOURCIS === */
.comptable-shortcuts {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.comptable-shortcuts h3 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.shortcut-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.shortcut-card:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.shortcut-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.shortcut-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

/* === JOURNAL === */
.journal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.journal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
}

.journal-filters {
  display: flex;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #64748b;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.9rem;
}

/* === TOTAUX === */
.journal-totaux {
  display: flex;
  gap: 24px;
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.total-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.total-item label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
}

.total-item span {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
}

.total-item.final span {
  color: #27ae60;
  font-size: 1.3rem;
}

/* === TABLEAUX === */
.journal-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.journal-table {
  width: 100%;
  border-collapse: collapse;
}

.journal-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e2e8f0;
  font-size: 0.9rem;
}

.journal-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.9rem;
}

.numero-facture .link-button {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
}

.montant {
  text-align: right;
  font-weight: 500;
}

.montant.total {
  font-weight: 700;
  color: #27ae60;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-paid {
  background: #dcfce7;
  color: #16a34a;
}

.status-sent {
  background: #dbeafe;
  color: #2563eb;
}

.status-draft {
  background: #f1f5f9;
  color: #64748b;
}

.status-overdue {
  background: #fee2e2;
  color: #dc2626;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* === COMING SOON === */
.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #64748b;
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.6;
}

.coming-soon h3 {
  margin: 0 0 16px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
}

.coming-soon p {
  margin: 0 0 16px 0;
  font-size: 1rem;
}

.coming-soon ul {
  text-align: left;
  max-width: 300px;
  margin: 0;
  padding: 0;
  list-style: none;
}

.coming-soon li {
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.coming-soon li:last-child {
  border-bottom: none;
}

/* === BOUTONS === */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn.btn-primary {
  background: #3498db;
  color: white;
}

.btn.btn-outline {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn.btn-sm {
  padding: 8px 16px;
  font-size: 0.8rem;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .comptable-module {
    padding: 15px;
  }

  .comptable-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .comptable-nav {
    justify-content: flex-start;
    padding: 4px;
  }

  .nav-tab {
    padding: 10px 16px;
    font-size: 0.8rem;
  }

  .kpi-grid {
    grid-template-columns: 1fr;
  }

  .comptable-charts {
    grid-template-columns: 1fr;
  }

  .journal-filters {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .journal-totaux {
    flex-direction: column;
    gap: 16px;
  }

  .shortcuts-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}
