/* Styles pour le module Paiements */

.payments-container {
  padding: 20px;
}

.payments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.payments-title {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.payments-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.payments-tab {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.payments-tab.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.payments-tab:hover {
  color: #007bff;
}

.payments-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.payment-item {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 15px;
  transition: all 0.2s;
}

.payment-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.payment-amount {
  font-weight: 600;
  color: #28a745;
  font-size: 1.1rem;
}

.payment-details {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.payment-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-failed {
  background: #f8d7da;
  color: #721c24;
}

/* === MODAL DÉTAILS DE PAIEMENT === */
.payment-details-modal {
  max-width: 800px;
  width: 90%;
}

.payment-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.detail-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

.detail-value {
  color: #2d3748;
  font-weight: 500;
  text-align: right;
}

.amount-display {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.amount-value {
  font-size: 2rem;
  font-weight: 700;
  display: block;
}

.notes-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  color: #4a5568;
  line-height: 1.5;
  font-style: italic;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.valide {
  background: #dcfce7;
  color: #16a34a;
}

.status.en_attente {
  background: #fef3c7;
  color: #d97706;
}

.status.refuse {
  background: #fee2e2;
  color: #dc2626;
}

/* Responsive */
@media (max-width: 768px) {
  .payments-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .payments-tabs {
    flex-wrap: wrap;
  }

  .payments-tab {
    flex: 1;
    min-width: 120px;
  }

  .payment-details-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .detail-section {
    padding: 15px;
  }

  .amount-display {
    padding: 15px;
  }

  .amount-value {
    font-size: 1.5rem;
  }
}
