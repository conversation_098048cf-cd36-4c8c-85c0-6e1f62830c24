"""
URLs pour l'authentification
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

from . import views

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'users', views.UserManagementViewSet, basename='user')

app_name = 'authentication'

urlpatterns = [
    # Authentification JWT
    path('login/', views.CustomTokenObtainPairView.as_view(), name='login'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('logout/', views.logout_user, name='logout'),
    
    # Inscription et profil
    path('register/', views.register_user, name='register'),
    path('profile/', views.user_profile, name='profile'),
    path('profile/update/', views.update_profile, name='update_profile'),
    path('change-password/', views.change_password, name='change_password'),

    # Récupération de mot de passe
    path('forgot-password/', views.forgot_password, name='forgot_password'),
    path('verify-reset-code/', views.verify_reset_code, name='verify_reset_code'),
    path('reset-password/', views.reset_password, name='reset_password'),
    
    # Gestion des utilisateurs (Admin)
    path('', include(router.urls)),
    
    # Permissions et sécurité
    path('check-permissions/', views.check_permissions, name='check_permissions'),
    path('login-attempts/', views.login_attempts, name='login_attempts'),
]
