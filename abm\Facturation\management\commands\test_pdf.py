"""
Commande Django pour tester la génération de PDF
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from Facturation.models import Categorie, Produit, Client, Facture, LigneFacture
from Facturation.pdf_generator_tunisian import TunisianFacturePDFGenerator


class Command(BaseCommand):
    help = 'Teste la génération de PDF pour les factures'

    def add_arguments(self, parser):
        parser.add_argument(
            '--facture-id',
            type=int,
            help='ID de la facture à convertir en PDF'
        )
        parser.add_argument(
            '--create-sample',
            action='store_true',
            help='Crée une facture d\'exemple pour tester'
        )

    def handle(self, *args, **options):
        if options['create_sample']:
            self.create_sample_facture()
        
        facture_id = options.get('facture_id')
        if facture_id:
            self.generate_pdf_for_facture(facture_id)
        else:
            # Générer PDF pour la dernière facture
            last_facture = Facture.objects.last()
            if last_facture:
                self.generate_pdf_for_facture(last_facture.id)
            else:
                self.stdout.write(
                    self.style.WARNING('Aucune facture trouvée. Utilisez --create-sample pour créer une facture d\'exemple.')
                )

    def create_sample_facture(self):
        """Crée une facture d'exemple pour tester"""
        self.stdout.write('Création d\'une facture d\'exemple...')
        
        # Créer ou récupérer une catégorie
        categorie, created = Categorie.objects.get_or_create(
            nom='Produits de test',
            defaults={'description': 'Catégorie pour les tests PDF'}
        )
        
        # Créer ou récupérer des produits
        produit1, created = Produit.objects.get_or_create(
            code_produit='TEST-PDF-001',
            defaults={
                'nom': 'Produit de test 1',
                'description': 'Premier produit pour tester la génération PDF',
                'prix_unitaire': Decimal('25.00'),
                'taux_tva': Decimal('20.00'),
                'categorie': categorie
            }
        )
        
        produit2, created = Produit.objects.get_or_create(
            code_produit='TEST-PDF-002',
            defaults={
                'nom': 'Produit de test 2',
                'description': 'Deuxième produit pour tester la génération PDF',
                'prix_unitaire': Decimal('15.50'),
                'taux_tva': Decimal('10.00'),
                'categorie': categorie
            }
        )
        
        # Créer ou récupérer un client
        client, created = Client.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'nom': 'Dupont',
                'prenom': 'Jean',
                'telephone': '01 23 45 67 89',
                'adresse_ligne1': '123 Rue de la Paix',
                'ville': 'Paris',
                'code_postal': '75001',
                'type_client': 'PARTICULIER'
            }
        )
        
        # Créer une facture
        facture = Facture.objects.create(
            client=client,
            date_emission=timezone.now().date(),
            date_echeance=timezone.now().date() + timezone.timedelta(days=30),
            statut='ENVOYEE',
            type_document='FACTURE',
            notes='Facture créée automatiquement pour tester la génération PDF'
        )
        
        # Créer les lignes de facture
        LigneFacture.objects.create(
            facture=facture,
            produit=produit1,
            quantite=2,
            prix_unitaire=produit1.prix_unitaire,
            taux_tva=produit1.taux_tva,
            description='Ligne de test 1 - Quantité 2'
        )
        
        LigneFacture.objects.create(
            facture=facture,
            produit=produit2,
            quantite=3,
            prix_unitaire=produit2.prix_unitaire,
            taux_tva=produit2.taux_tva,
            description='Ligne de test 2 - Quantité 3'
        )
        
        self.stdout.write(
            self.style.SUCCESS(f'Facture d\'exemple créée avec l\'ID: {facture.id}')
        )
        
        return facture

    def generate_pdf_for_facture(self, facture_id):
        """Génère le PDF pour une facture donnée"""
        try:
            facture = Facture.objects.get(id=facture_id)
            self.stdout.write(f'Génération du PDF pour la facture {facture.numero}...')

            # Préparer les données de la facture
            facture_data = {
                'numero': facture.numero,
                'date_emission': facture.date_emission.strftime('%d/%m/%Y'),
                'client': {
                    'nom': facture.client.nom_complet,
                    'adresse': f"{facture.client.adresse_ligne1}, {facture.client.ville}"
                },
                'produits': [
                    {
                        'designation': ligne.description or ligne.produit.nom,
                        'quantite': ligne.quantite,
                        'prix_unitaire': float(ligne.prix_unitaire),
                        'total': float(ligne.montant_ht)
                    }
                    for ligne in facture.lignes.all()
                ],
                'total_ht': float(facture.montant_ht),
                'tva': float(facture.montant_tva),
                'total_ttc': float(facture.montant_ttc)
            }

            generator = TunisianFacturePDFGenerator()
            pdf_buffer = generator.generate_facture_pdf(facture_data)

            # Sauvegarder le fichier
            filename = f'facture_{facture.numero}.pdf'
            with open(filename, 'wb') as f:
                f.write(pdf_buffer.getvalue())
            filepath = filename
            
            self.stdout.write(
                self.style.SUCCESS(f'PDF généré avec succès: {filepath}')
            )
            
            # Afficher les détails de la facture
            self.stdout.write('\nDétails de la facture:')
            self.stdout.write(f'  - Numéro: {facture.numero}')
            self.stdout.write(f'  - Client: {facture.client.nom_complet}')
            self.stdout.write(f'  - Date: {facture.date_emission}')
            self.stdout.write(f'  - Statut: {facture.get_statut_display()}')
            self.stdout.write(f'  - Montant TTC: {facture.montant_ttc}€')
            self.stdout.write(f'  - Nombre de lignes: {facture.lignes.count()}')
            
        except Facture.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Facture avec l\'ID {facture_id} non trouvée')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Erreur lors de la génération du PDF: {str(e)}')
            )
