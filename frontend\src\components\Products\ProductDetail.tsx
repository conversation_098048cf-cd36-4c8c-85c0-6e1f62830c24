/**
 * Détail complet d'un produit avec historique et statistiques
 */

import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ProductService } from "../../services/apiService";
import { toast } from "react-toastify";

interface Product {
  id: string;
  nom: string;
  description?: string;
  prix_unitaire: number;
  prix_achat?: number;
  taux_tva: number;
  unite: string;
  categorie: string;
  reference?: string;
  code_barre?: string;
  stock_actuel: number;
  stock_minimum: number;
  stock_maximum: number;
  statut: "ACTIF" | "INACTIF" | "RUPTURE";
  date_creation: string;
  derniere_vente?: string;
  nombre_ventes: number;
  chiffre_affaires_total: number;
  marge_beneficiaire: number;
  fournisseur?: string;
  delai_livraison?: number;
  poids?: number;
  dimensions?: string;
  image_url?: string;
}

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [salesHistory, setSalesHistory] = useState<any[]>([]);
  const [stockHistory, setStockHistory] = useState<any[]>([]);

  useEffect(() => {
    if (id) {
      loadProduct(id);
      loadSalesHistory(id);
      loadStockHistory(id);
    }
  }, [id]);

  const loadProduct = async (productId: string) => {
    try {
      setLoading(true);
      const response = await ProductService.getProduct(productId);

      if (response && response.data) {
        setProduct(response.data);
      } else {
        throw new Error("Erreur lors du chargement du produit");
      }
    } catch (error: any) {
      toast.error("Erreur lors du chargement du produit");
      navigate("/products");
    } finally {
      setLoading(false);
    }
  };

  const loadSalesHistory = async (productId: string) => {
    try {
      // Simulation d'historique des ventes pour le mode démo
      setSalesHistory([
        { date: "2024-01-15", quantite: 5, montant: 250 },
        { date: "2024-01-20", quantite: 3, montant: 150 },
      ]);
    } catch (error: any) {
      console.error("Erreur chargement historique ventes:", error);
    }
  };

  const loadStockHistory = async (productId: string) => {
    try {
      // Simulation d'historique du stock pour le mode démo
      setStockHistory([
        { date: "2024-01-10", type: "entree", quantite: 50, stock_apres: 100 },
        { date: "2024-01-15", type: "sortie", quantite: 5, stock_apres: 95 },
      ]);
    } catch (error: any) {
      console.error("Erreur chargement historique stock:", error);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!product) return;

    try {
      setActionLoading("status");
      // Simulation de mise à jour du statut pour le mode démo
      setProduct((prev) =>
        prev ? { ...prev, statut: newStatus as any } : null
      );
      toast.success("Statut mis à jour avec succès");
    } catch (error: any) {
      toast.error("Erreur lors de la mise à jour du statut");
    } finally {
      setActionLoading(null);
    }
  };

  const handleStockUpdate = async (newStock: number) => {
    if (!product) return;

    try {
      setActionLoading("stock");
      // Simulation de mise à jour du stock pour le mode démo
      setProduct((prev) => (prev ? { ...prev, stock_actuel: newStock } : null));
      toast.success("Stock mis à jour avec succès");
      loadStockHistory(product.id);
    } catch (error: any) {
      toast.error("Erreur lors de la mise à jour du stock");
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async () => {
    if (!product) return;

    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce produit ?")) {
      return;
    }

    try {
      setActionLoading("delete");
      const response = await ProductService.deleteProduct(product.id);

      if (response) {
        toast.success("Produit supprimé avec succès");
        navigate("/products");
      } else {
        throw new Error("Erreur lors de la suppression");
      }
    } catch (error: any) {
      toast.error("Erreur lors de la suppression");
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIF":
        return "status-active";
      case "INACTIF":
        return "status-inactive";
      case "RUPTURE":
        return "status-outofstock";
      default:
        return "status-default";
    }
  };

  const getStockStatus = (current: number, minimum: number) => {
    if (current === 0)
      return { status: "rupture", label: "❌ Rupture", color: "danger" };
    if (current <= minimum)
      return { status: "faible", label: "⚠️ Stock faible", color: "warning" };
    return { status: "ok", label: "✅ Stock OK", color: "success" };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="product-detail-loading">
        <div className="loading-spinner"></div>
        <p>Chargement du produit...</p>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="product-not-found">
        <div className="error-icon">❌</div>
        <h3>Produit introuvable</h3>
        <p>Le produit demandé n'existe pas ou a été supprimé</p>
        <button
          className="btn btn-primary"
          onClick={() => navigate("/products")}>
          ← Retour aux produits
        </button>
      </div>
    );
  }

  const stockStatus = getStockStatus(
    product.stock_actuel,
    product.stock_minimum
  );

  return (
    <div className="product-detail">
      {/* Header avec actions */}
      <div className="detail-header">
        <div className="header-info">
          <div className="product-title">
            <h1>📦 {product.nom}</h1>
            <div className="product-badges">
              <span
                className={`status-badge ${getStatusColor(product.statut)}`}>
                {product.statut}
              </span>
              <span className={`stock-badge ${stockStatus.color}`}>
                {stockStatus.label}
              </span>
            </div>
          </div>
          <div className="product-summary">
            <span>Créé le {formatDate(product.date_creation)}</span>
            <span>
              {product.nombre_ventes} ventes •{" "}
              {formatCurrency(product.chiffre_affaires_total)}
            </span>
            {product.derniere_vente && (
              <span>Dernière vente: {formatDate(product.derniere_vente)}</span>
            )}
          </div>
        </div>

        <div className="header-actions">
          <button
            className="btn btn-outline"
            onClick={() => navigate("/products")}>
            ← Retour
          </button>

          <button
            className="btn btn-info"
            onClick={() => navigate(`/products/${product.id}/edit`)}>
            ✏️ Modifier
          </button>

          <button
            className="btn btn-primary"
            onClick={() => navigate(`/invoices/new?product=${product.id}`)}>
            📄 Facturer
          </button>

          <div className="dropdown">
            <button className="btn btn-outline dropdown-toggle">⋮ Plus</button>
            <div className="dropdown-menu">
              <button
                className="dropdown-item"
                onClick={() => navigate(`/products/${product.id}/duplicate`)}>
                📋 Dupliquer
              </button>
              <button
                className="dropdown-item"
                onClick={() =>
                  window.open(`/api/products/${product.id}/export`, "_blank")
                }>
                📤 Exporter
              </button>
              <div className="dropdown-divider"></div>
              <button
                className="dropdown-item danger"
                onClick={handleDelete}
                disabled={actionLoading === "delete"}>
                🗑️ Supprimer
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Actions de statut */}
      {product.statut !== "ACTIF" && (
        <div className="status-actions">
          <h4>Actions rapides</h4>
          <div className="status-buttons">
            {product.statut === "INACTIF" && (
              <button
                className="btn btn-success"
                onClick={() => handleStatusChange("ACTIF")}
                disabled={actionLoading === "status"}>
                ✅ Activer le produit
              </button>
            )}
            {product.statut === "RUPTURE" && product.stock_actuel > 0 && (
              <button
                className="btn btn-primary"
                onClick={() => handleStatusChange("ACTIF")}
                disabled={actionLoading === "status"}>
                🔄 Remettre en vente
              </button>
            )}
            <button
              className="btn btn-warning btn-outline"
              onClick={() => handleStatusChange("INACTIF")}
              disabled={actionLoading === "status"}>
              ⏸️ Désactiver
            </button>
          </div>
        </div>
      )}

      {/* Informations détaillées */}
      <div className="product-section">
        <h3>📋 Informations Produit</h3>
        <div className="product-info-grid">
          <div className="info-item">
            <label>Nom:</label>
            <span>{product.nom}</span>
          </div>
          <div className="info-item">
            <label>Référence:</label>
            <span>{product.reference || "Non définie"}</span>
          </div>
          <div className="info-item">
            <label>Code-barres:</label>
            <span>{product.code_barre || "Non défini"}</span>
          </div>
          <div className="info-item">
            <label>Catégorie:</label>
            <span>{product.categorie}</span>
          </div>
          <div className="info-item">
            <label>Prix de vente:</label>
            <span className="price">
              {formatCurrency(product.prix_unitaire)}
            </span>
          </div>
          <div className="info-item">
            <label>Prix d'achat:</label>
            <span>
              {product.prix_achat
                ? formatCurrency(product.prix_achat)
                : "Non défini"}
            </span>
          </div>
          <div className="info-item">
            <label>TVA:</label>
            <span>{product.taux_tva}%</span>
          </div>
          <div className="info-item">
            <label>Unité:</label>
            <span>{product.unite}</span>
          </div>
          {product.fournisseur && (
            <div className="info-item">
              <label>Fournisseur:</label>
              <span>{product.fournisseur}</span>
            </div>
          )}
          {product.delai_livraison && (
            <div className="info-item">
              <label>Délai livraison:</label>
              <span>{product.delai_livraison} jours</span>
            </div>
          )}
          {product.poids && (
            <div className="info-item">
              <label>Poids:</label>
              <span>{product.poids} kg</span>
            </div>
          )}
          {product.dimensions && (
            <div className="info-item">
              <label>Dimensions:</label>
              <span>{product.dimensions}</span>
            </div>
          )}
          {product.description && (
            <div className="info-item full-width">
              <label>Description:</label>
              <span>{product.description}</span>
            </div>
          )}
        </div>
      </div>

      {/* Gestion du stock */}
      <div className="product-section">
        <h3>📦 Gestion du Stock</h3>
        <div className="stock-management">
          <div className="stock-info">
            <div className="stock-item">
              <label>Stock actuel:</label>
              <span className={`stock-value ${stockStatus.status}`}>
                {product.stock_actuel} {product.unite}
              </span>
            </div>
            <div className="stock-item">
              <label>Stock minimum:</label>
              <span>
                {product.stock_minimum} {product.unite}
              </span>
            </div>
            <div className="stock-item">
              <label>Stock maximum:</label>
              <span>
                {product.stock_maximum} {product.unite}
              </span>
            </div>
          </div>

          <div className="stock-actions">
            <div className="stock-update">
              <label>Ajuster le stock:</label>
              <div className="stock-controls">
                <input
                  type="number"
                  min="0"
                  defaultValue={product.stock_actuel}
                  className="stock-input"
                  id="new-stock"
                />
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => {
                    const input = document.getElementById(
                      "new-stock"
                    ) as HTMLInputElement;
                    const newStock = parseInt(input.value);
                    if (!isNaN(newStock)) {
                      handleStockUpdate(newStock);
                    }
                  }}
                  disabled={actionLoading === "stock"}>
                  {actionLoading === "stock" ? "⏳" : "✅"} Mettre à jour
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistiques produit */}
      <div className="product-section">
        <h3>📊 Statistiques</h3>
        <div className="stats-grid">
          <div className="stat-card primary">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h4>Chiffre d'affaires</h4>
              <div className="stat-value">
                {formatCurrency(product.chiffre_affaires_total)}
              </div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">📈</div>
            <div className="stat-content">
              <h4>Ventes</h4>
              <div className="stat-value">{product.nombre_ventes}</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">💎</div>
            <div className="stat-content">
              <h4>Marge</h4>
              <div className="stat-value">
                {product.marge_beneficiaire.toFixed(1)}%
              </div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">📦</div>
            <div className="stat-content">
              <h4>Stock</h4>
              <div className="stat-value">{product.stock_actuel}</div>
              <div className="stat-subtitle">{product.unite}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Historique des ventes */}
      <div className="product-section">
        <div className="section-header">
          <h3>📈 Historique des Ventes</h3>
          <button
            className="btn btn-primary btn-sm"
            onClick={() => navigate(`/invoices/new?product=${product.id}`)}>
            ➕ Nouvelle vente
          </button>
        </div>

        {salesHistory.length > 0 ? (
          <div className="sales-table-container">
            <table className="sales-table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Client</th>
                  <th>Quantité</th>
                  <th>Prix unitaire</th>
                  <th>Total</th>
                  <th>Facture</th>
                </tr>
              </thead>
              <tbody>
                {salesHistory.slice(0, 10).map((sale, index) => (
                  <tr key={index}>
                    <td>{formatDate(sale.date)}</td>
                    <td>{sale.client_nom}</td>
                    <td>
                      {sale.quantite} {product.unite}
                    </td>
                    <td>{formatCurrency(sale.prix_unitaire)}</td>
                    <td className="amount">{formatCurrency(sale.total)}</td>
                    <td>
                      <span
                        className="clickable"
                        onClick={() =>
                          navigate(`/invoices/${sale.facture_id}`)
                        }>
                        {sale.facture_numero}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">📈</div>
            <h4>Aucune vente</h4>
            <p>Ce produit n'a pas encore été vendu</p>
          </div>
        )}
      </div>

      {/* Historique du stock */}
      <div className="product-section">
        <h3>📦 Historique du Stock</h3>

        {stockHistory.length > 0 ? (
          <div className="stock-table-container">
            <table className="stock-table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Quantité</th>
                  <th>Stock après</th>
                  <th>Motif</th>
                </tr>
              </thead>
              <tbody>
                {stockHistory.slice(0, 10).map((movement, index) => (
                  <tr key={index}>
                    <td>{formatDate(movement.date)}</td>
                    <td>
                      <span
                        className={`movement-type ${movement.type.toLowerCase()}`}>
                        {movement.type === "ENTREE" ? "📥 Entrée" : "📤 Sortie"}
                      </span>
                    </td>
                    <td
                      className={
                        movement.type === "ENTREE" ? "positive" : "negative"
                      }>
                      {movement.type === "ENTREE" ? "+" : "-"}
                      {movement.quantite}
                    </td>
                    <td>{movement.stock_apres}</td>
                    <td>{movement.motif}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">📦</div>
            <h4>Aucun mouvement</h4>
            <p>Aucun mouvement de stock enregistré</p>
          </div>
        )}
      </div>

      {/* Actions rapides en bas */}
      <div className="detail-footer">
        <div className="footer-actions">
          <button
            className="btn btn-primary"
            onClick={() => navigate(`/invoices/new?product=${product.id}`)}>
            📄 Créer une facture
          </button>

          <button
            className="btn btn-info"
            onClick={() => navigate(`/orders/new?product=${product.id}`)}>
            🛒 Créer une commande
          </button>

          <button
            className="btn btn-secondary"
            onClick={() => navigate(`/products/${product.id}/edit`)}>
            ✏️ Modifier
          </button>

          <button
            className="btn btn-outline"
            onClick={() =>
              window.open(`/api/products/${product.id}/export`, "_blank")
            }>
            📤 Exporter
          </button>
        </div>

        <div className="footer-info">
          <span>
            Dernière modification: {formatDate(product.date_creation)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
