/**
 * Styles pour le composant de Facturation Professionnelle
 * Design moderne inspiré de <PERSON>
 */

.facturation-professionnelle {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* En-tête avec branding */
.facturation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #0d9488;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.company-logo {
  height: 60px;
  width: auto;
  object-fit: contain;
}

.logo-icon {
  font-size: 2.5rem;
  color: #0d9488;
}

.fallback-icon {
  display: none;
}

.fallback-icon.show-fallback {
  display: block;
}

.brand-text h1 {
  margin: 0;
  color: #1e293b;
  font-size: 1.8rem;
  font-weight: 700;
}

.brand-text span {
  color: #64748b;
  font-size: 0.9rem;
  font-style: italic;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Filtres */
.facturation-filters {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr auto;
  gap: 1rem;
  margin-bottom: 1rem;
}

.filters-row:last-child {
  margin-bottom: 0;
  grid-template-columns: 1fr 1fr auto;
}

.filter-input,
.filter-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #0d9488;
  box-shadow: 0 0 0 3px rgba(13, 148, 136, 0.1);
}

.search-input {
  background: #f8fafc;
}

/* Contenu principal */
.facturation-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* État vide */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.empty-state p {
  margin: 0 0 2rem 0;
}

/* Grille des factures */
.factures-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.facture-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.facture-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #0d9488;
}

.facture-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.facture-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.facture-numero {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.type-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge[data-type="FACTURE"] {
  background: #dbeafe;
  color: #1e40af;
}

.type-badge[data-type="DEVIS"] {
  background: #fef3c7;
  color: #92400e;
}

.facture-statut {
  font-weight: 600;
  font-size: 0.85rem;
  text-transform: uppercase;
}

.facture-client h4 {
  margin: 0 0 0.25rem 0;
  color: #1e293b;
  font-size: 1.1rem;
}

.facture-client p {
  margin: 0;
  color: #64748b;
  font-size: 0.9rem;
}

.facture-details {
  margin: 1rem 0;
  padding: 1rem 0;
  border-top: 1px solid #f1f5f9;
  border-bottom: 1px solid #f1f5f9;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row span:first-child {
  color: #64748b;
  font-size: 0.9rem;
}

.detail-row span:last-child {
  color: #1e293b;
  font-weight: 500;
}

.facture-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.btn-icon {
  padding: 0.5rem;
  border: none;
  background: #f8fafc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.btn-icon:hover {
  background: #e2e8f0;
  transform: scale(1.05);
}

.btn-icon.danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* Boutons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #0d9488;
  color: white;
}

.btn-primary:hover {
  background: #0f766e;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-outline {
  background: transparent;
  color: #0d9488;
  border: 1px solid #0d9488;
}

.btn-outline:hover {
  background: #0d9488;
  color: white;
}

/* Loading */
.facturation-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #0d9488;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accès refusé */
.access-denied {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  margin: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.access-denied h2 {
  color: #dc2626;
  margin-bottom: 1rem;
}

.access-denied p {
  color: #64748b;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f1f5f9;
  color: #374151;
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

/* Formulaire */
.form-section {
  margin-bottom: 2rem;
}

.form-section h4 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #0d9488;
  box-shadow: 0 0 0 3px rgba(13, 148, 136, 0.1);
}

/* Lignes de facture */
.lignes-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.ligne-row {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  gap: 1rem;
}

.ligne-row:last-child {
  border-bottom: none;
}

.ligne-fields {
  display: grid;
  grid-template-columns: 2fr 0.8fr 1fr 0.8fr 1fr;
  gap: 0.5rem;
  flex: 1;
  align-items: center;
}

.quantity-input,
.tva-input {
  max-width: 80px;
}

.price-input {
  max-width: 120px;
}

.ligne-total {
  font-weight: 600;
  color: #0d9488;
  text-align: right;
  min-width: 100px;
}

.btn-remove-ligne {
  background: #fee2e2;
  color: #dc2626;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-remove-ligne:hover {
  background: #fecaca;
  transform: scale(1.05);
}

.total-section {
  padding: 1rem;
  background: #f8fafc;
  border-top: 2px solid #0d9488;
  text-align: right;
  font-size: 1.1rem;
  color: #0d9488;
}

/* Responsive */
@media (max-width: 768px) {
  .facturation-professionnelle {
    padding: 1rem;
  }

  .facturation-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .filters-row {
    grid-template-columns: 1fr;
  }

  .factures-grid {
    grid-template-columns: 1fr;
  }

  .ligne-fields {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .modal-content {
    margin: 1rem;
    max-width: none;
  }

  .bons-livraison-list {
    flex-direction: column;
    gap: 0.125rem;
  }
}

/* Styles pour les bons de livraison */
.bons-livraison-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.bon-livraison-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid #bbdefb;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.type-badge[data-type="BON_LIVRAISON"] {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  color: white;
}
