from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count, Q, F, Avg
from django.utils import timezone
from datetime import datetime, timedelta

from .models import RapportTemplate, RapportGenere, TableauBord
from .serializers import RapportTemplateSerializer, RapportGenereSerializer, TableauBordSerializer
from Authentication.permissions import HasModulePermission

class RapportTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet pour les templates de rapports"""

    queryset = RapportTemplate.objects.all()
    serializer_class = RapportTemplateSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'rapports'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['type_rapport', 'format_export', 'automatique', 'public']
    search_fields = ['nom', 'description']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

class RapportGenereViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet pour les rapports générés"""

    queryset = RapportGenere.objects.all()
    serializer_class = RapportGenereSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'rapports'

    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['template', 'statut']
    ordering = ['-date_generation']

class TableauBordViewSet(viewsets.ModelViewSet):
    """ViewSet pour les tableaux de bord"""

    queryset = TableauBord.objects.all()
    serializer_class = TableauBordSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'rapports'

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

class RapportVentesView(APIView):
    """Rapport des ventes"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'rapports'

    def get(self, request):
        # Paramètres de période
        date_debut = request.query_params.get('date_debut')
        date_fin = request.query_params.get('date_fin')

        if not date_debut or not date_fin:
            # Par défaut : mois en cours
            now = timezone.now()
            date_debut = now.replace(day=1).date()
            date_fin = now.date()
        else:
            date_debut = datetime.strptime(date_debut, '%Y-%m-%d').date()
            date_fin = datetime.strptime(date_fin, '%Y-%m-%d').date()

        # Import des modèles nécessaires
        from Facturation.models import Facture
        from Commandes.models import Commande

        # Statistiques des ventes
        factures = Facture.objects.filter(
            date_facture__gte=date_debut,
            date_facture__lte=date_fin,
            statut__in=['ENVOYEE', 'PAYEE']
        )

        # CA facturé
        ca_facture = factures.aggregate(total=Sum('montant_total'))['total'] or 0
        nb_factures = factures.count()

        # Évolution quotidienne
        evolution_quotidienne = []
        current_date = date_debut
        while current_date <= date_fin:
            ca_jour = factures.filter(date_facture=current_date).aggregate(
                total=Sum('montant_total')
            )['total'] or 0

            evolution_quotidienne.append({
                'date': current_date.isoformat(),
                'ca': float(ca_jour)
            })
            current_date += timedelta(days=1)

        data = {
            'periode': {
                'debut': date_debut.isoformat(),
                'fin': date_fin.isoformat()
            },
            'resume': {
                'ca_total': float(ca_facture),
                'nb_factures': nb_factures,
                'panier_moyen': float(ca_facture / nb_factures) if nb_factures > 0 else 0
            },
            'evolution_quotidienne': evolution_quotidienne
        }

        return Response(data)

class RapportClientsView(APIView):
    """Rapport des clients"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'rapports'

    def get(self, request):
        from Clients.models import Client

        # Statistiques clients
        total_clients = Client.objects.count()
        clients_actifs = Client.objects.filter(statut='ACTIF').count()

        # Répartition par segment
        segments = Client.objects.values('segment').annotate(
            count=Count('id'),
            ca_moyen=Avg('total_achats')
        ).order_by('-count')

        data = {
            'total_clients': total_clients,
            'clients_actifs': clients_actifs,
            'taux_activite': (clients_actifs / total_clients * 100) if total_clients > 0 else 0,
            'segments': list(segments)
        }

        return Response(data)

class RapportProduitsView(APIView):
    """Rapport des produits"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'rapports'

    def get(self, request):
        from Produits.models import Produit

        # Statistiques produits
        total_produits = Produit.objects.count()
        produits_actifs = Produit.objects.filter(statut='ACTIF').count()

        # Valeur du stock
        valeur_stock = Produit.objects.filter(
            gestion_stock=True
        ).aggregate(
            valeur_vente=Sum(F('stock_actuel') * F('prix_vente'))
        )

        data = {
            'total_produits': total_produits,
            'produits_actifs': produits_actifs,
            'valeur_stock': float(valeur_stock['valeur_vente'] or 0)
        }

        return Response(data)

class RapportFinancierView(APIView):
    """Rapport financier"""

    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'rapports'

    def get(self, request):
        from Facturation.models import Facture
        from Paiements.models import Paiement

        # Paramètres de période
        date_debut = request.query_params.get('date_debut')
        date_fin = request.query_params.get('date_fin')

        if not date_debut or not date_fin:
            # Par défaut : mois en cours
            now = timezone.now()
            date_debut = now.replace(day=1).date()
            date_fin = now.date()
        else:
            date_debut = datetime.strptime(date_debut, '%Y-%m-%d').date()
            date_fin = datetime.strptime(date_fin, '%Y-%m-%d').date()

        # CA facturé
        ca_facture = Facture.objects.filter(
            date_facture__gte=date_debut,
            date_facture__lte=date_fin,
            statut__in=['ENVOYEE', 'PAYEE']
        ).aggregate(total=Sum('montant_total'))['total'] or 0

        # CA encaissé
        ca_encaisse = Paiement.objects.filter(
            date_paiement__gte=date_debut,
            date_paiement__lte=date_fin,
            type_paiement='ENCAISSEMENT',
            statut='VALIDE'
        ).aggregate(total=Sum('montant'))['total'] or 0

        data = {
            'periode': {
                'debut': date_debut.isoformat(),
                'fin': date_fin.isoformat()
            },
            'ca_facture': float(ca_facture),
            'ca_encaisse': float(ca_encaisse),
            'taux_recouvrement': (ca_encaisse / ca_facture * 100) if ca_facture > 0 else 0
        }

        return Response(data)
