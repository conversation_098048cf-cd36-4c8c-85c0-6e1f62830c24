from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import EmailValidator, RegexValidator
from decimal import Decimal

User = get_user_model()

class Client(models.Model):
    """Modèle Client pour la gestion CRM"""

    TYPE_CHOICES = [
        ('PARTICULIER', 'Particulier'),
        ('ENTREPRISE', 'Entreprise'),
        ('ASSOCIATION', 'Association'),
        ('ADMINISTRATION', 'Administration'),
    ]

    STATUT_CHOICES = [
        ('ACTIF', 'Actif'),
        ('INACTIF', 'Inactif'),
        ('SUSPENDU', 'Suspendu'),
        ('PROSPECT', 'Prospect'),
    ]

    SEGMENT_CHOICES = [
        ('VIP', 'VIP'),
        ('PREMIUM', 'Premium'),
        ('STANDARD', 'Standard'),
        ('NOUVEAU', 'Nouveau'),
    ]

    # Informations de base
    nom = models.CharField(max_length=200, verbose_name="Nom/Raison sociale")
    prenom = models.CharField(max_length=100, blank=True, verbose_name="Prénom")
    email = models.EmailField(unique=True, validators=[EmailValidator()])
    telephone = models.CharField(
        max_length=20,
        blank=True,
        validators=[RegexValidator(r'^\+?[\d\s\-\(\)]+$', 'Format de téléphone invalide')]
    )

    # Type et statut
    type_client = models.CharField(max_length=20, choices=TYPE_CHOICES, default='PARTICULIER')
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='ACTIF')
    segment = models.CharField(max_length=20, choices=SEGMENT_CHOICES, default='NOUVEAU')

    # Adresse
    adresse = models.TextField(blank=True, verbose_name="Adresse")
    ville = models.CharField(max_length=100, blank=True)
    code_postal = models.CharField(max_length=10, blank=True)
    pays = models.CharField(max_length=100, default='France')

    # Informations entreprise (si applicable)
    siret = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="Numéro d'identification fiscale",
        help_text="Numéro d'identification fiscale de l'entreprise (optionnel)"
    )
    tva_intracommunautaire = models.CharField(max_length=20, blank=True)

    # Informations commerciales
    remise_par_defaut = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name="Remise par défaut (%)"
    )
    limite_credit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name="Limite de crédit"
    )
    delai_paiement = models.IntegerField(default=30, verbose_name="Délai de paiement (jours)")

    # Statistiques calculées
    total_achats = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        verbose_name="Total des achats"
    )
    nb_commandes = models.IntegerField(default=0, verbose_name="Nombre de commandes")
    derniere_commande = models.DateTimeField(null=True, blank=True)

    # Métadonnées
    notes = models.TextField(blank=True, verbose_name="Notes internes")
    tags = models.CharField(max_length=500, blank=True, help_text="Tags séparés par des virgules")

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='clients_created')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='clients_updated')

    class Meta:
        verbose_name = "Client"
        verbose_name_plural = "Clients"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['statut']),
            models.Index(fields=['segment']),
            models.Index(fields=['type_client']),
        ]

    def __str__(self):
        if self.prenom:
            return f"{self.nom} {self.prenom}"
        return self.nom

    @property
    def nom_complet(self):
        if self.prenom:
            return f"{self.nom} {self.prenom}"
        return self.nom

    @property
    def adresse_complete(self):
        parts = [self.adresse, self.code_postal, self.ville, self.pays]
        return ", ".join([part for part in parts if part])

    def calculer_ca_annuel(self, annee=None):
        """Calcule le CA annuel du client"""
        from django.utils import timezone
        from Facturation.models import Facture

        if not annee:
            annee = timezone.now().year

        factures = Facture.objects.filter(
            client=self,
            date_emission__year=annee,
            statut__in=['ENVOYEE', 'PAYEE']
        )
        return sum(f.montant_ttc for f in factures)

    def get_segment_color(self):
        """Retourne la couleur associée au segment"""
        colors = {
            'VIP': '#e74c3c',
            'PREMIUM': '#f39c12',
            'STANDARD': '#3498db',
            'NOUVEAU': '#27ae60'
        }
        return colors.get(self.segment, '#95a5a6')


class ContactClient(models.Model):
    """Contacts associés à un client entreprise"""

    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='contacts')
    nom = models.CharField(max_length=100)
    prenom = models.CharField(max_length=100)
    fonction = models.CharField(max_length=100, blank=True)
    email = models.EmailField(blank=True)
    telephone = models.CharField(max_length=20, blank=True)
    principal = models.BooleanField(default=False, verbose_name="Contact principal")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Contact client"
        verbose_name_plural = "Contacts clients"
        ordering = ['-principal', 'nom']

    def __str__(self):
        return f"{self.nom} {self.prenom} - {self.client.nom}"
