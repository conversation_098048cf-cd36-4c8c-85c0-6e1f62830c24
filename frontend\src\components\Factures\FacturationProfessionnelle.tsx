/**
 * Composant de Facturation Professionnelle
 * Interface moderne pour la gestion des factures avec design Ben <PERSON>
 */

import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useNotify } from "../Common/NotificationSystem";
import {
  FacturationService,
  ClientService,
  ProductService,
} from "../../services/apiService";
import "./FacturationProfessionnelle.css";

interface Facture {
  id: string;
  numero: string;
  type_document: "FACTURE" | "DEVIS" | "BON_LIVRAISON";
  client: {
    id: string;
    nom: string;
    email: string;
    telephone: string;
    adresse: string;
  };
  date_emission: string;
  date_echeance?: string;
  statut: "BROUILLON" | "ENVOYE" | "ACCEPTE" | "PAYE" | "ANNULE";
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
  lignes: LigneFacture[];
  bons_livraison?: BonLivraison[];
  notes?: string;
  conditions_paiement?: string;
}

interface BonLivraison {
  id: string;
  numero: string;
  statut: "BROUILLON" | "ENVOYE" | "ACCEPTE" | "PAYE" | "ANNULE";
  date_emission: string;
  created_at: string;
}

interface LigneFacture {
  id: string;
  designation: string;
  quantite: number;
  prix_unitaire_ht: number;
  taux_tva: number;
  montant_ttc: number;
}

interface Client {
  id: string;
  nom: string;
  email: string;
  telephone: string;
  adresse: string;
  ville: string;
  code_postal: string;
  pays: string;
}

interface Produit {
  id: string;
  nom: string;
  reference: string;
  prix_vente_ht: number;
  taux_tva: number;
}

const FacturationProfessionnelle: React.FC = () => {
  const { user, hasModuleAccess } = useAuth();
  const notify = useNotify();

  // Fonction utilitaire pour extraire les données de manière sécurisée
  const extractArrayData = (response: any): any[] => {
    if (!response || !response.success) return [];

    // Essayer différentes propriétés où les données peuvent se trouver
    const data = response.data || response.results || response;

    // Si c'est déjà un tableau, le retourner
    if (Array.isArray(data)) return data;

    // Si c'est un objet avec une propriété results (pagination Django)
    if (data && Array.isArray(data.results)) return data.results;

    // Si c'est un objet avec une propriété data
    if (data && Array.isArray(data.data)) return data.data;

    // Sinon, retourner un tableau vide
    return [];
  };

  // États
  const [factures, setFactures] = useState<Facture[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [produits, setProduits] = useState<Produit[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewFacture, setShowNewFacture] = useState(false);
  const [selectedFacture, setSelectedFacture] = useState<Facture | null>(null);
  const [filters, setFilters] = useState({
    search: "",
    statut: "",
    type_document: "",
    client: "",
    date_debut: "",
    date_fin: "",
  });

  // Chargement des données
  useEffect(() => {
    loadData();
  }, []);

  // Vérification des permissions
  if (!hasModuleAccess("facturation")) {
    return (
      <div className="access-denied">
        <h2>🚫 Accès Refusé</h2>
        <p>
          Vous n'avez pas les permissions nécessaires pour accéder au module de
          facturation.
        </p>
      </div>
    );
  }

  const loadData = async () => {
    try {
      setLoading(true);

      // Charger les factures, clients et produits en parallèle
      const [facturesRes, clientsRes, produitsRes] = await Promise.all([
        FacturationService.getFactures(filters),
        ClientService.getClients(),
        ProductService.getProducts(),
      ]);

      // Utiliser la fonction utilitaire pour extraire les données
      setFactures(extractArrayData(facturesRes));
      setClients(extractArrayData(clientsRes));
      setProduits(extractArrayData(produitsRes));

      // Afficher les erreurs si nécessaire
      if (!facturesRes.success) {
        notify.error(
          facturesRes.error || "Erreur lors du chargement des factures"
        );
      }

      if (!clientsRes.success) {
        notify.error("Erreur lors du chargement des clients");
      }

      if (!produitsRes.success) {
        notify.error("Erreur lors du chargement des produits");
      }
    } catch (error) {
      console.error("Erreur lors du chargement:", error);
      // S'assurer que les états sont des tableaux même en cas d'erreur
      setFactures([]);
      setClients([]);
      setProduits([]);
      notify.error("Erreur lors du chargement des données");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFacture = () => {
    setSelectedFacture(null);
    setShowNewFacture(true);
  };

  const handleEditFacture = (facture: Facture) => {
    setSelectedFacture(facture);
    setShowNewFacture(true);
  };

  const handleDeleteFacture = async (factureId: string) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cette facture ?")) {
      return;
    }

    try {
      const response = await FacturationService.deleteFacture(factureId);

      if (response.success) {
        notify.success("Facture supprimée avec succès");
        loadData();
      } else {
        notify.error(response.error || "Erreur lors de la suppression");
      }
    } catch (error) {
      console.error("Erreur:", error);
      notify.error("Erreur lors de la suppression");
    }
  };

  const handleGeneratePDF = async (factureId: string) => {
    try {
      const response = await FacturationService.generatePDF(factureId);

      if (response.success && response.blob) {
        const url = window.URL.createObjectURL(response.blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `facture_${factureId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        notify.success("PDF généré avec succès");
      } else {
        notify.error(response.error || "Erreur lors de la génération du PDF");
      }
    } catch (error) {
      console.error("Erreur:", error);
      notify.error("Erreur lors de la génération du PDF");
    }
  };

  const getStatutColor = (statut: string) => {
    const colors = {
      BROUILLON: "#6b7280",
      ENVOYE: "#3b82f6",
      ACCEPTE: "#10b981",
      PAYE: "#059669",
      ANNULE: "#ef4444",
    };
    return colors[statut as keyof typeof colors] || "#6b7280";
  };

  const filteredFactures = (factures || []).filter((facture) => {
    return (
      (!filters.search ||
        facture.numero.toLowerCase().includes(filters.search.toLowerCase()) ||
        facture.client.nom
          .toLowerCase()
          .includes(filters.search.toLowerCase())) &&
      (!filters.statut || facture.statut === filters.statut) &&
      (!filters.type_document ||
        facture.type_document === filters.type_document) &&
      (!filters.client || facture.client.id === filters.client) &&
      (!filters.date_debut || facture.date_emission >= filters.date_debut) &&
      (!filters.date_fin || facture.date_emission <= filters.date_fin)
    );
  });

  if (loading) {
    return (
      <div className="facturation-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des factures...</p>
      </div>
    );
  }

  return (
    <div className="facturation-professionnelle">
      {/* En-tête avec logo Ben Chaabene */}
      <div className="facturation-header">
        <div className="header-brand">
          <div className="logo-container">
            <img
              src="/logo_ben_chaabene.png"
              alt="Ben Chaabène"
              className="company-logo"
              onError={(e) => {
                // Fallback vers l'icône si le logo ne charge pas
                e.currentTarget.style.display = "none";
                e.currentTarget.nextElementSibling?.classList.add(
                  "show-fallback"
                );
              }}
            />
            <div className="logo-icon fallback-icon">📄</div>
            <div className="brand-text">
              <h1>Facturation</h1>
              <span>Société Ben Chaabène de Commerce</span>
            </div>
          </div>
        </div>

        <div className="header-actions">
          <button
            className="btn btn-primary"
            onClick={handleCreateFacture}>
            ➕ Nouvelle Facture
          </button>
          <button
            className="btn btn-secondary"
            onClick={() => setShowNewFacture(true)}>
            📋 Nouveau Devis
          </button>
        </div>
      </div>

      {/* Filtres */}
      <div className="facturation-filters">
        <div className="filters-row">
          <input
            type="text"
            placeholder="🔍 Rechercher par numéro ou client..."
            value={filters.search}
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            className="filter-input search-input"
          />

          <select
            value={filters.statut}
            onChange={(e) => setFilters({ ...filters, statut: e.target.value })}
            className="filter-select">
            <option value="">Tous les statuts</option>
            <option value="BROUILLON">Brouillon</option>
            <option value="ENVOYE">Envoyé</option>
            <option value="ACCEPTE">Accepté</option>
            <option value="PAYE">Payé</option>
            <option value="ANNULE">Annulé</option>
          </select>

          <select
            value={filters.type_document}
            onChange={(e) =>
              setFilters({ ...filters, type_document: e.target.value })
            }
            className="filter-select">
            <option value="">Tous les types</option>
            <option value="FACTURE">Factures</option>
            <option value="DEVIS">Devis</option>
            <option value="BON_LIVRAISON">Bons de livraison</option>
          </select>

          <select
            value={filters.client}
            onChange={(e) => setFilters({ ...filters, client: e.target.value })}
            className="filter-select">
            <option value="">Tous les clients</option>
            {clients.map((client) => (
              <option
                key={client.id}
                value={client.id}>
                {client.nom}
              </option>
            ))}
          </select>
        </div>

        <div className="filters-row">
          <input
            type="date"
            value={filters.date_debut}
            onChange={(e) =>
              setFilters({ ...filters, date_debut: e.target.value })
            }
            className="filter-input"
            placeholder="Date début"
          />
          <input
            type="date"
            value={filters.date_fin}
            onChange={(e) =>
              setFilters({ ...filters, date_fin: e.target.value })
            }
            className="filter-input"
            placeholder="Date fin"
          />

          <button
            className="btn btn-outline"
            onClick={() =>
              setFilters({
                search: "",
                statut: "",
                type_document: "",
                client: "",
                date_debut: "",
                date_fin: "",
              })
            }>
            🔄 Réinitialiser
          </button>
        </div>
      </div>

      {/* Liste des factures */}
      <div className="facturation-content">
        {filteredFactures.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📄</div>
            <h3>Aucune facture trouvée</h3>
            <p>Commencez par créer votre première facture</p>
            <button
              className="btn btn-primary"
              onClick={handleCreateFacture}>
              ➕ Créer une facture
            </button>
          </div>
        ) : (
          <div className="factures-grid">
            {filteredFactures.map((facture) => (
              <div
                key={facture.id}
                className="facture-card">
                <div className="facture-header">
                  <div className="facture-numero">
                    <span
                      className="type-badge"
                      data-type={facture.type_document}>
                      {facture.type_document}
                    </span>
                    <strong>{facture.numero}</strong>
                  </div>
                  <div
                    className="facture-statut"
                    style={{ color: getStatutColor(facture.statut) }}>
                    {facture.statut}
                  </div>
                </div>

                <div className="facture-client">
                  <h4>{facture.client.nom}</h4>
                  <p>{facture.client.email}</p>
                </div>

                <div className="facture-details">
                  <div className="detail-row">
                    <span>Date d'émission:</span>
                    <span>
                      {new Date(facture.date_emission).toLocaleDateString(
                        "fr-FR"
                      )}
                    </span>
                  </div>
                  {facture.date_echeance && (
                    <div className="detail-row">
                      <span>Date d'échéance:</span>
                      <span>
                        {new Date(facture.date_echeance).toLocaleDateString(
                          "fr-FR"
                        )}
                      </span>
                    </div>
                  )}
                  <div className="detail-row">
                    <span>Montant TTC:</span>
                    <strong>{facture.montant_ttc.toFixed(3)} DT</strong>
                  </div>
                  {facture.type_document === "FACTURE" &&
                    facture.bons_livraison &&
                    facture.bons_livraison.length > 0 && (
                      <div className="detail-row">
                        <span>Bons de livraison:</span>
                        <div className="bons-livraison-list">
                          {facture.bons_livraison.map((bon) => (
                            <span
                              key={bon.id}
                              className="bon-livraison-badge">
                              📦 {bon.numero}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                </div>

                <div className="facture-actions">
                  <button
                    className="btn-icon"
                    onClick={() => handleEditFacture(facture)}
                    title="Modifier">
                    ✏️
                  </button>
                  <button
                    className="btn-icon"
                    onClick={() => handleGeneratePDF(facture.id)}
                    title="Télécharger PDF">
                    📄
                  </button>
                  <button
                    className="btn-icon danger"
                    onClick={() => handleDeleteFacture(facture.id)}
                    title="Supprimer">
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal de création/édition */}
      {showNewFacture && (
        <FactureModal
          facture={selectedFacture}
          clients={clients}
          produits={produits}
          onClose={() => {
            setShowNewFacture(false);
            setSelectedFacture(null);
          }}
          onSuccess={() => {
            setShowNewFacture(false);
            setSelectedFacture(null);
            loadData();
          }}
        />
      )}
    </div>
  );
};

// Composant Modal pour créer/éditer une facture
interface FactureModalProps {
  facture: Facture | null;
  clients: Client[];
  produits: Produit[];
  onClose: () => void;
  onSuccess: () => void;
}

const FactureModal: React.FC<FactureModalProps> = ({
  facture,
  clients,
  produits,
  onClose,
  onSuccess,
}) => {
  const notify = useNotify();
  const [formData, setFormData] = useState({
    type_document: "FACTURE" as "FACTURE" | "DEVIS" | "BON_LIVRAISON",
    client_id: "",
    date_emission: new Date().toISOString().split("T")[0],
    date_echeance: "",
    notes: "",
    conditions_paiement: "Paiement à 30 jours fin de mois",
  });

  const [lignes, setLignes] = useState<Partial<LigneFacture>[]>([
    { designation: "", quantite: 1, prix_unitaire_ht: 0, taux_tva: 19 },
  ]);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (facture) {
      setFormData({
        type_document: facture.type_document,
        client_id: facture.client.id,
        date_emission: facture.date_emission,
        date_echeance: facture.date_echeance || "",
        notes: facture.notes || "",
        conditions_paiement:
          facture.conditions_paiement || "Paiement à 30 jours fin de mois",
      });
      setLignes(
        facture.lignes.map((ligne) => ({
          designation: ligne.designation,
          quantite: ligne.quantite,
          prix_unitaire_ht: ligne.prix_unitaire_ht,
          taux_tva: ligne.taux_tva,
        }))
      );
    }
  }, [facture]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.client_id) {
      notify.error("Veuillez sélectionner un client");
      return;
    }

    if (lignes.length === 0 || !lignes[0].designation) {
      notify.error("Veuillez ajouter au moins une ligne");
      return;
    }

    setLoading(true);

    try {
      const payload = {
        ...formData,
        client: formData.client_id,
        lignes: lignes.filter((ligne) => ligne.designation),
      };

      const response = facture
        ? await FacturationService.updateFacture(facture.id, payload)
        : await FacturationService.createFacture(payload);

      if (response.success) {
        notify.success(
          facture ? "Facture modifiée avec succès" : "Facture créée avec succès"
        );
        onSuccess();
      } else {
        notify.error(response.error || "Erreur lors de la sauvegarde");
      }
    } catch (error) {
      console.error("Erreur:", error);
      notify.error("Erreur lors de la sauvegarde");
    } finally {
      setLoading(false);
    }
  };

  const addLigne = () => {
    setLignes([
      ...lignes,
      { designation: "", quantite: 1, prix_unitaire_ht: 0, taux_tva: 19 },
    ]);
  };

  const removeLigne = (index: number) => {
    setLignes(lignes.filter((_, i) => i !== index));
  };

  const updateLigne = (index: number, field: string, value: any) => {
    const newLignes = [...lignes];
    newLignes[index] = { ...newLignes[index], [field]: value };
    setLignes(newLignes);
  };

  const calculateTotal = () => {
    return lignes.reduce((total, ligne) => {
      const montantHT = (ligne.quantite || 0) * (ligne.prix_unitaire_ht || 0);
      const montantTVA = montantHT * ((ligne.taux_tva || 0) / 100);
      return total + montantHT + montantTVA;
    }, 0);
  };

  return (
    <div
      className="modal-overlay"
      onClick={onClose}>
      <div
        className="modal-content large-modal"
        onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>
            {facture ? "✏️ Modifier" : "➕ Nouvelle"} {formData.type_document}
          </h2>
          <button
            className="modal-close"
            onClick={onClose}>
            ✕
          </button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="modal-body">
          {/* Informations générales */}
          <div className="form-section">
            <h4>📋 Informations Générales</h4>
            <div className="form-grid">
              <div className="form-group">
                <label>Type de document</label>
                <select
                  value={formData.type_document}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      type_document: e.target.value as
                        | "FACTURE"
                        | "DEVIS"
                        | "BON_LIVRAISON",
                    })
                  }
                  className="form-input">
                  <option value="FACTURE">Facture</option>
                  <option value="DEVIS">Devis</option>
                  <option value="BON_LIVRAISON">Bon de livraison</option>
                </select>
              </div>

              <div className="form-group">
                <label>Client *</label>
                <select
                  value={formData.client_id}
                  onChange={(e) =>
                    setFormData({ ...formData, client_id: e.target.value })
                  }
                  className="form-input"
                  required>
                  <option value="">Sélectionner un client</option>
                  {clients.map((client) => (
                    <option
                      key={client.id}
                      value={client.id}>
                      {client.nom}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label>Date d'émission</label>
                <input
                  type="date"
                  value={formData.date_emission}
                  onChange={(e) =>
                    setFormData({ ...formData, date_emission: e.target.value })
                  }
                  className="form-input"
                  required
                />
              </div>

              <div className="form-group">
                <label>Date d'échéance</label>
                <input
                  type="date"
                  value={formData.date_echeance}
                  onChange={(e) =>
                    setFormData({ ...formData, date_echeance: e.target.value })
                  }
                  className="form-input"
                />
              </div>
            </div>
          </div>

          {/* Lignes de facture */}
          <div className="form-section">
            <div className="section-header">
              <h4>📦 Lignes de {formData.type_document}</h4>
              <button
                type="button"
                className="btn btn-outline"
                onClick={addLigne}>
                ➕ Ajouter une ligne
              </button>
            </div>

            <div className="lignes-container">
              {lignes.map((ligne, index) => (
                <div
                  key={index}
                  className="ligne-row">
                  <div className="ligne-fields">
                    <input
                      type="text"
                      placeholder="Désignation"
                      value={ligne.designation || ""}
                      onChange={(e) =>
                        updateLigne(index, "designation", e.target.value)
                      }
                      className="form-input"
                      required
                    />
                    <input
                      type="number"
                      placeholder="Qté"
                      value={ligne.quantite || 1}
                      onChange={(e) =>
                        updateLigne(
                          index,
                          "quantite",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="form-input quantity-input"
                      min="0"
                      step="0.001"
                    />
                    <input
                      type="number"
                      placeholder="Prix HT"
                      value={ligne.prix_unitaire_ht || 0}
                      onChange={(e) =>
                        updateLigne(
                          index,
                          "prix_unitaire_ht",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="form-input price-input"
                      min="0"
                      step="0.001"
                    />
                    <input
                      type="number"
                      placeholder="TVA %"
                      value={ligne.taux_tva || 19}
                      onChange={(e) =>
                        updateLigne(
                          index,
                          "taux_tva",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className="form-input tva-input"
                      min="0"
                      max="100"
                      step="0.01"
                    />
                    <div className="ligne-total">
                      {(
                        (ligne.quantite || 0) *
                        (ligne.prix_unitaire_ht || 0) *
                        (1 + (ligne.taux_tva || 0) / 100)
                      ).toFixed(3)}{" "}
                      DT
                    </div>
                  </div>
                  {lignes.length > 1 && (
                    <button
                      type="button"
                      className="btn-remove-ligne"
                      onClick={() => removeLigne(index)}>
                      🗑️
                    </button>
                  )}
                </div>
              ))}
            </div>

            <div className="total-section">
              <strong>Total TTC: {calculateTotal().toFixed(3)} DT</strong>
            </div>
          </div>

          {/* Notes et conditions */}
          <div className="form-section">
            <h4>📝 Informations Complémentaires</h4>
            <div className="form-group">
              <label>Notes</label>
              <textarea
                value={formData.notes}
                onChange={(e) =>
                  setFormData({ ...formData, notes: e.target.value })
                }
                className="form-textarea"
                rows={3}
                placeholder="Notes additionnelles..."
              />
            </div>
            <div className="form-group">
              <label>Conditions de paiement</label>
              <textarea
                value={formData.conditions_paiement}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    conditions_paiement: e.target.value,
                  })
                }
                className="form-textarea"
                rows={2}
              />
            </div>
          </div>
        </form>

        <div className="modal-footer">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onClose}>
            Annuler
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
            onClick={handleSubmit}>
            {loading ? "⏳ Enregistrement..." : "💾 Enregistrer"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FacturationProfessionnelle;
