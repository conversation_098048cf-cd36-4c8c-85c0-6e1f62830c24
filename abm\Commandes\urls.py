from django.urls import path, include
from rest_framework.routers import <PERSON>faultRout<PERSON>
from .views import CommandeViewSet, LigneCommandeViewSet, StatutCommandeViewSet

# Router pour les ViewSets
router = DefaultRouter()
router.register(r'commandes', CommandeViewSet, basename='commande')
router.register(r'lignes', LigneCommandeViewSet, basename='ligne-commande')
router.register(r'statuts', StatutCommandeViewSet, basename='statut-commande')

urlpatterns = [
    path('', include(router.urls)),
]
