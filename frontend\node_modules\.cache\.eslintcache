[{"C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Common\\ErrorBoundary.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Common\\SimpleNavigation.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\SimpleLoginForm.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\contexts\\AuthContext.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Settings\\AppSettings.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Dashboard\\SimpleDashboard.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Clients\\SimpleClients.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Settings\\ApiConnectionTest.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Common\\NotificationSystem.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\services\\apiService.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\config\\api.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\types\\auth.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\Payments.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\Stock.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\Orders.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\Products.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentsList.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentDetail.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentForm.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentStats.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentReconciliation.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrdersList.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\StockMovements.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\StockAlerts.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\StockOverview.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderStats.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderForm.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderDetail.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductsList.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductStats.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductForm.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductDetail.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Media\\FileUpload.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\services\\mediaService.ts": "37", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentDetails.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderDetails.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\NewProduct.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Clients\\NewClient.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Reports\\SimpleReports.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\NewOrder.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\RouteGuard.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\AuthPage.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Factures\\FacturationProfessionnelle.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\LoginForm.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Debug\\ServiceTest.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\hooks\\useAutoRefresh.ts": "49"}, {"size": 555, "mtime": 1755035085745, "results": "50", "hashOfConfig": "51"}, {"size": 711, "mtime": 1755035173253, "results": "52", "hashOfConfig": "51"}, {"size": 9189, "mtime": 1755446307419, "results": "53", "hashOfConfig": "51"}, {"size": 7426, "mtime": 1754443549182, "results": "54", "hashOfConfig": "51"}, {"size": 5550, "mtime": 1755446349938, "results": "55", "hashOfConfig": "51"}, {"size": 3703, "mtime": 1755381679734, "results": "56", "hashOfConfig": "51"}, {"size": 12403, "mtime": 1755891750656, "results": "57", "hashOfConfig": "51"}, {"size": 8523, "mtime": 1755116005999, "results": "58", "hashOfConfig": "51"}, {"size": 12553, "mtime": 1755298996778, "results": "59", "hashOfConfig": "51"}, {"size": 20514, "mtime": 1755643725808, "results": "60", "hashOfConfig": "51"}, {"size": 6818, "mtime": 1755120425117, "results": "61", "hashOfConfig": "51"}, {"size": 5635, "mtime": 1755108821800, "results": "62", "hashOfConfig": "51"}, {"size": 36493, "mtime": 1755813845998, "results": "63", "hashOfConfig": "51"}, {"size": 7834, "mtime": 1755291138178, "results": "64", "hashOfConfig": "51"}, {"size": 1488, "mtime": 1754782618030, "results": "65", "hashOfConfig": "51"}, {"size": 3936, "mtime": 1754856657926, "results": "66", "hashOfConfig": "51"}, {"size": 3967, "mtime": 1754856557984, "results": "67", "hashOfConfig": "51"}, {"size": 1991, "mtime": 1754853736157, "results": "68", "hashOfConfig": "51"}, {"size": 2208, "mtime": 1754853386218, "results": "69", "hashOfConfig": "51"}, {"size": 23659, "mtime": 1755196879766, "results": "70", "hashOfConfig": "51"}, {"size": 9603, "mtime": 1755112670540, "results": "71", "hashOfConfig": "51"}, {"size": 6881, "mtime": 1755112618796, "results": "72", "hashOfConfig": "51"}, {"size": 9163, "mtime": 1755112723463, "results": "73", "hashOfConfig": "51"}, {"size": 939, "mtime": 1755035889217, "results": "74", "hashOfConfig": "51"}, {"size": 31826, "mtime": 1755197018549, "results": "75", "hashOfConfig": "51"}, {"size": 23992, "mtime": 1754844321502, "results": "76", "hashOfConfig": "51"}, {"size": 17412, "mtime": 1755120556146, "results": "77", "hashOfConfig": "51"}, {"size": 13278, "mtime": 1755122851283, "results": "78", "hashOfConfig": "51"}, {"size": 9391, "mtime": 1755123035921, "results": "79", "hashOfConfig": "51"}, {"size": 25715, "mtime": 1755112553370, "results": "80", "hashOfConfig": "51"}, {"size": 19050, "mtime": 1755112582524, "results": "81", "hashOfConfig": "51"}, {"size": 16428, "mtime": 1755127150223, "results": "82", "hashOfConfig": "51"}, {"size": 10598, "mtime": 1755111015363, "results": "83", "hashOfConfig": "51"}, {"size": 15312, "mtime": 1755118420609, "results": "84", "hashOfConfig": "51"}, {"size": 20447, "mtime": 1755120511706, "results": "85", "hashOfConfig": "51"}, {"size": 8531, "mtime": 1754694603569, "results": "86", "hashOfConfig": "51"}, {"size": 8645, "mtime": 1755109845386, "results": "87", "hashOfConfig": "51"}, {"size": 7910, "mtime": 1755196797994, "results": "88", "hashOfConfig": "51"}, {"size": 8539, "mtime": 1755196947805, "results": "89", "hashOfConfig": "51"}, {"size": 13686, "mtime": 1755221555400, "results": "90", "hashOfConfig": "51"}, {"size": 13613, "mtime": 1755643504901, "results": "91", "hashOfConfig": "51"}, {"size": 8427, "mtime": 1755198356559, "results": "92", "hashOfConfig": "51"}, {"size": 12716, "mtime": 1755199207722, "results": "93", "hashOfConfig": "51"}, {"size": 5196, "mtime": 1755200067190, "results": "94", "hashOfConfig": "51"}, {"size": 3426, "mtime": 1755382298182, "results": "95", "hashOfConfig": "51"}, {"size": 27672, "mtime": 1755640564734, "results": "96", "hashOfConfig": "51"}, {"size": 9087, "mtime": 1755382233484, "results": "97", "hashOfConfig": "51"}, {"size": 7309, "mtime": 1755446230202, "results": "98", "hashOfConfig": "51"}, {"size": 4247, "mtime": 1755643675944, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9dhlpl", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\App.tsx", ["247"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Common\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Common\\SimpleNavigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\SimpleLoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\contexts\\AuthContext.tsx", ["248", "249"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Settings\\AppSettings.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Dashboard\\SimpleDashboard.tsx", [], ["250"], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Clients\\SimpleClients.tsx", ["251", "252"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Settings\\ApiConnectionTest.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Common\\NotificationSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\services\\apiService.ts", ["253", "254", "255", "256", "257"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\config\\api.ts", ["258", "259", "260"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\types\\auth.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\Payments.tsx", ["261"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\Stock.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\Orders.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\Products.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentsList.tsx", ["262", "263"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentDetail.tsx", ["264"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentForm.tsx", ["265"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentReconciliation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrdersList.tsx", ["266", "267"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\StockMovements.tsx", ["268", "269"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\StockAlerts.tsx", ["270", "271", "272"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Stock\\StockOverview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderStats.tsx", ["273", "274"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderForm.tsx", ["275", "276", "277"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderDetail.tsx", ["278"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductsList.tsx", ["279"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductStats.tsx", ["280", "281", "282"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductForm.tsx", ["283"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\ProductDetail.tsx", ["284"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Media\\FileUpload.tsx", ["285"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\services\\mediaService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Payments\\PaymentDetails.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\OrderDetails.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Products\\NewProduct.tsx", ["286"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Clients\\NewClient.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Reports\\SimpleReports.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Orders\\NewOrder.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\RouteGuard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\AuthPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Factures\\FacturationProfessionnelle.tsx", ["287", "288"], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\components\\Debug\\ServiceTest.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\abm\\frontend\\src\\hooks\\useAutoRefresh.ts", [], [], {"ruleId": "289", "severity": 1, "message": "290", "line": 42, "column": 45, "nodeType": "291", "messageId": "292", "endLine": 42, "endColumn": 60}, {"ruleId": "293", "severity": 1, "message": "294", "line": 198, "column": 6, "nodeType": "295", "endLine": 198, "endColumn": 8, "suggestions": "296"}, {"ruleId": "293", "severity": 1, "message": "294", "line": 213, "column": 6, "nodeType": "295", "endLine": 213, "endColumn": 27, "suggestions": "297"}, {"ruleId": "289", "severity": 1, "message": "298", "line": 126, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 126, "endColumn": 23, "suppressions": "299"}, {"ruleId": "289", "severity": 1, "message": "300", "line": 21, "column": 11, "nodeType": "291", "messageId": "292", "endLine": 21, "endColumn": 15}, {"ruleId": "289", "severity": 1, "message": "301", "line": 70, "column": 11, "nodeType": "291", "messageId": "292", "endLine": 70, "endColumn": 23}, {"ruleId": "289", "severity": 1, "message": "302", "line": 7, "column": 41, "nodeType": "291", "messageId": "292", "endLine": 7, "endColumn": 56}, {"ruleId": "289", "severity": 1, "message": "303", "line": 98, "column": 7, "nodeType": "291", "messageId": "292", "endLine": 98, "endColumn": 18}, {"ruleId": "289", "severity": 1, "message": "304", "line": 154, "column": 7, "nodeType": "291", "messageId": "292", "endLine": 154, "endColumn": 20}, {"ruleId": "289", "severity": 1, "message": "305", "line": 214, "column": 7, "nodeType": "291", "messageId": "292", "endLine": 214, "endColumn": 20}, {"ruleId": "306", "severity": 1, "message": "307", "line": 1413, "column": 1, "nodeType": "308", "endLine": 1428, "endColumn": 3}, {"ruleId": "309", "severity": 1, "message": "310", "line": 258, "column": 25, "nodeType": "311", "messageId": "312", "endLine": 258, "endColumn": 26, "suggestions": "313"}, {"ruleId": "309", "severity": 1, "message": "314", "line": 258, "column": 37, "nodeType": "311", "messageId": "312", "endLine": 258, "endColumn": 38, "suggestions": "315"}, {"ruleId": "309", "severity": 1, "message": "316", "line": 258, "column": 39, "nodeType": "311", "messageId": "312", "endLine": 258, "endColumn": 40, "suggestions": "317"}, {"ruleId": "289", "severity": 1, "message": "318", "line": 20, "column": 22, "nodeType": "291", "messageId": "292", "endLine": 20, "endColumn": 35}, {"ruleId": "289", "severity": 1, "message": "319", "line": 38, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 38, "endColumn": 15}, {"ruleId": "289", "severity": 1, "message": "320", "line": 41, "column": 24, "nodeType": "291", "messageId": "292", "endLine": 41, "endColumn": 39}, {"ruleId": "293", "severity": 1, "message": "321", "line": 37, "column": 6, "nodeType": "295", "endLine": 37, "endColumn": 10, "suggestions": "322"}, {"ruleId": "293", "severity": 1, "message": "323", "line": 36, "column": 6, "nodeType": "295", "endLine": 36, "endColumn": 17, "suggestions": "324"}, {"ruleId": "289", "severity": 1, "message": "325", "line": 6, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 6, "endColumn": 22}, {"ruleId": "289", "severity": 1, "message": "326", "line": 57, "column": 19, "nodeType": "291", "messageId": "292", "endLine": 57, "endColumn": 29}, {"ruleId": "289", "severity": 1, "message": "327", "line": 3, "column": 26, "nodeType": "291", "messageId": "292", "endLine": 3, "endColumn": 40}, {"ruleId": "293", "severity": 1, "message": "328", "line": 105, "column": 6, "nodeType": "295", "endLine": 105, "endColumn": 28, "suggestions": "329"}, {"ruleId": "289", "severity": 1, "message": "330", "line": 3, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 3, "endColumn": 24}, {"ruleId": "289", "severity": 1, "message": "331", "line": 57, "column": 7, "nodeType": "291", "messageId": "292", "endLine": 57, "endColumn": 24}, {"ruleId": "293", "severity": 1, "message": "332", "line": 85, "column": 6, "nodeType": "295", "endLine": 85, "endColumn": 28, "suggestions": "333"}, {"ruleId": "289", "severity": 1, "message": "325", "line": 6, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 6, "endColumn": 22}, {"ruleId": "334", "severity": 1, "message": "335", "line": 29, "column": 7, "nodeType": "291", "messageId": "336", "endLine": 29, "endColumn": 44}, {"ruleId": "289", "severity": 1, "message": "337", "line": 9, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 9, "endColumn": 16}, {"ruleId": "289", "severity": 1, "message": "330", "line": 10, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 10, "endColumn": 17}, {"ruleId": "293", "severity": 1, "message": "338", "line": 70, "column": 6, "nodeType": "295", "endLine": 70, "endColumn": 21, "suggestions": "339"}, {"ruleId": "293", "severity": 1, "message": "338", "line": 53, "column": 6, "nodeType": "295", "endLine": 53, "endColumn": 10, "suggestions": "340"}, {"ruleId": "289", "severity": 1, "message": "330", "line": 2, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 2, "endColumn": 24}, {"ruleId": "289", "severity": 1, "message": "330", "line": 7, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 7, "endColumn": 24}, {"ruleId": "334", "severity": 1, "message": "341", "line": 29, "column": 7, "nodeType": "291", "messageId": "336", "endLine": 29, "endColumn": 48}, {"ruleId": "293", "severity": 1, "message": "342", "line": 42, "column": 6, "nodeType": "295", "endLine": 42, "endColumn": 22, "suggestions": "343"}, {"ruleId": "289", "severity": 1, "message": "344", "line": 9, "column": 11, "nodeType": "291", "messageId": "292", "endLine": 9, "endColumn": 18}, {"ruleId": "293", "severity": 1, "message": "345", "line": 53, "column": 6, "nodeType": "295", "endLine": 53, "endColumn": 10, "suggestions": "346"}, {"ruleId": "289", "severity": 1, "message": "347", "line": 4, "column": 3, "nodeType": "291", "messageId": "292", "endLine": 4, "endColumn": 17}, {"ruleId": "289", "severity": 1, "message": "348", "line": 42, "column": 10, "nodeType": "291", "messageId": "292", "endLine": 42, "endColumn": 19}, {"ruleId": "289", "severity": 1, "message": "300", "line": 76, "column": 11, "nodeType": "291", "messageId": "292", "endLine": 76, "endColumn": 15}, {"ruleId": "293", "severity": 1, "message": "328", "line": 118, "column": 6, "nodeType": "295", "endLine": 118, "endColumn": 8, "suggestions": "349"}, "@typescript-eslint/no-unused-vars", "'hasModuleAccess' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'logout'. Either include it or remove the dependency array.", "ArrayExpression", ["350"], ["351"], "'notifications' is assigned a value but never used.", ["352"], "'user' is assigned a value but never used.", "'forceRefresh' is assigned a value but never used.", "'getErrorMessage' is defined but never used.", "'DEMO_ORDERS' is assigned a value but never used.", "'DEMO_INVOICES' is assigned a value but never used.", "'DEMO_PAYMENTS' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["353", "354"], "Unnecessary escape character: \\(.", ["355", "356"], "Unnecessary escape character: \\).", ["357", "358"], "'setActiveView' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setStatusFilter' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadPayment'. Either include it or remove the dependency array.", ["359"], "React Hook useEffect has a missing dependency: 'loadInvoice'. Either include it or remove the dependency array.", ["360"], "'OrderService' is defined but never used.", "'setFilters' is assigned a value but never used.", "'InvoiceService' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["361"], "'ProductService' is defined but never used.", "'getAlertTypeLabel' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadAlerts'. Either include it or remove the dependency array.", ["362"], "@typescript-eslint/no-redeclare", "'OrderStats' is already defined.", "redeclared", "'ClientService' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadOrder'. Either include it or remove the dependency array.", ["363"], ["364"], "'ProductStats' is already defined.", "React Hook useEffect has a missing dependency: 'loadStats'. Either include it or remove the dependency array.", ["365"], "'Product' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProduct'. Either include it or remove the dependency array.", ["366"], "'UploadProgress' is defined but never used.", "'isLoading' is assigned a value but never used.", ["367"], {"desc": "368", "fix": "369"}, {"desc": "370", "fix": "371"}, {"kind": "372", "justification": "373"}, {"messageId": "374", "fix": "375", "desc": "376"}, {"messageId": "377", "fix": "378", "desc": "379"}, {"messageId": "374", "fix": "380", "desc": "376"}, {"messageId": "377", "fix": "381", "desc": "379"}, {"messageId": "374", "fix": "382", "desc": "376"}, {"messageId": "377", "fix": "383", "desc": "379"}, {"desc": "384", "fix": "385"}, {"desc": "386", "fix": "387"}, {"desc": "388", "fix": "389"}, {"desc": "390", "fix": "391"}, {"desc": "392", "fix": "393"}, {"desc": "394", "fix": "395"}, {"desc": "396", "fix": "397"}, {"desc": "398", "fix": "399"}, {"desc": "400", "fix": "401"}, "Update the dependencies array to be: [logout]", {"range": "402", "text": "403"}, "Update the dependencies array to be: [user, sessionExpiry, logout]", {"range": "404", "text": "405"}, "directive", "", "removeEscape", {"range": "406", "text": "373"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "407", "text": "408"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "409", "text": "373"}, {"range": "410", "text": "408"}, {"range": "411", "text": "373"}, {"range": "412", "text": "408"}, "Update the dependencies array to be: [id, loadPayment]", {"range": "413", "text": "414"}, "Update the dependencies array to be: [invoiceId, loadInvoice]", {"range": "415", "text": "416"}, "Update the dependencies array to be: [filters, currentPage, loadData]", {"range": "417", "text": "418"}, "Update the dependencies array to be: [filters, currentPage, loadAlerts]", {"range": "419", "text": "420"}, "Update the dependencies array to be: [id, isEditing, loadOrder]", {"range": "421", "text": "422"}, "Update the dependencies array to be: [id, loadOrder]", {"range": "423", "text": "424"}, "Update the dependencies array to be: [loadStats, selectedPeriod]", {"range": "425", "text": "426"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "427", "text": "428"}, "Update the dependencies array to be: [loadData]", {"range": "429", "text": "430"}, [4929, 4931], "[logout]", [5349, 5370], "[user, sessionExpiry, logout]", [6659, 6660], [6659, 6659], "\\", [6671, 6672], [6671, 6671], [6673, 6674], [6673, 6673], [996, 1000], "[id, loadPayment]", [959, 970], "[invoiceId, loadInvoice]", [2493, 2515], "[filters, currentPage, loadData]", [2126, 2148], "[filters, currentPage, loadAlerts]", [1753, 1768], "[id, isEditing, loadOrder]", [1364, 1368], "[id, loadOrder]", [1062, 1078], "[loadStats, selectedPeriod]", [1443, 1447], "[id, loadProduct]", [3019, 3021], "[loadData]"]