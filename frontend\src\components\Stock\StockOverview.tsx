/**
 * Vue d'ensemble des stocks avec indicateurs clés
 */

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";

interface StockOverviewData {
  valeur_stock_total: number;
  nb_produits_total: number;
  nb_produits_en_stock: number;
  nb_produits_rupture: number;
  nb_produits_alerte: number;
  rotation_moyenne: number;
  couverture_moyenne: number;
  mouvements_jour: number;
  derniere_maj: string;
  alertes_critiques: Array<{
    id: string;
    produit_nom: string;
    stock_actuel: number;
    stock_minimum: number;
    statut: "RUPTURE" | "ALERTE" | "CRITIQUE";
  }>;
  top_mouvements: Array<{
    id: string;
    produit_nom: string;
    type: "ENTREE" | "SORTIE";
    quantite: number;
    date: string;
    motif: string;
  }>;
}

const StockOverview: React.FC = () => {
  const [data, setData] = useState<StockOverviewData>({
    valeur_stock_total: 0,
    nb_produits_total: 0,
    nb_produits_en_stock: 0,
    nb_produits_rupture: 0,
    nb_produits_alerte: 0,
    rotation_moyenne: 0,
    couverture_moyenne: 0,
    mouvements_jour: 0,
    derniere_maj: "",
    alertes_critiques: [],
    top_mouvements: [],
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStockOverview();
  }, []);

  const loadStockOverview = async () => {
    try {
      setLoading(true);

      // Simuler le chargement des données de stock
      const mockData: StockOverviewData = {
        valeur_stock_total: 125680.5,
        nb_produits_total: 245,
        nb_produits_en_stock: 198,
        nb_produits_rupture: 8,
        nb_produits_alerte: 15,
        rotation_moyenne: 4.2,
        couverture_moyenne: 45,
        mouvements_jour: 23,
        derniere_maj: new Date().toISOString(),
        alertes_critiques: [
          {
            id: "1",
            produit_nom: "Ordinateur portable Dell",
            stock_actuel: 0,
            stock_minimum: 5,
            statut: "RUPTURE",
          },
          {
            id: "2",
            produit_nom: "Souris Logitech",
            stock_actuel: 2,
            stock_minimum: 10,
            statut: "CRITIQUE",
          },
          {
            id: "3",
            produit_nom: "Clavier mécanique",
            stock_actuel: 8,
            stock_minimum: 15,
            statut: "ALERTE",
          },
        ],
        top_mouvements: [
          {
            id: "1",
            produit_nom: 'Écran 24" Samsung',
            type: "SORTIE",
            quantite: 5,
            date: "2024-08-10T14:30:00Z",
            motif: "Vente commande #CMD-2024-156",
          },
          {
            id: "2",
            produit_nom: "Imprimante HP",
            type: "ENTREE",
            quantite: 10,
            date: "2024-08-10T10:15:00Z",
            motif: "Réception commande fournisseur",
          },
          {
            id: "3",
            produit_nom: "Webcam Logitech",
            type: "SORTIE",
            quantite: 3,
            date: "2024-08-10T09:45:00Z",
            motif: "Vente directe",
          },
        ],
      };

      setData(mockData);
    } catch (error: any) {
      toast.error("Erreur lors du chargement des données de stock");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("fr-FR").format(value);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("fr-FR");
  };

  const getAlertColor = (statut: string) => {
    switch (statut) {
      case "RUPTURE":
        return "alert-danger";
      case "CRITIQUE":
        return "alert-warning";
      case "ALERTE":
        return "alert-info";
      default:
        return "alert-default";
    }
  };

  const getAlertLabel = (statut: string) => {
    switch (statut) {
      case "RUPTURE":
        return "🔴 Rupture";
      case "CRITIQUE":
        return "🟡 Critique";
      case "ALERTE":
        return "🟠 Alerte";
      default:
        return statut;
    }
  };

  const getMouvementIcon = (type: string) => {
    return type === "ENTREE" ? "📥" : "📤";
  };

  const getMouvementColor = (type: string) => {
    return type === "ENTREE" ? "movement-in" : "movement-out";
  };

  if (loading) {
    return (
      <div className="stock-loading">
        <div className="loading-spinner"></div>
        <p>Chargement de la vue d'ensemble des stocks...</p>
      </div>
    );
  }

  return (
    <div className="stock-overview">
      {/* KPI Stock */}
      <div className="stock-kpi-grid">
        <div className="kpi-card value">
          <div className="kpi-header">
            <h4>💰 Valeur du Stock</h4>
            <span className="kpi-period">Total</span>
          </div>
          <div className="kpi-value">
            {formatCurrency(data.valeur_stock_total)}
          </div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>
                {formatNumber(data.nb_produits_total)} produits référencés
              </span>
            </div>
          </div>
        </div>

        <div className="kpi-card availability">
          <div className="kpi-header">
            <h4>📦 Disponibilité</h4>
            <span className="kpi-period">Produits en stock</span>
          </div>
          <div className="kpi-value">
            {formatNumber(data.nb_produits_en_stock)}
          </div>
          <div className="kpi-percentage">
            {(
              (data.nb_produits_en_stock / data.nb_produits_total) *
              100
            ).toFixed(1)}
            %
          </div>
        </div>

        <div className="kpi-card alerts">
          <div className="kpi-header">
            <h4>🚨 Alertes</h4>
            <span className="kpi-period">Produits à surveiller</span>
          </div>
          <div className="kpi-value">
            {formatNumber(data.nb_produits_alerte + data.nb_produits_rupture)}
          </div>
          <div className="kpi-breakdown">
            <div className="breakdown-item danger">
              <span>🔴 {data.nb_produits_rupture} ruptures</span>
            </div>
            <div className="breakdown-item warning">
              <span>🟡 {data.nb_produits_alerte} alertes</span>
            </div>
          </div>
        </div>

        <div className="kpi-card rotation">
          <div className="kpi-header">
            <h4>🔄 Rotation</h4>
            <span className="kpi-period">Moyenne annuelle</span>
          </div>
          <div className="kpi-value">{data.rotation_moyenne.toFixed(1)}x</div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>Couverture: {data.couverture_moyenne} jours</span>
            </div>
          </div>
        </div>

        <div className="kpi-card activity">
          <div className="kpi-header">
            <h4>⚡ Activité</h4>
            <span className="kpi-period">Aujourd'hui</span>
          </div>
          <div className="kpi-value">{formatNumber(data.mouvements_jour)}</div>
          <div className="kpi-details">
            <div className="detail-item">
              <span>mouvements de stock</span>
            </div>
          </div>
        </div>

        <div className="kpi-card update">
          <div className="kpi-header">
            <h4>🔄 Dernière MAJ</h4>
            <span className="kpi-period">Synchronisation</span>
          </div>
          <div className="kpi-value-small">
            {formatDateTime(data.derniere_maj)}
          </div>
          <button
            className="kpi-action"
            onClick={() => loadStockOverview()}>
            🔄 Actualiser
          </button>
        </div>
      </div>

      {/* Alertes critiques */}
      <div className="critical-alerts">
        <div className="section-header">
          <h3>🚨 Alertes Critiques</h3>
          <button
            className="btn btn-warning btn-sm"
            onClick={() => toast.info("Fonctionnalité en développement")}>
            Voir toutes les alertes →
          </button>
        </div>

        <div className="alerts-list">
          {data.alertes_critiques.map((alerte) => (
            <div
              key={alerte.id}
              className={`alert-item ${getAlertColor(alerte.statut)}`}>
              <div className="alert-status">
                <span className="status-badge">
                  {getAlertLabel(alerte.statut)}
                </span>
              </div>
              <div className="alert-content">
                <h4>{alerte.produit_nom}</h4>
                <p>
                  Stock actuel: <strong>{alerte.stock_actuel}</strong> |
                  Minimum: <strong>{alerte.stock_minimum}</strong>
                </p>
              </div>
              <div className="alert-actions">
                <button
                  className="btn btn-sm btn-primary"
                  onClick={() =>
                    toast.info(`Ajuster le produit: ${alerte.produit_nom}`)
                  }>
                  📝 Ajuster
                </button>
                <button
                  className="btn btn-sm btn-success"
                  onClick={() =>
                    toast.info(`Commander: ${alerte.produit_nom}`)
                  }>
                  🛒 Commander
                </button>
              </div>
            </div>
          ))}
        </div>

        {data.alertes_critiques.length === 0 && (
          <div className="no-alerts">
            <div className="no-alerts-icon">✅</div>
            <p>Aucune alerte critique</p>
            <small>Tous les stocks sont dans les seuils normaux</small>
          </div>
        )}
      </div>

      {/* Derniers mouvements */}
      <div className="recent-movements">
        <div className="section-header">
          <h3>📦 Derniers Mouvements</h3>
          <button
            className="btn btn-info btn-sm"
            onClick={() => toast.info("Fonctionnalité en développement")}>
            Voir tous les mouvements →
          </button>
        </div>

        <div className="movements-list">
          {data.top_mouvements.map((mouvement) => (
            <div
              key={mouvement.id}
              className="movement-item">
              <div className="movement-icon">
                <span className={getMouvementColor(mouvement.type)}>
                  {getMouvementIcon(mouvement.type)}
                </span>
              </div>
              <div className="movement-content">
                <h4>{mouvement.produit_nom}</h4>
                <p className="movement-details">
                  <span
                    className={`movement-type ${mouvement.type.toLowerCase()}`}>
                    {mouvement.type === "ENTREE" ? "+" : "-"}
                    {mouvement.quantite}
                  </span>
                  <span className="movement-date">
                    {formatDateTime(mouvement.date)}
                  </span>
                </p>
                <p className="movement-reason">{mouvement.motif}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Actions rapides */}
      <div className="stock-quick-actions">
        <h3>⚡ Actions Rapides</h3>
        <div className="actions-grid">
          <button
            className="action-card"
            onClick={() => toast.info("Fonctionnalité en développement")}>
            <div className="action-icon">📥</div>
            <div className="action-label">Mouvement de stock</div>
          </button>

          <button
            className="action-card"
            onClick={() => toast.info("Fonctionnalité en développement")}>
            <div className="action-icon">📋</div>
            <div className="action-label">Nouvel inventaire</div>
          </button>

          <button
            className="action-card"
            onClick={() => toast.info("Fonctionnalité en développement")}>
            <div className="action-icon">📦</div>
            <div className="action-label">Nouveau produit</div>
          </button>

          <button
            className="action-card"
            onClick={() => toast.info("Fonctionnalité en développement")}>
            <div className="action-icon">🛒</div>
            <div className="action-label">Commander fournisseur</div>
          </button>

          <button
            className="action-card"
            onClick={() => toast.info("Fonctionnalité en développement")}>
            <div className="action-icon">📊</div>
            <div className="action-label">Analyses détaillées</div>
          </button>

          <button
            className="action-card"
            onClick={() => window.open("/api/stock/export", "_blank")}>
            <div className="action-icon">📤</div>
            <div className="action-label">Exporter données</div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default StockOverview;
