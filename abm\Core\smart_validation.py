"""
Système de validation intelligente des données avec prévention d'erreurs
"""
import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from decimal import Decimal, InvalidOperation
from datetime import datetime, date
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from django.utils import timezone
import phonenumbers
from phonenumbers import NumberParseException

logger = logging.getLogger(__name__)


class SmartValidator:
    """Validateur intelligent avec suggestions de correction"""
    
    def __init__(self):
        self.validation_rules = {
            'email': self.validate_email_smart,
            'phone': self.validate_phone_smart,
            'price': self.validate_price_smart,
            'quantity': self.validate_quantity_smart,
            'date': self.validate_date_smart,
            'text': self.validate_text_smart,
            'code': self.validate_code_smart,
            'fiscal_number': self.validate_fiscal_number_smart
        }
        
        # Patterns pour la validation tunisienne
        self.tunisian_patterns = {
            'phone': [
                r'^(\+216|216)?[2-9]\d{7}$',  # Téléphones fixes
                r'^(\+216|216)?[2-9]\d{7}$',  # Mobiles
            ],
            'fiscal_number': r'^\d{7}[A-Z]/[A-Z]/[A-Z]/\d{3}$',
            'postal_code': r'^\d{4}$'
        }
        
        # Suggestions communes
        self.common_corrections = {
            'email_domains': [
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
                'live.com', 'msn.com', 'orange.tn', 'topnet.tn'
            ],
            'tunisian_cities': [
                'Tunis', 'Sfax', 'Sousse', 'Kairouan', 'Bizerte', 'Gabès',
                'Ariana', 'Gafsa', 'Monastir', 'Ben Arous', 'Kasserine',
                'Médenine', 'Nabeul', 'Tataouine', 'Beja', 'Jendouba',
                'Mahdia', 'Manouba', 'Sidi Bouzid', 'Siliana', 'Tozeur',
                'Zaghouan', 'Kébili', 'Le Kef'
            ]
        }
    
    def validate_email_smart(self, email: str) -> Dict[str, Any]:
        """Validation intelligente d'email avec suggestions"""
        result = {
            'is_valid': False,
            'value': email,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if not email:
            result['errors'].append('Email requis')
            return result
        
        # Nettoyage basique
        cleaned_email = email.strip().lower()
        
        try:
            validate_email(cleaned_email)
            result['is_valid'] = True
            result['value'] = cleaned_email
        except ValidationError:
            result['errors'].append('Format d\'email invalide')
            
            # Suggestions de correction
            suggestions = self._suggest_email_corrections(email)
            result['suggestions'] = suggestions
        
        # Vérifications supplémentaires
        if '@' in cleaned_email:
            domain = cleaned_email.split('@')[1]
            
            # Vérifier les domaines suspects
            if domain in ['test.com', 'example.com', 'temp.com']:
                result['warnings'].append('Domaine email suspect')
            
            # Suggérer des domaines populaires si proche
            if not result['is_valid']:
                for common_domain in self.common_corrections['email_domains']:
                    if self._calculate_similarity(domain, common_domain) > 0.7:
                        suggested_email = cleaned_email.split('@')[0] + '@' + common_domain
                        result['suggestions'].append(suggested_email)
        
        return result
    
    def validate_phone_smart(self, phone: str) -> Dict[str, Any]:
        """Validation intelligente de téléphone tunisien"""
        result = {
            'is_valid': False,
            'value': phone,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if not phone:
            result['errors'].append('Numéro de téléphone requis')
            return result
        
        # Nettoyage
        cleaned_phone = re.sub(r'[\s\-\(\)]', '', phone.strip())
        
        try:
            # Parser avec phonenumbers pour la Tunisie
            parsed = phonenumbers.parse(cleaned_phone, 'TN')
            
            if phonenumbers.is_valid_number(parsed):
                result['is_valid'] = True
                result['value'] = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
            else:
                result['errors'].append('Numéro de téléphone invalide')
                
        except NumberParseException:
            result['errors'].append('Format de téléphone invalide')
            
            # Suggestions de correction
            suggestions = self._suggest_phone_corrections(cleaned_phone)
            result['suggestions'] = suggestions
        
        return result
    
    def validate_price_smart(self, price: Union[str, float, Decimal]) -> Dict[str, Any]:
        """Validation intelligente de prix"""
        result = {
            'is_valid': False,
            'value': price,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if price is None or price == '':
            result['errors'].append('Prix requis')
            return result
        
        try:
            # Conversion en Decimal
            if isinstance(price, str):
                # Nettoyer la chaîne (supprimer espaces, remplacer virgule par point)
                cleaned_price = price.strip().replace(',', '.').replace(' ', '')
                decimal_price = Decimal(cleaned_price)
            else:
                decimal_price = Decimal(str(price))
            
            # Validations
            if decimal_price < 0:
                result['errors'].append('Le prix ne peut pas être négatif')
            elif decimal_price == 0:
                result['warnings'].append('Prix à zéro - vérifiez si c\'est intentionnel')
            elif decimal_price > Decimal('1000000'):
                result['warnings'].append('Prix très élevé - vérifiez la saisie')
            
            # Vérifier le nombre de décimales
            if decimal_price.as_tuple().exponent < -3:
                result['warnings'].append('Plus de 3 décimales - sera arrondi')
                result['suggestions'].append(str(round(decimal_price, 3)))
            
            result['is_valid'] = len(result['errors']) == 0
            result['value'] = float(decimal_price)
            
        except (InvalidOperation, ValueError):
            result['errors'].append('Format de prix invalide')
            
            # Suggestions de correction
            if isinstance(price, str):
                # Essayer de corriger les erreurs communes
                corrected = price.replace(',', '.').replace(' ', '')
                if re.match(r'^\d+[\.,]?\d*$', corrected):
                    result['suggestions'].append(corrected)
        
        return result
    
    def validate_quantity_smart(self, quantity: Union[str, int, float]) -> Dict[str, Any]:
        """Validation intelligente de quantité"""
        result = {
            'is_valid': False,
            'value': quantity,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if quantity is None or quantity == '':
            result['errors'].append('Quantité requise')
            return result
        
        try:
            # Conversion
            if isinstance(quantity, str):
                cleaned_qty = quantity.strip().replace(',', '.')
                float_qty = float(cleaned_qty)
            else:
                float_qty = float(quantity)
            
            # Validations
            if float_qty < 0:
                result['errors'].append('La quantité ne peut pas être négative')
            elif float_qty == 0:
                result['warnings'].append('Quantité à zéro')
            elif float_qty != int(float_qty) and float_qty < 1:
                result['warnings'].append('Quantité fractionnaire inférieure à 1')
            
            result['is_valid'] = len(result['errors']) == 0
            result['value'] = float_qty
            
        except (ValueError, TypeError):
            result['errors'].append('Format de quantité invalide')
            
            # Suggestions
            if isinstance(quantity, str) and quantity.strip():
                # Essayer de nettoyer
                cleaned = re.sub(r'[^\d\.,]', '', quantity)
                if cleaned:
                    result['suggestions'].append(cleaned)
        
        return result
    
    def validate_date_smart(self, date_value: Union[str, date, datetime]) -> Dict[str, Any]:
        """Validation intelligente de date"""
        result = {
            'is_valid': False,
            'value': date_value,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if not date_value:
            result['errors'].append('Date requise')
            return result
        
        try:
            if isinstance(date_value, str):
                # Essayer différents formats
                formats = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%Y/%m/%d']
                parsed_date = None
                
                for fmt in formats:
                    try:
                        parsed_date = datetime.strptime(date_value.strip(), fmt).date()
                        break
                    except ValueError:
                        continue
                
                if not parsed_date:
                    result['errors'].append('Format de date invalide')
                    return result
                
            elif isinstance(date_value, datetime):
                parsed_date = date_value.date()
            else:
                parsed_date = date_value
            
            # Validations logiques
            today = timezone.now().date()
            
            if parsed_date > today + timezone.timedelta(days=365):
                result['warnings'].append('Date très éloignée dans le futur')
            elif parsed_date < today - timezone.timedelta(days=365*10):
                result['warnings'].append('Date très ancienne')
            
            result['is_valid'] = True
            result['value'] = parsed_date.isoformat()
            
        except (ValueError, TypeError):
            result['errors'].append('Date invalide')
            
            # Suggestions de format
            if isinstance(date_value, str):
                result['suggestions'].append('Format attendu: YYYY-MM-DD ou DD/MM/YYYY')
        
        return result
    
    def validate_text_smart(self, text: str, min_length: int = 1, max_length: int = 255) -> Dict[str, Any]:
        """Validation intelligente de texte"""
        result = {
            'is_valid': False,
            'value': text,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if not text:
            result['errors'].append('Texte requis')
            return result
        
        cleaned_text = text.strip()
        
        # Validations de longueur
        if len(cleaned_text) < min_length:
            result['errors'].append(f'Texte trop court (minimum {min_length} caractères)')
        elif len(cleaned_text) > max_length:
            result['errors'].append(f'Texte trop long (maximum {max_length} caractères)')
            result['suggestions'].append(cleaned_text[:max_length])
        
        # Vérifications de qualité
        if cleaned_text.isupper():
            result['warnings'].append('Texte entièrement en majuscules')
            result['suggestions'].append(cleaned_text.title())
        elif cleaned_text.islower():
            result['warnings'].append('Texte entièrement en minuscules')
            result['suggestions'].append(cleaned_text.title())
        
        # Vérifier les caractères suspects
        if re.search(r'[<>{}[\]\\]', cleaned_text):
            result['warnings'].append('Caractères suspects détectés')
        
        result['is_valid'] = len(result['errors']) == 0
        result['value'] = cleaned_text
        
        return result
    
    def validate_code_smart(self, code: str) -> Dict[str, Any]:
        """Validation intelligente de code produit"""
        result = {
            'is_valid': False,
            'value': code,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if not code:
            result['errors'].append('Code requis')
            return result
        
        cleaned_code = code.strip().upper()
        
        # Validations de format
        if len(cleaned_code) < 3:
            result['errors'].append('Code trop court (minimum 3 caractères)')
        elif len(cleaned_code) > 20:
            result['errors'].append('Code trop long (maximum 20 caractères)')
        
        # Vérifier les caractères autorisés
        if not re.match(r'^[A-Z0-9\-_]+$', cleaned_code):
            result['errors'].append('Code doit contenir uniquement lettres, chiffres, tirets et underscores')
            # Suggérer une correction
            suggested = re.sub(r'[^A-Z0-9\-_]', '', cleaned_code.upper())
            if suggested:
                result['suggestions'].append(suggested)
        
        result['is_valid'] = len(result['errors']) == 0
        result['value'] = cleaned_code
        
        return result
    
    def validate_fiscal_number_smart(self, fiscal_number: str) -> Dict[str, Any]:
        """Validation intelligente de numéro fiscal tunisien"""
        result = {
            'is_valid': False,
            'value': fiscal_number,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        if not fiscal_number:
            result['errors'].append('Numéro fiscal requis')
            return result
        
        cleaned = fiscal_number.strip().upper()
        
        # Format tunisien: 1234567X/Y/Z/000
        if re.match(self.tunisian_patterns['fiscal_number'], cleaned):
            result['is_valid'] = True
            result['value'] = cleaned
        else:
            result['errors'].append('Format de numéro fiscal invalide')
            result['suggestions'].append('Format attendu: 1234567X/Y/Z/000')
        
        return result
    
    def validate_form_data(self, data: Dict[str, Any], validation_schema: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Valide un formulaire complet selon un schéma"""
        results = {
            'is_valid': True,
            'fields': {},
            'global_errors': [],
            'global_warnings': []
        }
        
        for field_name, field_config in validation_schema.items():
            field_value = data.get(field_name)
            validation_type = field_config.get('type', 'text')
            
            # Appliquer la validation appropriée
            if validation_type in self.validation_rules:
                field_result = self.validation_rules[validation_type](field_value)
                
                # Appliquer les paramètres spécifiques du champ
                if validation_type == 'text':
                    min_len = field_config.get('min_length', 1)
                    max_len = field_config.get('max_length', 255)
                    field_result = self.validate_text_smart(field_value, min_len, max_len)
                
                results['fields'][field_name] = field_result
                
                if not field_result['is_valid']:
                    results['is_valid'] = False
        
        return results
    
    def _suggest_email_corrections(self, email: str) -> List[str]:
        """Suggère des corrections pour un email invalide"""
        suggestions = []
        
        if '@' not in email:
            # Ajouter @ si manquant
            if '.' in email:
                parts = email.split('.')
                if len(parts) >= 2:
                    suggestions.append(f"{parts[0]}@{'.'.join(parts[1:])}")
        
        return suggestions
    
    def _suggest_phone_corrections(self, phone: str) -> List[str]:
        """Suggère des corrections pour un téléphone invalide"""
        suggestions = []
        
        # Ajouter l'indicatif tunisien si manquant
        if len(phone) == 8 and phone.isdigit():
            suggestions.append(f"+216{phone}")
            suggestions.append(f"216{phone}")
        
        return suggestions
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calcule la similarité entre deux chaînes"""
        from difflib import SequenceMatcher
        return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()


# Instance globale du validateur
smart_validator = SmartValidator()


class ValidationTasks:
    """Tâches de validation automatisées"""
    
    @staticmethod
    def validate_existing_data():
        """Valide les données existantes et génère un rapport"""
        try:
            from Facturation.models import Client, Produit
            
            validation_report = {
                'clients': {'total': 0, 'errors': 0, 'warnings': 0},
                'produits': {'total': 0, 'errors': 0, 'warnings': 0},
                'details': []
            }
            
            # Valider les clients
            clients = Client.objects.filter(is_active=True)
            validation_report['clients']['total'] = clients.count()
            
            for client in clients:
                client_validation = {
                    'email': smart_validator.validate_email_smart(client.email or ''),
                    'phone': smart_validator.validate_phone_smart(client.telephone or ''),
                    'name': smart_validator.validate_text_smart(client.nom or '', min_length=2)
                }
                
                has_errors = any(not v['is_valid'] for v in client_validation.values())
                has_warnings = any(v['warnings'] for v in client_validation.values())
                
                if has_errors:
                    validation_report['clients']['errors'] += 1
                if has_warnings:
                    validation_report['clients']['warnings'] += 1
                
                if has_errors or has_warnings:
                    validation_report['details'].append({
                        'type': 'client',
                        'id': str(client.id),
                        'name': client.nom,
                        'validation': client_validation
                    })
            
            # Valider les produits
            produits = Produit.objects.filter(is_active=True)
            validation_report['produits']['total'] = produits.count()
            
            for produit in produits:
                produit_validation = {
                    'name': smart_validator.validate_text_smart(produit.nom or '', min_length=2),
                    'code': smart_validator.validate_code_smart(produit.code_produit or ''),
                    'price': smart_validator.validate_price_smart(produit.prix_unitaire)
                }
                
                has_errors = any(not v['is_valid'] for v in produit_validation.values())
                has_warnings = any(v['warnings'] for v in produit_validation.values())
                
                if has_errors:
                    validation_report['produits']['errors'] += 1
                if has_warnings:
                    validation_report['produits']['warnings'] += 1
                
                if has_errors or has_warnings:
                    validation_report['details'].append({
                        'type': 'produit',
                        'id': str(produit.id),
                        'name': produit.nom,
                        'validation': produit_validation
                    })
            
            logger.info(f"Validation terminée: {validation_report['clients']['errors'] + validation_report['produits']['errors']} erreurs trouvées")
            
            return validation_report
            
        except Exception as e:
            logger.error(f"Erreur lors de la validation des données: {e}")
            return None
