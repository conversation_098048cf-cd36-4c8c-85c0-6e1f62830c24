"""
Administration Django pour le module E-commerce
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    CategorieEcommerce, ProduitEcommerce, ImageProduitEcommerce,
    Panier, ItemPanier, CommandeEcommerce, LigneCommandeEcommerce
)


@admin.register(CategorieEcommerce)
class CategorieEcommerceAdmin(admin.ModelAdmin):
    """Administration des catégories e-commerce"""
    list_display = ['nom', 'parent', 'ordre', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['nom', 'description']
    prepopulated_fields = {'slug': ('nom',)}
    ordering = ['ordre', 'nom']
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('nom', 'slug', 'description', 'parent', 'ordre', 'is_active')
        }),
        ('Image', {
            'fields': ('image',)
        }),
        ('SEO', {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
    )


class ImageProduitEcommerceInline(admin.TabularInline):
    """Inline pour les images de produits e-commerce"""
    model = ImageProduitEcommerce
    extra = 1
    fields = ['image', 'alt_text', 'ordre', 'principale']


@admin.register(ProduitEcommerce)
class ProduitEcommerceAdmin(admin.ModelAdmin):
    """Administration des produits e-commerce"""
    list_display = [
        'produit_nom', 'slug', 'visible_en_ligne', 'en_vedette', 'nouveau',
        'en_promotion', 'prix_actuel', 'stock_disponible_reel', 'nombre_vues', 'nombre_ventes'
    ]
    list_filter = [
        'visible_en_ligne', 'en_vedette', 'nouveau', 'en_promotion',
        'gestion_stock', 'categories_ecommerce', 'created_at'
    ]
    search_fields = ['produit__nom', 'produit__code_produit', 'slug']
    # prepopulated_fields = {'slug': ('produit__nom',)}  # Ne fonctionne pas avec les relations
    filter_horizontal = ['categories_ecommerce']
    inlines = [ImageProduitEcommerceInline]
    
    fieldsets = (
        ('Produit', {
            'fields': ('produit', 'slug', 'categories_ecommerce')
        }),
        ('Visibilité', {
            'fields': ('visible_en_ligne', 'en_vedette', 'nouveau', 'en_promotion')
        }),
        ('Prix', {
            'fields': ('prix_public', 'prix_promo', 'date_debut_promo', 'date_fin_promo')
        }),
        ('Stock', {
            'fields': ('stock_disponible', 'stock_reserve', 'gestion_stock', 'autoriser_commande_sans_stock')
        }),
        ('Descriptions', {
            'fields': ('description_courte', 'description_longue', 'caracteristiques')
        }),
        ('Dimensions et poids', {
            'fields': ('poids', 'dimensions_longueur', 'dimensions_largeur', 'dimensions_hauteur'),
            'classes': ('collapse',)
        }),
        ('SEO', {
            'fields': ('meta_title', 'meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
        ('Statistiques', {
            'fields': ('nombre_vues', 'nombre_ventes', 'note_moyenne', 'nombre_avis'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['nombre_vues', 'nombre_ventes', 'note_moyenne', 'nombre_avis']
    
    def produit_nom(self, obj):
        return obj.produit.nom
    produit_nom.short_description = 'Produit'
    produit_nom.admin_order_field = 'produit__nom'


class ItemPanierInline(admin.TabularInline):
    """Inline pour les articles du panier"""
    model = ItemPanier
    extra = 0
    readonly_fields = ['total_ht', 'total_tva', 'total_ttc']


@admin.register(Panier)
class PanierAdmin(admin.ModelAdmin):
    """Administration des paniers"""
    list_display = ['__str__', 'total_articles', 'total_ttc', 'created_at', 'updated_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['client__nom', 'client__prenom', 'client__email', 'session_key']
    readonly_fields = ['total_articles', 'total_ht', 'total_tva', 'total_ttc']
    inlines = [ItemPanierInline]
    
    fieldsets = (
        ('Informations', {
            'fields': ('client', 'session_key')
        }),
        ('Totaux', {
            'fields': ('total_articles', 'total_ht', 'total_tva', 'total_ttc'),
            'classes': ('collapse',)
        }),
    )


class LigneCommandeEcommerceInline(admin.TabularInline):
    """Inline pour les lignes de commande e-commerce"""
    model = LigneCommandeEcommerce
    extra = 0
    readonly_fields = ['total_ht', 'total_tva', 'total_ttc']
    fields = [
        'produit_ecommerce', 'nom_produit', 'code_produit',
        'quantite', 'prix_unitaire', 'taux_tva',
        'total_ht', 'total_tva', 'total_ttc'
    ]


@admin.register(CommandeEcommerce)
class CommandeEcommerceAdmin(admin.ModelAdmin):
    """Administration des commandes e-commerce"""
    list_display = [
        'numero', 'client_nom', 'statut', 'total_ttc',
        'methode_paiement', 'statut_paiement', 'created_at'
    ]
    list_filter = [
        'statut', 'statut_paiement', 'methode_paiement',
        'created_at', 'date_expedition'
    ]
    search_fields = [
        'numero', 'client__nom', 'client__prenom', 'client__email',
        'reference_paiement', 'numero_suivi'
    ]
    readonly_fields = [
        'numero', 'total_ht', 'total_tva', 'total_ttc',
        'created_at', 'updated_at'
    ]
    inlines = [LigneCommandeEcommerceInline]
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('numero', 'client', 'statut', 'created_at', 'updated_at')
        }),
        ('Montants', {
            'fields': ('total_ht', 'total_tva', 'total_ttc', 'frais_livraison')
        }),
        ('Adresses', {
            'fields': ('adresse_facturation', 'adresse_livraison'),
            'classes': ('collapse',)
        }),
        ('Livraison', {
            'fields': (
                'transporteur', 'numero_suivi', 'date_expedition',
                'date_livraison_prevue', 'date_livraison_reelle'
            )
        }),
        ('Paiement', {
            'fields': ('methode_paiement', 'statut_paiement', 'reference_paiement')
        }),
        ('Notes', {
            'fields': ('notes_client', 'notes_interne'),
            'classes': ('collapse',)
        }),
    )
    
    def client_nom(self, obj):
        return obj.client.nom_complet
    client_nom.short_description = 'Client'
    client_nom.admin_order_field = 'client__nom'
    
    actions = ['marquer_confirmee', 'marquer_en_preparation', 'marquer_expediee']
    
    def marquer_confirmee(self, request, queryset):
        """Marquer les commandes comme confirmées"""
        updated = queryset.filter(statut='EN_ATTENTE').update(statut='CONFIRMEE')
        self.message_user(request, f'{updated} commande(s) marquée(s) comme confirmée(s).')
    marquer_confirmee.short_description = "Marquer comme confirmée"
    
    def marquer_en_preparation(self, request, queryset):
        """Marquer les commandes en préparation"""
        updated = queryset.filter(statut='CONFIRMEE').update(statut='EN_PREPARATION')
        self.message_user(request, f'{updated} commande(s) marquée(s) en préparation.')
    marquer_en_preparation.short_description = "Marquer en préparation"
    
    def marquer_expediee(self, request, queryset):
        """Marquer les commandes comme expédiées"""
        from django.utils import timezone
        updated = 0
        for commande in queryset.filter(statut='EN_PREPARATION'):
            commande.statut = 'EXPEDIEE'
            if not commande.date_expedition:
                commande.date_expedition = timezone.now()
            commande.save()
            updated += 1
        self.message_user(request, f'{updated} commande(s) marquée(s) comme expédiée(s).')
    marquer_expediee.short_description = "Marquer comme expédiée"


# Configuration de l'admin
admin.site.site_header = "ABM - Administration E-commerce"
admin.site.site_title = "ABM Admin"
admin.site.index_title = "Gestion E-commerce"
