/**
 * Formulaire de création/édition de commandes
 */

import React, { useState, useEffect } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  OrderService,
  ClientService,
  ProductService,
} from "../../services/apiService";
import { useNotify } from "../Common/NotificationSystem";

interface OrderLine {
  id?: string;
  produit_id: string;
  produit_nom: string;
  description: string;
  quantite: number;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
}

interface OrderFormData {
  numero: string;
  client_id: string;
  date_commande: string;
  date_livraison_prevue: string;
  priorite: "NORMALE" | "HAUTE" | "URGENTE";
  notes: string;
  lignes: OrderLine[];
}

const OrderForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const notify = useNotify();

  const [formData, setFormData] = useState<OrderFormData>({
    numero: "",
    client_id: searchParams.get("client") || "",
    date_commande: new Date().toISOString().split("T")[0],
    date_livraison_prevue: "",
    priorite: "NORMALE",
    notes: "",
    lignes: [],
  });

  const [clients, setClients] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [currentStep, setCurrentStep] = useState(1);

  const isEditing = !!id;

  useEffect(() => {
    loadClients();
    loadProducts();
    generateOrderNumber();

    if (isEditing) {
      loadOrder(id);
    }
  }, [id, isEditing]);

  const loadClients = async () => {
    try {
      // Simulation de données clients
      const mockClients = [
        {
          id: "2017",
          nom: "CLIENT FOND FUTURISTE",
          email: "<EMAIL>",
          telephone: "+216 71 123 456",
          adresse: "7123 Avenue Information Futuriste",
        },
        {
          id: "2018",
          nom: "ENTREPRISE MODERNE SARL",
          email: "<EMAIL>",
          telephone: "+216 71 234 567",
          adresse: "456 Rue de l'Innovation, Tunis",
        },
        {
          id: "2019",
          nom: "SOCIÉTÉ TECHNOLOGIE AVANCÉE",
          email: "<EMAIL>",
          telephone: "+216 71 345 678",
          adresse: "789 Boulevard du Progrès, Sfax",
        },
      ];
      setClients(mockClients);
    } catch (error) {
      console.error("Erreur chargement clients:", error);
    }
  };

  const loadProducts = async () => {
    try {
      // Simulation de données produits
      const mockProducts = [
        {
          id: "B1994471111",
          nom: "PRODUIT DESIGN FUTURISTE",
          code_produit: "B1994471111",
          prix_unitaire: 57.015,
          taux_tva: 19,
          stock_actuel: 50,
          description: "Produit design moderne et futuriste",
        },
        {
          id: "B1991720001",
          nom: "ARTICLE PREMIER PLAN M...",
          code_produit: "B1991720001",
          prix_unitaire: 25.0,
          taux_tva: 19,
          stock_actuel: 30,
          description: "Article de première qualité",
        },
        {
          id: "B1993360002",
          nom: "SERVICE PLUS DANS ELEGANT",
          code_produit: "B1993360002",
          prix_unitaire: 45.5,
          taux_tva: 19,
          stock_actuel: 20,
          description: "Service élégant et professionnel",
        },
      ];
      setProducts(mockProducts);
    } catch (error) {
      console.error("Erreur chargement produits:", error);
    }
  };

  const generateOrderNumber = () => {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, "0");
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");
    setFormData((prev) => ({
      ...prev,
      numero: `CMD-${year}${month}-${random}`,
    }));
  };

  const loadOrder = async (orderId: string) => {
    try {
      setLoading(true);
      const response = await OrderService.getOrder(orderId);

      if (response.success) {
        const order = response.data;
        setFormData({
          numero: order.numero,
          client_id: order.client_id,
          date_commande: order.date_commande,
          date_livraison_prevue: order.date_livraison_prevue || "",
          priorite: order.priorite,
          notes: order.notes || "",
          lignes: order.lignes || [],
        });
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      notify.error("Erreur lors du chargement de la commande");
      navigate("/orders");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const addLine = () => {
    const newLine: OrderLine = {
      produit_id: "",
      produit_nom: "",
      description: "",
      quantite: 1,
      prix_unitaire: 0,
      taux_tva: 20,
      montant_ht: 0,
      montant_tva: 0,
      montant_ttc: 0,
    };

    setFormData((prev) => ({
      ...prev,
      lignes: [...prev.lignes, newLine],
    }));
  };

  const removeLine = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      lignes: prev.lignes.filter((_, i) => i !== index),
    }));
  };

  const updateLine = (index: number, field: string, value: any) => {
    const newLines = [...formData.lignes];
    newLines[index] = { ...newLines[index], [field]: value };

    // Recalculer les montants si nécessaire
    if (["quantite", "prix_unitaire", "taux_tva"].includes(field)) {
      const line = newLines[index];
      line.montant_ht = line.quantite * line.prix_unitaire;
      line.montant_tva = line.montant_ht * (line.taux_tva / 100);
      line.montant_ttc = line.montant_ht + line.montant_tva;
    }

    // Si produit sélectionné, remplir les infos
    if (field === "produit_id" && value) {
      const product = products.find((p) => p.id === value);
      if (product) {
        newLines[index] = {
          ...newLines[index],
          produit_nom: product.nom,
          description: product.description || product.nom,
          prix_unitaire: product.prix_unitaire,
          taux_tva: product.taux_tva,
        };

        // Recalculer
        const line = newLines[index];
        line.montant_ht = line.quantite * line.prix_unitaire;
        line.montant_tva = line.montant_ht * (line.taux_tva / 100);
        line.montant_ttc = line.montant_ht + line.montant_tva;
      }
    }

    setFormData((prev) => ({ ...prev, lignes: newLines }));
  };

  const calculateTotals = () => {
    const totalHT = formData.lignes.reduce(
      (sum, ligne) => sum + ligne.montant_ht,
      0
    );
    const totalTVA = formData.lignes.reduce(
      (sum, ligne) => sum + ligne.montant_tva,
      0
    );
    const totalTTC = totalHT + totalTVA;

    return { totalHT, totalTVA, totalTTC };
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.numero.trim()) {
      newErrors.numero = "Numéro de commande requis";
    }

    if (!formData.client_id) {
      newErrors.client_id = "Client requis";
    }

    if (!formData.date_commande) {
      newErrors.date_commande = "Date de commande requise";
    }

    if (formData.lignes.length === 0) {
      newErrors.lignes = "Au moins une ligne est requise";
    }

    formData.lignes.forEach((ligne, index) => {
      if (!ligne.produit_id) {
        newErrors[`ligne_${index}_produit`] = "Produit requis";
      }
      if (ligne.quantite <= 0) {
        newErrors[`ligne_${index}_quantite`] = "Quantité doit être positive";
      }
      if (ligne.prix_unitaire <= 0) {
        newErrors[`ligne_${index}_prix`] = "Prix doit être positif";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (
    status: "BROUILLON" | "CONFIRMEE" = "BROUILLON"
  ) => {
    if (!validateForm()) {
      notify.error("Veuillez corriger les erreurs du formulaire");
      return;
    }

    try {
      setSaving(true);

      const { totalHT, totalTVA, totalTTC } = calculateTotals();

      const orderData = {
        ...formData,
        statut: status,
        montant_ht: totalHT,
        montant_tva: totalTVA,
        montant_total: totalTTC,
      };

      let response;
      if (isEditing) {
        response = await OrderService.updateOrder(id, orderData);
      } else {
        response = await OrderService.createOrder(orderData);
      }

      if (response.success) {
        notify.success(
          isEditing
            ? "Commande mise à jour avec succès"
            : `Commande ${
                status === "CONFIRMEE" ? "créée et confirmée" : "sauvegardée"
              } avec succès`
        );
        navigate(`/orders/${response.data.id}`);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      notify.error(error.message || "Erreur lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  const { totalHT, totalTVA, totalTTC } = calculateTotals();

  if (loading) {
    return (
      <div className="order-form-loading">
        <div className="loading-spinner"></div>
        <p>Chargement de la commande...</p>
      </div>
    );
  }

  return (
    <div className="order-form">
      {/* Header */}
      <div className="form-header">
        <div className="header-title">
          <h1>
            {isEditing ? "✏️ Modifier la commande" : "➕ Nouvelle commande"}
          </h1>
          <p>
            {isEditing
              ? "Modifiez les informations de la commande"
              : "Créez une nouvelle commande client"}
          </p>
        </div>
        <div className="header-actions">
          <button
            className="btn btn-outline"
            onClick={() => navigate("/orders")}>
            ← Retour
          </button>
        </div>
      </div>

      {/* Étapes */}
      <div className="form-steps">
        <div className={`step ${currentStep >= 1 ? "active" : ""}`}>
          <span className="step-number">1</span>
          <span className="step-label">Informations</span>
        </div>
        <div className={`step ${currentStep >= 2 ? "active" : ""}`}>
          <span className="step-number">2</span>
          <span className="step-label">Produits</span>
        </div>
        <div className={`step ${currentStep >= 3 ? "active" : ""}`}>
          <span className="step-number">3</span>
          <span className="step-label">Finalisation</span>
        </div>
      </div>

      {/* Étape 1: Informations générales */}
      {currentStep === 1 && (
        <div className="form-section">
          <h3>📋 Informations Générales</h3>

          <div className="form-grid">
            <div className="form-group">
              <label>Numéro de commande *</label>
              <input
                type="text"
                value={formData.numero}
                onChange={(e) => handleInputChange("numero", e.target.value)}
                className={`form-input ${errors.numero ? "error" : ""}`}
                placeholder="CMD-2024-001"
              />
              {errors.numero && (
                <span className="error-text">{errors.numero}</span>
              )}
            </div>

            <div className="form-group">
              <label>Client *</label>
              <select
                value={formData.client_id}
                onChange={(e) => handleInputChange("client_id", e.target.value)}
                className={`form-select ${errors.client_id ? "error" : ""}`}>
                <option value="">Sélectionner un client</option>
                {clients.map((client) => (
                  <option
                    key={client.id}
                    value={client.id}>
                    {client.nom} - {client.email}
                  </option>
                ))}
              </select>
              {errors.client_id && (
                <span className="error-text">{errors.client_id}</span>
              )}
            </div>

            <div className="form-group">
              <label>Date de commande *</label>
              <input
                type="date"
                value={formData.date_commande}
                onChange={(e) =>
                  handleInputChange("date_commande", e.target.value)
                }
                className={`form-input ${errors.date_commande ? "error" : ""}`}
              />
              {errors.date_commande && (
                <span className="error-text">{errors.date_commande}</span>
              )}
            </div>

            <div className="form-group">
              <label>Date de livraison prévue</label>
              <input
                type="date"
                value={formData.date_livraison_prevue}
                onChange={(e) =>
                  handleInputChange("date_livraison_prevue", e.target.value)
                }
                className="form-input"
                min={formData.date_commande}
              />
            </div>

            <div className="form-group">
              <label>Priorité</label>
              <select
                value={formData.priorite}
                onChange={(e) => handleInputChange("priorite", e.target.value)}
                className="form-select">
                <option value="NORMALE">🟢 Normale</option>
                <option value="HAUTE">🟡 Haute</option>
                <option value="URGENTE">🔴 Urgente</option>
              </select>
            </div>

            <div className="form-group full-width">
              <label>Notes</label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                className="form-textarea"
                rows={3}
                placeholder="Notes sur la commande..."
              />
            </div>
          </div>

          <div className="step-actions">
            <button
              className="btn btn-primary"
              onClick={() => setCurrentStep(2)}
              disabled={!formData.client_id || !formData.numero}>
              Suivant → Produits
            </button>
          </div>
        </div>
      )}

      {/* Étape 2: Lignes de commande */}
      {currentStep === 2 && (
        <div className="form-section">
          <div className="section-header">
            <h3>📦 Produits Commandés</h3>
            <button
              className="btn btn-primary btn-sm"
              onClick={addLine}>
              ➕ Ajouter un produit
            </button>
          </div>

          {errors.lignes && (
            <div className="error-message">
              <span className="error-icon">❌</span>
              <span>{errors.lignes}</span>
            </div>
          )}

          <div className="lines-container">
            {formData.lignes.map((ligne, index) => (
              <div
                key={index}
                className="order-line">
                <div className="line-header">
                  <span className="line-number">Ligne {index + 1}</span>
                  <button
                    className="btn btn-danger btn-sm"
                    onClick={() => removeLine(index)}>
                    🗑️ Supprimer
                  </button>
                </div>

                <div className="line-form">
                  <div className="form-group">
                    <label>Produit *</label>
                    <select
                      value={ligne.produit_id}
                      onChange={(e) =>
                        updateLine(index, "produit_id", e.target.value)
                      }
                      className={`form-select ${
                        errors[`ligne_${index}_produit`] ? "error" : ""
                      }`}>
                      <option value="">Sélectionner un produit</option>
                      {products.map((product) => (
                        <option
                          key={product.id}
                          value={product.id}>
                          {product.nom} - {product.prix_unitaire}€
                        </option>
                      ))}
                    </select>
                    {errors[`ligne_${index}_produit`] && (
                      <span className="error-text">
                        {errors[`ligne_${index}_produit`]}
                      </span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Description</label>
                    <input
                      type="text"
                      value={ligne.description}
                      onChange={(e) =>
                        updateLine(index, "description", e.target.value)
                      }
                      className="form-input"
                      placeholder="Description du produit"
                    />
                  </div>

                  <div className="form-group">
                    <label>Quantité *</label>
                    <input
                      type="number"
                      min="1"
                      step="1"
                      value={ligne.quantite}
                      onChange={(e) =>
                        updateLine(
                          index,
                          "quantite",
                          parseInt(e.target.value) || 0
                        )
                      }
                      className={`form-input ${
                        errors[`ligne_${index}_quantite`] ? "error" : ""
                      }`}
                    />
                    {errors[`ligne_${index}_quantite`] && (
                      <span className="error-text">
                        {errors[`ligne_${index}_quantite`]}
                      </span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Prix unitaire *</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={ligne.prix_unitaire}
                      onChange={(e) =>
                        updateLine(
                          index,
                          "prix_unitaire",
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className={`form-input ${
                        errors[`ligne_${index}_prix`] ? "error" : ""
                      }`}
                    />
                    {errors[`ligne_${index}_prix`] && (
                      <span className="error-text">
                        {errors[`ligne_${index}_prix`]}
                      </span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>TVA (%)</label>
                    <select
                      value={ligne.taux_tva}
                      onChange={(e) =>
                        updateLine(
                          index,
                          "taux_tva",
                          parseFloat(e.target.value)
                        )
                      }
                      className="form-select">
                      <option value={0}>0%</option>
                      <option value={5.5}>5.5%</option>
                      <option value={10}>10%</option>
                      <option value={20}>20%</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Total HT</label>
                    <input
                      type="text"
                      value={ligne.montant_ht.toFixed(2) + " €"}
                      readOnly
                      className="form-input readonly"
                    />
                  </div>

                  <div className="form-group">
                    <label>Total TTC</label>
                    <input
                      type="text"
                      value={ligne.montant_ttc.toFixed(2) + " €"}
                      readOnly
                      className="form-input readonly total"
                    />
                  </div>
                </div>
              </div>
            ))}

            {formData.lignes.length === 0 && (
              <div className="empty-lines">
                <div className="empty-icon">📦</div>
                <h4>Aucun produit ajouté</h4>
                <p>Commencez par ajouter des produits à votre commande</p>
                <button
                  className="btn btn-primary"
                  onClick={addLine}>
                  ➕ Ajouter le premier produit
                </button>
              </div>
            )}
          </div>

          <div className="step-actions">
            <button
              className="btn btn-outline"
              onClick={() => setCurrentStep(1)}>
              ← Précédent
            </button>
            <button
              className="btn btn-primary"
              onClick={() => setCurrentStep(3)}
              disabled={formData.lignes.length === 0}>
              Suivant → Finalisation
            </button>
          </div>
        </div>
      )}

      {/* Étape 3: Finalisation */}
      {currentStep === 3 && (
        <div className="form-section">
          <h3>✅ Finalisation</h3>

          {/* Résumé de la commande */}
          <div className="order-summary">
            <h4>📋 Résumé de la commande</h4>

            <div className="summary-info">
              <div className="summary-item">
                <label>Numéro:</label>
                <span>{formData.numero}</span>
              </div>
              <div className="summary-item">
                <label>Client:</label>
                <span>
                  {clients.find((c) => c.id === formData.client_id)?.nom}
                </span>
              </div>
              <div className="summary-item">
                <label>Date commande:</label>
                <span>
                  {new Date(formData.date_commande).toLocaleDateString("fr-FR")}
                </span>
              </div>
              {formData.date_livraison_prevue && (
                <div className="summary-item">
                  <label>Livraison prévue:</label>
                  <span>
                    {new Date(
                      formData.date_livraison_prevue
                    ).toLocaleDateString("fr-FR")}
                  </span>
                </div>
              )}
              <div className="summary-item">
                <label>Priorité:</label>
                <span
                  className={`priority-badge priority-${formData.priorite.toLowerCase()}`}>
                  {formData.priorite === "URGENTE"
                    ? "🔴 Urgente"
                    : formData.priorite === "HAUTE"
                    ? "🟡 Haute"
                    : "🟢 Normale"}
                </span>
              </div>
            </div>

            <div className="summary-lines">
              <h5>Produits commandés ({formData.lignes.length})</h5>
              {formData.lignes.map((ligne, index) => (
                <div
                  key={index}
                  className="summary-line">
                  <span className="line-product">{ligne.produit_nom}</span>
                  <span className="line-quantity">Qté: {ligne.quantite}</span>
                  <span className="line-total">
                    {ligne.montant_ttc.toFixed(2)} €
                  </span>
                </div>
              ))}
            </div>

            <div className="summary-totals">
              <div className="total-line">
                <span>Total HT:</span>
                <span>{totalHT.toFixed(2)} €</span>
              </div>
              <div className="total-line">
                <span>TVA:</span>
                <span>{totalTVA.toFixed(2)} €</span>
              </div>
              <div className="total-line final">
                <span>
                  <strong>Total TTC:</strong>
                </span>
                <span>
                  <strong>{totalTTC.toFixed(2)} €</strong>
                </span>
              </div>
            </div>
          </div>

          <div className="step-actions">
            <button
              className="btn btn-outline"
              onClick={() => setCurrentStep(2)}>
              ← Précédent
            </button>

            <div className="final-actions">
              <button
                className="btn btn-secondary"
                onClick={() => handleSubmit("BROUILLON")}
                disabled={saving}>
                {saving ? "⏳" : "💾"} Sauvegarder brouillon
              </button>

              <button
                className="btn btn-success"
                onClick={() => handleSubmit("CONFIRMEE")}
                disabled={saving}>
                {saving ? "⏳" : "✅"} Confirmer la commande
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderForm;
