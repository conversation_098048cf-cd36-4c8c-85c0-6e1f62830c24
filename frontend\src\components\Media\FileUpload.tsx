import React, { useState, useRef } from "react";
import MediaService, {
  MediaFile,
  UploadProgress,
} from "../../services/mediaService";
import { useNotify } from "../Common/NotificationSystem";

interface FileUploadProps {
  onUploadComplete?: (files: MediaFile[]) => void;
  onUploadError?: (error: string) => void;
  multiple?: boolean;
  accept?: string;
  maxSize?: number;
  maxFiles?: number;
  showPreview?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: "uploading" | "completed" | "error";
  result?: MediaFile;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUploadComplete,
  onUploadError,
  multiple = false,
  accept = "*/*",
  maxSize = 10 * 1024 * 1024, // 10MB par défaut
  maxFiles = 5,
  showPreview = true,
  className = "",
  style = {},
}) => {
  const notify = useNotify();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);

    // Vérifier le nombre de fichiers
    if (!multiple && fileArray.length > 1) {
      notify.error("Erreur", "Un seul fichier autorisé");
      return;
    }

    if (fileArray.length > maxFiles) {
      notify.error("Erreur", `Maximum ${maxFiles} fichiers autorisés`);
      return;
    }

    // Valider chaque fichier
    const validFiles: File[] = [];
    for (const file of fileArray) {
      const validation = MediaService.validateFile(file, {
        maxSize,
        allowedTypes:
          accept === "*/*" ? undefined : accept.split(",").map((t) => t.trim()),
      });

      if (validation.valid) {
        validFiles.push(file);
      } else {
        notify.error("Erreur", `${file.name}: ${validation.error}`);
      }
    }

    if (validFiles.length > 0) {
      uploadFiles(validFiles);
    }
  };

  const uploadFiles = async (files: File[]) => {
    // Initialiser l'état des fichiers en cours d'upload
    const initialUploadingFiles: UploadingFile[] = files.map((file) => ({
      file,
      progress: 0,
      status: "uploading",
    }));

    setUploadingFiles(initialUploadingFiles);

    const completedFiles: MediaFile[] = [];
    const errors: string[] = [];

    // Upload des fichiers un par un
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      try {
        const result = await MediaService.uploadFile(file, {
          onProgress: (progress) => {
            setUploadingFiles((prev) =>
              prev.map((uf, index) =>
                index === i ? { ...uf, progress: progress.percentage } : uf
              )
            );
          },
        });

        if (result.success && result.data) {
          completedFiles.push(result.data);
          setUploadingFiles((prev) =>
            prev.map((uf, index) =>
              index === i
                ? { ...uf, status: "completed", result: result.data }
                : uf
            )
          );
        } else {
          const error = result.message || "Erreur d'upload";
          errors.push(`${file.name}: ${error}`);
          setUploadingFiles((prev) =>
            prev.map((uf, index) =>
              index === i ? { ...uf, status: "error", error } : uf
            )
          );
        }
      } catch (error) {
        const errorMsg = "Erreur d'upload";
        errors.push(`${file.name}: ${errorMsg}`);
        setUploadingFiles((prev) =>
          prev.map((uf, index) =>
            index === i ? { ...uf, status: "error", error: errorMsg } : uf
          )
        );
      }
    }

    // Notifier les résultats
    if (completedFiles.length > 0) {
      notify.success(
        "Succès",
        `${completedFiles.length} fichier(s) uploadé(s)`
      );
      onUploadComplete?.(completedFiles);
    }

    if (errors.length > 0) {
      errors.forEach((error) => notify.error("Erreur", error));
      onUploadError?.(errors.join(", "));
    }

    // Nettoyer après 3 secondes
    setTimeout(() => {
      setUploadingFiles([]);
    }, 3000);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div
      className={className}
      style={style}>
      {/* Zone de drop */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
        style={{
          border: `2px dashed ${isDragOver ? "#3498db" : "#dee2e6"}`,
          borderRadius: "8px",
          padding: "40px 20px",
          textAlign: "center",
          cursor: "pointer",
          background: isDragOver ? "#f8f9fa" : "white",
          transition: "all 0.2s",
        }}>
        <div style={{ fontSize: "48px", marginBottom: "16px" }}>📁</div>
        <h4 style={{ margin: "0 0 8px 0" }}>
          Glissez vos fichiers ici ou cliquez pour sélectionner
        </h4>
        <p style={{ margin: "0", color: "#666", fontSize: "14px" }}>
          {multiple ? `Maximum ${maxFiles} fichiers` : "Un seul fichier"} •
          Taille max: {MediaService.formatFileSize(maxSize)}
        </p>
        {accept !== "*/*" && (
          <p style={{ margin: "8px 0 0 0", color: "#666", fontSize: "12px" }}>
            Types acceptés: {accept}
          </p>
        )}
      </div>

      {/* Input file caché */}
      <input
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={accept}
        onChange={(e) => handleFileSelect(e.target.files)}
        style={{ display: "none" }}
      />

      {/* Aperçu des fichiers en cours d'upload */}
      {uploadingFiles.length > 0 && showPreview && (
        <div style={{ marginTop: "20px" }}>
          <h5 style={{ margin: "0 0 12px 0" }}>Upload en cours...</h5>
          <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
            {uploadingFiles.map((uploadingFile, index) => (
              <div
                key={index}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  padding: "8px 12px",
                  background: "#f8f9fa",
                  borderRadius: "6px",
                }}>
                <div style={{ fontSize: "20px" }}>
                  {MediaService.getFileIcon(uploadingFile.file.type)}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ fontSize: "14px", fontWeight: "bold" }}>
                    {uploadingFile.file.name}
                  </div>
                  <div style={{ fontSize: "12px", color: "#666" }}>
                    {MediaService.formatFileSize(uploadingFile.file.size)}
                  </div>

                  {/* Barre de progression */}
                  {uploadingFile.status === "uploading" && (
                    <div
                      style={{
                        width: "100%",
                        height: "4px",
                        background: "#dee2e6",
                        borderRadius: "2px",
                        marginTop: "4px",
                        overflow: "hidden",
                      }}>
                      <div
                        style={{
                          width: `${uploadingFile.progress}%`,
                          height: "100%",
                          background: "#3498db",
                          transition: "width 0.3s",
                        }}
                      />
                    </div>
                  )}
                </div>

                {/* Statut */}
                <div style={{ fontSize: "20px" }}>
                  {uploadingFile.status === "uploading" && "⏳"}
                  {uploadingFile.status === "completed" && "✅"}
                  {uploadingFile.status === "error" && "❌"}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
