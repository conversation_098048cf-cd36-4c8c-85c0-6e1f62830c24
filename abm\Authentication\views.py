"""
Vues API pour l'authentification
"""

from rest_framework import status, permissions, generics
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import login, logout
from django.utils import timezone
from django.db.models import Q


def get_client_ip(request):
    """Récupère l'adresse IP du client"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

from .models import CustomUser, LoginAttempt, UserRole, PasswordResetCode
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    UserListSerializer, UserUpdateSerializer, ChangePasswordSerializer,
    LoginAttemptSerializer, ForgotPasswordSerializer, VerifyResetCodeSerializer,
    ResetPasswordSerializer
)
from .email_service import EmailService
from .permissions import (
    IsOwnerOrAdmin, IsAdminOrAbove, IsSuperAdmin, UserManagementPermission
)


def get_client_ip(request):
    """Récupère l'adresse IP du client"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def register_user(request):
    """Inscription d'un nouvel utilisateur"""
    serializer = UserRegistrationSerializer(data=request.data)
    
    if serializer.is_valid():
        user = serializer.save()
        
        # Générer les tokens JWT
        refresh = RefreshToken.for_user(user)
        
        # Enregistrer la tentative de connexion réussie
        LoginAttempt.objects.create(
            username=user.username,
            ip_address=get_client_ip(request),
            success=True,
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        return Response({
            'success': True,
            'message': 'Utilisateur créé avec succès',
            'user': UserProfileSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        }, status=status.HTTP_201_CREATED)
    
    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


class CustomTokenObtainPairView(TokenObtainPairView):
    """Vue personnalisée pour l'obtention des tokens JWT"""
    
    def post(self, request, *args, **kwargs):
        username = request.data.get('username', '')
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Appeler la vue parent pour obtenir les tokens
        response = super().post(request, *args, **kwargs)
        
        # Enregistrer la tentative de connexion
        success = response.status_code == 200
        
        LoginAttempt.objects.create(
            username=username,
            ip_address=ip_address,
            success=success,
            user_agent=user_agent
        )
        
        if success:
            # Ajouter les informations utilisateur à la réponse
            try:
                user = CustomUser.objects.get(username=username)
                # Restructurer la réponse pour le frontend
                tokens = {
                    'access': response.data.get('access'),
                    'refresh': response.data.get('refresh')
                }
                response.data = {
                    'success': True,
                    'message': 'Connexion réussie',
                    'user': UserProfileSerializer(user).data,
                    'tokens': tokens
                }
            except CustomUser.DoesNotExist:
                pass
        else:
            response.data = {
                'success': False,
                'message': 'Identifiants invalides',
                'errors': response.data
            }
        
        return response


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_user(request):
    """Déconnexion de l'utilisateur"""
    try:
        # Récupérer le refresh token depuis la requête
        refresh_token = request.data.get('refresh_token')
        
        if refresh_token:
            # Blacklister le token
            token = RefreshToken(refresh_token)
            token.blacklist()
        
        return Response({
            'success': True,
            'message': 'Déconnexion réussie'
        }, status=status.HTTP_200_OK)
    
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Erreur lors de la déconnexion',
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile(request):
    """Récupérer le profil de l'utilisateur connecté"""
    serializer = UserProfileSerializer(request.user)
    return Response({
        'success': True,
        'user': serializer.data
    })


@api_view(['PUT', 'PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_profile(request):
    """Mettre à jour le profil de l'utilisateur connecté"""
    serializer = UserProfileSerializer(
        request.user,
        data=request.data,
        partial=request.method == 'PATCH'
    )
    
    if serializer.is_valid():
        serializer.save()
        return Response({
            'success': True,
            'message': 'Profil mis à jour avec succès',
            'user': serializer.data
        })
    
    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password(request):
    """Changer le mot de passe de l'utilisateur"""
    serializer = ChangePasswordSerializer(
        data=request.data,
        context={'request': request}
    )
    
    if serializer.is_valid():
        serializer.save()
        return Response({
            'success': True,
            'message': 'Mot de passe modifié avec succès'
        })
    
    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


class UserManagementViewSet(ModelViewSet):
    """ViewSet pour la gestion des utilisateurs (Admin)"""
    
    queryset = CustomUser.objects.all()
    permission_classes = [UserManagementPermission]
    
    def get_serializer_class(self):
        if self.action == 'list':
            return UserListSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserProfileSerializer
    
    def get_queryset(self):
        """Filtrer les utilisateurs selon les permissions"""
        user = self.request.user
        
        if user.is_superadmin:
            # Super admin voit tous les utilisateurs
            return CustomUser.objects.all()
        elif user.has_permission(UserRole.ADMIN):
            # Admin voit tous sauf les super admins
            return CustomUser.objects.exclude(role=UserRole.SUPERADMIN)
        else:
            # Autres utilisateurs voient seulement leur profil
            return CustomUser.objects.filter(id=user.id)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Statistiques des utilisateurs (Admin seulement)"""
        if not request.user.has_permission(UserRole.ADMIN):
            return Response({
                'success': False,
                'message': 'Permission refusée'
            }, status=status.HTTP_403_FORBIDDEN)
        
        stats = {
            'total_users': CustomUser.objects.count(),
            'active_users': CustomUser.objects.filter(is_active=True).count(),
            'users_by_role': {
                role.value: CustomUser.objects.filter(role=role.value).count()
                for role in UserRole
            },
            'recent_registrations': CustomUser.objects.filter(
                date_joined__gte=timezone.now() - timezone.timedelta(days=30)
            ).count()
        }
        
        return Response({
            'success': True,
            'statistics': stats
        })


@api_view(['GET'])
@permission_classes([IsAdminOrAbove])
def login_attempts(request):
    """Récupérer les tentatives de connexion (Admin)"""
    attempts = LoginAttempt.objects.all()[:100]  # Dernières 100 tentatives
    
    # Filtres optionnels
    username = request.GET.get('username')
    success = request.GET.get('success')
    
    if username:
        attempts = attempts.filter(username__icontains=username)
    
    if success is not None:
        attempts = attempts.filter(success=success.lower() == 'true')
    
    serializer = LoginAttemptSerializer(attempts, many=True)
    
    return Response({
        'success': True,
        'attempts': serializer.data
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def check_permissions(request):
    """Vérifier les permissions de l'utilisateur pour un module"""
    module = request.GET.get('module')
    
    if not module:
        return Response({
            'success': False,
            'message': 'Module requis'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    accessible_modules = request.user.get_accessible_modules()
    has_access = module in accessible_modules
    
    return Response({
        'success': True,
        'has_access': has_access,
        'accessible_modules': accessible_modules,
        'user_role': request.user.role
    })


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def forgot_password(request):
    """Demande de récupération de mot de passe"""
    serializer = ForgotPasswordSerializer(data=request.data)

    if serializer.is_valid():
        email = serializer.validated_data['email']

        try:
            user = CustomUser.objects.get(email=email, is_active=True)

            # Invalider tous les codes précédents pour cet utilisateur
            PasswordResetCode.objects.filter(
                user=user,
                is_used=False
            ).update(is_used=True)

            # Créer un nouveau code de récupération
            reset_code = PasswordResetCode.objects.create(
                user=user,
                email=email,
                ip_address=get_client_ip(request)
            )

            # Envoyer l'email avec le code
            email_sent = EmailService.send_password_reset_code(
                user=user,
                code=reset_code.code,
                email=email
            )

            if email_sent:
                return Response({
                    'success': True,
                    'message': 'Un code de vérification a été envoyé à votre adresse email.'
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Erreur lors de l\'envoi de l\'email. Veuillez réessayer.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except CustomUser.DoesNotExist:
            # Pour des raisons de sécurité, on ne révèle pas si l'email existe
            return Response({
                'success': True,
                'message': 'Si cette adresse email existe, un code de vérification a été envoyé.'
            })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def verify_reset_code(request):
    """Vérification du code de récupération"""
    serializer = VerifyResetCodeSerializer(data=request.data)

    if serializer.is_valid():
        return Response({
            'success': True,
            'message': 'Code de vérification valide.',
            'can_reset_password': True
        })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def reset_password(request):
    """Réinitialisation du mot de passe avec code de vérification"""
    serializer = ResetPasswordSerializer(data=request.data)

    if serializer.is_valid():
        user = serializer.save()

        # Envoyer une notification de changement de mot de passe
        EmailService.send_password_changed_notification(
            user=user,
            email=user.email
        )

        return Response({
            'success': True,
            'message': 'Votre mot de passe a été réinitialisé avec succès.'
        })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)
