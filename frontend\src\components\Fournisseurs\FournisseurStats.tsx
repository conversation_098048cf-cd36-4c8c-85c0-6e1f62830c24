import React, { useState, useEffect } from "react";

interface FournisseurStatsData {
  total_fournisseurs: number;
  fournisseurs_actifs: number;
  total_achats_mois: number;
  total_achats_annee: number;
  commandes_en_cours: number;
  delai_moyen_livraison: number;
  taux_conformite: number;
  economies_remises: number;
  top_fournisseurs: Array<{
    id: string;
    nom: string;
    montant_achats: number;
    nb_commandes: number;
    delai_moyen: number;
  }>;
  evolution_mensuelle: Array<{
    mois: string;
    montant: number;
    nb_commandes: number;
  }>;
}

const FournisseurStats: React.FC = () => {
  const [stats, setStats] = useState<FournisseurStatsData>({
    total_fournisseurs: 0,
    fournisseurs_actifs: 0,
    total_achats_mois: 0,
    total_achats_annee: 0,
    commandes_en_cours: 0,
    delai_moyen_livraison: 0,
    taux_conformite: 0,
    economies_remises: 0,
    top_fournisseurs: [],
    evolution_mensuelle: [],
  });
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState("month");

  useEffect(() => {
    loadStats();
  }, [period]);

  const loadStats = async () => {
    try {
      setLoading(true);

      // Simulation de données statistiques
      const mockStats: FournisseurStatsData = {
        total_fournisseurs: 25,
        fournisseurs_actifs: 18,
        total_achats_mois: 45000,
        total_achats_annee: 520000,
        commandes_en_cours: 8,
        delai_moyen_livraison: 6,
        taux_conformite: 94.5,
        economies_remises: 12500,
        top_fournisseurs: [
          {
            id: "1",
            nom: "FOURNISSEUR DESIGN MODERNE",
            montant_achats: 125000,
            nb_commandes: 45,
            delai_moyen: 5,
          },
          {
            id: "2",
            nom: "SOCIÉTÉ TECH AVANCÉE",
            montant_achats: 98000,
            nb_commandes: 32,
            delai_moyen: 7,
          },
          {
            id: "3",
            nom: "COMMERCE FUTURISTE SARL",
            montant_achats: 76000,
            nb_commandes: 28,
            delai_moyen: 4,
          },
        ],
        evolution_mensuelle: [
          { mois: "Jan", montant: 38000, nb_commandes: 12 },
          { mois: "Fév", montant: 42000, nb_commandes: 15 },
          { mois: "Mar", montant: 39000, nb_commandes: 13 },
          { mois: "Avr", montant: 45000, nb_commandes: 16 },
          { mois: "Mai", montant: 48000, nb_commandes: 18 },
          { mois: "Jun", montant: 52000, nb_commandes: 20 },
          { mois: "Jul", montant: 47000, nb_commandes: 17 },
          { mois: "Aoû", montant: 45000, nb_commandes: 16 },
        ],
      };

      setStats(mockStats);
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="fournisseur-stats-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des statistiques...</p>
      </div>
    );
  }

  return (
    <div className="fournisseur-stats">
      <div className="stats-header">
        <div className="header-content">
          <h1>📊 Statistiques Fournisseurs</h1>
          <p>Analyse complète de vos relations fournisseurs</p>
        </div>

        <div className="period-selector">
          <label>Période:</label>
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}>
            <option value="month">Ce mois</option>
            <option value="quarter">Ce trimestre</option>
            <option value="year">Cette année</option>
          </select>
        </div>
      </div>

      {/* KPIs principaux */}
      <div className="kpi-grid">
        <div className="kpi-card">
          <div className="kpi-icon">🏢</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.total_fournisseurs}</div>
            <div className="kpi-label">Total fournisseurs</div>
            <div className="kpi-sublabel">
              {stats.fournisseurs_actifs} actifs
            </div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">💰</div>
          <div className="kpi-content">
            <div className="kpi-value">
              {formatCurrency(stats.total_achats_mois)}
            </div>
            <div className="kpi-label">Achats ce mois</div>
            <div className="kpi-sublabel">
              {formatCurrency(stats.total_achats_annee)} cette année
            </div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">📦</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.commandes_en_cours}</div>
            <div className="kpi-label">Commandes en cours</div>
            <div className="kpi-sublabel">En attente de livraison</div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">⏱️</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.delai_moyen_livraison} jours</div>
            <div className="kpi-label">Délai moyen</div>
            <div className="kpi-sublabel">Livraison</div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">✅</div>
          <div className="kpi-content">
            <div className="kpi-value">{stats.taux_conformite}%</div>
            <div className="kpi-label">Taux conformité</div>
            <div className="kpi-sublabel">Qualité livraisons</div>
          </div>
        </div>

        <div className="kpi-card">
          <div className="kpi-icon">💸</div>
          <div className="kpi-content">
            <div className="kpi-value">
              {formatCurrency(stats.economies_remises)}
            </div>
            <div className="kpi-label">Économies remises</div>
            <div className="kpi-sublabel">Cette année</div>
          </div>
        </div>
      </div>

      {/* Top fournisseurs */}
      <div className="stats-section">
        <h3>🏆 Top fournisseurs</h3>
        <div className="top-fournisseurs">
          {stats.top_fournisseurs.map((fournisseur, index) => (
            <div
              key={fournisseur.id}
              className="top-fournisseur-item">
              <div className="rank">#{index + 1}</div>
              <div className="fournisseur-info">
                <h4>{fournisseur.nom}</h4>
                <p>{fournisseur.nb_commandes} commandes</p>
                <p>Délai moyen: {fournisseur.delai_moyen} jours</p>
              </div>
              <div className="fournisseur-amount">
                <span className="amount">
                  {formatCurrency(fournisseur.montant_achats)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Évolution mensuelle */}
      <div className="stats-section">
        <h3>📈 Évolution mensuelle</h3>
        <div className="evolution-chart">
          <div className="chart-container">
            {stats.evolution_mensuelle.map((data, index) => (
              <div
                key={index}
                className="chart-bar">
                <div
                  className="bar"
                  style={{
                    height: `${
                      (data.montant /
                        Math.max(
                          ...stats.evolution_mensuelle.map((d) => d.montant)
                        )) *
                      100
                    }%`,
                  }}></div>
                <div className="bar-label">{data.mois}</div>
                <div className="bar-value">{formatCurrency(data.montant)}</div>
                <div className="bar-count">{data.nb_commandes} cmd</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FournisseurStats;
