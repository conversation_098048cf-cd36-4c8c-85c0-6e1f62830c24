#!/usr/bin/env node

/**
 * Script pour corriger TOUTES les URLs des APIs dans apiService.ts
 * Ajoute le préfixe /api/ aux endpoints qui n'en ont pas
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/services/apiService.ts');

console.log('🔧 Correction complète des URLs API dans apiService.ts...');

try {
  // Lire le fichier
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Définir les corrections à appliquer
  const corrections = [
    // Clients
    { from: /(?<!\/api)\/clients\//g, to: '/api/clients/' },
    
    // Products
    { from: /(?<!\/api)\/products\//g, to: '/api/products/' },
    
    // Orders
    { from: /(?<!\/api)\/orders\//g, to: '/api/orders/' },
    { from: /(?<!\/api)\/commandes\//g, to: '/api/orders/' },
    
    // Payments
    { from: /(?<!\/api)\/payments\//g, to: '/api/payments/' },
    { from: /(?<!\/api)\/paiements\//g, to: '/api/payments/' },
    
    // Stock
    { from: /(?<!\/api)\/stock\//g, to: '/api/stock/' },
    
    // Fournisseurs
    { from: /(?<!\/api)\/fournisseurs\//g, to: '/api/suppliers/' },
    
    // Rapports
    { from: /(?<!\/api)\/rapports\//g, to: '/api/reports/' },
    
    // Factures (autres que celles déjà corrigées)
    { from: /(?<!\/api)\/factures\//g, to: '/api/invoices/' },
  ];
  
  let totalReplacements = 0;
  
  // Appliquer chaque correction
  corrections.forEach(({ from, to }, index) => {
    const beforeCount = (content.match(from) || []).length;
    if (beforeCount > 0) {
      content = content.replace(from, to);
      const afterCount = (content.match(from) || []).length;
      const replacements = beforeCount - afterCount;
      totalReplacements += replacements;
      
      console.log(`✅ ${replacements} occurrences corrigées: ${from.source} → ${to}`);
    }
  });
  
  // Corrections spéciales pour les endpoints complexes
  const specialCorrections = [
    // Statistiques
    { from: '"/clients/stats/"', to: '"/api/clients/stats/"' },
    { from: '"/products/stats/"', to: '"/api/products/stats/"' },
    { from: '"/stock/stats/"', to: '"/api/stock/stats/"' },
    
    // Historiques
    { from: '/clients/${id}/historique/', to: '/api/clients/${id}/historique/' },
    { from: '/products/${id}/stock/', to: '/api/products/${id}/stock/' },
    
    // Actions spéciales
    { from: '/stock/movements/', to: '/api/stock/movements/' },
    { from: '/stock/alerts/', to: '/api/stock/alerts/' },
    { from: '/stock/inventaires/', to: '/api/stock/inventaires/' },
  ];
  
  specialCorrections.forEach(({ from, to }) => {
    if (content.includes(from)) {
      content = content.replace(new RegExp(from.replace(/\$\{[^}]+\}/g, '\\${[^}]+}'), 'g'), to);
      totalReplacements++;
      console.log(`🔧 Correction spéciale: ${from} → ${to}`);
    }
  });
  
  // Écrire le fichier modifié
  fs.writeFileSync(filePath, content, 'utf8');
  
  console.log(`\n🎉 ${totalReplacements} URLs corrigées au total !`);
  console.log(`📝 Fichier mis à jour: ${filePath}`);
  
  // Vérifier s'il reste des URLs sans /api/
  const remainingIssues = content.match(/this\.get\(["']\/(?!api\/)[^"']+["']/g) || [];
  if (remainingIssues.length > 0) {
    console.log(`\n⚠️  URLs potentiellement problématiques détectées:`);
    remainingIssues.forEach(issue => {
      console.log(`   ${issue}`);
    });
  } else {
    console.log(`\n✅ Toutes les URLs semblent correctes !`);
  }
  
} catch (error) {
  console.error('❌ Erreur lors de la correction:', error.message);
  process.exit(1);
}

console.log('\n🔄 Redémarrez le serveur React pour appliquer les changements.');
console.log('🧪 Utilisez le composant "Test Services" pour vérifier la connectivité.');
