#!/usr/bin/env node

/**
 * Script de validation complète de l'application ABM
 * Vérifie tous les composants, services et configurations
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 VALIDATION COMPLÈTE DE L\'APPLICATION ABM\n');

// Configuration des modules et composants requis
const REQUIRED_STRUCTURE = {
  'src/components/Auth': [
    'AuthPage.tsx',
    'SimpleLoginForm.tsx',
    'ProtectedRoute.tsx',
    'RouteGuard.tsx'
  ],
  'src/components/Dashboard': [
    'SimpleDashboard.tsx',
    'index.ts'
  ],
  'src/components/Factures': [
    'FacturationProfessionnelle.tsx',
    'FacturationProfessionnelle.css',
    'index.ts'
  ],
  'src/components/Clients': [
    'SimpleClients.tsx',
    'SimpleClients.css',
    'ClientForm.tsx',
    'index.ts'
  ],
  'src/components/Stock': [
    'Stock.tsx',
    'StockOverview.tsx',
    'StockMovements.tsx',
    'StockAlerts.tsx',
    'StockInventory.tsx',
    'StockStats.tsx',
    'index.ts'
  ],
  'src/components/Products': [
    'Products.tsx',
    'ProductsList.tsx',
    'ProductForm.tsx',
    'index.ts'
  ],
  'src/components/Orders': [
    'Orders.tsx',
    'OrdersList.tsx',
    'OrderForm.tsx',
    'index.ts'
  ],
  'src/components/Payments': [
    'Payments.tsx',
    'PaymentsList.tsx',
    'PaymentForm.tsx',
    'index.ts'
  ],
  'src/components/Fournisseurs': [
    'Fournisseurs.tsx',
    'FournisseursList.tsx',
    'FournisseurForm.tsx',
    'index.ts'
  ],
  'src/components/Reports': [
    'SimpleReports.tsx',
    'index.ts'
  ],
  'src/components/Settings': [
    'Settings.tsx',
    'AppSettings.tsx',
    'SystemSettings.tsx',
    'index.ts'
  ],
  'src/components/Common': [
    'SimpleNavigation.tsx',
    'BenChaabeneHeader.tsx',
    'NotificationSystem.tsx',
    'LoadingSpinner.tsx',
    'ErrorBoundary.tsx'
  ],
  'src/services': [
    'apiService.ts'
  ],
  'src/contexts': [
    'AuthContext.tsx'
  ],
  'public': [
    'index.html',
    'logo_ben_chaabene.png'
  ]
};

const CRITICAL_FILES = [
  'src/index.tsx',
  'src/App.tsx',
  'src/App.css',
  'package.json',
  'tsconfig.json'
];

// Fonction de validation
function validateStructure() {
  console.log('📁 Validation de la structure des fichiers...\n');
  
  let totalFiles = 0;
  let existingFiles = 0;
  let missingFiles = [];

  // Vérifier les fichiers critiques
  console.log('🔧 Fichiers critiques:');
  CRITICAL_FILES.forEach(file => {
    totalFiles++;
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`  ✅ ${file}`);
      existingFiles++;
    } else {
      console.log(`  ❌ ${file} - MANQUANT`);
      missingFiles.push(file);
    }
  });

  // Vérifier la structure des modules
  console.log('\n📦 Structure des modules:');
  Object.entries(REQUIRED_STRUCTURE).forEach(([directory, files]) => {
    console.log(`\n  📂 ${directory}:`);
    
    files.forEach(file => {
      totalFiles++;
      const filePath = path.join(__dirname, directory, file);
      if (fs.existsSync(filePath)) {
        console.log(`    ✅ ${file}`);
        existingFiles++;
      } else {
        console.log(`    ❌ ${file} - MANQUANT`);
        missingFiles.push(`${directory}/${file}`);
      }
    });
  });

  return {
    totalFiles,
    existingFiles,
    missingFiles,
    completionRate: ((existingFiles / totalFiles) * 100).toFixed(1)
  };
}

// Fonction de validation des imports
function validateImports() {
  console.log('\n🔗 Validation des imports critiques...\n');
  
  const criticalImports = [
    {
      file: 'src/App.tsx',
      imports: ['FacturationProfessionnelle', 'SimpleClients', 'SimpleDashboard']
    },
    {
      file: 'src/components/Factures/index.ts',
      imports: ['FacturationProfessionnelle']
    },
    {
      file: 'src/components/Clients/index.ts',
      imports: ['SimpleClients']
    },
    {
      file: 'src/components/Dashboard/index.ts',
      imports: ['SimpleDashboard']
    }
  ];

  let importErrors = [];

  criticalImports.forEach(({ file, imports }) => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      imports.forEach(importName => {
        if (content.includes(importName)) {
          console.log(`  ✅ ${file} → ${importName}`);
        } else {
          console.log(`  ❌ ${file} → ${importName} - MANQUANT`);
          importErrors.push(`${file}: ${importName}`);
        }
      });
    } else {
      console.log(`  ❌ ${file} - FICHIER MANQUANT`);
      importErrors.push(file);
    }
  });

  return importErrors;
}

// Fonction de validation des services
function validateServices() {
  console.log('\n🌐 Validation des services API...\n');
  
  const requiredServices = [
    'FacturationService',
    'ClientService',
    'ProductService',
    'StockService',
    'PaymentService',
    'OrderService',
    'InvoiceService'
  ];

  const apiServicePath = path.join(__dirname, 'src/services/apiService.ts');
  let serviceErrors = [];

  if (fs.existsSync(apiServicePath)) {
    const content = fs.readFileSync(apiServicePath, 'utf8');
    
    requiredServices.forEach(service => {
      if (content.includes(`class ${service}`) || content.includes(`export const ${service}`)) {
        console.log(`  ✅ ${service}`);
      } else {
        console.log(`  ❌ ${service} - MANQUANT`);
        serviceErrors.push(service);
      }
    });
  } else {
    console.log('  ❌ apiService.ts - FICHIER MANQUANT');
    serviceErrors.push('apiService.ts');
  }

  return serviceErrors;
}

// Exécution de la validation
function runValidation() {
  const structureResult = validateStructure();
  const importErrors = validateImports();
  const serviceErrors = validateServices();

  // Résumé final
  console.log('\n' + '='.repeat(60));
  console.log('📊 RÉSUMÉ DE LA VALIDATION');
  console.log('='.repeat(60));
  
  console.log(`\n📁 Structure des fichiers:`);
  console.log(`   • Fichiers existants: ${structureResult.existingFiles}/${structureResult.totalFiles}`);
  console.log(`   • Taux de completion: ${structureResult.completionRate}%`);
  
  console.log(`\n🔗 Imports:`);
  console.log(`   • Erreurs d'imports: ${importErrors.length}`);
  
  console.log(`\n🌐 Services API:`);
  console.log(`   • Services manquants: ${serviceErrors.length}`);

  // Statut global
  const totalErrors = structureResult.missingFiles.length + importErrors.length + serviceErrors.length;
  
  console.log(`\n🎯 STATUT GLOBAL:`);
  if (totalErrors === 0) {
    console.log('   ✅ APPLICATION ENTIÈREMENT VALIDÉE !');
    console.log('   🚀 Prête pour le démarrage');
  } else if (totalErrors < 5) {
    console.log('   ⚠️  APPLICATION PRESQUE PRÊTE');
    console.log(`   🔧 ${totalErrors} problème(s) mineur(s) à corriger`);
  } else {
    console.log('   ❌ APPLICATION NÉCESSITE DES CORRECTIONS');
    console.log(`   🔧 ${totalErrors} problème(s) à corriger`);
  }

  // Détails des erreurs
  if (structureResult.missingFiles.length > 0) {
    console.log(`\n📋 Fichiers manquants:`);
    structureResult.missingFiles.forEach(file => {
      console.log(`   • ${file}`);
    });
  }

  if (importErrors.length > 0) {
    console.log(`\n📋 Erreurs d'imports:`);
    importErrors.forEach(error => {
      console.log(`   • ${error}`);
    });
  }

  if (serviceErrors.length > 0) {
    console.log(`\n📋 Services manquants:`);
    serviceErrors.forEach(service => {
      console.log(`   • ${service}`);
    });
  }

  console.log('\n' + '='.repeat(60));
  
  return totalErrors === 0;
}

// Lancement de la validation
const isValid = runValidation();
process.exit(isValid ? 0 : 1);
