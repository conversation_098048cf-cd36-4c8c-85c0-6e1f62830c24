from django.db import models
from django.contrib.auth import get_user_model
import uuid
import json

User = get_user_model()

class RapportTemplate(models.Model):
    """Templates de rapports personnalisables"""

    TYPE_CHOICES = [
        ('VENTES', 'Rapport de ventes'),
        ('CLIENTS', 'Rapport clients'),
        ('PRODUITS', 'Rapport produits'),
        ('COMMANDES', 'Rapport commandes'),
        ('FINANCIER', 'Rapport financier'),
        ('STOCK', 'Rapport stock'),
        ('CUSTOM', 'Rapport personnalisé'),
    ]

    FORMAT_CHOICES = [
        ('PDF', 'PDF'),
        ('EXCEL', 'Excel'),
        ('CSV', 'CSV'),
        ('JSON', 'JSON'),
    ]

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=200, verbose_name="Nom du rapport")
    description = models.TextField(blank=True)

    # Configuration
    type_rapport = models.CharField(max_length=20, choices=TYPE_CHOICES)
    format_export = models.CharField(max_length=10, choices=FORMAT_CHOICES, default='PDF')

    # Paramètres du rapport (JSON)
    colonnes = models.JSONField(default=list, verbose_name="Colonnes à inclure")
    filtres = models.JSONField(default=dict, verbose_name="Filtres par défaut")
    groupements = models.JSONField(default=list, verbose_name="Groupements")
    totaux = models.JSONField(default=list, verbose_name="Colonnes de totaux")

    # Planification
    automatique = models.BooleanField(default=False, verbose_name="Génération automatique")
    frequence = models.CharField(
        max_length=20,
        choices=[
            ('QUOTIDIEN', 'Quotidien'),
            ('HEBDOMADAIRE', 'Hebdomadaire'),
            ('MENSUEL', 'Mensuel'),
            ('TRIMESTRIEL', 'Trimestriel'),
            ('ANNUEL', 'Annuel'),
        ],
        blank=True
    )
    prochaine_execution = models.DateTimeField(null=True, blank=True)

    # Destinataires (pour envoi automatique)
    destinataires = models.JSONField(default=list, verbose_name="Emails destinataires")

    # Métadonnées
    public = models.BooleanField(default=False, verbose_name="Visible par tous")
    actif = models.BooleanField(default=True)

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='rapports_created')

    class Meta:
        verbose_name = "Template de rapport"
        verbose_name_plural = "Templates de rapports"
        ordering = ['nom']

    def __str__(self):
        return f"{self.nom} ({self.get_type_rapport_display()})"


class RapportGenere(models.Model):
    """Historique des rapports générés"""

    STATUT_CHOICES = [
        ('EN_COURS', 'En cours de génération'),
        ('TERMINE', 'Terminé'),
        ('ERREUR', 'Erreur'),
    ]

    # Relations
    template = models.ForeignKey(RapportTemplate, on_delete=models.CASCADE, related_name='executions')

    # Informations de génération
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='EN_COURS')
    date_generation = models.DateTimeField(auto_now_add=True)
    duree_generation = models.DurationField(null=True, blank=True)

    # Paramètres utilisés
    periode_debut = models.DateField()
    periode_fin = models.DateField()
    filtres_appliques = models.JSONField(default=dict)

    # Résultats
    nb_lignes = models.IntegerField(default=0, verbose_name="Nombre de lignes")
    taille_fichier = models.IntegerField(default=0, verbose_name="Taille du fichier (bytes)")
    chemin_fichier = models.CharField(max_length=500, blank=True)

    # Erreurs
    message_erreur = models.TextField(blank=True)

    # Audit
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "Rapport généré"
        verbose_name_plural = "Rapports générés"
        ordering = ['-date_generation']

    def __str__(self):
        return f"{self.template.nom} - {self.date_generation.strftime('%d/%m/%Y %H:%M')}"


class TableauBord(models.Model):
    """Tableaux de bord personnalisés"""

    # Identifiants
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Configuration
    widgets = models.JSONField(default=list, verbose_name="Configuration des widgets")
    layout = models.JSONField(default=dict, verbose_name="Disposition des widgets")

    # Permissions
    public = models.BooleanField(default=False)
    roles_autorises = models.JSONField(default=list, verbose_name="Rôles autorisés")

    # Métadonnées
    actif = models.BooleanField(default=True)

    # Audit
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "Tableau de bord"
        verbose_name_plural = "Tableaux de bord"
        ordering = ['nom']

    def __str__(self):
        return self.nom
