import React from "react";
import "./OrderDetails.css";

interface OrderDetailsProps {
  order: any;
  onClose: () => void;
  onUpdateStatus?: (orderId: string, newStatus: string) => void;
}

const OrderDetails: React.FC<OrderDetailsProps> = ({
  order,
  onClose,
  onUpdateStatus,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "LIVREE":
        return "#10b981";
      case "EXPEDIEE":
        return "#3b82f6";
      case "EN_PREPARATION":
        return "#f59e0b";
      case "CONFIRMEE":
        return "#8b5cf6";
      case "BROUILLON":
        return "#6b7280";
      case "ANNULEE":
        return "#ef4444";
      default:
        return "#6b7280";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "LIVREE":
        return "Livrée";
      case "EXPEDIEE":
        return "Expédiée";
      case "EN_PREPARATION":
        return "En préparation";
      case "CONFIRMEE":
        return "Confirmée";
      case "BROUILLON":
        return "Brouillon";
      case "ANNULEE":
        return "Annulée";
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "URGENTE":
        return "#ef4444";
      case "HAUTE":
        return "#f59e0b";
      case "NORMALE":
        return "#10b981";
      default:
        return "#6b7280";
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "URGENTE":
        return "🔴 Urgente";
      case "HAUTE":
        return "🟡 Haute";
      case "NORMALE":
        return "🟢 Normale";
      default:
        return priority;
    }
  };

  const handleStatusUpdate = (newStatus: string) => {
    if (onUpdateStatus) {
      onUpdateStatus(order.id, newStatus);
    }
  };

  return (
    <div className="order-details-overlay">
      <div className="order-details-modal">
        <div className="order-details-header">
          <div className="header-left">
            <h2>📦 Détails de la Commande</h2>
            <span className="order-number">{order.numero}</span>
          </div>
          <button className="close-btn" onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="order-details-content">
          <div className="details-grid">
            {/* Informations principales */}
            <div className="detail-section">
              <h3>📋 Informations Générales</h3>
              <div className="detail-row">
                <span className="label">Numéro:</span>
                <span className="value">{order.numero}</span>
              </div>
              <div className="detail-row">
                <span className="label">Statut:</span>
                <span 
                  className="value status-badge"
                  style={{ 
                    backgroundColor: getStatusColor(order.statut),
                    color: 'white',
                    padding: '4px 12px',
                    borderRadius: '20px',
                    fontSize: '0.85rem',
                    fontWeight: '600'
                  }}
                >
                  {getStatusLabel(order.statut)}
                </span>
              </div>
              <div className="detail-row">
                <span className="label">Priorité:</span>
                <span 
                  className="value priority-badge"
                  style={{ 
                    color: getPriorityColor(order.priorite),
                    fontWeight: '600'
                  }}
                >
                  {getPriorityLabel(order.priorite)}
                </span>
              </div>
            </div>

            {/* Client */}
            <div className="detail-section">
              <h3>👤 Client</h3>
              <div className="detail-row">
                <span className="label">Nom:</span>
                <span className="value">{order.client_nom}</span>
              </div>
              <div className="detail-row">
                <span className="label">Email:</span>
                <span className="value">{order.client_email}</span>
              </div>
            </div>

            {/* Montants */}
            <div className="detail-section">
              <h3>💰 Montants</h3>
              <div className="detail-row">
                <span className="label">Montant HT:</span>
                <span className="value">{order.montant_ht.toFixed(3)} TND</span>
              </div>
              <div className="detail-row">
                <span className="label">TVA:</span>
                <span className="value">{order.montant_tva.toFixed(3)} TND</span>
              </div>
              <div className="detail-row">
                <span className="label">Total TTC:</span>
                <span className="value amount">{order.montant_total.toFixed(3)} TND</span>
              </div>
            </div>

            {/* Dates */}
            <div className="detail-section">
              <h3>📅 Dates</h3>
              <div className="detail-row">
                <span className="label">Date de commande:</span>
                <span className="value">
                  {new Date(order.date_commande).toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
              {order.date_livraison_prevue && (
                <div className="detail-row">
                  <span className="label">Livraison prévue:</span>
                  <span className="value">
                    {new Date(order.date_livraison_prevue).toLocaleDateString('fr-FR', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
              )}
              {order.date_livraison_reelle && (
                <div className="detail-row">
                  <span className="label">Livraison réelle:</span>
                  <span className="value">
                    {new Date(order.date_livraison_reelle).toLocaleDateString('fr-FR', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
              )}
            </div>

            {/* Notes */}
            {order.notes && (
              <div className="detail-section full-width">
                <h3>📝 Notes</h3>
                <div className="notes-content">
                  {order.notes}
                </div>
              </div>
            )}
          </div>

          {/* Actions rapides */}
          <div className="quick-actions-section">
            <h3>⚙️ Actions Rapides</h3>
            <div className="quick-actions">
              <button
                className="action-btn confirm"
                onClick={() => handleStatusUpdate("CONFIRMEE")}
                disabled={order.statut === "CONFIRMEE" || order.statut === "LIVREE"}>
                ✅ Confirmer
              </button>
              <button
                className="action-btn prepare"
                onClick={() => handleStatusUpdate("EN_PREPARATION")}
                disabled={order.statut === "EN_PREPARATION" || order.statut === "EXPEDIEE" || order.statut === "LIVREE"}>
                🔄 En Préparation
              </button>
              <button
                className="action-btn ship"
                onClick={() => handleStatusUpdate("EXPEDIEE")}
                disabled={order.statut === "EXPEDIEE" || order.statut === "LIVREE"}>
                📦 Expédier
              </button>
              <button
                className="action-btn deliver"
                onClick={() => handleStatusUpdate("LIVREE")}
                disabled={order.statut === "LIVREE"}>
                🎯 Livrer
              </button>
            </div>
          </div>
        </div>

        <div className="order-details-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Fermer
          </button>
          <button className="btn btn-primary">
            📄 Générer bon de livraison
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
