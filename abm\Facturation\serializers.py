"""
Serializers professionnels pour le système de facturation moderne
"""

from rest_framework import serializers
from decimal import Decimal
from django.utils import timezone
from django.db.models import Sum

from .models import (
    ConfigurationFacturation, Facture,
    LigneFacture, PaiementFacture
)
from Clients.serializers import ClientSerializer
from Produits.serializers import ProduitSerializer


class ConfigurationFacturationSerializer(serializers.ModelSerializer):
    """Serializer pour la configuration de facturation"""

    class Meta:
        model = ConfigurationFacturation
        fields = [
            'id', 'nom_societe', 'adresse_ligne1', 'adresse_ligne2',
            'code_postal', 'ville', 'pays', 'siret', 'numero_tva', 'code_ape',
            'capital_social', 'telephone', 'email', 'site_web',
            'numerotation_auto', 'prefixe_facture', 'prefixe_devis',
            'compteur_facture', 'compteur_devis', 'conditions_paiement',
            'mentions_legales', 'logo', 'couleur_principale',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class LigneFactureSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes de facture"""

    produit_nom = serializers.CharField(source='produit.nom', read_only=True)
    produit_reference = serializers.CharField(source='produit.reference', read_only=True)

    class Meta:
        model = LigneFacture
        fields = [
            'id', 'produit', 'produit_nom', 'produit_reference',
            'designation', 'description', 'quantite', 'prix_unitaire_ht',
            'taux_tva', 'montant_ht', 'montant_tva', 'montant_ttc', 'ordre'
        ]
        read_only_fields = ['montant_ht', 'montant_tva', 'montant_ttc']

    def validate_quantite(self, value):
        """Validation de la quantité"""
        if value <= 0:
            raise serializers.ValidationError("La quantité doit être positive")
        return value

    def validate_prix_unitaire_ht(self, value):
        """Validation du prix unitaire"""
        if value < 0:
            raise serializers.ValidationError("Le prix unitaire ne peut pas être négatif")
        return value


class PaiementFactureSerializer(serializers.ModelSerializer):
    """Serializer pour les paiements"""

    class Meta:
        model = PaiementFacture
        fields = [
            'id', 'date_paiement', 'montant', 'mode_paiement',
            'reference', 'notes', 'created_at'
        ]
        read_only_fields = ['created_at']

    def validate_montant(self, value):
        """Validation du montant"""
        if value <= 0:
            raise serializers.ValidationError("Le montant doit être positif")
        return value


class FactureSerializer(serializers.ModelSerializer):
    """Serializer principal pour les factures"""

    client_details = ClientSerializer(source='client', read_only=True)
    lignes = LigneFactureSerializer(many=True, read_only=True)
    paiements = PaiementFactureSerializer(many=True, read_only=True)

    # Champs calculés
    montant_paye = serializers.SerializerMethodField()
    montant_restant = serializers.SerializerMethodField()
    est_payee = serializers.ReadOnlyField()
    est_en_retard = serializers.ReadOnlyField()
    bons_livraison = serializers.SerializerMethodField()

    class Meta:
        model = Facture
        fields = [
            'id', 'numero', 'type_document', 'client', 'client_details',
            'date_emission', 'date_echeance', 'date_paiement', 'statut',
            'montant_ht', 'montant_tva', 'montant_ttc',
            'remise_pourcentage', 'remise_montant',
            'notes', 'conditions_paiement', 'fichier_pdf',
            'lignes', 'paiements', 'creee_par', 'facture_origine', 'bons_livraison',
            'montant_paye', 'montant_restant', 'est_payee', 'est_en_retard',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'numero', 'montant_ht', 'montant_tva', 'montant_ttc',
            'montant_paye', 'montant_restant', 'est_payee', 'est_en_retard',
            'bons_livraison', 'created_at', 'updated_at'
        ]

    def get_montant_paye(self, obj):
        """Calcule le montant total payé"""
        return sum(p.montant for p in obj.paiements.all())

    def get_montant_restant(self, obj):
        """Calcule le montant restant à payer"""
        return obj.montant_ttc - self.get_montant_paye(obj)


class FactureCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de factures"""

    lignes = LigneFactureSerializer(many=True)

    class Meta:
        model = Facture
        fields = [
            'type_document', 'client', 'date_emission', 'date_echeance',
            'remise_pourcentage', 'notes', 'conditions_paiement', 'lignes'
        ]

    def validate_lignes(self, value):
        """Validation des lignes"""
        if not value:
            raise serializers.ValidationError("Au moins une ligne est requise")
        return value

    def validate(self, data):
        """Validation globale"""
        # Vérifier que la date d'échéance est après la date d'émission
        if data.get('date_echeance') and data.get('date_emission'):
            if data['date_echeance'] < data['date_emission']:
                raise serializers.ValidationError(
                    "La date d'échéance doit être postérieure à la date d'émission"
                )
        return data

    def create(self, validated_data):
        """Création d'une facture avec ses lignes"""
        lignes_data = validated_data.pop('lignes')

        # Ajouter l'utilisateur créateur
        if 'request' in self.context:
            validated_data['creee_par'] = self.context['request'].user

        facture = Facture.objects.create(**validated_data)

        # Créer les lignes
        for ordre, ligne_data in enumerate(lignes_data):
            ligne_data['ordre'] = ordre
            LigneFacture.objects.create(facture=facture, **ligne_data)

        return facture


class FactureUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour de factures"""

    lignes = LigneFactureSerializer(many=True, required=False)

    class Meta:
        model = Facture
        fields = [
            'type_document', 'client', 'date_emission', 'date_echeance',
            'notes', 'conditions_paiement', 'remise_pourcentage', 'lignes'
        ]

    def update(self, instance, validated_data):
        """Met à jour une facture avec ses lignes"""
        lignes_data = validated_data.pop('lignes', [])

        # Mettre à jour les champs de la facture
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Mettre à jour les lignes si fournies
        if lignes_data:
            # Supprimer les anciennes lignes
            instance.lignes.all().delete()

            # Créer les nouvelles lignes
            for ligne_data in lignes_data:
                LigneFacture.objects.create(facture=instance, **ligne_data)

        return instance


class FactureListSerializer(serializers.ModelSerializer):
    """Serializer optimisé pour la liste des factures"""

    client_nom = serializers.CharField(source='client.nom', read_only=True)
    client_email = serializers.CharField(source='client.email', read_only=True)
    montant_paye = serializers.SerializerMethodField()
    montant_restant = serializers.SerializerMethodField()
    est_en_retard = serializers.SerializerMethodField()

    class Meta:
        model = Facture
        fields = [
            'id', 'numero', 'type_document', 'client_nom', 'client_email',
            'date_emission', 'date_echeance', 'statut', 'montant_ht',
            'montant_tva', 'montant_ttc', 'montant_paye', 'montant_restant',
            'est_en_retard', 'created_at'
        ]

    def get_montant_paye(self, obj):
        """Calcule le montant déjà payé"""
        return obj.paiements.aggregate(total=Sum('montant'))['total'] or Decimal('0')

    def get_montant_restant(self, obj):
        """Calcule le montant restant à payer"""
        return obj.montant_ttc - self.get_montant_paye(obj)

    def get_est_en_retard(self, obj):
        """Vérifie si la facture est en retard"""
        if not obj.date_echeance or obj.statut in ['PAYE', 'ANNULE']:
            return False
        return obj.date_echeance < timezone.now().date()

    def get_bons_livraison(self, obj):
        """Récupère les bons de livraison associés à cette facture"""
        if obj.type_document != 'FACTURE':
            return []

        bons = obj.bons_livraison.all()
        return [{
            'id': bon.id,
            'numero': bon.numero,
            'statut': bon.statut,
            'date_emission': bon.date_emission,
            'created_at': bon.created_at
        } for bon in bons]


class FactureStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques de facturation"""

    total_factures = serializers.IntegerField()
    total_montant = serializers.DecimalField(max_digits=10, decimal_places=3)
    factures_payees = serializers.IntegerField()
    montant_paye = serializers.DecimalField(max_digits=10, decimal_places=3)
    factures_en_attente = serializers.IntegerField()
    montant_en_attente = serializers.DecimalField(max_digits=10, decimal_places=3)
    factures_en_retard = serializers.IntegerField()
    montant_en_retard = serializers.DecimalField(max_digits=10, decimal_places=3)
    taux_paiement = serializers.DecimalField(max_digits=5, decimal_places=2)
    montant_moyen = serializers.DecimalField(max_digits=10, decimal_places=3)
