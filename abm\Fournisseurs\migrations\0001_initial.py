# Generated by Django 5.2.4 on 2025-08-12 02:34

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Fournisseur',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('code_fournisseur', models.CharField(max_length=50, unique=True, verbose_name='Code fournisseur')),
                ('nom', models.CharField(max_length=200, verbose_name='Nom/Raison sociale')),
                ('email', models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('telephone', models.CharField(blank=True, max_length=20, validators=[django.core.validators.RegexValidator('^\\+?[\\d\\s\\-\\(\\)]+$', 'Format de téléphone invalide')])),
                ('site_web', models.URLField(blank=True)),
                ('type_fournisseur', models.CharField(choices=[('PRODUITS', 'Fournisseur de produits'), ('SERVICES', 'Fournisseur de services'), ('MIXTE', 'Produits et services')], default='PRODUITS', max_length=20)),
                ('statut', models.CharField(choices=[('ACTIF', 'Actif'), ('INACTIF', 'Inactif'), ('SUSPENDU', 'Suspendu'), ('PROSPECT', 'Prospect')], default='ACTIF', max_length=20)),
                ('adresse', models.TextField(verbose_name='Adresse')),
                ('ville', models.CharField(max_length=100)),
                ('code_postal', models.CharField(max_length=10)),
                ('pays', models.CharField(default='France', max_length=100)),
                ('siret', models.CharField(blank=True, max_length=14, validators=[django.core.validators.RegexValidator('^\\d{14}$', 'SIRET doit contenir 14 chiffres')])),
                ('tva_intracommunautaire', models.CharField(blank=True, max_length=20)),
                ('conditions_paiement', models.CharField(default='30 jours net', max_length=200)),
                ('delai_paiement', models.IntegerField(default=30, verbose_name='Délai de paiement (jours)')),
                ('remise_accordee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5, verbose_name='Remise accordée (%)')),
                ('contact_principal', models.CharField(blank=True, max_length=200)),
                ('email_contact', models.EmailField(blank=True, max_length=254)),
                ('telephone_contact', models.CharField(blank=True, max_length=20)),
                ('total_achats', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12, verbose_name='Total des achats')),
                ('nb_commandes', models.IntegerField(default=0, verbose_name='Nombre de commandes')),
                ('derniere_commande', models.DateTimeField(blank=True, null=True)),
                ('note_qualite', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Note qualité (1-5)')),
                ('note_delai', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Note délai (1-5)')),
                ('note_service', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Note service (1-5)')),
                ('notes', models.TextField(blank=True, verbose_name='Notes internes')),
                ('tags', models.CharField(blank=True, help_text='Tags séparés par des virgules', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fournisseurs_created', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fournisseurs_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Fournisseur',
                'verbose_name_plural': 'Fournisseurs',
                'ordering': ['nom'],
            },
        ),
        migrations.CreateModel(
            name='CommandeFournisseur',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('numero', models.CharField(max_length=50, unique=True, verbose_name='Numéro de commande')),
                ('statut', models.CharField(choices=[('BROUILLON', 'Brouillon'), ('ENVOYEE', 'Envoyée'), ('CONFIRMEE', 'Confirmée'), ('RECEPTION_PARTIELLE', 'Réception partielle'), ('RECUE', 'Reçue'), ('ANNULEE', 'Annulée')], default='BROUILLON', max_length=20)),
                ('date_commande', models.DateTimeField(auto_now_add=True)),
                ('date_livraison_prevue', models.DateField(blank=True, null=True)),
                ('date_reception', models.DateField(blank=True, null=True)),
                ('sous_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('montant_tva', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('montant_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('fournisseur', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='commandes', to='Fournisseurs.fournisseur')),
            ],
            options={
                'verbose_name': 'Commande fournisseur',
                'verbose_name_plural': 'Commandes fournisseurs',
                'ordering': ['-date_commande'],
            },
        ),
        migrations.AddIndex(
            model_name='fournisseur',
            index=models.Index(fields=['code_fournisseur'], name='Fournisseur_code_fo_122491_idx'),
        ),
        migrations.AddIndex(
            model_name='fournisseur',
            index=models.Index(fields=['statut'], name='Fournisseur_statut_293af7_idx'),
        ),
        migrations.AddIndex(
            model_name='fournisseur',
            index=models.Index(fields=['type_fournisseur'], name='Fournisseur_type_fo_aeba5d_idx'),
        ),
    ]
