from rest_framework import serializers
from .models import MouvementStock, Inventaire, LigneInventaire

class MouvementStockSerializer(serializers.ModelSerializer):
    """Serializer pour les mouvements de stock"""
    
    produit_nom = serializers.Char<PERSON>ield(source='produit.nom', read_only=True)
    produit_reference = serializers.CharField(source='produit.reference', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = MouvementStock
        fields = '__all__'
        read_only_fields = (
            'reference', 'quantite_avant', 'quantite_apres', 
            'valeur_totale', 'date_mouvement', 'created_by'
        )

class LigneInventaireSerializer(serializers.ModelSerializer):
    """Serializer pour les lignes d'inventaire"""
    
    produit_nom = serializers.CharField(source='produit.nom', read_only=True)
    produit_reference = serializers.Cha<PERSON><PERSON><PERSON>(source='produit.reference', read_only=True)
    counted_by_name = serializers.CharField(source='counted_by.get_full_name', read_only=True)
    
    class Meta:
        model = LigneInventaire
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'ecart', 'valeur_ecart')

class InventaireSerializer(serializers.ModelSerializer):
    """Serializer pour les inventaires"""
    
    lignes = LigneInventaireSerializer(many=True, read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    validated_by_name = serializers.CharField(source='validated_by.get_full_name', read_only=True)
    
    class Meta:
        model = Inventaire
        fields = '__all__'
        read_only_fields = (
            'numero', 'created_at', 'updated_at', 'created_by', 'validated_by',
            'nb_produits_comptes', 'nb_ecarts_detectes', 'valeur_ecarts',
            'date_validation'
        )

class StockStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques de stock"""
    
    mouvements_jour = serializers.IntegerField()
    mouvements_semaine = serializers.ListField()
    valeur_entrees_semaine = serializers.FloatField()
    valeur_sorties_semaine = serializers.FloatField()

class StockOverviewSerializer(serializers.Serializer):
    """Serializer pour la vue d'ensemble du stock"""
    
    valeur_stock_total = serializers.FloatField()
    nb_produits_total = serializers.IntegerField()
    nb_produits_en_stock = serializers.IntegerField()
    nb_produits_rupture = serializers.IntegerField()
    nb_produits_alerte = serializers.IntegerField()
    rotation_moyenne = serializers.FloatField()
    couverture_moyenne = serializers.FloatField()
    mouvements_jour = serializers.IntegerField()
    derniere_maj = serializers.DateTimeField()
    alertes_critiques = serializers.ListField()
    top_mouvements = serializers.ListField()
