from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Count, Q, F
from django.utils import timezone
from datetime import datetime, timedelta

from .models import <PERSON><PERSON>, LigneCommande, StatutCommande
from .serializers import CommandeSerializer, LigneCommandeSerializer, StatutCommandeSerializer
from Authentication.permissions import HasModulePermission

class CommandeViewSet(viewsets.ModelViewSet):
    """ViewSet pour la gestion des commandes"""

    queryset = Commande.objects.all()
    serializer_class = CommandeSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'commandes'

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['statut', 'priorite', 'type_commande', 'client']
    search_fields = ['numero', 'client__nom', 'notes']
    ordering_fields = ['date_commande', 'montant_total', 'date_livraison_prevue']
    ordering = ['-date_commande']

    def perform_create(self, serializer):
        # Générer un numéro de commande automatique
        numero = self.generer_numero_commande()
        serializer.save(
            numero=numero,
            created_by=self.request.user
        )

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    def generer_numero_commande(self):
        """Génère un numéro de commande unique"""
        today = timezone.now()
        prefix = f"CMD-{today.year}-{today.month:02d}-"

        # Trouver le dernier numéro du mois
        last_commande = Commande.objects.filter(
            numero__startswith=prefix
        ).order_by('-numero').first()

        if last_commande:
            last_number = int(last_commande.numero.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:04d}"

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Statistiques des commandes"""

        # Statistiques de base
        total_commandes = Commande.objects.count()
        commandes_mois = Commande.objects.filter(
            date_commande__gte=timezone.now().replace(day=1)
        ).count()

        # CA du mois
        ca_mois = Commande.objects.filter(
            date_commande__gte=timezone.now().replace(day=1),
            statut__in=['CONFIRMEE', 'PREPARATION', 'EXPEDITION', 'LIVREE']
        ).aggregate(total=Sum('montant_total'))['total'] or 0

        # Répartition par statut
        statuts = Commande.objects.values('statut').annotate(
            count=Count('id'),
            montant_total=Sum('montant_total')
        ).order_by('-count')

        # Commandes en retard
        commandes_retard = Commande.objects.filter(
            date_livraison_prevue__lt=timezone.now().date(),
            statut__in=['CONFIRMEE', 'PREPARATION', 'EXPEDITION']
        ).count()

        data = {
            'total_commandes': total_commandes,
            'commandes_mois': commandes_mois,
            'ca_mois': float(ca_mois),
            'commandes_retard': commandes_retard,
            'statuts': list(statuts)
        }

        return Response(data)

    @action(detail=True, methods=['post'])
    def changer_statut(self, request, pk=None):
        """Change le statut d'une commande"""
        commande = self.get_object()
        nouveau_statut = request.data.get('statut')
        commentaire = request.data.get('commentaire', '')

        if nouveau_statut not in dict(Commande.STATUT_CHOICES):
            return Response(
                {'error': 'Statut invalide'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Enregistrer le changement de statut
        StatutCommande.objects.create(
            commande=commande,
            ancien_statut=commande.statut,
            nouveau_statut=nouveau_statut,
            commentaire=commentaire,
            created_by=request.user
        )

        commande.statut = nouveau_statut
        commande.updated_by = request.user

        # Actions spéciales selon le statut
        if nouveau_statut == 'LIVREE':
            commande.date_livraison_reelle = timezone.now().date()

        commande.save()

        return Response({
            'message': f'Statut changé vers {nouveau_statut}',
            'nouveau_statut': nouveau_statut
        })


class LigneCommandeViewSet(viewsets.ModelViewSet):
    """ViewSet pour les lignes de commande"""

    queryset = LigneCommande.objects.all()
    serializer_class = LigneCommandeSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'commandes'

    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['commande', 'produit']


class StatutCommandeViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet pour l'historique des statuts"""

    queryset = StatutCommande.objects.all()
    serializer_class = StatutCommandeSerializer
    permission_classes = [IsAuthenticated, HasModulePermission]
    required_permission = 'commandes'

    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['commande']
