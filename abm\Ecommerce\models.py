"""
Modèles pour le module E-commerce
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from Produits.models import Produit
from Clients.models import Client
from Core.models import BaseModel


class CategorieEcommerce(BaseModel):
    """Catégorie intelligente pour l'e-commerce"""
    nom = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='categories_ecommerce/', blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='enfants')
    ordre = models.IntegerField(default=0)

    # SEO intelligent
    meta_title = models.CharField(max_length=200, blank=True)
    meta_description = models.TextField(blank=True)
    meta_keywords = models.CharField(max_length=500, blank=True)

    # Analytics
    vues_total = models.PositiveIntegerField(default=0)
    produits_vendus = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = "Catégorie E-commerce"
        verbose_name_plural = "Catégories E-commerce"
        ordering = ['ordre', 'nom']

    def __str__(self):
        return self.nom

    @property
    def chemin_complet(self):
        """Retourne le chemin complet de la catégorie"""
        if self.parent:
            return f"{self.parent.chemin_complet} > {self.nom}"
        return self.nom


class ProduitEcommerce(models.Model):
    """Extension du produit pour l'e-commerce"""
    produit = models.OneToOneField(Produit, on_delete=models.CASCADE, related_name='ecommerce')
    
    # Informations e-commerce
    slug = models.SlugField(unique=True)
    categories_ecommerce = models.ManyToManyField(CategorieEcommerce, blank=True)
    
    # Visibilité et statut
    visible_en_ligne = models.BooleanField(default=True)
    en_vedette = models.BooleanField(default=False)
    nouveau = models.BooleanField(default=False)
    en_promotion = models.BooleanField(default=False)
    
    # Prix e-commerce (peut différer du prix interne)
    prix_public = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    prix_promo = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    date_debut_promo = models.DateTimeField(null=True, blank=True)
    date_fin_promo = models.DateTimeField(null=True, blank=True)
    
    # Stock e-commerce
    stock_disponible = models.IntegerField(default=0)
    stock_reserve = models.IntegerField(default=0)  # Stock réservé par les commandes
    gestion_stock = models.BooleanField(default=True)
    autoriser_commande_sans_stock = models.BooleanField(default=False)
    
    # SEO
    meta_title = models.CharField(max_length=200, blank=True)
    meta_description = models.TextField(blank=True)
    meta_keywords = models.CharField(max_length=500, blank=True)
    
    # Informations supplémentaires
    description_courte = models.TextField(blank=True)
    description_longue = models.TextField(blank=True)
    caracteristiques = models.JSONField(default=dict, blank=True)  # Stockage flexible des caractéristiques
    
    # Livraison
    poids = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True)  # en kg
    dimensions_longueur = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)  # en cm
    dimensions_largeur = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    dimensions_hauteur = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    
    # Statistiques
    nombre_vues = models.IntegerField(default=0)
    nombre_ventes = models.IntegerField(default=0)
    note_moyenne = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    nombre_avis = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Produit E-commerce"
        verbose_name_plural = "Produits E-commerce"
        ordering = ['-en_vedette', '-nouveau', 'produit__nom']

    def __str__(self):
        return f"{self.produit.nom} (E-commerce)"

    @property
    def prix_actuel(self):
        """Retourne le prix actuel (promo si active, sinon prix public ou prix produit)"""
        from django.utils import timezone
        now = timezone.now()
        
        # Vérifier si une promotion est active
        if (self.en_promotion and self.prix_promo and 
            self.date_debut_promo and self.date_fin_promo and
            self.date_debut_promo <= now <= self.date_fin_promo):
            return self.prix_promo
        
        # Sinon prix public ou prix du produit
        return self.prix_public or self.produit.prix_unitaire

    @property
    def stock_disponible_reel(self):
        """Stock réellement disponible (stock - réservé)"""
        return max(0, self.stock_disponible - self.stock_reserve)

    @property
    def peut_etre_commande(self):
        """Vérifie si le produit peut être commandé"""
        if not self.visible_en_ligne or not self.produit.actif:
            return False
        
        if self.gestion_stock and not self.autoriser_commande_sans_stock:
            return self.stock_disponible_reel > 0
        
        return True


class ImageProduitEcommerce(models.Model):
    """Images supplémentaires pour les produits e-commerce"""
    produit_ecommerce = models.ForeignKey(ProduitEcommerce, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='produits_ecommerce/')
    alt_text = models.CharField(max_length=200, blank=True)
    ordre = models.IntegerField(default=0)
    principale = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Image Produit E-commerce"
        verbose_name_plural = "Images Produits E-commerce"
        ordering = ['ordre', '-principale']

    def __str__(self):
        return f"Image {self.ordre} - {self.produit_ecommerce}"


class Panier(models.Model):
    """Panier d'achat"""
    session_key = models.CharField(max_length=40, null=True, blank=True)  # Pour les utilisateurs non connectés
    client = models.ForeignKey(Client, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Panier"
        verbose_name_plural = "Paniers"

    def __str__(self):
        if self.client:
            return f"Panier de {self.client.nom_complet}"
        return f"Panier session {self.session_key}"

    @property
    def total_articles(self):
        """Nombre total d'articles dans le panier"""
        return sum(item.quantite for item in self.items.all())

    @property
    def total_ht(self):
        """Total HT du panier"""
        return sum(item.total_ht for item in self.items.all())

    @property
    def total_tva(self):
        """Total TVA du panier"""
        return sum(item.total_tva for item in self.items.all())

    @property
    def total_ttc(self):
        """Total TTC du panier"""
        return self.total_ht + self.total_tva


class ItemPanier(models.Model):
    """Article dans un panier"""
    panier = models.ForeignKey(Panier, on_delete=models.CASCADE, related_name='items')
    produit_ecommerce = models.ForeignKey(ProduitEcommerce, on_delete=models.CASCADE)
    quantite = models.IntegerField(validators=[MinValueValidator(1)])
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2)  # Prix au moment de l'ajout
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Article Panier"
        verbose_name_plural = "Articles Panier"
        unique_together = ['panier', 'produit_ecommerce']

    def __str__(self):
        return f"{self.quantite}x {self.produit_ecommerce.produit.nom}"

    @property
    def total_ht(self):
        """Total HT de la ligne"""
        return self.quantite * self.prix_unitaire

    @property
    def total_tva(self):
        """Total TVA de la ligne"""
        taux_tva = self.produit_ecommerce.produit.taux_tva / 100
        return self.total_ht * taux_tva

    @property
    def total_ttc(self):
        """Total TTC de la ligne"""
        return self.total_ht + self.total_tva

    def save(self, *args, **kwargs):
        # Sauvegarder le prix actuel si pas déjà défini
        if not self.prix_unitaire:
            self.prix_unitaire = self.produit_ecommerce.prix_actuel
        super().save(*args, **kwargs)


class CommandeEcommerce(models.Model):
    """Commande e-commerce"""
    STATUT_CHOICES = [
        ('EN_ATTENTE', 'En attente'),
        ('CONFIRMEE', 'Confirmée'),
        ('EN_PREPARATION', 'En préparation'),
        ('EXPEDIEE', 'Expédiée'),
        ('LIVREE', 'Livrée'),
        ('ANNULEE', 'Annulée'),
        ('REMBOURSEE', 'Remboursée'),
    ]

    numero = models.CharField(max_length=50, unique=True)
    client = models.ForeignKey(Client, on_delete=models.PROTECT, related_name='commandes_ecommerce')
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='EN_ATTENTE')
    
    # Montants
    total_ht = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_tva = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_ttc = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    frais_livraison = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Adresses (copiées au moment de la commande)
    adresse_facturation = models.JSONField()
    adresse_livraison = models.JSONField()
    
    # Informations de livraison
    transporteur = models.CharField(max_length=100, blank=True)
    numero_suivi = models.CharField(max_length=100, blank=True)
    date_expedition = models.DateTimeField(null=True, blank=True)
    date_livraison_prevue = models.DateTimeField(null=True, blank=True)
    date_livraison_reelle = models.DateTimeField(null=True, blank=True)
    
    # Paiement
    methode_paiement = models.CharField(max_length=50, blank=True)
    statut_paiement = models.CharField(max_length=20, default='EN_ATTENTE')
    reference_paiement = models.CharField(max_length=100, blank=True)
    
    # Notes
    notes_client = models.TextField(blank=True)
    notes_interne = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Commande E-commerce"
        verbose_name_plural = "Commandes E-commerce"
        ordering = ['-created_at']

    def __str__(self):
        return f"Commande {self.numero} - {self.client.nom_complet}"

    def save(self, *args, **kwargs):
        if not self.numero:
            self.numero = self.generer_numero()
        super().save(*args, **kwargs)

    def generer_numero(self):
        """Génère un numéro de commande automatique"""
        from datetime import datetime
        year = datetime.now().year
        
        last_commande = CommandeEcommerce.objects.filter(
            created_at__year=year
        ).order_by('-numero').first()
        
        if last_commande and last_commande.numero:
            try:
                last_number = int(last_commande.numero.split('-')[-1])
                new_number = last_number + 1
            except (ValueError, IndexError):
                new_number = 1
        else:
            new_number = 1
        
        return f"CMD-{year}-{new_number:06d}"


class LigneCommandeEcommerce(models.Model):
    """Ligne de commande e-commerce"""
    commande = models.ForeignKey(CommandeEcommerce, on_delete=models.CASCADE, related_name='lignes')
    produit_ecommerce = models.ForeignKey(ProduitEcommerce, on_delete=models.PROTECT)
    
    # Informations au moment de la commande (pour historique)
    nom_produit = models.CharField(max_length=200)
    code_produit = models.CharField(max_length=50)
    
    quantite = models.IntegerField(validators=[MinValueValidator(1)])
    prix_unitaire = models.DecimalField(max_digits=10, decimal_places=2)
    taux_tva = models.DecimalField(max_digits=5, decimal_places=2)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Ligne Commande E-commerce"
        verbose_name_plural = "Lignes Commandes E-commerce"

    def __str__(self):
        return f"{self.quantite}x {self.nom_produit}"

    @property
    def total_ht(self):
        """Total HT de la ligne"""
        return self.quantite * self.prix_unitaire

    @property
    def total_tva(self):
        """Total TVA de la ligne"""
        return self.total_ht * (self.taux_tva / 100)

    @property
    def total_ttc(self):
        """Total TTC de la ligne"""
        return self.total_ht + self.total_tva

    def save(self, *args, **kwargs):
        # Sauvegarder les informations du produit au moment de la commande
        if not self.nom_produit:
            self.nom_produit = self.produit_ecommerce.produit.nom
        if not self.code_produit:
            self.code_produit = self.produit_ecommerce.produit.code_produit
        if not self.prix_unitaire:
            self.prix_unitaire = self.produit_ecommerce.prix_actuel
        if not self.taux_tva:
            self.taux_tva = self.produit_ecommerce.produit.taux_tva
        
        super().save(*args, **kwargs)
