{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\abm\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { AuthService } from \"../services/apiService\";\n\n// Types spécifiques pour l'authentification\n\n// Types pour l'authentification\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Configuration de sécurité\nconst AUTH_CONFIG = {\n  SESSION_DURATION: 8 * 60 * 60 * 1000,\n  // 8 heures\n  STORAGE_KEY: \"abm_auth_token\",\n  USER_KEY: \"abm_user_data\",\n  REMEMBER_KEY: \"abm_remember_me\"\n};\n\n// Hiérarchie des rôles\nconst ROLE_HIERARCHY = {\n  NORMAL: 1,\n  COMPTABLE: 2,\n  ADMIN: 3,\n  SUPERADMIN: 4\n};\n\n// Permissions par module\nconst MODULE_PERMISSIONS = {\n  dashboard: [\"NORMAL\", \"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  clients: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  produits: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  factures: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  commandes: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  paiements: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  stock: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  rapports: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\n  settings: [\"ADMIN\", \"SUPERADMIN\"],\n  users: [\"SUPERADMIN\"]\n};\n\n// Utilisateurs de démonstration\nconst demoUsers = {\n  \"<EMAIL>\": {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    nom: \"Administrateur\",\n    prenom: \"Système\",\n    role: \"ADMIN\",\n    permissions: [\"*\"],\n    modules: Object.keys(MODULE_PERMISSIONS),\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  },\n  \"<EMAIL>\": {\n    id: \"2\",\n    email: \"<EMAIL>\",\n    nom: \"Manager\",\n    prenom: \"Principal\",\n    role: \"ADMIN\",\n    permissions: [\"read\", \"write\", \"delete\"],\n    modules: [\"dashboard\", \"clients\", \"produits\", \"factures\", \"commandes\", \"paiements\", \"stock\", \"rapports\"],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  },\n  \"<EMAIL>\": {\n    id: \"3\",\n    email: \"<EMAIL>\",\n    nom: \"Comptable\",\n    prenom: \"Principal\",\n    role: \"COMPTABLE\",\n    permissions: [\"read\", \"write\"],\n    modules: [\"dashboard\", \"clients\", \"factures\", \"paiements\", \"rapports\"],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  },\n  \"<EMAIL>\": {\n    id: \"4\",\n    email: \"<EMAIL>\",\n    nom: \"Vendeur\",\n    prenom: \"Commercial\",\n    role: \"NORMAL\",\n    permissions: [\"read\", \"write\"],\n    modules: [\"dashboard\", \"clients\", \"produits\", \"factures\", \"commandes\", \"stock\"],\n    statut: \"ACTIF\",\n    derniere_connexion: new Date().toISOString()\n  }\n};\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [sessionExpiry, setSessionExpiry] = useState(null);\n\n  // Vérifier l'authentification au chargement\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem(AUTH_CONFIG.STORAGE_KEY);\n        const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);\n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n\n          // Vérifier si le token n'est pas expiré\n          const tokenExpiry = new Date(parsedUser.tokenExpiry || 0);\n          if (tokenExpiry > new Date()) {\n            setUser(parsedUser);\n            setSessionExpiry(tokenExpiry);\n          } else {\n            // Token expiré, nettoyer le stockage\n            localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\n            localStorage.removeItem(AUTH_CONFIG.USER_KEY);\n            localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\n          }\n        }\n      } catch (error) {\n        console.error(\"Erreur lors de la vérification de l'authentification:\", error);\n        localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\n        localStorage.removeItem(AUTH_CONFIG.USER_KEY);\n        localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    checkAuth();\n  }, []);\n\n  // Étendre la session\n  const extendSession = useCallback(() => {\n    if (user && sessionExpiry) {\n      const newExpiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n      setSessionExpiry(newExpiry);\n      const updatedUser = {\n        ...user,\n        tokenExpiry: newExpiry.toISOString()\n      };\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(updatedUser));\n    }\n  }, [user, sessionExpiry]);\n\n  // Fonction de connexion\n  const login = useCallback(async credentials => {\n    try {\n      setIsLoading(true);\n\n      // Appel API réel vers Django\n      const response = await AuthService.login(credentials.email, credentials.password);\n      if (response.success && response.tokens) {\n        // Stocker les tokens JWT\n        localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, response.tokens.access);\n        localStorage.setItem(\"refresh_token\", response.tokens.refresh);\n\n        // Récupérer le profil utilisateur\n        const profileResponse = await AuthService.getProfile();\n        if (profileResponse.success) {\n          const user = {\n            id: profileResponse.user.id,\n            email: profileResponse.user.email,\n            nom: profileResponse.user.last_name,\n            prenom: profileResponse.user.first_name,\n            role: profileResponse.user.role,\n            permissions: profileResponse.user.permissions || [],\n            modules: profileResponse.user.modules || [],\n            statut: profileResponse.user.is_active ? \"ACTIF\" : \"INACTIF\",\n            derniere_connexion: new Date().toISOString()\n          };\n          const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n          localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(user));\n          if (credentials.remember) {\n            localStorage.setItem(AUTH_CONFIG.REMEMBER_KEY, \"true\");\n          }\n          setUser(user);\n          setSessionExpiry(expiry);\n          toast.success(`Bienvenue ${user.prenom} ${user.nom} !`);\n          return {\n            success: true,\n            message: \"Connexion réussie\"\n          };\n        }\n      }\n      return {\n        success: false,\n        message: response.message || \"Email ou mot de passe incorrect\",\n        errors: response.errors || {\n          email: [\"Identifiants invalides\"]\n        }\n      };\n    } catch (error) {\n      console.error(\"Erreur lors de la connexion:\", error);\n      return {\n        success: false,\n        message: \"Erreur de connexion. Veuillez réessayer.\",\n        errors: {\n          general: [\"Erreur système\"]\n        }\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Fonction d'inscription\n  const register = useCallback(async credentials => {\n    try {\n      setIsLoading(true);\n\n      // Validation côté client\n      if (credentials.password !== credentials.confirmPassword) {\n        return {\n          success: false,\n          message: \"Les mots de passe ne correspondent pas\",\n          errors: {\n            confirmPassword: [\"Les mots de passe ne correspondent pas\"]\n          }\n        };\n      }\n      if (credentials.password.length < 6) {\n        return {\n          success: false,\n          message: \"Le mot de passe doit contenir au moins 6 caractères\",\n          errors: {\n            password: [\"Le mot de passe doit contenir au moins 6 caractères\"]\n          }\n        };\n      }\n\n      // Préparer les données pour l'API Django\n      const registrationData = {\n        username: credentials.email,\n        // Utiliser l'email comme username\n        email: credentials.email,\n        first_name: credentials.prenom,\n        last_name: credentials.nom,\n        password: credentials.password,\n        password_confirm: credentials.confirmPassword,\n        phone: credentials.phone || \"\",\n        company: credentials.company || \"\"\n      };\n\n      // Appel API réel vers Django\n      const response = await AuthService.register(registrationData);\n      if (response.success) {\n        // Inscription réussie - connecter automatiquement l'utilisateur\n        const loginResult = await login({\n          email: credentials.email,\n          password: credentials.password\n        });\n        return {\n          success: true,\n          message: \"Inscription réussie ! Vous êtes maintenant connecté.\",\n          user: response.user\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message || \"Erreur lors de l'inscription\",\n          errors: response.errors || {}\n        };\n      }\n\n      // Ajouter l'utilisateur aux utilisateurs de démo (en production, ceci serait sauvé en base)\n      demoUsers[credentials.email] = newUser;\n\n      // Créer automatiquement une session pour le nouvel utilisateur\n      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n      const token = `abm_token_${newUser.id}_${Date.now()}`;\n      const userWithToken = {\n        ...newUser,\n        tokenExpiry: expiry.toISOString()\n      };\n\n      // Stocker les données\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));\n      setUser(newUser);\n      setSessionExpiry(expiry);\n      toast.success(`Bienvenue ${newUser.prenom} ${newUser.nom} ! Votre compte a été créé avec succès.`);\n      return {\n        success: true,\n        message: \"Inscription réussie ! Vous êtes maintenant connecté.\"\n      };\n    } catch (error) {\n      console.error(\"Erreur lors de l'inscription:\", error);\n      return {\n        success: false,\n        message: \"Erreur lors de l'inscription. Veuillez réessayer.\",\n        errors: {\n          general: [\"Erreur système\"]\n        }\n      };\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Fonction de déconnexion\n  const logout = useCallback(async () => {\n    try {\n      localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\n      localStorage.removeItem(AUTH_CONFIG.USER_KEY);\n      localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\n      setUser(null);\n      setSessionExpiry(null);\n      toast.info(\"Vous avez été déconnecté\");\n    } catch (error) {\n      console.error(\"Erreur lors de la déconnexion:\", error);\n    }\n  }, []);\n\n  // Rafraîchir le token\n  const refreshToken = useCallback(async () => {\n    try {\n      if (!user) return false;\n      const newExpiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\n      const newToken = `abm_token_${user.id}_${Date.now()}`;\n      const updatedUser = {\n        ...user,\n        tokenExpiry: newExpiry.toISOString(),\n        derniere_connexion: new Date().toISOString()\n      };\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, newToken);\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(updatedUser));\n      setSessionExpiry(newExpiry);\n      return true;\n    } catch (error) {\n      console.error(\"Erreur lors du rafraîchissement du token:\", error);\n      await logout();\n      return false;\n    }\n  }, [user, logout]);\n\n  // Vérifier si l'utilisateur a un rôle spécifique\n  const hasRole = useCallback(role => {\n    return (user === null || user === void 0 ? void 0 : user.role) === role;\n  }, [user]);\n\n  // Vérifier si l'utilisateur a au minimum un certain rôle\n  const hasMinimumRole = useCallback(minimumRole => {\n    if (!user) return false;\n    return ROLE_HIERARCHY[user.role] >= ROLE_HIERARCHY[minimumRole];\n  }, [user]);\n\n  // Vérifier l'accès à un module\n  const hasModuleAccess = useCallback(moduleName => {\n    if (!user) return false;\n\n    // Admin a accès à tout\n    if (user.role === \"ADMIN\") return true;\n\n    // Vérifier les permissions du module\n    const moduleRoles = MODULE_PERMISSIONS[moduleName];\n    if (!moduleRoles) return false;\n    return moduleRoles.includes(user.role);\n  }, [user]);\n\n  // Vérifier une permission spécifique\n  const hasPermission = useCallback(permission => {\n    if (!user) return false;\n\n    // Admin a toutes les permissions\n    if (user.role === \"ADMIN\" || user.permissions.includes(\"*\")) return true;\n    return user.permissions.includes(permission);\n  }, [user]);\n\n  // Valeur du contexte mémorisée\n  const contextValue = useMemo(() => ({\n    user,\n    isAuthenticated: !!user,\n    isLoading,\n    sessionExpiry,\n    login,\n    register,\n    logout,\n    refreshToken,\n    hasRole,\n    hasMinimumRole,\n    hasModuleAccess,\n    hasPermission,\n    extendSession\n  }), [user, isLoading, sessionExpiry, login, register, logout, refreshToken, hasRole, hasMinimumRole, hasModuleAccess, hasPermission, extendSession]);\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 545,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook personnalisé pour utiliser le contexte d'authentification\n_s(AuthProvider, \"gaYYbbx7OAE5kqtEjo7sIOhOej0=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth doit être utilisé dans un AuthProvider\");\n  }\n  return context;\n};\n\n// Export des types pour utilisation externe\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "useCallback", "useMemo", "toast", "AuthService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "AUTH_CONFIG", "SESSION_DURATION", "STORAGE_KEY", "USER_KEY", "REMEMBER_KEY", "ROLE_HIERARCHY", "NORMAL", "COMPTABLE", "ADMIN", "SUPERADMIN", "MODULE_PERMISSIONS", "dashboard", "clients", "produits", "factures", "commandes", "paiements", "stock", "rapports", "settings", "users", "demoUsers", "id", "email", "nom", "prenom", "role", "permissions", "modules", "Object", "keys", "statut", "derniere_connexion", "Date", "toISOString", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "isLoading", "setIsLoading", "sessionExpiry", "setSessionExpiry", "checkAuth", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "tokenExpiry", "removeItem", "error", "console", "extendSession", "newExpiry", "now", "updatedUser", "setItem", "stringify", "login", "credentials", "response", "password", "success", "tokens", "access", "refresh", "profileResponse", "getProfile", "last_name", "first_name", "is_active", "expiry", "remember", "message", "errors", "general", "register", "confirmPassword", "length", "registrationData", "username", "password_confirm", "phone", "company", "loginResult", "newUser", "userWithToken", "logout", "info", "refreshToken", "newToken", "hasRole", "hasMinimumRole", "minimumRole", "hasModuleAccess", "moduleName", "moduleRoles", "includes", "hasPermission", "permission", "contextValue", "isAuthenticated", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/abm/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, {\r\n  create<PERSON>ontext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  use<PERSON><PERSON>back,\r\n  useMemo,\r\n  ReactNode,\r\n} from \"react\";\r\nimport { toast } from \"react-toastify\";\r\nimport { AuthService } from \"../services/apiService\";\r\n\r\n// Types spécifiques pour l'authentification\r\ninterface AuthTokens {\r\n  access: string;\r\n  refresh: string;\r\n}\r\n\r\ninterface AuthResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  tokens?: AuthTokens;\r\n  user?: any;\r\n  errors?: Record<string, string[]>;\r\n}\r\n\r\ninterface ProfileResponse {\r\n  success: boolean;\r\n  user?: {\r\n    id: string;\r\n    email: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    role: string;\r\n    is_active: boolean;\r\n    permissions?: string[];\r\n    modules?: string[];\r\n  };\r\n}\r\n\r\n// Types pour l'authentification\r\ninterface User {\r\n  id: string;\r\n  email: string;\r\n  nom: string;\r\n  prenom: string;\r\n  role: UserRole;\r\n  permissions: string[];\r\n  modules: string[];\r\n  avatar?: string;\r\n  derniere_connexion?: string;\r\n  statut: \"ACTIF\" | \"INACTIF\" | \"SUSPENDU\";\r\n}\r\n\r\ninterface LoginCredentials {\r\n  email: string;\r\n  password: string;\r\n  remember?: boolean;\r\n}\r\n\r\ninterface RegisterCredentials {\r\n  email: string;\r\n  password: string;\r\n  confirmPassword: string;\r\n  prenom: string;\r\n  nom: string;\r\n  role?: UserRole;\r\n  phone?: string;\r\n  company?: string;\r\n}\r\n\r\ntype UserRole = \"NORMAL\" | \"COMPTABLE\" | \"ADMIN\" | \"SUPERADMIN\";\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  sessionExpiry: Date | null;\r\n  login: (credentials: LoginCredentials) => Promise<{\r\n    success: boolean;\r\n    message?: string;\r\n    errors?: Record<string, string[]>;\r\n  }>;\r\n  register: (credentials: RegisterCredentials) => Promise<{\r\n    success: boolean;\r\n    message?: string;\r\n    errors?: Record<string, string[]>;\r\n  }>;\r\n  logout: () => Promise<void>;\r\n  refreshToken: () => Promise<boolean>;\r\n  hasRole: (role: UserRole) => boolean;\r\n  hasMinimumRole: (minimumRole: UserRole) => boolean;\r\n  hasModuleAccess: (moduleName: string) => boolean;\r\n  hasPermission: (permission: string) => boolean;\r\n  extendSession: () => void;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\n// Configuration de sécurité\r\nconst AUTH_CONFIG = {\r\n  SESSION_DURATION: 8 * 60 * 60 * 1000, // 8 heures\r\n  STORAGE_KEY: \"abm_auth_token\",\r\n  USER_KEY: \"abm_user_data\",\r\n  REMEMBER_KEY: \"abm_remember_me\",\r\n};\r\n\r\n// Hiérarchie des rôles\r\nconst ROLE_HIERARCHY: Record<UserRole, number> = {\r\n  NORMAL: 1,\r\n  COMPTABLE: 2,\r\n  ADMIN: 3,\r\n  SUPERADMIN: 4,\r\n};\r\n\r\n// Permissions par module\r\nconst MODULE_PERMISSIONS: Record<string, UserRole[]> = {\r\n  dashboard: [\"NORMAL\", \"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  clients: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  produits: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  factures: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  commandes: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  paiements: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  stock: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  rapports: [\"COMPTABLE\", \"ADMIN\", \"SUPERADMIN\"],\r\n  settings: [\"ADMIN\", \"SUPERADMIN\"],\r\n  users: [\"SUPERADMIN\"],\r\n};\r\n\r\n// Utilisateurs de démonstration\r\nconst demoUsers: Record<string, User> = {\r\n  \"<EMAIL>\": {\r\n    id: \"1\",\r\n    email: \"<EMAIL>\",\r\n    nom: \"Administrateur\",\r\n    prenom: \"Système\",\r\n    role: \"ADMIN\",\r\n    permissions: [\"*\"],\r\n    modules: Object.keys(MODULE_PERMISSIONS),\r\n    statut: \"ACTIF\",\r\n    derniere_connexion: new Date().toISOString(),\r\n  },\r\n  \"<EMAIL>\": {\r\n    id: \"2\",\r\n    email: \"<EMAIL>\",\r\n    nom: \"Manager\",\r\n    prenom: \"Principal\",\r\n    role: \"ADMIN\",\r\n    permissions: [\"read\", \"write\", \"delete\"],\r\n    modules: [\r\n      \"dashboard\",\r\n      \"clients\",\r\n      \"produits\",\r\n      \"factures\",\r\n      \"commandes\",\r\n      \"paiements\",\r\n      \"stock\",\r\n      \"rapports\",\r\n    ],\r\n    statut: \"ACTIF\",\r\n    derniere_connexion: new Date().toISOString(),\r\n  },\r\n  \"<EMAIL>\": {\r\n    id: \"3\",\r\n    email: \"<EMAIL>\",\r\n    nom: \"Comptable\",\r\n    prenom: \"Principal\",\r\n    role: \"COMPTABLE\",\r\n    permissions: [\"read\", \"write\"],\r\n    modules: [\"dashboard\", \"clients\", \"factures\", \"paiements\", \"rapports\"],\r\n    statut: \"ACTIF\",\r\n    derniere_connexion: new Date().toISOString(),\r\n  },\r\n  \"<EMAIL>\": {\r\n    id: \"4\",\r\n    email: \"<EMAIL>\",\r\n    nom: \"Vendeur\",\r\n    prenom: \"Commercial\",\r\n    role: \"NORMAL\",\r\n    permissions: [\"read\", \"write\"],\r\n    modules: [\r\n      \"dashboard\",\r\n      \"clients\",\r\n      \"produits\",\r\n      \"factures\",\r\n      \"commandes\",\r\n      \"stock\",\r\n    ],\r\n    statut: \"ACTIF\",\r\n    derniere_connexion: new Date().toISOString(),\r\n  },\r\n};\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);\r\n\r\n  // Vérifier l'authentification au chargement\r\n  useEffect(() => {\r\n    const checkAuth = async () => {\r\n      try {\r\n        const token = localStorage.getItem(AUTH_CONFIG.STORAGE_KEY);\r\n        const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);\r\n\r\n        if (token && userData) {\r\n          const parsedUser = JSON.parse(userData);\r\n\r\n          // Vérifier si le token n'est pas expiré\r\n          const tokenExpiry = new Date(parsedUser.tokenExpiry || 0);\r\n          if (tokenExpiry > new Date()) {\r\n            setUser(parsedUser);\r\n            setSessionExpiry(tokenExpiry);\r\n          } else {\r\n            // Token expiré, nettoyer le stockage\r\n            localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\r\n            localStorage.removeItem(AUTH_CONFIG.USER_KEY);\r\n            localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"Erreur lors de la vérification de l'authentification:\",\r\n          error\r\n        );\r\n        localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\r\n        localStorage.removeItem(AUTH_CONFIG.USER_KEY);\r\n        localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    checkAuth();\r\n  }, []);\r\n\r\n  // Étendre la session\r\n  const extendSession = useCallback(() => {\r\n    if (user && sessionExpiry) {\r\n      const newExpiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\r\n      setSessionExpiry(newExpiry);\r\n\r\n      const updatedUser = { ...user, tokenExpiry: newExpiry.toISOString() };\r\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(updatedUser));\r\n    }\r\n  }, [user, sessionExpiry]);\r\n\r\n  // Fonction de connexion\r\n  const login = useCallback(async (credentials: LoginCredentials) => {\r\n    try {\r\n      setIsLoading(true);\r\n\r\n      // Appel API réel vers Django\r\n      const response = await AuthService.login(\r\n        credentials.email,\r\n        credentials.password\r\n      );\r\n\r\n      if (response.success && response.tokens) {\r\n        // Stocker les tokens JWT\r\n        localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, response.tokens.access);\r\n        localStorage.setItem(\"refresh_token\", response.tokens.refresh);\r\n\r\n        // Récupérer le profil utilisateur\r\n        const profileResponse = await AuthService.getProfile();\r\n\r\n        if (profileResponse.success) {\r\n          const user = {\r\n            id: profileResponse.user.id,\r\n            email: profileResponse.user.email,\r\n            nom: profileResponse.user.last_name,\r\n            prenom: profileResponse.user.first_name,\r\n            role: profileResponse.user.role as UserRole,\r\n            permissions: profileResponse.user.permissions || [],\r\n            modules: profileResponse.user.modules || [],\r\n            statut: profileResponse.user.is_active ? \"ACTIF\" : \"INACTIF\",\r\n            derniere_connexion: new Date().toISOString(),\r\n          };\r\n\r\n          const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\r\n\r\n          localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(user));\r\n\r\n          if (credentials.remember) {\r\n            localStorage.setItem(AUTH_CONFIG.REMEMBER_KEY, \"true\");\r\n          }\r\n\r\n          setUser(user);\r\n          setSessionExpiry(expiry);\r\n\r\n          toast.success(`Bienvenue ${user.prenom} ${user.nom} !`);\r\n\r\n          return {\r\n            success: true,\r\n            message: \"Connexion réussie\",\r\n          };\r\n        }\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: response.message || \"Email ou mot de passe incorrect\",\r\n        errors: response.errors || { email: [\"Identifiants invalides\"] },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de la connexion:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"Erreur de connexion. Veuillez réessayer.\",\r\n        errors: { general: [\"Erreur système\"] },\r\n      };\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Fonction d'inscription\r\n  const register = useCallback(async (credentials: RegisterCredentials) => {\r\n    try {\r\n      setIsLoading(true);\r\n\r\n      // Validation côté client\r\n      if (credentials.password !== credentials.confirmPassword) {\r\n        return {\r\n          success: false,\r\n          message: \"Les mots de passe ne correspondent pas\",\r\n          errors: {\r\n            confirmPassword: [\"Les mots de passe ne correspondent pas\"],\r\n          },\r\n        };\r\n      }\r\n\r\n      if (credentials.password.length < 6) {\r\n        return {\r\n          success: false,\r\n          message: \"Le mot de passe doit contenir au moins 6 caractères\",\r\n          errors: {\r\n            password: [\"Le mot de passe doit contenir au moins 6 caractères\"],\r\n          },\r\n        };\r\n      }\r\n\r\n      // Préparer les données pour l'API Django\r\n      const registrationData = {\r\n        username: credentials.email, // Utiliser l'email comme username\r\n        email: credentials.email,\r\n        first_name: credentials.prenom,\r\n        last_name: credentials.nom,\r\n        password: credentials.password,\r\n        password_confirm: credentials.confirmPassword,\r\n        phone: credentials.phone || \"\",\r\n        company: credentials.company || \"\",\r\n      };\r\n\r\n      // Appel API réel vers Django\r\n      const response = await AuthService.register(registrationData);\r\n\r\n      if (response.success) {\r\n        // Inscription réussie - connecter automatiquement l'utilisateur\r\n        const loginResult = await login({\r\n          email: credentials.email,\r\n          password: credentials.password,\r\n        });\r\n\r\n        return {\r\n          success: true,\r\n          message: \"Inscription réussie ! Vous êtes maintenant connecté.\",\r\n          user: response.user,\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: response.message || \"Erreur lors de l'inscription\",\r\n          errors: response.errors || {},\r\n        };\r\n      }\r\n\r\n      // Ajouter l'utilisateur aux utilisateurs de démo (en production, ceci serait sauvé en base)\r\n      demoUsers[credentials.email] = newUser;\r\n\r\n      // Créer automatiquement une session pour le nouvel utilisateur\r\n      const expiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\r\n      const token = `abm_token_${newUser.id}_${Date.now()}`;\r\n\r\n      const userWithToken = {\r\n        ...newUser,\r\n        tokenExpiry: expiry.toISOString(),\r\n      };\r\n\r\n      // Stocker les données\r\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, token);\r\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(userWithToken));\r\n\r\n      setUser(newUser);\r\n      setSessionExpiry(expiry);\r\n\r\n      toast.success(\r\n        `Bienvenue ${newUser.prenom} ${newUser.nom} ! Votre compte a été créé avec succès.`\r\n      );\r\n\r\n      return {\r\n        success: true,\r\n        message: \"Inscription réussie ! Vous êtes maintenant connecté.\",\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de l'inscription:\", error);\r\n      return {\r\n        success: false,\r\n        message: \"Erreur lors de l'inscription. Veuillez réessayer.\",\r\n        errors: { general: [\"Erreur système\"] },\r\n      };\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Fonction de déconnexion\r\n  const logout = useCallback(async () => {\r\n    try {\r\n      localStorage.removeItem(AUTH_CONFIG.STORAGE_KEY);\r\n      localStorage.removeItem(AUTH_CONFIG.USER_KEY);\r\n      localStorage.removeItem(AUTH_CONFIG.REMEMBER_KEY);\r\n\r\n      setUser(null);\r\n      setSessionExpiry(null);\r\n\r\n      toast.info(\"Vous avez été déconnecté\");\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de la déconnexion:\", error);\r\n    }\r\n  }, []);\r\n\r\n  // Rafraîchir le token\r\n  const refreshToken = useCallback(async (): Promise<boolean> => {\r\n    try {\r\n      if (!user) return false;\r\n\r\n      const newExpiry = new Date(Date.now() + AUTH_CONFIG.SESSION_DURATION);\r\n      const newToken = `abm_token_${user.id}_${Date.now()}`;\r\n\r\n      const updatedUser = {\r\n        ...user,\r\n        tokenExpiry: newExpiry.toISOString(),\r\n        derniere_connexion: new Date().toISOString(),\r\n      };\r\n\r\n      localStorage.setItem(AUTH_CONFIG.STORAGE_KEY, newToken);\r\n      localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(updatedUser));\r\n\r\n      setSessionExpiry(newExpiry);\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Erreur lors du rafraîchissement du token:\", error);\r\n      await logout();\r\n      return false;\r\n    }\r\n  }, [user, logout]);\r\n\r\n  // Vérifier si l'utilisateur a un rôle spécifique\r\n  const hasRole = useCallback(\r\n    (role: UserRole): boolean => {\r\n      return user?.role === role;\r\n    },\r\n    [user]\r\n  );\r\n\r\n  // Vérifier si l'utilisateur a au minimum un certain rôle\r\n  const hasMinimumRole = useCallback(\r\n    (minimumRole: UserRole): boolean => {\r\n      if (!user) return false;\r\n      return ROLE_HIERARCHY[user.role] >= ROLE_HIERARCHY[minimumRole];\r\n    },\r\n    [user]\r\n  );\r\n\r\n  // Vérifier l'accès à un module\r\n  const hasModuleAccess = useCallback(\r\n    (moduleName: string): boolean => {\r\n      if (!user) return false;\r\n\r\n      // Admin a accès à tout\r\n      if (user.role === \"ADMIN\") return true;\r\n\r\n      // Vérifier les permissions du module\r\n      const moduleRoles = MODULE_PERMISSIONS[moduleName];\r\n      if (!moduleRoles) return false;\r\n\r\n      return moduleRoles.includes(user.role);\r\n    },\r\n    [user]\r\n  );\r\n\r\n  // Vérifier une permission spécifique\r\n  const hasPermission = useCallback(\r\n    (permission: string): boolean => {\r\n      if (!user) return false;\r\n\r\n      // Admin a toutes les permissions\r\n      if (user.role === \"ADMIN\" || user.permissions.includes(\"*\")) return true;\r\n\r\n      return user.permissions.includes(permission);\r\n    },\r\n    [user]\r\n  );\r\n\r\n  // Valeur du contexte mémorisée\r\n  const contextValue = useMemo(\r\n    () => ({\r\n      user,\r\n      isAuthenticated: !!user,\r\n      isLoading,\r\n      sessionExpiry,\r\n      login,\r\n      register,\r\n      logout,\r\n      refreshToken,\r\n      hasRole,\r\n      hasMinimumRole,\r\n      hasModuleAccess,\r\n      hasPermission,\r\n      extendSession,\r\n    }),\r\n    [\r\n      user,\r\n      isLoading,\r\n      sessionExpiry,\r\n      login,\r\n      register,\r\n      logout,\r\n      refreshToken,\r\n      hasRole,\r\n      hasMinimumRole,\r\n      hasModuleAccess,\r\n      hasPermission,\r\n      extendSession,\r\n    ]\r\n  );\r\n\r\n  return (\r\n    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>\r\n  );\r\n};\r\n\r\n// Hook personnalisé pour utiliser le contexte d'authentification\r\nexport const useAuth = (): AuthContextType => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useAuth doit être utilisé dans un AuthProvider\");\r\n  }\r\n  return context;\r\n};\r\n\r\n// Export des types pour utilisation externe\r\nexport type { User, LoginCredentials, RegisterCredentials, UserRole };\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IACVC,aAAa,EACbC,UAAU,EACVC,QAAQ,EACRC,SAAS,EACTC,WAAW,EACXC,OAAO,QAEF,OAAO;AACd,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,QAAQ,wBAAwB;;AAEpD;;AA4BA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAyDA,MAAMC,WAAW,gBAAGV,aAAa,CAA8BW,SAAS,CAAC;;AAEzE;AACA,MAAMC,WAAW,GAAG;EAClBC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EAAE;EACtCC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,eAAe;EACzBC,YAAY,EAAE;AAChB,CAAC;;AAED;AACA,MAAMC,cAAwC,GAAG;EAC/CC,MAAM,EAAE,CAAC;EACTC,SAAS,EAAE,CAAC;EACZC,KAAK,EAAE,CAAC;EACRC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,MAAMC,kBAA8C,GAAG;EACrDC,SAAS,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EACzDC,OAAO,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC7CC,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC9CC,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC9CC,SAAS,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC/CC,SAAS,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC/CC,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC3CC,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;EAC9CC,QAAQ,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;EACjCC,KAAK,EAAE,CAAC,YAAY;AACtB,CAAC;;AAED;AACA,MAAMC,SAA+B,GAAG;EACtC,cAAc,EAAE;IACdC,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,cAAc;IACrBC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,SAAS;IACjBC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,CAAC,GAAG,CAAC;IAClBC,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACpB,kBAAkB,CAAC;IACxCqB,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C,CAAC;EACD,gBAAgB,EAAE;IAChBZ,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,gBAAgB;IACvBC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;IACxCC,OAAO,EAAE,CACP,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACX,OAAO,EACP,UAAU,CACX;IACDG,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C,CAAC;EACD,kBAAkB,EAAE;IAClBZ,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,kBAAkB;IACzBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;IAC9BC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;IACtEG,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C,CAAC;EACD,gBAAgB,EAAE;IAChBZ,EAAE,EAAE,GAAG;IACPC,KAAK,EAAE,gBAAgB;IACvBC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAE,YAAY;IACpBC,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;IAC9BC,OAAO,EAAE,CACP,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,OAAO,CACR;IACDG,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC7C;AACF,CAAC;AAMD,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjD,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAc,IAAI,CAAC;;EAErE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC/C,WAAW,CAACE,WAAW,CAAC;QAC3D,MAAM8C,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC/C,WAAW,CAACG,QAAQ,CAAC;QAE3D,IAAI0C,KAAK,IAAIG,QAAQ,EAAE;UACrB,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;;UAEvC;UACA,MAAMI,WAAW,GAAG,IAAInB,IAAI,CAACgB,UAAU,CAACG,WAAW,IAAI,CAAC,CAAC;UACzD,IAAIA,WAAW,GAAG,IAAInB,IAAI,CAAC,CAAC,EAAE;YAC5BM,OAAO,CAACU,UAAU,CAAC;YACnBN,gBAAgB,CAACS,WAAW,CAAC;UAC/B,CAAC,MAAM;YACL;YACAN,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACE,WAAW,CAAC;YAChD4C,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACG,QAAQ,CAAC;YAC7C2C,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACI,YAAY,CAAC;UACnD;QACF;MACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CACX,uDAAuD,EACvDA,KACF,CAAC;QACDR,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACE,WAAW,CAAC;QAChD4C,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACG,QAAQ,CAAC;QAC7C2C,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACI,YAAY,CAAC;MACnD,CAAC,SAAS;QACRqC,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,aAAa,GAAGhE,WAAW,CAAC,MAAM;IACtC,IAAI8C,IAAI,IAAII,aAAa,EAAE;MACzB,MAAMe,SAAS,GAAG,IAAIxB,IAAI,CAACA,IAAI,CAACyB,GAAG,CAAC,CAAC,GAAG1D,WAAW,CAACC,gBAAgB,CAAC;MACrE0C,gBAAgB,CAACc,SAAS,CAAC;MAE3B,MAAME,WAAW,GAAG;QAAE,GAAGrB,IAAI;QAAEc,WAAW,EAAEK,SAAS,CAACvB,WAAW,CAAC;MAAE,CAAC;MACrEY,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACG,QAAQ,EAAE+C,IAAI,CAACW,SAAS,CAACF,WAAW,CAAC,CAAC;IACzE;EACF,CAAC,EAAE,CAACrB,IAAI,EAAEI,aAAa,CAAC,CAAC;;EAEzB;EACA,MAAMoB,KAAK,GAAGtE,WAAW,CAAC,MAAOuE,WAA6B,IAAK;IACjE,IAAI;MACFtB,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMuB,QAAQ,GAAG,MAAMrE,WAAW,CAACmE,KAAK,CACtCC,WAAW,CAACxC,KAAK,EACjBwC,WAAW,CAACE,QACd,CAAC;MAED,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,MAAM,EAAE;QACvC;QACArB,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACE,WAAW,EAAE8D,QAAQ,CAACG,MAAM,CAACC,MAAM,CAAC;QACrEtB,YAAY,CAACc,OAAO,CAAC,eAAe,EAAEI,QAAQ,CAACG,MAAM,CAACE,OAAO,CAAC;;QAE9D;QACA,MAAMC,eAAe,GAAG,MAAM3E,WAAW,CAAC4E,UAAU,CAAC,CAAC;QAEtD,IAAID,eAAe,CAACJ,OAAO,EAAE;UAC3B,MAAM5B,IAAI,GAAG;YACXhB,EAAE,EAAEgD,eAAe,CAAChC,IAAI,CAAChB,EAAE;YAC3BC,KAAK,EAAE+C,eAAe,CAAChC,IAAI,CAACf,KAAK;YACjCC,GAAG,EAAE8C,eAAe,CAAChC,IAAI,CAACkC,SAAS;YACnC/C,MAAM,EAAE6C,eAAe,CAAChC,IAAI,CAACmC,UAAU;YACvC/C,IAAI,EAAE4C,eAAe,CAAChC,IAAI,CAACZ,IAAgB;YAC3CC,WAAW,EAAE2C,eAAe,CAAChC,IAAI,CAACX,WAAW,IAAI,EAAE;YACnDC,OAAO,EAAE0C,eAAe,CAAChC,IAAI,CAACV,OAAO,IAAI,EAAE;YAC3CG,MAAM,EAAEuC,eAAe,CAAChC,IAAI,CAACoC,SAAS,GAAG,OAAO,GAAG,SAAS;YAC5D1C,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UAC7C,CAAC;UAED,MAAMyC,MAAM,GAAG,IAAI1C,IAAI,CAACA,IAAI,CAACyB,GAAG,CAAC,CAAC,GAAG1D,WAAW,CAACC,gBAAgB,CAAC;UAElE6C,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACG,QAAQ,EAAE+C,IAAI,CAACW,SAAS,CAACvB,IAAI,CAAC,CAAC;UAEhE,IAAIyB,WAAW,CAACa,QAAQ,EAAE;YACxB9B,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACI,YAAY,EAAE,MAAM,CAAC;UACxD;UAEAmC,OAAO,CAACD,IAAI,CAAC;UACbK,gBAAgB,CAACgC,MAAM,CAAC;UAExBjF,KAAK,CAACwE,OAAO,CAAC,aAAa5B,IAAI,CAACb,MAAM,IAAIa,IAAI,CAACd,GAAG,IAAI,CAAC;UAEvD,OAAO;YACL0C,OAAO,EAAE,IAAI;YACbW,OAAO,EAAE;UACX,CAAC;QACH;MACF;MAEA,OAAO;QACLX,OAAO,EAAE,KAAK;QACdW,OAAO,EAAEb,QAAQ,CAACa,OAAO,IAAI,iCAAiC;QAC9DC,MAAM,EAAEd,QAAQ,CAACc,MAAM,IAAI;UAAEvD,KAAK,EAAE,CAAC,wBAAwB;QAAE;MACjE,CAAC;IACH,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QACLY,OAAO,EAAE,KAAK;QACdW,OAAO,EAAE,0CAA0C;QACnDC,MAAM,EAAE;UAAEC,OAAO,EAAE,CAAC,gBAAgB;QAAE;MACxC,CAAC;IACH,CAAC,SAAS;MACRtC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMuC,QAAQ,GAAGxF,WAAW,CAAC,MAAOuE,WAAgC,IAAK;IACvE,IAAI;MACFtB,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,IAAIsB,WAAW,CAACE,QAAQ,KAAKF,WAAW,CAACkB,eAAe,EAAE;QACxD,OAAO;UACLf,OAAO,EAAE,KAAK;UACdW,OAAO,EAAE,wCAAwC;UACjDC,MAAM,EAAE;YACNG,eAAe,EAAE,CAAC,wCAAwC;UAC5D;QACF,CAAC;MACH;MAEA,IAAIlB,WAAW,CAACE,QAAQ,CAACiB,MAAM,GAAG,CAAC,EAAE;QACnC,OAAO;UACLhB,OAAO,EAAE,KAAK;UACdW,OAAO,EAAE,qDAAqD;UAC9DC,MAAM,EAAE;YACNb,QAAQ,EAAE,CAAC,qDAAqD;UAClE;QACF,CAAC;MACH;;MAEA;MACA,MAAMkB,gBAAgB,GAAG;QACvBC,QAAQ,EAAErB,WAAW,CAACxC,KAAK;QAAE;QAC7BA,KAAK,EAAEwC,WAAW,CAACxC,KAAK;QACxBkD,UAAU,EAAEV,WAAW,CAACtC,MAAM;QAC9B+C,SAAS,EAAET,WAAW,CAACvC,GAAG;QAC1ByC,QAAQ,EAAEF,WAAW,CAACE,QAAQ;QAC9BoB,gBAAgB,EAAEtB,WAAW,CAACkB,eAAe;QAC7CK,KAAK,EAAEvB,WAAW,CAACuB,KAAK,IAAI,EAAE;QAC9BC,OAAO,EAAExB,WAAW,CAACwB,OAAO,IAAI;MAClC,CAAC;;MAED;MACA,MAAMvB,QAAQ,GAAG,MAAMrE,WAAW,CAACqF,QAAQ,CAACG,gBAAgB,CAAC;MAE7D,IAAInB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMsB,WAAW,GAAG,MAAM1B,KAAK,CAAC;UAC9BvC,KAAK,EAAEwC,WAAW,CAACxC,KAAK;UACxB0C,QAAQ,EAAEF,WAAW,CAACE;QACxB,CAAC,CAAC;QAEF,OAAO;UACLC,OAAO,EAAE,IAAI;UACbW,OAAO,EAAE,sDAAsD;UAC/DvC,IAAI,EAAE0B,QAAQ,CAAC1B;QACjB,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACL4B,OAAO,EAAE,KAAK;UACdW,OAAO,EAAEb,QAAQ,CAACa,OAAO,IAAI,8BAA8B;UAC3DC,MAAM,EAAEd,QAAQ,CAACc,MAAM,IAAI,CAAC;QAC9B,CAAC;MACH;;MAEA;MACAzD,SAAS,CAAC0C,WAAW,CAACxC,KAAK,CAAC,GAAGkE,OAAO;;MAEtC;MACA,MAAMd,MAAM,GAAG,IAAI1C,IAAI,CAACA,IAAI,CAACyB,GAAG,CAAC,CAAC,GAAG1D,WAAW,CAACC,gBAAgB,CAAC;MAClE,MAAM4C,KAAK,GAAG,aAAa4C,OAAO,CAACnE,EAAE,IAAIW,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAE;MAErD,MAAMgC,aAAa,GAAG;QACpB,GAAGD,OAAO;QACVrC,WAAW,EAAEuB,MAAM,CAACzC,WAAW,CAAC;MAClC,CAAC;;MAED;MACAY,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACE,WAAW,EAAE2C,KAAK,CAAC;MACpDC,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACG,QAAQ,EAAE+C,IAAI,CAACW,SAAS,CAAC6B,aAAa,CAAC,CAAC;MAEzEnD,OAAO,CAACkD,OAAO,CAAC;MAChB9C,gBAAgB,CAACgC,MAAM,CAAC;MAExBjF,KAAK,CAACwE,OAAO,CACX,aAAauB,OAAO,CAAChE,MAAM,IAAIgE,OAAO,CAACjE,GAAG,yCAC5C,CAAC;MAED,OAAO;QACL0C,OAAO,EAAE,IAAI;QACbW,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QACLY,OAAO,EAAE,KAAK;QACdW,OAAO,EAAE,mDAAmD;QAC5DC,MAAM,EAAE;UAAEC,OAAO,EAAE,CAAC,gBAAgB;QAAE;MACxC,CAAC;IACH,CAAC,SAAS;MACRtC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkD,MAAM,GAAGnG,WAAW,CAAC,YAAY;IACrC,IAAI;MACFsD,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACE,WAAW,CAAC;MAChD4C,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACG,QAAQ,CAAC;MAC7C2C,YAAY,CAACO,UAAU,CAACrD,WAAW,CAACI,YAAY,CAAC;MAEjDmC,OAAO,CAAC,IAAI,CAAC;MACbI,gBAAgB,CAAC,IAAI,CAAC;MAEtBjD,KAAK,CAACkG,IAAI,CAAC,0BAA0B,CAAC;IACxC,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMuC,YAAY,GAAGrG,WAAW,CAAC,YAA8B;IAC7D,IAAI;MACF,IAAI,CAAC8C,IAAI,EAAE,OAAO,KAAK;MAEvB,MAAMmB,SAAS,GAAG,IAAIxB,IAAI,CAACA,IAAI,CAACyB,GAAG,CAAC,CAAC,GAAG1D,WAAW,CAACC,gBAAgB,CAAC;MACrE,MAAM6F,QAAQ,GAAG,aAAaxD,IAAI,CAAChB,EAAE,IAAIW,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAE;MAErD,MAAMC,WAAW,GAAG;QAClB,GAAGrB,IAAI;QACPc,WAAW,EAAEK,SAAS,CAACvB,WAAW,CAAC,CAAC;QACpCF,kBAAkB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAC7C,CAAC;MAEDY,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACE,WAAW,EAAE4F,QAAQ,CAAC;MACvDhD,YAAY,CAACc,OAAO,CAAC5D,WAAW,CAACG,QAAQ,EAAE+C,IAAI,CAACW,SAAS,CAACF,WAAW,CAAC,CAAC;MAEvEhB,gBAAgB,CAACc,SAAS,CAAC;MAE3B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMqC,MAAM,CAAC,CAAC;MACd,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACrD,IAAI,EAAEqD,MAAM,CAAC,CAAC;;EAElB;EACA,MAAMI,OAAO,GAAGvG,WAAW,CACxBkC,IAAc,IAAc;IAC3B,OAAO,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEZ,IAAI,MAAKA,IAAI;EAC5B,CAAC,EACD,CAACY,IAAI,CACP,CAAC;;EAED;EACA,MAAM0D,cAAc,GAAGxG,WAAW,CAC/ByG,WAAqB,IAAc;IAClC,IAAI,CAAC3D,IAAI,EAAE,OAAO,KAAK;IACvB,OAAOjC,cAAc,CAACiC,IAAI,CAACZ,IAAI,CAAC,IAAIrB,cAAc,CAAC4F,WAAW,CAAC;EACjE,CAAC,EACD,CAAC3D,IAAI,CACP,CAAC;;EAED;EACA,MAAM4D,eAAe,GAAG1G,WAAW,CAChC2G,UAAkB,IAAc;IAC/B,IAAI,CAAC7D,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAIA,IAAI,CAACZ,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;;IAEtC;IACA,MAAM0E,WAAW,GAAG1F,kBAAkB,CAACyF,UAAU,CAAC;IAClD,IAAI,CAACC,WAAW,EAAE,OAAO,KAAK;IAE9B,OAAOA,WAAW,CAACC,QAAQ,CAAC/D,IAAI,CAACZ,IAAI,CAAC;EACxC,CAAC,EACD,CAACY,IAAI,CACP,CAAC;;EAED;EACA,MAAMgE,aAAa,GAAG9G,WAAW,CAC9B+G,UAAkB,IAAc;IAC/B,IAAI,CAACjE,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAIA,IAAI,CAACZ,IAAI,KAAK,OAAO,IAAIY,IAAI,CAACX,WAAW,CAAC0E,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI;IAExE,OAAO/D,IAAI,CAACX,WAAW,CAAC0E,QAAQ,CAACE,UAAU,CAAC;EAC9C,CAAC,EACD,CAACjE,IAAI,CACP,CAAC;;EAED;EACA,MAAMkE,YAAY,GAAG/G,OAAO,CAC1B,OAAO;IACL6C,IAAI;IACJmE,eAAe,EAAE,CAAC,CAACnE,IAAI;IACvBE,SAAS;IACTE,aAAa;IACboB,KAAK;IACLkB,QAAQ;IACRW,MAAM;IACNE,YAAY;IACZE,OAAO;IACPC,cAAc;IACdE,eAAe;IACfI,aAAa;IACb9C;EACF,CAAC,CAAC,EACF,CACElB,IAAI,EACJE,SAAS,EACTE,aAAa,EACboB,KAAK,EACLkB,QAAQ,EACRW,MAAM,EACNE,YAAY,EACZE,OAAO,EACPC,cAAc,EACdE,eAAe,EACfI,aAAa,EACb9C,aAAa,CAEjB,CAAC;EAED,oBACE3D,OAAA,CAACC,WAAW,CAAC4G,QAAQ;IAACC,KAAK,EAAEH,YAAa;IAAApE,QAAA,EAAEA;EAAQ;IAAAwE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAEhF,CAAC;;AAED;AAAA1E,EAAA,CA/VaF,YAAyC;AAAA6E,EAAA,GAAzC7E,YAAyC;AAgWtD,OAAO,MAAM8E,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAG9H,UAAU,CAACS,WAAW,CAAC;EACvC,IAAIqH,OAAO,KAAKpH,SAAS,EAAE;IACzB,MAAM,IAAIqH,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,GAAA,CARaD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}