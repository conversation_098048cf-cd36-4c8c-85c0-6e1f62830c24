/**
 * Index des composants Produits
 * Exporte tous les composants de gestion des produits
 */

// Composants principaux
export { default as ProductsList } from "./ProductsList";
export { default as ProductForm } from "./ProductForm";
export { default as ProductDetail } from "./ProductDetail";
export { default as ProductStats } from "./ProductStats";

// Composant principal (routing)
export { default as Products } from "./Products";

// Types TypeScript
export interface Product {
  id: string;
  nom: string;
  description?: string;
  prix_unitaire: number;
  prix_achat?: number;
  taux_tva: number;
  unite: string;
  categorie: string;
  reference?: string;
  code_barre?: string;
  stock_actuel: number;
  stock_minimum: number;
  stock_maximum: number;
  statut: "ACTIF" | "INACTIF" | "RUPTURE";
  date_creation: string;
  derniere_vente?: string;
  nombre_ventes: number;
  chiffre_affaires_total: number;
  marge_beneficiaire: number;
  fournisseur?: string;
  delai_livraison?: number;
  poids?: number;
  dimensions?: string;
  image_url?: string;
}

export interface ProductFilters {
  search: string;
  categorie: string;
  statut: string;
  stock_faible: boolean;
  prix_min: string;
  prix_max: string;
  fournisseur: string;
}

export interface ProductStatsData {
  total_produits: number;
  produits_actifs: number;
  produits_inactifs: number;
  produits_rupture: number;
  stock_total_value: number;
  ca_total_produits: number;
  marge_moyenne: number;
  top_ventes: any[];
  categories: any[];
  alertes_stock: any[];
  evolution_ventes: any[];
}

// Constantes
export const PRODUCT_STATUS = {
  ACTIVE: "ACTIF",
  INACTIVE: "INACTIF",
  OUT_OF_STOCK: "RUPTURE",
} as const;

export const PRODUCT_UNITS = [
  "pièce",
  "kg",
  "g",
  "litre",
  "ml",
  "mètre",
  "cm",
  "m²",
  "heure",
  "jour",
  "forfait",
] as const;

export const VAT_RATES = [
  { label: "0%", value: 0 },
  { label: "5.5%", value: 5.5 },
  { label: "10%", value: 10 },
  { label: "20%", value: 20 },
] as const;

export const PRODUCT_CATEGORIES = [
  "Services",
  "Produits physiques",
  "Formations",
  "Consultations",
  "Logiciels",
  "Abonnements",
  "Autres",
] as const;

// Fonctions utilitaires
export const formatProductName = (product: Product) => {
  return product.nom || "Produit sans nom";
};

export const formatProductPrice = (price: number, currency = "EUR") => {
  return new Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency,
  }).format(price);
};

export const getProductStatusIcon = (status: string) => {
  switch (status) {
    case "ACTIF":
      return "✅";
    case "INACTIF":
      return "⏸️";
    case "RUPTURE":
      return "❌";
    default:
      return "❓";
  }
};

export const getStockStatus = (current: number, minimum: number) => {
  if (current === 0) {
    return { status: "rupture", label: "Rupture", color: "danger", icon: "❌" };
  }
  if (current <= minimum) {
    return {
      status: "faible",
      label: "Stock faible",
      color: "warning",
      icon: "⚠️",
    };
  }
  return { status: "ok", label: "Stock OK", color: "success", icon: "✅" };
};

export const calculateMargin = (sellPrice: number, buyPrice: number) => {
  if (buyPrice === 0) return 0;
  return ((sellPrice - buyPrice) / sellPrice) * 100;
};

export const validateProductData = (data: Partial<Product>) => {
  const errors: Record<string, string> = {};

  if (!data.nom?.trim()) {
    errors.nom = "Nom du produit requis";
  }

  if (!data.prix_unitaire || data.prix_unitaire <= 0) {
    errors.prix_unitaire = "Prix de vente requis et positif";
  }

  if (!data.unite?.trim()) {
    errors.unite = "Unité requise";
  }

  if (!data.categorie?.trim()) {
    errors.categorie = "Catégorie requise";
  }

  if (data.taux_tva === undefined || data.taux_tva < 0) {
    errors.taux_tva = "Taux de TVA requis";
  }

  if (data.stock_minimum !== undefined && data.stock_minimum < 0) {
    errors.stock_minimum = "Stock minimum ne peut pas être négatif";
  }

  if (data.stock_maximum !== undefined && data.stock_maximum < 0) {
    errors.stock_maximum = "Stock maximum ne peut pas être négatif";
  }

  if (
    data.stock_minimum !== undefined &&
    data.stock_maximum !== undefined &&
    data.stock_minimum > data.stock_maximum
  ) {
    errors.stock_maximum = "Stock maximum doit être supérieur au minimum";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export const generateProductReference = (category: string, name: string) => {
  const categoryCode = category.substring(0, 3).toUpperCase();
  const nameCode = name.substring(0, 3).toUpperCase();
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `${categoryCode}-${nameCode}-${random}`;
};

// Routes des produits
export const PRODUCT_ROUTES = {
  LIST: "/products",
  NEW: "/products/new",
  DETAIL: "/products/:id",
  EDIT: "/products/:id/edit",
  CATEGORIES: "/products/categories",
  STATS: "/products/stats",
} as const;

// Configuration par défaut
export const PRODUCT_CONFIG = {
  DEFAULT_VAT_RATE: 20,
  DEFAULT_UNIT: "pièce",
  DEFAULT_CATEGORY: "Autres",
  DEFAULT_STATUS: "ACTIF",
  ITEMS_PER_PAGE: 20,
  DEFAULT_STOCK_MINIMUM: 0,
  DEFAULT_STOCK_MAXIMUM: 1000,
} as const;
