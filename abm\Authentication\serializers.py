"""
Serializers pour l'authentification
"""

from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import CustomUser, UserRole, LoginAttempt, PasswordResetCode


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer pour l'inscription d'un nouvel utilisateur"""
    
    password = serializers.CharField(
        write_only=True,
        validators=[validate_password]
    )
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'username', 'email', 'first_name', 'last_name',
            'phone', 'company', 'password', 'password_confirm'
        ]
    
    def validate(self, attrs):
        """Validation des données"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Les mots de passe ne correspondent pas.")
        return attrs
    
    def create(self, validated_data):
        """Création d'un nouvel utilisateur"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        user = CustomUser.objects.create_user(
            password=password,
            **validated_data
        )
        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer pour la connexion"""
    
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validation des identifiants"""
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            
            if not user:
                raise serializers.ValidationError("Identifiants invalides.")
            
            if not user.is_active:
                raise serializers.ValidationError("Compte désactivé.")
            
            attrs['user'] = user
            return attrs
        
        raise serializers.ValidationError("Nom d'utilisateur et mot de passe requis.")


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer pour le profil utilisateur"""
    
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    accessible_modules = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone', 'company', 'role', 'role_display',
            'is_active', 'date_joined', 'last_login',
            'accessible_modules'
        ]
        read_only_fields = ['id', 'username', 'role', 'date_joined', 'last_login']
    
    def get_accessible_modules(self, obj):
        """Retourne les modules accessibles pour cet utilisateur"""
        return obj.get_accessible_modules()


class UserListSerializer(serializers.ModelSerializer):
    """Serializer pour la liste des utilisateurs (admin)"""
    
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'role', 'role_display', 'is_active', 'date_joined', 'last_login'
        ]


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer pour la mise à jour d'un utilisateur (admin)"""
    
    class Meta:
        model = CustomUser
        fields = [
            'email', 'first_name', 'last_name', 'phone', 'company',
            'role', 'is_active'
        ]
    
    def validate_role(self, value):
        """Validation du rôle"""
        request = self.context.get('request')
        if request and request.user:
            # Seuls les SuperAdmin peuvent créer d'autres SuperAdmin
            if value == UserRole.SUPERADMIN and not request.user.is_superadmin:
                raise serializers.ValidationError(
                    "Seuls les Super Administrateurs peuvent assigner ce rôle."
                )
            
            # Les Admin ne peuvent pas modifier des SuperAdmin
            if (hasattr(self, 'instance') and 
                self.instance.is_superadmin and 
                not request.user.is_superadmin):
                raise serializers.ValidationError(
                    "Vous n'avez pas les permissions pour modifier cet utilisateur."
                )
        
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer pour changer le mot de passe"""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(
        write_only=True,
        validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        """Validation de l'ancien mot de passe"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Ancien mot de passe incorrect.")
        return value
    
    def validate(self, attrs):
        """Validation des nouveaux mots de passe"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("Les nouveaux mots de passe ne correspondent pas.")
        return attrs
    
    def save(self):
        """Sauvegarde du nouveau mot de passe"""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class LoginAttemptSerializer(serializers.ModelSerializer):
    """Serializer pour les tentatives de connexion"""

    class Meta:
        model = LoginAttempt
        fields = ['username', 'ip_address', 'success', 'timestamp', 'user_agent']


class ForgotPasswordSerializer(serializers.Serializer):
    """Serializer pour la demande de récupération de mot de passe"""

    email = serializers.EmailField()

    def validate_email(self, value):
        """Validation de l'email"""
        try:
            user = CustomUser.objects.get(email=value, is_active=True)
        except CustomUser.DoesNotExist:
            raise serializers.ValidationError("Aucun compte actif trouvé avec cette adresse email.")
        return value


class VerifyResetCodeSerializer(serializers.Serializer):
    """Serializer pour la vérification du code de récupération"""

    email = serializers.EmailField()
    code = serializers.CharField(max_length=6, min_length=6)

    def validate(self, attrs):
        """Validation du code de récupération"""
        email = attrs.get('email')
        code = attrs.get('code')

        try:
            user = CustomUser.objects.get(email=email, is_active=True)
        except CustomUser.DoesNotExist:
            raise serializers.ValidationError("Email invalide.")

        try:
            reset_code = PasswordResetCode.objects.get(
                user=user,
                email=email,
                code=code,
                is_used=False
            )
        except PasswordResetCode.DoesNotExist:
            raise serializers.ValidationError("Code de vérification invalide.")

        if not reset_code.is_valid():
            raise serializers.ValidationError("Code de vérification expiré.")

        attrs['user'] = user
        attrs['reset_code'] = reset_code
        return attrs


class ResetPasswordSerializer(serializers.Serializer):
    """Serializer pour la réinitialisation du mot de passe"""

    email = serializers.EmailField()
    code = serializers.CharField(max_length=6, min_length=6)
    new_password = serializers.CharField(
        write_only=True,
        validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        """Validation des données de réinitialisation"""
        email = attrs.get('email')
        code = attrs.get('code')
        new_password = attrs.get('new_password')
        new_password_confirm = attrs.get('new_password_confirm')

        # Vérifier que les mots de passe correspondent
        if new_password != new_password_confirm:
            raise serializers.ValidationError("Les mots de passe ne correspondent pas.")

        try:
            user = CustomUser.objects.get(email=email, is_active=True)
        except CustomUser.DoesNotExist:
            raise serializers.ValidationError("Email invalide.")

        try:
            reset_code = PasswordResetCode.objects.get(
                user=user,
                email=email,
                code=code,
                is_used=False
            )
        except PasswordResetCode.DoesNotExist:
            raise serializers.ValidationError("Code de vérification invalide.")

        if not reset_code.is_valid():
            raise serializers.ValidationError("Code de vérification expiré.")

        attrs['user'] = user
        attrs['reset_code'] = reset_code
        return attrs

    def save(self):
        """Sauvegarde du nouveau mot de passe"""
        user = self.validated_data['user']
        reset_code = self.validated_data['reset_code']
        new_password = self.validated_data['new_password']

        # Changer le mot de passe
        user.set_password(new_password)
        user.save()

        # Marquer le code comme utilisé
        reset_code.mark_as_used()

        # Invalider tous les autres codes de récupération pour cet utilisateur
        PasswordResetCode.objects.filter(
            user=user,
            is_used=False
        ).update(is_used=True)

        return user
