/**
 * Index des composants Factures - Version Professionnelle Consolidée
 * Système de facturation moderne Ben Chaabene
 */

export { default as FacturationProfessionnelle } from "./FacturationProfessionnelle";

// Types TypeScript
export interface Invoice {
  id: string;
  numero: string;
  client_id: string;
  client_nom: string;
  client_email: string;
  client_telephone?: string;
  client_adresse?: string;
  date_emission: string;
  date_echeance?: string;
  date_paiement?: string;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
  remise_globale: number;
  statut: "BROUILLON" | "ENVOYEE" | "PAYEE" | "ANNULEE";
  type_document: "FACTURE" | "DEVIS" | "BON_LIVRAISON";
  notes?: string;
  conditions_paiement?: string;
  est_en_retard: boolean;
  lignes: InvoiceLine[];
  bons_livraison?: Bon<PERSON><PERSON><PERSON><PERSON>[];
  created_at: string;
  updated_at: string;
}

export interface BonLivraison {
  id: string;
  numero: string;
  statut: "BROUILLON" | "ENVOYEE" | "PAYEE" | "ANNULEE";
  date_emission: string;
  created_at: string;
}

export interface InvoiceLine {
  id?: string;
  produit_id: string;
  produit_nom: string;
  description: string;
  quantite: number;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
}

export interface InvoiceFilters {
  search: string;
  statut: string;
  date_debut: string;
  date_fin: string;
  client: string;
  montant_min: string;
  montant_max: string;
  en_retard: boolean;
}

export interface InvoiceStats {
  total_factures: number;
  total_montant: number;
  factures_payees: number;
  montant_paye: number;
  factures_en_attente: number;
  montant_en_attente: number;
  factures_en_retard: number;
  montant_en_retard: number;
  taux_paiement: number;
  montant_moyen: number;
}

// Constantes
export const INVOICE_STATUS = {
  DRAFT: "BROUILLON",
  SENT: "ENVOYEE",
  PAID: "PAYEE",
  CANCELLED: "ANNULEE",
} as const;

export const INVOICE_STATUS_LABELS = {
  BROUILLON: "Brouillon",
  ENVOYEE: "Envoyée",
  PAYEE: "Payée",
  ANNULEE: "Annulée",
} as const;

export const INVOICE_STATUS_COLORS = {
  BROUILLON: "status-draft",
  ENVOYEE: "status-sent",
  PAYEE: "status-paid",
  ANNULEE: "status-cancelled",
} as const;

export const PDF_TEMPLATES = {
  MODERN: "modern",
  CLASSIC: "classic",
  MINIMAL: "minimal",
} as const;

export const PAYMENT_TERMS = [
  "Paiement à réception",
  "Paiement à 15 jours",
  "Paiement à 30 jours",
  "Paiement à 60 jours",
  "Paiement à 90 jours",
] as const;

export const DEFAULT_VAT_RATES = [
  { label: "0%", value: 0 },
  { label: "5.5%", value: 5.5 },
  { label: "10%", value: 10 },
  { label: "20%", value: 20 },
] as const;

// Fonctions utilitaires
export const formatInvoiceNumber = (numero: string) => {
  return numero || "N/A";
};

export const formatInvoiceAmount = (amount: number, currency = "EUR") => {
  return new Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency,
  }).format(amount);
};

export const formatInvoiceDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("fr-FR", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export const getInvoiceStatusLabel = (status: string) => {
  return (
    INVOICE_STATUS_LABELS[status as keyof typeof INVOICE_STATUS_LABELS] ||
    status
  );
};

export const getInvoiceStatusColor = (status: string) => {
  return (
    INVOICE_STATUS_COLORS[status as keyof typeof INVOICE_STATUS_COLORS] ||
    "status-default"
  );
};

export const isInvoiceOverdue = (dateEcheance: string, statut: string) => {
  if (!dateEcheance || statut === "PAYEE" || statut === "ANNULEE") {
    return false;
  }
  return new Date(dateEcheance) < new Date();
};

export const calculateInvoiceTotals = (
  lignes: InvoiceLine[],
  remiseGlobale = 0
) => {
  const totalHT = lignes.reduce((sum, ligne) => sum + ligne.montant_ht, 0);
  const totalTVA = lignes.reduce((sum, ligne) => sum + ligne.montant_tva, 0);
  const sousTotal = totalHT + totalTVA;
  const remise = sousTotal * (remiseGlobale / 100);
  const totalTTC = sousTotal - remise;

  return {
    totalHT,
    totalTVA,
    remise,
    totalTTC,
    sousTotal,
  };
};

export const generateInvoiceNumber = (prefix = "FAC") => {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, "0");
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `${prefix}-${year}${month}-${random}`;
};

export const validateInvoiceData = (data: Partial<Invoice>) => {
  const errors: Record<string, string> = {};

  if (!data.client_id) {
    errors.client_id = "Client requis";
  }

  if (!data.numero) {
    errors.numero = "Numéro de facture requis";
  }

  if (!data.date_emission) {
    errors.date_emission = "Date d'émission requise";
  }

  if (!data.lignes || data.lignes.length === 0) {
    errors.lignes = "Au moins une ligne est requise";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// Routes des factures
export const INVOICE_ROUTES = {
  LIST: "/invoices",
  NEW: "/invoices/new",
  DETAIL: "/invoices/:id",
  EDIT: "/invoices/:id/edit",
  PDF: "/invoices/:id/pdf",
  DRAFTS: "/invoices/drafts",
} as const;

// Configuration par défaut
export const INVOICE_CONFIG = {
  DEFAULT_VAT_RATE: 20,
  DEFAULT_PAYMENT_TERMS: "Paiement à 30 jours",
  ITEMS_PER_PAGE: 20,
  MAX_LINES_PER_INVOICE: 50,
  CURRENCY: "EUR",
  DATE_FORMAT: "fr-FR",
} as const;
