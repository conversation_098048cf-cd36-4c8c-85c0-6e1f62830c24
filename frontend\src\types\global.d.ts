// Déclarations globales pour ignorer temporairement les erreurs TypeScript

declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.scss' {
  const content: { [className: string]: string };
  export default content;
}

// Déclarations pour les services API
declare global {
  interface Window {
    // Ajout de propriétés globales si nécessaire
  }
}

// Types temporaires pour éviter les erreurs
export interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: any;
  token?: string;
  tokens?: {
    access?: string;
    refresh?: string;
  };
  user?: any;
  results?: T[];
  count?: number;
}

export {};
