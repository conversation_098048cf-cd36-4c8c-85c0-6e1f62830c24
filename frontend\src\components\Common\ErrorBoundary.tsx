import React, { Component, ErrorInfo, ReactNode } from 'react';
import './ErrorBoundary.css';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="error-boundary">
          <div className="error-boundary-content">
            <div className="error-icon">
              <div className="error-icon-circle">
                <span>⚠️</span>
              </div>
            </div>
            
            <div className="error-details">
              <h1 className="error-title">Oups ! Une erreur s'est produite</h1>
              <p className="error-message">
                Nous sommes désolés, mais quelque chose s'est mal passé. 
                Notre équipe a été notifiée de ce problème.
              </p>
              
              <div className="error-actions">
                <button 
                  className="error-btn primary"
                  onClick={this.handleReload}
                >
                  🔄 Recharger la page
                </button>
                <button 
                  className="error-btn secondary"
                  onClick={this.handleGoHome}
                >
                  🏠 Retour à l'accueil
                </button>
              </div>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="error-technical">
                  <summary>Détails techniques (développement)</summary>
                  <div className="error-stack">
                    <h4>Erreur:</h4>
                    <pre>{this.state.error.toString()}</pre>
                    
                    {this.state.errorInfo && (
                      <>
                        <h4>Stack trace:</h4>
                        <pre>{this.state.errorInfo.componentStack}</pre>
                      </>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Composant d'erreur pour les pages spécifiques
interface PageErrorProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showHomeButton?: boolean;
}

export const PageError: React.FC<PageErrorProps> = ({
  title = "Erreur de chargement",
  message = "Impossible de charger cette page. Veuillez réessayer.",
  onRetry,
  showHomeButton = true,
}) => (
  <div className="page-error">
    <div className="page-error-content">
      <div className="page-error-icon">
        <span>😞</span>
      </div>
      <h2 className="page-error-title">{title}</h2>
      <p className="page-error-message">{message}</p>
      
      <div className="page-error-actions">
        {onRetry && (
          <button className="error-btn primary" onClick={onRetry}>
            🔄 Réessayer
          </button>
        )}
        {showHomeButton && (
          <button 
            className="error-btn secondary"
            onClick={() => window.location.href = '/dashboard'}
          >
            🏠 Tableau de bord
          </button>
        )}
      </div>
    </div>
  </div>
);

// Composant d'erreur pour les sections
interface SectionErrorProps {
  message?: string;
  onRetry?: () => void;
  compact?: boolean;
}

export const SectionError: React.FC<SectionErrorProps> = ({
  message = "Erreur de chargement",
  onRetry,
  compact = false,
}) => (
  <div className={`section-error ${compact ? 'compact' : ''}`}>
    <div className="section-error-content">
      <span className="section-error-icon">⚠️</span>
      <span className="section-error-message">{message}</span>
      {onRetry && (
        <button className="section-error-retry" onClick={onRetry}>
          🔄
        </button>
      )}
    </div>
  </div>
);

// Hook pour gérer les erreurs
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const handleError = React.useCallback((error: Error) => {
    console.error('Error handled:', error);
    setError(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  const withErrorHandling = React.useCallback(async (asyncFn: () => Promise<any>) => {
    try {
      clearError();
      const result = await asyncFn();
      return result;
    } catch (error) {
      handleError(error as Error);
      throw error;
    }
  }, [handleError, clearError]);

  return {
    error,
    handleError,
    clearError,
    withErrorHandling,
  };
};

// Composant d'erreur réseau
export const NetworkError: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <div className="network-error">
    <div className="network-error-content">
      <div className="network-error-icon">
        <span>📡</span>
      </div>
      <h3>Problème de connexion</h3>
      <p>Vérifiez votre connexion internet et réessayez.</p>
      {onRetry && (
        <button className="error-btn primary" onClick={onRetry}>
          🔄 Réessayer
        </button>
      )}
    </div>
  </div>
);

// Composant d'erreur 404
export const NotFound: React.FC = () => (
  <div className="not-found">
    <div className="not-found-content">
      <div className="not-found-icon">
        <span>🔍</span>
      </div>
      <h1>404</h1>
      <h2>Page non trouvée</h2>
      <p>La page que vous recherchez n'existe pas ou a été déplacée.</p>
      <div className="not-found-actions">
        <button 
          className="error-btn primary"
          onClick={() => window.history.back()}
        >
          ← Retour
        </button>
        <button 
          className="error-btn secondary"
          onClick={() => window.location.href = '/dashboard'}
        >
          🏠 Accueil
        </button>
      </div>
    </div>
  </div>
);

// Composant d'erreur d'autorisation
export const Unauthorized: React.FC = () => (
  <div className="unauthorized">
    <div className="unauthorized-content">
      <div className="unauthorized-icon">
        <span>🔒</span>
      </div>
      <h2>Accès non autorisé</h2>
      <p>Vous n'avez pas les permissions nécessaires pour accéder à cette page.</p>
      <div className="unauthorized-actions">
        <button 
          className="error-btn primary"
          onClick={() => window.location.href = '/dashboard'}
        >
          🏠 Tableau de bord
        </button>
        <button 
          className="error-btn secondary"
          onClick={() => window.location.href = '/login'}
        >
          🔑 Se connecter
        </button>
      </div>
    </div>
  </div>
);

export default ErrorBoundary;
