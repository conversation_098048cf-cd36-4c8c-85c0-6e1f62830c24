#!/usr/bin/env node

/**
 * Script de démarrage final pour l'application ABM
 * Société Ben Chaabène de Commerce
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🏢 SOCIÉTÉ BEN CHAABÈNE DE COMMERCE');
console.log('📊 ABM - Automated Business Manager');
console.log('🚀 Démarrage de l\'application...\n');

// Vérification préalable
console.log('🔍 Vérification de l\'environnement...');

// Vérifier Node.js
const nodeVersion = process.version;
console.log(`✅ Node.js: ${nodeVersion}`);

// Vérifier les dépendances
const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  console.log('✅ package.json trouvé');
} else {
  console.log('❌ package.json manquant');
  process.exit(1);
}

// Vérifier node_modules
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (fs.existsSync(nodeModulesPath)) {
  console.log('✅ Dépendances installées');
} else {
  console.log('⚠️  Installation des dépendances...');
  const npmInstall = spawn('npm', ['install'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  });
  
  npmInstall.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Dépendances installées avec succès');
      startApplication();
    } else {
      console.log('❌ Erreur lors de l\'installation des dépendances');
      process.exit(1);
    }
  });
  
  return;
}

// Vérifier les fichiers critiques
const criticalFiles = [
  'src/index.tsx',
  'src/App.tsx',
  'public/index.html',
  'public/logo_ben_chaabene.png'
];

let allFilesExist = true;
criticalFiles.forEach(file => {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MANQUANT`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Des fichiers critiques sont manquants !');
  console.log('💡 Exécutez: node validate-app.js pour plus de détails');
  process.exit(1);
}

console.log('\n🎯 Tous les contrôles sont passés !');
startApplication();

function startApplication() {
  console.log('\n' + '='.repeat(60));
  console.log('🚀 DÉMARRAGE DE L\'APPLICATION ABM');
  console.log('='.repeat(60));
  console.log('🏢 Société Ben Chaabène de Commerce');
  console.log('📊 Système de Gestion d\'Entreprise');
  console.log('🌐 Interface Web Moderne');
  console.log('='.repeat(60));
  
  // Configuration de l'environnement
  process.env.GENERATE_SOURCEMAP = 'false';
  process.env.BROWSER = 'none';
  process.env.PORT = '3000';
  process.env.REACT_APP_API_URL = 'http://localhost:8000';
  
  console.log('\n📋 Configuration:');
  console.log('   • Port Frontend: 3000');
  console.log('   • API Backend: http://localhost:8000');
  console.log('   • Mode: Développement');
  console.log('   • Sourcemaps: Désactivées (performance)');
  
  console.log('\n⏳ Compilation en cours...');
  console.log('📱 L\'application s\'ouvrira automatiquement dans votre navigateur');
  console.log('🔄 Rechargement automatique activé');
  
  // Démarrage du serveur React
  const reactServer = spawn('npm', ['start'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      FORCE_COLOR: '1'
    }
  });

  reactServer.on('error', (error) => {
    console.error('\n❌ Erreur lors du démarrage:', error.message);
    console.log('\n💡 Solutions possibles:');
    console.log('   • Vérifiez que Node.js est installé');
    console.log('   • Exécutez: npm install');
    console.log('   • Vérifiez que le port 3000 est libre');
    process.exit(1);
  });

  reactServer.on('close', (code) => {
    if (code !== 0) {
      console.log(`\n⚠️  Serveur fermé avec le code: ${code}`);
    }
    console.log('\n👋 Application ABM fermée');
    console.log('🏢 Merci d\'avoir utilisé le système Ben Chaabène !');
  });

  // Gestion des signaux
  process.on('SIGINT', () => {
    console.log('\n\n🛑 Arrêt de l\'application ABM...');
    console.log('🏢 Société Ben Chaabène de Commerce');
    console.log('👋 À bientôt !');
    reactServer.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Arrêt du serveur...');
    reactServer.kill('SIGTERM');
  });

  // Message de bienvenue après démarrage
  setTimeout(() => {
    console.log('\n' + '='.repeat(60));
    console.log('🎉 APPLICATION ABM DÉMARRÉE AVEC SUCCÈS !');
    console.log('='.repeat(60));
    console.log('🌐 Frontend: http://localhost:3000');
    console.log('🔧 Backend: http://localhost:8000 (Django)');
    console.log('👤 Admin: http://localhost:8000/admin');
    console.log('='.repeat(60));
    console.log('📋 Fonctionnalités disponibles:');
    console.log('   • 📄 Facturation professionnelle');
    console.log('   • 👥 Gestion des clients');
    console.log('   • 📦 Gestion des produits');
    console.log('   • 📊 Gestion des stocks');
    console.log('   • 💰 Suivi des paiements');
    console.log('   • 📈 Rapports et statistiques');
    console.log('   • ⚙️  Configuration système');
    console.log('='.repeat(60));
    console.log('🏢 Société Ben Chaabène de Commerce - Système ABM');
    console.log('✨ Prêt pour la gestion d\'entreprise moderne !');
    console.log('='.repeat(60));
  }, 5000);
}
