import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ProductService } from "../../services/apiService";
import { toast } from "react-toastify";
import "../Invoices/Invoice.css";

// Service Stock
const StockService = {
  async getAlerts(params?: any) {
    // Simulation d'appel API - à remplacer par le vrai service
    return { success: true, data: { results: [] } };
  },

  async resolveAlert(alertId: number) {
    // Simulation d'appel API - à remplacer par le vrai service
    return { success: true, message: "Alerte résolue" };
  },

  getAlertLevelColor(level: string) {
    const colors: Record<string, string> = {
      CRITIQUE: "#e74c3c",
      ELEVE: "#f39c12",
      MOYEN: "#f1c40f",
      FAIBLE: "#2ecc71",
    };
    return colors[level] || "#95a5a6";
  },
};

interface StockAlert {
  id: number;
  produit_id: number;
  produit_nom: string;
  stock_actuel: number;
  stock_minimum: number;
  niveau_alerte: string;
  type_alerte: string;
  message: string;
  date_creation: string;
  resolu: boolean;
  date_resolution?: string;
  produit: number;
  produit_details?: {
    nom: string;
    code_produit: string;
    stock_actuel: number;
    stock_minimum: number;
    stock_maximum: number;
  };
}

// Fonctions utilitaires
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("fr-FR");
};

const getAlertTypeLabel = (type: string) => {
  switch (type) {
    case "STOCK_FAIBLE":
      return "Stock faible";
    case "RUPTURE":
      return "Rupture de stock";
    case "STOCK_NEGATIF":
      return "Stock négatif";
    default:
      return type;
  }
};

const StockAlerts: React.FC = () => {
  const navigate = useNavigate();

  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    type_alerte: "all",
    niveau_alerte: "all",
    resolu: "false",
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadAlerts();
  }, [filters, currentPage]);

  const loadAlerts = async () => {
    setLoading(true);
    const filterParams: any = {
      page: currentPage,
      page_size: 10,
    };

    if (filters.type_alerte !== "all")
      filterParams.type_alerte = filters.type_alerte;
    if (filters.niveau_alerte !== "all")
      filterParams.niveau_alerte = filters.niveau_alerte;
    if (filters.resolu !== "all")
      filterParams.resolu = filters.resolu === "true";

    const res = await StockService.getAlerts(filterParams);
    if (res.success && res.data) {
      const data = res.data as any;
      setAlerts(data.results || []);
      setTotalPages(Math.ceil(data.count / 10));
    }
    setLoading(false);
  };

  const handleResolveAlert = async (alertId: number) => {
    const res = await StockService.resolveAlert(alertId);
    if (res.success) {
      toast.success("Alerte marquée comme résolue");
      loadAlerts();
    } else {
      toast.error(res.message || "Impossible de résoudre l'alerte");
    }
  };

  const resetFilters = () => {
    setFilters({
      type_alerte: "all",
      niveau_alerte: "all",
      resolu: "false",
    });
    setCurrentPage(1);
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "STOCK_FAIBLE":
        return "⚠️";
      case "RUPTURE":
        return "🚨";
      case "SURSTOCK":
        return "📦";
      default:
        return "⚠️";
    }
  };

  const getAlertTypeLabel = (type: string) => {
    switch (type) {
      case "STOCK_FAIBLE":
        return "Stock faible";
      case "RUPTURE":
        return "Rupture de stock";
      case "SURSTOCK":
        return "Surstock";
      default:
        return type;
    }
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="header-content">
          <div>
            <h1 className="page-title">Alertes Stock</h1>
            <p className="page-subtitle">Surveillance des niveaux de stock</p>
          </div>
          <div className="page-actions">
            <button
              className="page-action secondary"
              onClick={() => navigate("/dashboard/stock/mouvements")}>
              📋 Mouvements
            </button>
            <button
              className="page-action secondary"
              onClick={() => navigate("/dashboard/stock/resume")}>
              📊 Résumé
            </button>
            <button
              className="page-action"
              onClick={loadAlerts}>
              🔄 Actualiser
            </button>
          </div>
        </div>
      </div>

      <div className="page-content">
        {/* Filtres */}
        <div
          style={{
            marginBottom: "20px",
            background: "#f8f9fa",
            padding: "20px",
            borderRadius: "8px",
          }}>
          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: "wrap",
              alignItems: "center",
            }}>
            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Type d'alerte:
              </label>
              <select
                value={filters.type_alerte}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    type_alerte: e.target.value,
                  }))
                }
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="all">Tous</option>
                <option value="STOCK_FAIBLE">Stock faible</option>
                <option value="RUPTURE">Rupture</option>
                <option value="SURSTOCK">Surstock</option>
              </select>
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Niveau:
              </label>
              <select
                value={filters.niveau_alerte}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    niveau_alerte: e.target.value,
                  }))
                }
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="all">Tous</option>
                <option value="CRITIQUE">Critique</option>
                <option value="MOYEN">Moyen</option>
                <option value="FAIBLE">Faible</option>
              </select>
            </div>

            <div>
              <label style={{ fontSize: "14px", marginRight: "8px" }}>
                Statut:
              </label>
              <select
                value={filters.resolu}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, resolu: e.target.value }))
                }
                style={{
                  padding: "8px 12px",
                  border: "1px solid #dee2e6",
                  borderRadius: "6px",
                  fontSize: "14px",
                }}>
                <option value="false">Non résolues</option>
                <option value="true">Résolues</option>
                <option value="all">Toutes</option>
              </select>
            </div>

            <button
              onClick={resetFilters}
              style={{
                padding: "8px 12px",
                background: "#e74c3c",
                color: "white",
                border: "none",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
              }}>
              🗑️ Effacer
            </button>
          </div>
        </div>

        {/* Liste des alertes */}
        {loading ? (
          <div style={{ textAlign: "center", padding: "40px" }}>
            <div className="loading-spinner"></div>
            <div className="loading-text">Chargement des alertes...</div>
          </div>
        ) : (
          <>
            <div
              style={{ display: "flex", flexDirection: "column", gap: "15px" }}>
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  style={{
                    background: "white",
                    borderRadius: "8px",
                    padding: "20px",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    borderLeft: `4px solid ${StockService.getAlertLevelColor(
                      alert.niveau_alerte
                    )}`,
                    opacity: alert.resolu ? 0.6 : 1,
                  }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                    }}>
                    <div style={{ flex: 1 }}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "12px",
                          marginBottom: "8px",
                        }}>
                        <span style={{ fontSize: "24px" }}>
                          {getAlertIcon(alert.type_alerte)}
                        </span>
                        <div>
                          <h4 style={{ margin: "0", fontSize: "16px" }}>
                            {alert.produit_details?.nom ||
                              `Produit #${alert.produit}`}
                          </h4>
                          <div style={{ fontSize: "12px", color: "#666" }}>
                            {alert.produit_details?.code_produit}
                          </div>
                        </div>
                        <span
                          style={{
                            padding: "4px 8px",
                            borderRadius: "4px",
                            fontSize: "12px",
                            background:
                              StockService.getAlertLevelColor(
                                alert.niveau_alerte
                              ) + "20",
                            color: StockService.getAlertLevelColor(
                              alert.niveau_alerte
                            ),
                            fontWeight: "bold",
                          }}>
                          {alert.niveau_alerte}
                        </span>
                      </div>

                      <p style={{ margin: "8px 0", fontSize: "14px" }}>
                        <strong>{getAlertTypeLabel(alert.type_alerte)}:</strong>{" "}
                        {alert.message}
                      </p>

                      {alert.produit_details && (
                        <div
                          style={{
                            display: "grid",
                            gridTemplateColumns:
                              "repeat(auto-fit, minmax(120px, 1fr))",
                            gap: "15px",
                            marginTop: "12px",
                            padding: "12px",
                            background: "#f8f9fa",
                            borderRadius: "6px",
                          }}>
                          <div>
                            <div style={{ fontSize: "12px", color: "#666" }}>
                              Stock actuel
                            </div>
                            <div
                              style={{ fontWeight: "bold", fontSize: "16px" }}>
                              {alert.produit_details.stock_actuel}
                            </div>
                          </div>
                          <div>
                            <div style={{ fontSize: "12px", color: "#666" }}>
                              Stock minimum
                            </div>
                            <div
                              style={{ fontWeight: "bold", fontSize: "16px" }}>
                              {alert.produit_details.stock_minimum}
                            </div>
                          </div>
                          <div>
                            <div style={{ fontSize: "12px", color: "#666" }}>
                              Stock maximum
                            </div>
                            <div
                              style={{ fontWeight: "bold", fontSize: "16px" }}>
                              {alert.produit_details.stock_maximum}
                            </div>
                          </div>
                        </div>
                      )}

                      <div
                        style={{
                          fontSize: "12px",
                          color: "#666",
                          marginTop: "8px",
                        }}>
                        Créée le {formatDate(alert.date_creation)}
                        {alert.resolu && alert.date_resolution && (
                          <span>
                            {" "}
                            • Résolue le {formatDate(alert.date_resolution)}
                          </span>
                        )}
                      </div>
                    </div>

                    <div
                      style={{
                        display: "flex",
                        gap: "8px",
                        marginLeft: "20px",
                      }}>
                      {!alert.resolu && (
                        <button
                          onClick={() => handleResolveAlert(alert.id)}
                          style={{
                            padding: "8px 16px",
                            background: "#27ae60",
                            color: "white",
                            border: "none",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "14px",
                          }}>
                          ✅ Résoudre
                        </button>
                      )}
                      <button
                        onClick={() =>
                          navigate(`/dashboard/produits/${alert.produit}`)
                        }
                        style={{
                          padding: "8px 16px",
                          background: "#3498db",
                          color: "white",
                          border: "none",
                          borderRadius: "6px",
                          cursor: "pointer",
                          fontSize: "14px",
                        }}>
                        👁️ Voir produit
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: "10px",
                  alignItems: "center",
                  marginTop: "20px",
                }}>
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  disabled={currentPage === 1}
                  style={{
                    padding: "8px 12px",
                    background: currentPage === 1 ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor: currentPage === 1 ? "not-allowed" : "pointer",
                  }}>
                  ← Précédent
                </button>

                <span style={{ margin: "0 15px" }}>
                  Page {currentPage} sur {totalPages}
                </span>

                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  disabled={currentPage === totalPages}
                  style={{
                    padding: "8px 12px",
                    background:
                      currentPage === totalPages ? "#95a5a6" : "#3498db",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    cursor:
                      currentPage === totalPages ? "not-allowed" : "pointer",
                  }}>
                  Suivant →
                </button>
              </div>
            )}
          </>
        )}

        {alerts.length === 0 && !loading && (
          <div
            style={{ textAlign: "center", padding: "60px", color: "#7f8c8d" }}>
            <div style={{ fontSize: "48px", marginBottom: "20px" }}>✅</div>
            <h3>Aucune alerte</h3>
            <p>Tous vos stocks sont dans les niveaux normaux</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockAlerts;
