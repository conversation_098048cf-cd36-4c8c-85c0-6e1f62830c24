.notification-center {
  position: relative;
  display: inline-block;
}

.notification-trigger {
  background: none;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
}

.notification-trigger:hover {
  background: rgba(0, 0, 0, 0.1);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.7em;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.notification-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 400px;
  max-height: 500px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
}

.notification-header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
}

.notification-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2em;
  font-weight: 600;
}

.notification-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.mark-all-read {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  font-size: 0.85em;
  padding: 5px 10px;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.mark-all-read:hover {
  background: rgba(52, 152, 219, 0.1);
}

.refresh-notifications {
  background: none;
  border: none;
  font-size: 1.2em;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.refresh-notifications:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: rotate(180deg);
}

.refresh-notifications:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.notification-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 20px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background 0.3s ease;
  position: relative;
}

.notification-item:hover {
  background: #f8fafc;
}

.notification-item.unread {
  background: #f0f9ff;
  border-left: 3px solid #3498db;
}

.notification-icon {
  font-size: 1.5em;
  margin-right: 15px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95em;
}

.notification-priority {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.notification-message {
  color: #64748b;
  font-size: 0.9em;
  line-height: 1.4;
  margin-bottom: 5px;
}

.notification-time {
  color: #94a3b8;
  font-size: 0.8em;
}

.notification-unread-dot {
  width: 8px;
  height: 8px;
  background: #3498db;
  border-radius: 50%;
  margin-left: 10px;
  margin-top: 8px;
  flex-shrink: 0;
}

.notification-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.notification-loading .loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notification-loading p {
  color: #64748b;
  margin: 0;
  font-size: 0.9em;
}

.notification-empty {
  text-align: center;
  padding: 40px 20px;
  color: #94a3b8;
}

.notification-empty p {
  margin: 0;
  font-size: 0.9em;
}

.notification-footer {
  padding: 15px 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  text-align: center;
}

.view-all-notifications {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.view-all-notifications:hover {
  background: rgba(52, 152, 219, 0.1);
}

/* Responsive */
@media (max-width: 480px) {
  .notification-panel {
    width: 350px;
    right: -50px;
  }
}

@media (max-width: 400px) {
  .notification-panel {
    width: 300px;
    right: -100px;
  }
  
  .notification-item {
    padding: 12px 15px;
  }
  
  .notification-header {
    padding: 15px;
  }
}
