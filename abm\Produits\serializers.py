from rest_framework import serializers
from .models import Produit, Categorie, ImageProduit

class CategorieSerializer(serializers.ModelSerializer):
    """Serializer pour les catégories"""
    
    sous_categories = serializers.SerializerMethodField()
    nb_produits = serializers.SerializerMethodField()
    
    class Meta:
        model = Categorie
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')
    
    def get_sous_categories(self, obj):
        if obj.sous_categories.exists():
            return CategorieSerializer(obj.sous_categories.all(), many=True).data
        return []
    
    def get_nb_produits(self, obj):
        return obj.produit_set.filter(statut='ACTIF').count()

class ImageProduitSerializer(serializers.ModelSerializer):
    """Serializer pour les images de produits"""
    
    class Meta:
        model = ImageProduit
        fields = '__all__'
        read_only_fields = ('created_at',)

class ProduitSerializer(serializers.ModelSerializer):
    """Serializer pour les produits"""
    
    categorie_nom = serializers.CharField(source='categorie.nom', read_only=True)
    images = ImageProduitSerializer(many=True, read_only=True)
    prix_ttc = serializers.ReadOnlyField()
    marge_brute = serializers.ReadOnlyField()
    taux_marge = serializers.ReadOnlyField()
    en_rupture = serializers.ReadOnlyField()
    alerte_stock = serializers.ReadOnlyField()
    
    class Meta:
        model = Produit
        fields = '__all__'
        read_only_fields = (
            'created_at', 'updated_at', 'created_by', 'updated_by'
        )
    
    def validate_prix_vente(self, value):
        """Valide que le prix de vente est supérieur au prix d'achat"""
        prix_achat = self.initial_data.get('prix_achat', 0)
        if value <= prix_achat:
            raise serializers.ValidationError(
                "Le prix de vente doit être supérieur au prix d'achat"
            )
        return value
    
    def validate_stock_minimum(self, value):
        """Valide que le stock minimum est positif"""
        if value < 0:
            raise serializers.ValidationError(
                "Le stock minimum ne peut pas être négatif"
            )
        return value

class ProduitCreateSerializer(serializers.ModelSerializer):
    """Serializer pour la création de produits"""
    
    images = ImageProduitSerializer(many=True, required=False)
    
    class Meta:
        model = Produit
        exclude = ('created_at', 'updated_at', 'created_by', 'updated_by')
    
    def create(self, validated_data):
        images_data = validated_data.pop('images', [])
        produit = Produit.objects.create(**validated_data)
        
        for image_data in images_data:
            ImageProduit.objects.create(produit=produit, **image_data)
        
        return produit

class ProduitStockSerializer(serializers.Serializer):
    """Serializer pour les ajustements de stock"""
    
    nouveau_stock = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0)
    motif = serializers.CharField(max_length=200, required=False, default='Ajustement manuel')

class ProduitStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques produits"""
    
    total_produits = serializers.IntegerField()
    produits_actifs = serializers.IntegerField()
    produits_rupture = serializers.IntegerField()
    produits_alerte = serializers.IntegerField()
    valeur_stock_achat = serializers.DecimalField(max_digits=12, decimal_places=2)
    valeur_stock_vente = serializers.DecimalField(max_digits=12, decimal_places=2)
    categories = serializers.ListField()
    top_produits = ProduitSerializer(many=True)
