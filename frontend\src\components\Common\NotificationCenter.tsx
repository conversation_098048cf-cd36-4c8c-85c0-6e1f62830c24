/**
 * Centre de notifications temps réel
 */

import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { DashboardService } from "../../services/apiService";
import "./NotificationCenter.css";

interface Notification {
  id: string;
  type: string;
  titre: string;
  message: string;
  priorite: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  is_read: boolean;
  created_at: string;
  action_url?: string;
}

const NotificationCenter: React.FC = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      loadNotifications();
      // Simuler les notifications temps réel
      const interval = setInterval(loadNotifications, 30000); // Actualiser toutes les 30s
      return () => clearInterval(interval);
    }
  }, [user]);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      // Simulation d'activité récente pour le mode démo
      const result = {
        results: [
          {
            id: 1,
            type: "facture",
            titre: "Nouvelle facture",
            message: "Nouvelle facture créée",
            priorite: "normale" as const,
            is_read: false,
            created_at: new Date().toISOString(),
            timestamp: new Date(),
          },
          {
            id: 2,
            type: "client",
            titre: "Nouveau client",
            message: "Nouveau client ajouté",
            priorite: "normale" as const,
            is_read: false,
            created_at: new Date().toISOString(),
            timestamp: new Date(),
          },
        ],
      };

      if (result?.results) {
        setNotifications(result.results as any);
        const unread = result.results.filter((n: any) => !n.is_read).length;
        setUnreadCount(unread);
      } else {
        // Notifications simulées si l'API n'est pas disponible
        const mockNotifications: Notification[] = [
          {
            id: "1",
            type: "FACTURE",
            titre: "Nouvelle facture",
            message: "Facture FAC-2024-001 créée pour Client ABC",
            priorite: "MEDIUM",
            is_read: false,
            created_at: new Date().toISOString(),
          },
          {
            id: "2",
            type: "PAIEMENT",
            titre: "Paiement reçu",
            message: "Paiement de 1250€ reçu pour la facture FAC-2024-001",
            priorite: "HIGH",
            is_read: false,
            created_at: new Date(Date.now() - 3600000).toISOString(),
          },
          {
            id: "3",
            type: "STOCK",
            titre: "Stock faible",
            message: 'Le produit "Produit A" a un stock faible (5 unités)',
            priorite: "URGENT",
            is_read: true,
            created_at: new Date(Date.now() - 7200000).toISOString(),
          },
        ];
        setNotifications(mockNotifications);
        setUnreadCount(mockNotifications.filter((n) => !n.is_read).length);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des notifications:", error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      // Simuler l'appel API pour marquer comme lu
      // await DashboardService.markNotificationAsRead(notificationId);

      setNotifications((prev) =>
        prev.map((n) => (n.id === notificationId ? { ...n, is_read: true } : n))
      );
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Erreur lors du marquage comme lu:", error);
    }
  };

  const markAllAsRead = async () => {
    try {
      // Simuler l'appel API pour marquer toutes comme lues
      setNotifications((prev) => prev.map((n) => ({ ...n, is_read: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error("Erreur lors du marquage de toutes comme lues:", error);
    }
  };

  const getPriorityColor = (priorite: string) => {
    switch (priorite) {
      case "URGENT":
        return "#e74c3c";
      case "HIGH":
        return "#f39c12";
      case "MEDIUM":
        return "#3498db";
      case "LOW":
        return "#95a5a6";
      default:
        return "#95a5a6";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "FACTURE":
        return "📄";
      case "PAIEMENT":
        return "💰";
      case "STOCK":
        return "📦";
      case "CLIENT":
        return "👤";
      case "SYSTEM":
        return "⚙️";
      default:
        return "🔔";
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return "À l'instant";
    if (diffMins < 60) return `Il y a ${diffMins} min`;
    if (diffHours < 24) return `Il y a ${diffHours}h`;
    if (diffDays < 7) return `Il y a ${diffDays}j`;
    return date.toLocaleDateString();
  };

  return (
    <div className="notification-center">
      <button
        className="notification-trigger"
        onClick={() => setIsOpen(!isOpen)}>
        🔔
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
      </button>

      {isOpen && (
        <>
          <div
            className="notification-overlay"
            onClick={() => setIsOpen(false)}
          />
          <div className="notification-panel">
            <div className="notification-header">
              <h3>Notifications</h3>
              <div className="notification-actions">
                {unreadCount > 0 && (
                  <button
                    className="mark-all-read"
                    onClick={markAllAsRead}>
                    Tout marquer comme lu
                  </button>
                )}
                <button
                  className="refresh-notifications"
                  onClick={loadNotifications}
                  disabled={loading}>
                  🔄
                </button>
              </div>
            </div>

            <div className="notification-list">
              {loading ? (
                <div className="notification-loading">
                  <div className="loading-spinner"></div>
                  <p>Chargement...</p>
                </div>
              ) : notifications.length === 0 ? (
                <div className="notification-empty">
                  <p>Aucune notification</p>
                </div>
              ) : (
                notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`notification-item ${
                      !notification.is_read ? "unread" : ""
                    }`}
                    onClick={() =>
                      !notification.is_read && markAsRead(notification.id)
                    }>
                    <div className="notification-icon">
                      {getTypeIcon(notification.type)}
                    </div>
                    <div className="notification-content">
                      <div className="notification-title">
                        {notification.titre}
                        <span
                          className="notification-priority"
                          style={{
                            backgroundColor: getPriorityColor(
                              notification.priorite
                            ),
                          }}
                        />
                      </div>
                      <div className="notification-message">
                        {notification.message}
                      </div>
                      <div className="notification-time">
                        {formatTime(notification.created_at)}
                      </div>
                    </div>
                    {!notification.is_read && (
                      <div className="notification-unread-dot" />
                    )}
                  </div>
                ))
              )}
            </div>

            {notifications.length > 0 && (
              <div className="notification-footer">
                <button
                  className="view-all-notifications"
                  onClick={() => setIsOpen(false)}>
                  Voir toutes les notifications
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationCenter;
