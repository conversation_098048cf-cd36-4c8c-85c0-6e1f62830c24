import React, { createContext, useContext, useState, useCallback } from 'react';
import './ConfirmDialog.css';

interface ConfirmOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'danger' | 'success';
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
}

interface ConfirmContextType {
  confirm: (options: ConfirmOptions) => void;
}

const ConfirmContext = createContext<ConfirmContextType | undefined>(undefined);

export const useConfirm = () => {
  const context = useContext(ConfirmContext);
  if (!context) {
    throw new Error('useConfirm must be used within a ConfirmProvider');
  }
  return context;
};

interface ConfirmProviderProps {
  children: React.ReactNode;
}

export const ConfirmProvider: React.FC<ConfirmProviderProps> = ({ children }) => {
  const [confirmState, setConfirmState] = useState<{
    isOpen: boolean;
    options: ConfirmOptions | null;
  }>({
    isOpen: false,
    options: null,
  });

  const confirm = useCallback((options: ConfirmOptions) => {
    setConfirmState({
      isOpen: true,
      options: {
        confirmText: 'Confirmer',
        cancelText: 'Annuler',
        type: 'info',
        ...options,
      },
    });
  }, []);

  const handleConfirm = async () => {
    if (confirmState.options?.onConfirm) {
      await confirmState.options.onConfirm();
    }
    setConfirmState({ isOpen: false, options: null });
  };

  const handleCancel = () => {
    if (confirmState.options?.onCancel) {
      confirmState.options.onCancel();
    }
    setConfirmState({ isOpen: false, options: null });
  };

  return (
    <ConfirmContext.Provider value={{ confirm }}>
      {children}
      {confirmState.isOpen && confirmState.options && (
        <ConfirmDialog
          options={confirmState.options}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
        />
      )}
    </ConfirmContext.Provider>
  );
};

interface ConfirmDialogProps {
  options: ConfirmOptions;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  options,
  onConfirm,
  onCancel,
}) => {
  const getTypeConfig = (type: ConfirmOptions['type']) => {
    switch (type) {
      case 'warning':
        return {
          icon: '⚠️',
          className: 'confirm-warning',
          color: '#ffa502'
        };
      case 'danger':
        return {
          icon: '🚨',
          className: 'confirm-danger',
          color: '#ff4757'
        };
      case 'success':
        return {
          icon: '✅',
          className: 'confirm-success',
          color: '#2ed573'
        };
      default:
        return {
          icon: 'ℹ️',
          className: 'confirm-info',
          color: '#3742fa'
        };
    }
  };

  const config = getTypeConfig(options.type);

  return (
    <div className="confirm-overlay" onClick={onCancel}>
      <div 
        className={`confirm-dialog ${config.className}`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="confirm-header">
          <div className="confirm-icon" style={{ color: config.color }}>
            {config.icon}
          </div>
          <h3 className="confirm-title">{options.title}</h3>
        </div>
        
        <div className="confirm-content">
          <p className="confirm-message">{options.message}</p>
        </div>
        
        <div className="confirm-actions">
          <button
            className="confirm-btn confirm-btn-cancel"
            onClick={onCancel}
          >
            {options.cancelText}
          </button>
          <button
            className={`confirm-btn confirm-btn-confirm ${config.className}`}
            onClick={onConfirm}
            style={{ backgroundColor: config.color }}
          >
            {options.confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

// Hook utilitaire pour les confirmations courantes
export const useConfirmActions = () => {
  const { confirm } = useConfirm();

  return {
    confirmDelete: (itemName: string, onConfirm: () => void | Promise<void>) =>
      confirm({
        title: 'Confirmer la suppression',
        message: `Êtes-vous sûr de vouloir supprimer "${itemName}" ? Cette action est irréversible.`,
        confirmText: 'Supprimer',
        cancelText: 'Annuler',
        type: 'danger',
        onConfirm,
      }),

    confirmSave: (onConfirm: () => void | Promise<void>) =>
      confirm({
        title: 'Sauvegarder les modifications',
        message: 'Voulez-vous sauvegarder les modifications apportées ?',
        confirmText: 'Sauvegarder',
        cancelText: 'Annuler',
        type: 'success',
        onConfirm,
      }),

    confirmDiscard: (onConfirm: () => void | Promise<void>) =>
      confirm({
        title: 'Abandonner les modifications',
        message: 'Vous avez des modifications non sauvegardées. Voulez-vous vraiment quitter sans sauvegarder ?',
        confirmText: 'Abandonner',
        cancelText: 'Continuer l\'édition',
        type: 'warning',
        onConfirm,
      }),

    confirmAction: (
      title: string,
      message: string,
      onConfirm: () => void | Promise<void>,
      type: ConfirmOptions['type'] = 'info'
    ) =>
      confirm({
        title,
        message,
        type,
        onConfirm,
      }),
  };
};
