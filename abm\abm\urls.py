"""
URL configuration for abm project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('', admin.site.urls),

    # API Authentication
    path('api/auth/', include('Authentication.urls')),

    # API Modules - Architecture nettoyée et cohérente
    path('api/', include('Clients.urls')),           # CRM et gestion clients
    path('api/', include('Produits.urls')),         # Catalogue produits (correspond au frontend)
    path('api/', include('Commandes.urls')),          # Gestion commandes (correspond au frontend)
    path('api/', include('Facturation.urls')),      # Facturation (correspond au frontend)
    path('api/', include('Stock.urls')),               # Gestion stocks
    path('api/', include('Paiements.urls')),        # Paiements (correspond au frontend)
    path('api/', include('Fournisseurs.urls')), # Gestion fournisseurs
    path('api/', include('Comptabilite.urls')), # Comptabilité
    path('api/', include('Rapports.urls')),         # Rapports et analytics
    path('api/', include('Ecommerce.urls')),       # E-commerce
    path('api/', include('Core.urls')),                 # Fonctionnalités core

    # DRF Auth (pour le browsable API)
    path('api-auth/', include('rest_framework.urls')),
]

# Servir les fichiers media en développement
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
