/**
 * Index des composants Commandes
 * Exporte tous les composants de gestion des commandes
 */

// Composants principaux
export { default as OrdersList } from './OrdersList';
export { default as OrderForm } from './OrderForm';
export { default as OrderDetail } from './OrderDetail';
export { default as OrderStats } from './OrderStats';

// Composant principal (routing)
export { default as Orders } from './Orders';

// Types TypeScript
export interface Order {
  id: string;
  numero: string;
  client_id: string;
  client_nom: string;
  client_email: string;
  date_commande: string;
  date_livraison_prevue?: string;
  date_livraison_reelle?: string;
  montant_ht: number;
  montant_tva: number;
  montant_total: number;
  statut: 'BROUILLON' | 'CONFIRMEE' | 'EN_PREPARATION' | 'EXPEDIEE' | 'LIVREE' | 'ANNULEE';
  priorite: 'NORMALE' | 'HAUTE' | 'URGENTE';
  notes?: string;
  lignes: OrderLine[];
  created_at: string;
  updated_at: string;
}

export interface OrderLine {
  id?: string;
  produit_id: string;
  produit_nom: string;
  description: string;
  quantite: number;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
}

export interface OrderFilters {
  search: string;
  statut: string;
  priorite: string;
  client: string;
  date_debut: string;
  date_fin: string;
  montant_min: string;
  montant_max: string;
}

export interface OrderStats {
  total_commandes: number;
  commandes_confirmees: number;
  commandes_en_preparation: number;
  commandes_expediees: number;
  commandes_livrees: number;
  commandes_annulees: number;
  ca_total_commandes: number;
  panier_moyen: number;
  delai_moyen_livraison: number;
  taux_livraison_temps: number;
  commandes_urgentes: number;
  retards_livraison: number;
}

// Constantes
export const ORDER_STATUS = {
  DRAFT: 'BROUILLON',
  CONFIRMED: 'CONFIRMEE',
  PREPARING: 'EN_PREPARATION',
  SHIPPED: 'EXPEDIEE',
  DELIVERED: 'LIVREE',
  CANCELLED: 'ANNULEE'
} as const;

export const ORDER_PRIORITY = {
  NORMAL: 'NORMALE',
  HIGH: 'HAUTE',
  URGENT: 'URGENTE'
} as const;

export const ORDER_STATUS_LABELS = {
  BROUILLON: 'Brouillon',
  CONFIRMEE: 'Confirmée',
  EN_PREPARATION: 'En préparation',
  EXPEDIEE: 'Expédiée',
  LIVREE: 'Livrée',
  ANNULEE: 'Annulée'
} as const;

export const ORDER_PRIORITY_LABELS = {
  NORMALE: 'Normale',
  HAUTE: 'Haute',
  URGENTE: 'Urgente'
} as const;

// Fonctions utilitaires
export const formatOrderNumber = (numero: string) => {
  return numero || 'N/A';
};

export const formatOrderAmount = (amount: number, currency = 'EUR') => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency
  }).format(amount);
};

export const formatOrderDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const getOrderStatusLabel = (status: string) => {
  return ORDER_STATUS_LABELS[status as keyof typeof ORDER_STATUS_LABELS] || status;
};

export const getPriorityLabel = (priority: string) => {
  return ORDER_PRIORITY_LABELS[priority as keyof typeof ORDER_PRIORITY_LABELS] || priority;
};

export const isOrderOverdue = (dateLivraisonPrevue: string, statut: string) => {
  if (!dateLivraisonPrevue || statut === 'LIVREE' || statut === 'ANNULEE') {
    return false;
  }
  return new Date(dateLivraisonPrevue) < new Date();
};

export const calculateOrderTotals = (lignes: OrderLine[]) => {
  const totalHT = lignes.reduce((sum, ligne) => sum + ligne.montant_ht, 0);
  const totalTVA = lignes.reduce((sum, ligne) => sum + ligne.montant_tva, 0);
  const totalTTC = totalHT + totalTVA;

  return {
    totalHT,
    totalTVA,
    totalTTC
  };
};

export const generateOrderNumber = (prefix = 'CMD') => {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${year}${month}-${random}`;
};

export const validateOrderData = (data: Partial<Order>) => {
  const errors: Record<string, string> = {};

  if (!data.client_id) {
    errors.client_id = 'Client requis';
  }

  if (!data.numero) {
    errors.numero = 'Numéro de commande requis';
  }

  if (!data.date_commande) {
    errors.date_commande = 'Date de commande requise';
  }

  if (!data.lignes || data.lignes.length === 0) {
    errors.lignes = 'Au moins une ligne est requise';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Routes des commandes
export const ORDER_ROUTES = {
  LIST: '/orders',
  NEW: '/orders/new',
  DETAIL: '/orders/:id',
  EDIT: '/orders/:id/edit',
  STATS: '/orders/stats'
} as const;

// Configuration par défaut
export const ORDER_CONFIG = {
  DEFAULT_PRIORITY: 'NORMALE',
  DEFAULT_STATUS: 'BROUILLON',
  ITEMS_PER_PAGE: 20,
  MAX_LINES_PER_ORDER: 50,
  DEFAULT_DELIVERY_DAYS: 7
} as const;
