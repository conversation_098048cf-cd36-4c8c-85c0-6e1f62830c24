"""
Permissions personnalisées pour l'authentification
"""

from rest_framework import permissions
from .models import UserRole


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Permission personnalisée pour permettre aux utilisateurs de modifier
    seulement leurs propres données, ou aux admins de modifier toutes les données.
    """
    
    def has_object_permission(self, request, view, obj):
        # Permissions de lecture pour tous les utilisateurs authentifiés
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Permissions d'écriture seulement pour le propriétaire ou les admins
        return obj == request.user or request.user.has_permission(UserRole.ADMIN)


class IsComptableOrAbove(permissions.BasePermission):
    """Permission pour les comptables et plus"""
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.has_permission(UserRole.COMPTABLE)
        )


class IsAdminOrAbove(permissions.BasePermission):
    """Permission pour les administrateurs et plus"""
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.has_permission(UserRole.ADMIN)
        )


class IsSuperAdmin(permissions.BasePermission):
    """Permission pour les super administrateurs seulement"""
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_superadmin
        )


class RoleBasedPermission(permissions.BasePermission):
    """
    Permission basée sur les rôles avec configuration flexible
    """
    
    # Définir les rôles requis pour chaque action
    required_roles = {
        'list': [UserRole.NORMAL],
        'retrieve': [UserRole.NORMAL],
        'create': [UserRole.COMPTABLE],
        'update': [UserRole.COMPTABLE],
        'partial_update': [UserRole.COMPTABLE],
        'destroy': [UserRole.ADMIN],
    }
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        action = getattr(view, 'action', None)
        if not action:
            return True
        
        required_roles = self.required_roles.get(action, [])
        if not required_roles:
            return True
        
        # Vérifier si l'utilisateur a au moins un des rôles requis
        for role in required_roles:
            if request.user.has_permission(role):
                return True
        
        return False


class ModuleAccessPermission(permissions.BasePermission):
    """
    Permission pour vérifier l'accès aux modules selon le rôle
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Récupérer le module requis depuis la vue
        required_module = getattr(view, 'required_module', None)
        if not required_module:
            return True
        
        # Vérifier si l'utilisateur a accès à ce module
        accessible_modules = request.user.get_accessible_modules()
        return required_module in accessible_modules


class FacturationPermission(RoleBasedPermission):
    """Permission spécifique pour la facturation"""
    
    required_roles = {
        'list': [UserRole.COMPTABLE],
        'retrieve': [UserRole.COMPTABLE],
        'create': [UserRole.COMPTABLE],
        'update': [UserRole.COMPTABLE],
        'partial_update': [UserRole.COMPTABLE],
        'destroy': [UserRole.ADMIN],
        'generate_pdf': [UserRole.COMPTABLE],
    }


class StatisticsPermission(RoleBasedPermission):
    """Permission pour les statistiques"""
    
    required_roles = {
        'list': [UserRole.ADMIN],
        'retrieve': [UserRole.ADMIN],
        'dashboard_stats': [UserRole.COMPTABLE],
        'advanced_stats': [UserRole.ADMIN],
        'system_stats': [UserRole.SUPERADMIN],
    }


class HasModulePermission(permissions.BasePermission):
    """Permission générique pour vérifier l'accès aux modules"""

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        # Récupérer le module requis depuis la vue
        required_permission = getattr(view, 'required_permission', None)
        if not required_permission:
            return True

        # Mapping des permissions par module
        module_permissions = {
            'clients': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'produits': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'commandes': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'stock': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'paiements': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'fournisseurs': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'comptabilite': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'rapports': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'facturation': [UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
            'ecommerce': [UserRole.NORMAL, UserRole.COMPTABLE, UserRole.ADMIN, UserRole.SUPERADMIN],
        }

        required_roles = module_permissions.get(required_permission, [])

        # Vérifier si l'utilisateur a au moins un des rôles requis
        for role in required_roles:
            if request.user.has_permission(role):
                return True

        return False


class UserManagementPermission(permissions.BasePermission):
    """Permission pour la gestion des utilisateurs"""

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        action = getattr(view, 'action', None)

        # Seuls les admins peuvent gérer les utilisateurs
        if action in ['list', 'retrieve', 'create', 'update', 'partial_update']:
            return request.user.has_permission(UserRole.ADMIN)

        # Seuls les super admins peuvent supprimer des utilisateurs
        if action == 'destroy':
            return request.user.is_superadmin

        return True

    def has_object_permission(self, request, view, obj):
        # Un utilisateur peut toujours voir/modifier son propre profil
        if obj == request.user:
            return True

        # Les admins ne peuvent pas modifier les super admins
        if obj.is_superadmin and not request.user.is_superadmin:
            return False

        # Les admins peuvent modifier les autres utilisateurs
        return request.user.has_permission(UserRole.ADMIN)
