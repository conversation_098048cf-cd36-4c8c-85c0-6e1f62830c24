#!/usr/bin/env python
"""
Script pour changer le rôle d'un utilisateur
Usage: python change_user_role.py <username> <nouveau_role>
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'abm.settings')
django.setup()

from Authentication.models import CustomUser, UserRole

def change_user_role(username, new_role):
    """Change le rôle d'un utilisateur"""
    
    # Vérifier que le rôle est valide
    valid_roles = [choice[0] for choice in UserRole.choices]
    if new_role not in valid_roles:
        print(f"❌ Rôle invalide. Rôles disponibles : {', '.join(valid_roles)}")
        return False
    
    try:
        # Trouver l'utilisateur
        user = CustomUser.objects.get(username=username)
        old_role = user.role
        
        # Changer le rôle
        user.role = new_role
        user.save()
        
        print(f"✅ Rôle de l'utilisateur '{username}' changé avec succès !")
        print(f"   Ancien rôle : {old_role} ({user.get_role_display()})")
        print(f"   Nouveau rôle : {new_role} ({user.get_role_display()})")
        
        # Afficher les permissions
        print(f"\n📋 Modules accessibles avec ce rôle :")
        modules = user.get_accessible_modules()
        for module in modules:
            print(f"   - {module}")
        
        return True
        
    except CustomUser.DoesNotExist:
        print(f"❌ Utilisateur '{username}' introuvable.")
        return False
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def list_users():
    """Liste tous les utilisateurs avec leurs rôles"""
    print("\n👥 Liste des utilisateurs :")
    print("-" * 60)
    users = CustomUser.objects.all().order_by('username')
    
    for user in users:
        status = "🟢 Actif" if user.is_active else "🔴 Inactif"
        print(f"{user.username:15} | {user.get_role_display():15} | {status}")

def main():
    if len(sys.argv) == 1:
        # Afficher l'aide et la liste des utilisateurs
        print("🔧 Script de gestion des rôles utilisateurs")
        print("=" * 50)
        print("\nUsage :")
        print("  python change_user_role.py <username> <nouveau_role>")
        print("\nRôles disponibles :")
        for role_code, role_name in UserRole.choices:
            print(f"  - {role_code:12} : {role_name}")
        
        list_users()
        
    elif len(sys.argv) == 3:
        username = sys.argv[1]
        new_role = sys.argv[2].upper()
        change_user_role(username, new_role)
        
    else:
        print("❌ Nombre d'arguments incorrect.")
        print("Usage: python change_user_role.py <username> <nouveau_role>")

if __name__ == "__main__":
    main()
