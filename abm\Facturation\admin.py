"""
Administration Django pour le système de facturation professionnel
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Sum
from .models import (
    ConfigurationFacturation, Facture,
    LigneFacture, PaiementFacture
)


@admin.register(ConfigurationFacturation)
class ConfigurationFacturationAdmin(admin.ModelAdmin):
    """Administration de la configuration de facturation"""

    list_display = ['nom_societe', 'siret', 'numero_tva', 'email', 'created_at']
    fieldsets = (
        ('Informations Société', {
            'fields': ('nom_societe', 'adresse_ligne1', 'adresse_ligne2',
                      'code_postal', 'ville', 'pays')
        }),
        ('Informations Légales', {
            'fields': ('siret', 'numero_tva', 'code_ape', 'capital_social')
        }),
        ('Contact', {
            'fields': ('telephone', 'email', 'site_web')
        }),
        ('Configuration Facturation', {
            'fields': ('numerotation_auto', 'prefixe_facture', 'prefixe_devis',
                      'compteur_facture', 'compteur_devis')
        }),
        ('Conditions et Mentions', {
            'fields': ('conditions_paiement', 'mentions_legales')
        }),
        ('Design', {
            'fields': ('logo', 'couleur_principale')
        })
    )

    def has_add_permission(self, request):
        # Une seule configuration autorisée
        return not ConfigurationFacturation.objects.exists()


class LigneFactureInline(admin.TabularInline):
    """Inline pour les lignes de facture"""
    model = LigneFacture
    extra = 1
    fields = ['ordre', 'designation', 'quantite', 'prix_unitaire_ht', 'taux_tva', 'montant_ttc']
    readonly_fields = ['montant_ttc']
    ordering = ['ordre']


class PaiementFactureInline(admin.TabularInline):
    """Inline pour les paiements"""
    model = PaiementFacture
    extra = 0
    fields = ['date_paiement', 'montant', 'mode_paiement', 'reference']


@admin.register(Facture)
class FactureAdmin(admin.ModelAdmin):
    """Administration des factures"""

    list_display = [
        'numero', 'type_document', 'client', 'date_emission',
        'montant_ttc_display', 'statut_display', 'actions_display'
    ]
    list_filter = ['type_document', 'statut', 'date_emission', 'date_echeance']
    search_fields = ['numero', 'client__nom', 'client__email']
    date_hierarchy = 'date_emission'
    ordering = ['-date_emission', '-numero']

    fieldsets = (
        ('Informations Générales', {
            'fields': ('numero', 'type_document', 'client', 'statut')
        }),
        ('Dates', {
            'fields': ('date_emission', 'date_echeance', 'date_paiement')
        }),
        ('Montants', {
            'fields': ('montant_ht', 'montant_tva', 'montant_ttc',
                      'remise_pourcentage', 'remise_montant'),
            'classes': ['collapse']
        }),
        ('Informations Complémentaires', {
            'fields': ('notes', 'conditions_paiement', 'fichier_pdf'),
            'classes': ['collapse']
        }),
        ('Métadonnées', {
            'fields': ('creee_par',),
            'classes': ['collapse']
        })
    )

    readonly_fields = ['numero', 'montant_ht', 'montant_tva', 'montant_ttc']
    inlines = [LigneFactureInline, PaiementFactureInline]

    def montant_ttc_display(self, obj):
        """Affichage formaté du montant TTC"""
        return f"{obj.montant_ttc:.3f} DT"
    montant_ttc_display.short_description = "Montant TTC"

    def statut_display(self, obj):
        """Affichage coloré du statut"""
        colors = {
            'BROUILLON': '#6b7280',
            'ENVOYE': '#3b82f6',
            'ACCEPTE': '#10b981',
            'REFUSE': '#ef4444',
            'PAYE': '#059669',
            'ANNULE': '#6b7280'
        }
        color = colors.get(obj.statut, '#6b7280')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_statut_display()
        )
    statut_display.short_description = "Statut"

    def actions_display(self, obj):
        """Actions rapides"""
        actions = []

        # Lien vers le PDF
        if obj.pk:
            pdf_url = reverse('facturation:facture-pdf', args=[obj.pk])
            actions.append(f'<a href="{pdf_url}" target="_blank">📄 PDF</a>')

        # Marquer comme payé
        if obj.statut not in ['PAYE', 'ANNULE']:
            payer_url = reverse('admin:facturation_facture_change', args=[obj.pk])
            actions.append(f'<a href="{payer_url}">💰 Payer</a>')

        return format_html(' | '.join(actions))
    actions_display.short_description = "Actions"

    def get_queryset(self, request):
        """Optimisation des requêtes"""
        return super().get_queryset(request).select_related('client', 'creee_par')

    def save_model(self, request, obj, form, change):
        """Ajouter l'utilisateur créateur"""
        if not change:
            obj.creee_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(LigneFacture)
class LigneFactureAdmin(admin.ModelAdmin):
    """Administration des lignes de facture"""

    list_display = ['facture', 'designation', 'quantite', 'prix_unitaire_ht', 'montant_ttc']
    list_filter = ['facture__type_document', 'taux_tva']
    search_fields = ['designation', 'facture__numero']
    ordering = ['facture', 'ordre']


@admin.register(PaiementFacture)
class PaiementFactureAdmin(admin.ModelAdmin):
    """Administration des paiements"""

    list_display = ['facture', 'date_paiement', 'montant', 'mode_paiement', 'reference']
    list_filter = ['mode_paiement', 'date_paiement']
    search_fields = ['facture__numero', 'reference']
    date_hierarchy = 'date_paiement'
    ordering = ['-date_paiement']
