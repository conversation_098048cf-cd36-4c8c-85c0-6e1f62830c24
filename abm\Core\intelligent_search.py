"""
Système de recherche intelligente avec suggestions IA
"""
import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from django.db.models import Q, Count, Sum, Avg
from django.db.models.functions import Lower
from django.utils import timezone
from datetime import timed<PERSON>ta
from difflib import SequenceMatcher
import unicodedata

logger = logging.getLogger(__name__)


class IntelligentSearchEngine:
    """Moteur de recherche intelligent avec IA"""
    
    def __init__(self):
        self.search_history = []
        self.popular_searches = {}
        self.synonyms = {
            # Synonymes français pour améliorer la recherche
            'facture': ['invoice', 'bill', 'facturation'],
            'client': ['customer', 'acheteur', 'clientele'],
            'produit': ['product', 'article', 'item', 'marchandise'],
            'stock': ['inventory', 'inventaire', 'stockage'],
            'prix': ['price', 'tarif', 'cout', 'montant'],
            'vente': ['sale', 'selling', 'commerce'],
            'achat': ['purchase', 'buying', 'acquisition'],
            'commande': ['order', 'command', 'demande'],
            'livraison': ['delivery', 'shipping', 'expedition'],
            'paiement': ['payment', 'reglement', 'versement']
        }
        
        # Mots vides à ignorer
        self.stop_words = {
            'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais',
            'pour', 'avec', 'sans', 'dans', 'sur', 'sous', 'par', 'ce', 'cette',
            'ces', 'son', 'sa', 'ses', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes'
        }
    
    def normalize_text(self, text: str) -> str:
        """Normalise le texte pour la recherche"""
        if not text:
            return ""
        
        # Convertir en minuscules
        text = text.lower()
        
        # Supprimer les accents
        text = unicodedata.normalize('NFD', text)
        text = ''.join(c for c in text if unicodedata.category(c) != 'Mn')
        
        # Supprimer les caractères spéciaux sauf espaces et tirets
        text = re.sub(r'[^\w\s\-]', ' ', text)
        
        # Normaliser les espaces
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_keywords(self, query: str) -> List[str]:
        """Extrait les mots-clés d'une requête"""
        normalized = self.normalize_text(query)
        words = normalized.split()
        
        # Filtrer les mots vides
        keywords = [word for word in words if word not in self.stop_words and len(word) > 2]
        
        # Ajouter les synonymes
        expanded_keywords = keywords.copy()
        for keyword in keywords:
            for main_word, synonyms in self.synonyms.items():
                if keyword in synonyms or keyword == main_word:
                    expanded_keywords.extend([main_word] + synonyms)
        
        return list(set(expanded_keywords))
    
    def calculate_relevance_score(self, text: str, keywords: List[str]) -> float:
        """Calcule un score de pertinence"""
        if not text or not keywords:
            return 0.0
        
        normalized_text = self.normalize_text(text)
        score = 0.0
        
        for keyword in keywords:
            # Correspondance exacte
            if keyword in normalized_text:
                score += 2.0
            
            # Correspondance partielle
            for word in normalized_text.split():
                similarity = SequenceMatcher(None, keyword, word).ratio()
                if similarity > 0.8:
                    score += similarity
        
        return score
    
    def search_products(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Recherche intelligente de produits"""
        try:
            from Facturation.models import Produit
            
            keywords = self.extract_keywords(query)
            if not keywords:
                return []
            
            # Construction de la requête Q
            q_objects = Q()
            for keyword in keywords:
                q_objects |= (
                    Q(nom__icontains=keyword) |
                    Q(description__icontains=keyword) |
                    Q(code__icontains=keyword) |
                    Q(categorie__nom__icontains=keyword)
                )
            
            # Recherche dans la base
            produits = Produit.objects.filter(
                q_objects,
                is_active=True
            ).select_related('categorie')[:limit * 2]  # Récupérer plus pour le scoring
            
            # Calculer les scores de pertinence
            results = []
            for produit in produits:
                search_text = f"{produit.nom} {produit.description or ''} {produit.code} {produit.categorie.nom if produit.categorie else ''}"
                score = self.calculate_relevance_score(search_text, keywords)
                
                if score > 0:
                    results.append({
                        'id': str(produit.id),
                        'nom': produit.nom,
                        'code': produit.code,
                        'description': produit.description,
                        'prix_vente': float(produit.prix_vente),
                        'stock_total': produit.stock_total or 0,
                        'categorie': produit.categorie.nom if produit.categorie else None,
                        'score': score,
                        'type': 'produit'
                    })
            
            # Trier par score de pertinence
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Erreur lors de la recherche de produits: {e}")
            return []
    
    def search_clients(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Recherche intelligente de clients"""
        try:
            from Facturation.models import Client
            
            keywords = self.extract_keywords(query)
            if not keywords:
                return []
            
            # Construction de la requête Q
            q_objects = Q()
            for keyword in keywords:
                q_objects |= (
                    Q(nom__icontains=keyword) |
                    Q(email__icontains=keyword) |
                    Q(telephone__icontains=keyword) |
                    Q(adresse__icontains=keyword) |
                    Q(ville__icontains=keyword)
                )
            
            # Recherche dans la base
            clients = Client.objects.filter(
                q_objects,
                is_active=True
            )[:limit * 2]
            
            # Calculer les scores de pertinence
            results = []
            for client in clients:
                search_text = f"{client.nom} {client.email or ''} {client.telephone or ''} {client.adresse or ''} {client.ville or ''}"
                score = self.calculate_relevance_score(search_text, keywords)
                
                if score > 0:
                    results.append({
                        'id': str(client.id),
                        'nom': client.nom,
                        'email': client.email,
                        'telephone': client.telephone,
                        'adresse': client.adresse,
                        'ville': client.ville,
                        'score': score,
                        'type': 'client'
                    })
            
            # Trier par score de pertinence
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Erreur lors de la recherche de clients: {e}")
            return []
    
    def search_invoices(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Recherche intelligente de factures"""
        try:
            from Facturation.models import Facture
            
            keywords = self.extract_keywords(query)
            if not keywords:
                return []
            
            # Construction de la requête Q
            q_objects = Q()
            for keyword in keywords:
                q_objects |= (
                    Q(numero__icontains=keyword) |
                    Q(client__nom__icontains=keyword) |
                    Q(statut__icontains=keyword) |
                    Q(notes__icontains=keyword)
                )
            
            # Recherche dans la base
            factures = Facture.objects.filter(
                q_objects
            ).select_related('client')[:limit * 2]
            
            # Calculer les scores de pertinence
            results = []
            for facture in factures:
                search_text = f"{facture.numero} {facture.client.nom if facture.client else ''} {facture.statut} {facture.notes or ''}"
                score = self.calculate_relevance_score(search_text, keywords)
                
                if score > 0:
                    results.append({
                        'id': str(facture.id),
                        'numero': facture.numero,
                        'client_nom': facture.client.nom if facture.client else 'N/A',
                        'date_emission': facture.date_emission.isoformat(),
                        'montant_ttc': float(facture.montant_ttc),
                        'statut': facture.statut,
                        'score': score,
                        'type': 'facture'
                    })
            
            # Trier par score de pertinence
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Erreur lors de la recherche de factures: {e}")
            return []
    
    def global_search(self, query: str, limit_per_type: int = 10) -> Dict[str, List[Dict[str, Any]]]:
        """Recherche globale dans tous les types d'entités"""
        if not query or len(query.strip()) < 2:
            return {
                'produits': [],
                'clients': [],
                'factures': [],
                'total_results': 0
            }
        
        # Enregistrer la recherche
        self.record_search(query)
        
        # Effectuer les recherches
        produits = self.search_products(query, limit_per_type)
        clients = self.search_clients(query, limit_per_type)
        factures = self.search_invoices(query, limit_per_type)
        
        total_results = len(produits) + len(clients) + len(factures)
        
        return {
            'produits': produits,
            'clients': clients,
            'factures': factures,
            'total_results': total_results,
            'query': query,
            'timestamp': timezone.now().isoformat()
        }
    
    def record_search(self, query: str):
        """Enregistre une recherche pour les statistiques"""
        normalized_query = self.normalize_text(query)
        
        # Ajouter à l'historique
        self.search_history.append({
            'query': query,
            'normalized': normalized_query,
            'timestamp': timezone.now()
        })
        
        # Garder seulement les 1000 dernières recherches
        if len(self.search_history) > 1000:
            self.search_history = self.search_history[-1000:]
        
        # Mettre à jour les recherches populaires
        if normalized_query in self.popular_searches:
            self.popular_searches[normalized_query] += 1
        else:
            self.popular_searches[normalized_query] = 1
    
    def get_search_suggestions(self, partial_query: str, limit: int = 5) -> List[str]:
        """Génère des suggestions de recherche"""
        if not partial_query or len(partial_query) < 2:
            # Retourner les recherches populaires
            popular = sorted(
                self.popular_searches.items(),
                key=lambda x: x[1],
                reverse=True
            )[:limit]
            return [query for query, count in popular]
        
        normalized_partial = self.normalize_text(partial_query)
        suggestions = []
        
        # Rechercher dans les recherches populaires
        for query, count in self.popular_searches.items():
            if normalized_partial in query:
                suggestions.append((query, count))
        
        # Rechercher dans les noms de produits
        try:
            from Facturation.models import Produit
            produits = Produit.objects.filter(
                nom__icontains=partial_query,
                is_active=True
            ).values_list('nom', flat=True)[:limit]
            
            for nom in produits:
                normalized_nom = self.normalize_text(nom)
                if normalized_nom not in [s[0] for s in suggestions]:
                    suggestions.append((normalized_nom, 0))
        except:
            pass
        
        # Rechercher dans les noms de clients
        try:
            from Facturation.models import Client
            clients = Client.objects.filter(
                nom__icontains=partial_query,
                is_active=True
            ).values_list('nom', flat=True)[:limit]
            
            for nom in clients:
                normalized_nom = self.normalize_text(nom)
                if normalized_nom not in [s[0] for s in suggestions]:
                    suggestions.append((normalized_nom, 0))
        except:
            pass
        
        # Trier par popularité et pertinence
        suggestions.sort(key=lambda x: x[1], reverse=True)
        
        return [suggestion[0] for suggestion in suggestions[:limit]]
    
    def get_search_analytics(self) -> Dict[str, Any]:
        """Récupère les analytics de recherche"""
        if not self.search_history:
            return {
                'total_searches': 0,
                'popular_queries': [],
                'recent_searches': [],
                'search_trends': {}
            }
        
        # Recherches récentes (dernières 24h)
        recent_cutoff = timezone.now() - timedelta(hours=24)
        recent_searches = [
            s for s in self.search_history 
            if s['timestamp'] > recent_cutoff
        ]
        
        # Top recherches populaires
        popular = sorted(
            self.popular_searches.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        return {
            'total_searches': len(self.search_history),
            'searches_24h': len(recent_searches),
            'popular_queries': [{'query': q, 'count': c} for q, c in popular],
            'recent_searches': [s['query'] for s in self.search_history[-10:]],
            'unique_queries': len(self.popular_searches),
            'timestamp': timezone.now().isoformat()
        }


# Instance globale du moteur de recherche
search_engine = IntelligentSearchEngine()


class SearchTasks:
    """Tâches automatisées pour la recherche"""
    
    @staticmethod
    def cleanup_search_history():
        """Nettoie l'historique de recherche ancien"""
        try:
            cutoff_date = timezone.now() - timedelta(days=30)
            
            # Filtrer l'historique
            search_engine.search_history = [
                s for s in search_engine.search_history 
                if s['timestamp'] > cutoff_date
            ]
            
            # Nettoyer les recherches populaires avec peu d'occurrences
            min_count = 2
            search_engine.popular_searches = {
                query: count for query, count in search_engine.popular_searches.items()
                if count >= min_count
            }
            
            logger.info("Nettoyage de l'historique de recherche terminé")
            
        except Exception as e:
            logger.error(f"Erreur lors du nettoyage de l'historique: {e}")
    
    @staticmethod
    def generate_search_insights():
        """Génère des insights sur les recherches"""
        try:
            analytics = search_engine.get_search_analytics()
            
            if analytics['total_searches'] > 0:
                # Créer un insight sur les tendances de recherche
                from Core.models import AIInsight
                
                AIInsight.objects.create(
                    type='SEARCH',
                    title='Analyse des Recherches Utilisateurs',
                    description=f"Analyse de {analytics['total_searches']} recherches effectuées",
                    data=analytics,
                    confidence_score=0.9,
                    recommendations=[
                        f"Top recherche: {analytics['popular_queries'][0]['query']}" if analytics['popular_queries'] else "Aucune recherche populaire",
                        f"{analytics['searches_24h']} recherches dans les dernières 24h",
                        f"{analytics['unique_queries']} requêtes uniques"
                    ]
                )
            
            return analytics
            
        except Exception as e:
            logger.error(f"Erreur lors de la génération des insights de recherche: {e}")
            return None
