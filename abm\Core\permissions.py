"""
Système de permissions pour l'application ABM
"""

from rest_framework import permissions
from django.contrib.auth import get_user_model

User = get_user_model()


class IsAuthenticated(permissions.BasePermission):
    """Permission de base : utilisateur authentifié"""
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated)


class HasModulePermission(permissions.BasePermission):
    """Permission basée sur les modules"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Les superusers ont tous les droits
        if request.user.is_superuser:
            return True
        
        # Pour l'instant, tous les utilisateurs authentifiés ont accès
        # TODO: Implémenter un système de permissions granulaires
        return True


class IsOwnerOrReadOnly(permissions.BasePermission):
    """Permission : propriétaire ou lecture seule"""
    
    def has_object_permission(self, request, view, obj):
        # Permissions de lecture pour tous
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Permissions d'écriture seulement pour le propriétaire
        return obj.created_by == request.user


class IsAdminOrReadOnly(permissions.BasePermission):
    """Permission : admin ou lecture seule"""
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_staff


class CanManageFacturation(permissions.BasePermission):
    """Permission spécifique pour la facturation"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Les superusers et staff ont accès
        if request.user.is_superuser or request.user.is_staff:
            return True
        
        # TODO: Vérifier les permissions spécifiques du module facturation
        return hasattr(request.user, 'role') and request.user.role in ['COMPTABLE', 'ADMIN']


class CanManageClients(permissions.BasePermission):
    """Permission spécifique pour la gestion des clients"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser or request.user.is_staff:
            return True
        
        return True  # Tous les utilisateurs authentifiés pour l'instant


class CanManageProducts(permissions.BasePermission):
    """Permission spécifique pour la gestion des produits"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser or request.user.is_staff:
            return True
        
        return True  # Tous les utilisateurs authentifiés pour l'instant


class CanViewReports(permissions.BasePermission):
    """Permission pour voir les rapports"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser or request.user.is_staff:
            return True
        
        # TODO: Implémenter la logique métier pour les rapports
        return hasattr(request.user, 'role') and request.user.role in ['COMPTABLE', 'ADMIN', 'MANAGER']


def has_module_permission(user, module_name, action='view'):
    """
    Fonction utilitaire pour vérifier les permissions de module
    
    Args:
        user: Instance utilisateur
        module_name: Nom du module (ex: 'facturation', 'clients')
        action: Action demandée ('view', 'add', 'change', 'delete')
    
    Returns:
        bool: True si l'utilisateur a la permission
    """
    if not user or not user.is_authenticated:
        return False
    
    if user.is_superuser:
        return True
    
    # TODO: Implémenter la logique de permissions granulaires
    # basée sur les rôles et les modules
    
    return True  # Temporaire : tous les utilisateurs authentifiés


def get_user_permissions(user):
    """
    Récupère toutes les permissions d'un utilisateur
    
    Args:
        user: Instance utilisateur
    
    Returns:
        dict: Dictionnaire des permissions par module
    """
    if not user or not user.is_authenticated:
        return {}
    
    if user.is_superuser:
        return {
            'facturation': ['view', 'add', 'change', 'delete'],
            'clients': ['view', 'add', 'change', 'delete'],
            'produits': ['view', 'add', 'change', 'delete'],
            'commandes': ['view', 'add', 'change', 'delete'],
            'stock': ['view', 'add', 'change', 'delete'],
            'paiements': ['view', 'add', 'change', 'delete'],
            'fournisseurs': ['view', 'add', 'change', 'delete'],
            'comptabilite': ['view', 'add', 'change', 'delete'],
            'rapports': ['view', 'add', 'change', 'delete'],
            'ecommerce': ['view', 'add', 'change', 'delete'],
        }
    
    # TODO: Implémenter la logique basée sur les rôles
    permissions = {}
    
    # Permissions de base pour tous les utilisateurs authentifiés
    base_modules = ['facturation', 'clients', 'produits', 'commandes']
    for module in base_modules:
        permissions[module] = ['view', 'add', 'change']
    
    # Permissions spéciales selon le rôle
    user_role = getattr(user, 'role', 'NORMAL')
    
    if user_role in ['COMPTABLE', 'ADMIN']:
        permissions.update({
            'paiements': ['view', 'add', 'change', 'delete'],
            'comptabilite': ['view', 'add', 'change', 'delete'],
            'rapports': ['view', 'add', 'change'],
        })
    
    if user_role == 'ADMIN':
        for module in permissions:
            if 'delete' not in permissions[module]:
                permissions[module].append('delete')
    
    return permissions
