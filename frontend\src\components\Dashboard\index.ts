/**
 * Index des composants Dashboard
 * Exporte tous les composants du dashboard pour faciliter les imports
 */

// Composants principaux
export { default as SimpleDashboard } from "./SimpleDashboard";

// Alias pour compatibilité
export { default as ModernDashboard } from "./SimpleDashboard";
export { default as DashboardLayout } from "./SimpleDashboard";
export { default as Header } from "./SimpleDashboard";
export { default as Sidebar } from "./SimpleDashboard";
export { default as DashboardMain } from "./SimpleDashboard";

// Composants utilitaires et widgets - Disponibles dans SimpleDashboard
// export { StatCard, ChartWidget, etc. } - Intégrés dans SimpleDashboard

// Types (si nécessaire)
export interface DashboardStats {
  chiffre_affaires_mensuel: number;
  nombre_factures_mois: number;
  nombre_clients_actifs: number;
  commandes_en_attente: number;
  produits_stock_faible: number;
  ventes_aujourd_hui: number;
  objectif_mensuel: number;
  taux_conversion: number;
}

export interface DashboardActivity {
  id: string;
  type: string;
  message: string;
  time: string;
  icon: string;
  data?: any;
}

export interface DashboardInsight {
  id: string;
  type: "info" | "warning" | "success" | "error";
  title: string;
  description: string;
  icon: string;
  action?: {
    label: string;
    url: string;
  };
}

// Constantes utiles
export const DASHBOARD_ROUTES = {
  MAIN: "/dashboard",
  INVOICES: "/invoices",
  CLIENTS: "/clients",
  PRODUCTS: "/products",
  STOCK: "/stock",
  REPORTS: "/reports",
  SETTINGS: "/settings",
} as const;

export const DASHBOARD_COLORS = {
  PRIMARY: "#667eea",
  SUCCESS: "#27ae60",
  WARNING: "#f39c12",
  DANGER: "#e74c3c",
  INFO: "#17a2b8",
  SECONDARY: "#6c757d",
} as const;

export const DASHBOARD_ICONS = {
  DASHBOARD: "📊",
  INVOICES: "📄",
  CLIENTS: "👥",
  PRODUCTS: "📦",
  STOCK: "📈",
  REPORTS: "📊",
  SETTINGS: "⚙️",
  NOTIFICATIONS: "🔔",
  USER: "👤",
  LOGOUT: "🚪",
} as const;
